{".class": "MypyFile", "_fullname": "IPython.core.inputtransformer2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CommandCompiler": {".class": "SymbolTableNode", "cross_ref": "codeop.CommandCompiler", "kind": "Gdef"}, "Compile": {".class": "SymbolTableNode", "cross_ref": "codeop.Compile", "kind": "Gdef"}, "ESCAPE_DOUBLES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESCAPE_DOUBLES", "name": "ESCAPE_DOUBLES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "ESCAPE_SINGLES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESCAPE_SINGLES", "name": "ESCAPE_SINGLES", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "ESC_HELP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_HELP", "name": "ESC_HELP", "setter_type": null, "type": "builtins.str"}}, "ESC_HELP2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_HELP2", "name": "ESC_HELP2", "setter_type": null, "type": "builtins.str"}}, "ESC_MAGIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_MAGIC", "name": "ESC_MAGIC", "setter_type": null, "type": "builtins.str"}}, "ESC_MAGIC2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_MAGIC2", "name": "ESC_MAGIC2", "setter_type": null, "type": "builtins.str"}}, "ESC_PAREN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_PAREN", "name": "ESC_PAREN", "setter_type": null, "type": "builtins.str"}}, "ESC_QUOTE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_QUOTE", "name": "ESC_QUOTE", "setter_type": null, "type": "builtins.str"}}, "ESC_QUOTE2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_QUOTE2", "name": "ESC_QUOTE2", "setter_type": null, "type": "builtins.str"}}, "ESC_SHELL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_SHELL", "name": "ESC_SHELL", "setter_type": null, "type": "builtins.str"}}, "ESC_SH_CAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ESC_SH_CAP", "name": "ESC_SH_CAP", "setter_type": null, "type": "builtins.str"}}, "EscapedCommand": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.inputtransformer2.TokenTransformBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.EscapedCommand", "name": "Escaped<PERSON>ommand", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.EscapedCommand", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.EscapedCommand", "IPython.core.inputtransformer2.TokenTransformBase", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.EscapedCommand.find", "name": "find", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.EscapedCommand.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.EscapedCommand"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.EscapedCommand.transform", "name": "transform", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.EscapedCommand.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.EscapedCommand", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HelpEnd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.inputtransformer2.TokenTransformBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.HelpEnd", "name": "HelpEnd", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.HelpEnd", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.HelpEnd", "IPython.core.inputtransformer2.TokenTransformBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "start", "q_locn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.HelpEnd.__init__", "name": "__init__", "type": null}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.HelpEnd.find", "name": "find", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.HelpEnd.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.HelpEnd"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of HelpEnd", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.HelpEnd.priority", "name": "priority", "setter_type": null, "type": "builtins.int"}}, "q_col": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.HelpEnd.q_col", "name": "q_col", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "q_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.HelpEnd.q_line", "name": "q_line", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.HelpEnd.transform", "name": "transform", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.HelpEnd.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.HelpEnd", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MagicAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.inputtransformer2.TokenTransformBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.MagicAssign", "name": "MagicAssign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.MagicAssign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.MagicAssign", "IPython.core.inputtransformer2.TokenTransformBase", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.MagicAssign.find", "name": "find", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.MagicAssign.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.MagicAssign"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of MagicAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.core.inputtransformer2.MagicAssign.transform", "name": "transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["IPython.core.inputtransformer2.MagicAssign", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform of MagicAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.MagicAssign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.MagicAssign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaybeAsyncCommandCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["codeop.CommandCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler", "name": "MaybeAsyncCommandCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.MaybeAsyncCommandCompiler", "codeop.CommandCompiler", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "extra_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaybeAsyncCompile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["codeop.Compile"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.MaybeAsyncCompile", "name": "MaybeAsyncCompile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.MaybeAsyncCompile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.MaybeAsyncCompile", "codeop.Compile", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "extra_flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.MaybeAsyncCompile.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.MaybeAsyncCompile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.MaybeAsyncCompile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PromptStripper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.PromptStripper", "name": "PromptStripper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.PromptStripper", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.PromptStripper", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.PromptStripper.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "prompt_re", "initial_re"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.PromptStripper.__init__", "name": "__init__", "type": null}}, "_strip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.PromptStripper._strip", "name": "_strip", "type": null}}, "initial_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.PromptStripper.initial_re", "name": "initial_re", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prompt_re": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.PromptStripper.prompt_re", "name": "prompt_re", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.PromptStripper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.PromptStripper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SystemAssign": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["IPython.core.inputtransformer2.TokenTransformBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.SystemAssign", "name": "SystemAssign", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.SystemAssign", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.SystemAssign", "IPython.core.inputtransformer2.TokenTransformBase", "builtins.object"], "names": {".class": "SymbolTable", "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find", "name": "find", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.SystemAssign"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of SystemAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_post_312": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find_post_312", "name": "find_post_312", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find_post_312", "name": "find_post_312", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.SystemAssign"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_post_312 of SystemAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "find_pre_312": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find_pre_312", "name": "find_pre_312", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.SystemAssign.find_pre_312", "name": "find_pre_312", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.SystemAssign"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_pre_312 of SystemAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.core.inputtransformer2.SystemAssign.transform", "name": "transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["IPython.core.inputtransformer2.SystemAssign", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform of SystemAssign", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.SystemAssign.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.SystemAssign", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TRANSFORM_LOOP_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.TRANSFORM_LOOP_LIMIT", "name": "TRANSFORM_LOOP_LIMIT", "setter_type": null, "type": "builtins.int"}}, "TokenTransformBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.TokenTransformBase", "name": "TokenTransformBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TokenTransformBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.TokenTransformBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.__init__", "name": "__init__", "type": null}}, "find": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.find", "name": "find", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.find", "name": "find", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "tokens_by_line"], "arg_types": [{".class": "TypeType", "item": "IPython.core.inputtransformer2.TokenTransformBase"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find of TokenTransformBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.priority", "name": "priority", "setter_type": null, "type": "builtins.int"}}, "sortby": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.sortby", "name": "sortby", "type": null}}, "start_col": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.start_col", "name": "start_col", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "start_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.start_line", "name": "start_line", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.core.inputtransformer2.TokenTransformBase.transform", "name": "transform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["IPython.core.inputtransformer2.TokenTransformBase", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform of TokenTransformBase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.TokenTransformBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.TokenTransformBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TransformerManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.core.inputtransformer2.TransformerManager", "name": "TransformerManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TransformerManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.core.inputtransformer2", "mro": ["IPython.core.inputtransformer2.TransformerManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TransformerManager.__init__", "name": "__init__", "type": null}}, "check_complete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.core.inputtransformer2.TransformerManager.check_complete", "name": "check_complete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cell"], "arg_types": ["IPython.core.inputtransformer2.TransformerManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "check_complete of TransformerManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cleanup_transforms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.TransformerManager.cleanup_transforms", "name": "cleanup_transforms", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "do_one_token_transform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TransformerManager.do_one_token_transform", "name": "do_one_token_transform", "type": null}}, "do_token_transforms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.TransformerManager.do_token_transforms", "name": "do_token_transforms", "type": null}}, "line_transforms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.TransformerManager.line_transforms", "name": "line_transforms", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["lines"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cell_magic", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "token_transformers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.core.inputtransformer2.TransformerManager.token_transformers", "name": "token_transformers", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "transform_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.core.inputtransformer2.TransformerManager.transform_cell", "name": "transform_cell", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cell"], "arg_types": ["IPython.core.inputtransformer2.TransformerManager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transform_cell of TransformerManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.core.inputtransformer2.TransformerManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.core.inputtransformer2.TransformerManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.core.inputtransformer2.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_extra_flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2._extra_flags", "name": "_extra_flags", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 8192}}}, "_find_assign_op": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._find_assign_op", "name": "_find_assign_op", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token_line"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_assign_op", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_help_end_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2._help_end_re", "name": "_help_end_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_indent_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2._indent_re", "name": "_indent_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_make_help_call": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["target", "esc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._make_help_call", "name": "_make_help_call", "type": null}}, "_tr_help": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_help", "name": "_tr_help", "type": null}}, "_tr_help2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_help2", "name": "_tr_help2", "type": null}}, "_tr_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_magic", "name": "_tr_magic", "type": null}}, "_tr_paren": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_paren", "name": "_tr_paren", "type": null}}, "_tr_quote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_quote", "name": "_tr_quote", "type": null}}, "_tr_quote2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2._tr_quote2", "name": "_tr_quote2", "type": null}}, "assemble_continued_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["lines", "start", "end_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.assemble_continued_line", "name": "assemble_continued_line", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["lines", "start", "end_line"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "assemble_continued_line", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "cell_magic": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.cell_magic", "name": "cell_magic", "type": null}}, "classic_prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.classic_prompt", "name": "classic_prompt", "setter_type": null, "type": "IPython.core.inputtransformer2.PromptStripper"}}, "compile_command": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.compile_command", "name": "compile_command", "setter_type": null, "type": "IPython.core.inputtransformer2.MaybeAsyncCommandCompiler"}}, "dedent": {".class": "SymbolTableNode", "cross_ref": "textwrap.dedent", "kind": "Gdef"}, "find_end_of_continued_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["lines", "start_line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.find_end_of_continued_line", "name": "find_end_of_continued_line", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["lines", "start_line"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "find_end_of_continued_line", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "find_last_indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.find_last_indent", "name": "find_last_indent", "type": null}}, "has_sunken_brackets": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.has_sunken_brackets", "name": "has_sunken_brackets", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tokens"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "tokenize.TokenInfo"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "has_sunken_brackets", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ipython_prompt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.ipython_prompt", "name": "ipython_prompt", "setter_type": null, "type": "IPython.core.inputtransformer2.PromptStripper"}}, "leading_empty_lines": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.leading_empty_lines", "name": "leading_empty_lines", "type": null}}, "leading_indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.leading_indent", "name": "leading_indent", "type": null}}, "make_tokens_by_line": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.core.inputtransformer2.make_tokens_by_line", "name": "make_tokens_by_line", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lines"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_tokens_by_line", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tokenize": {".class": "SymbolTableNode", "cross_ref": "tokenize", "kind": "Gdef"}, "tokenutil": {".class": "SymbolTableNode", "cross_ref": "IPython.utils.tokenutil", "kind": "Gdef"}, "tr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.core.inputtransformer2.tr", "name": "tr", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "builtins.function"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\core\\inputtransformer2.py"}