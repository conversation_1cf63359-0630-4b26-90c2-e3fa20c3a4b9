{"data_mtime": 1751955670, "dep_lines": [50, 36, 42, 47, 47, 47, 52, 56, 57, 58, 1, 1, 1, 1, 1, 1, 1, 1, 45, 54], "dep_prios": [10, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["collections.abc", "__future__", "pathlib", "os", "stat", "time", "collections", "pickle", "errno", "sys", "builtins", "_frozen_importlib", "_typeshed", "abc", "posixpath", "types", "typing", "typing_extensions"], "hash": "0c19d85a605767e079666a6bd4230be4e99d3b4a", "id": "IPython.external.pickleshare", "ignore_all": true, "interface_hash": "2b629cdc9c8c8541e60feb27f8c4a37b4bb8cb8c", "mtime": 1748947675, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\external\\pickleshare.py", "plugin_data": null, "size": 9974, "suppressed": ["pathlib2", "c<PERSON><PERSON><PERSON>"], "version_id": "1.16.1"}