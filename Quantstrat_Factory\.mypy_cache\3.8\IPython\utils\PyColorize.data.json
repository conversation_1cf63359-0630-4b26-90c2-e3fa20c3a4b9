{".class": "MypyFile", "_fullname": "IPython.utils.PyColorize", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "C1": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C1", "name": "C1", "setter_type": null, "type": "builtins.str"}}, "C2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C2", "name": "C2", "setter_type": null, "type": "builtins.str"}}, "C3": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C3", "name": "C3", "setter_type": null, "type": "builtins.str"}}, "C5": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C5", "name": "C5", "setter_type": null, "type": "builtins.str"}}, "C6": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C6", "name": "C6", "setter_type": null, "type": "builtins.str"}}, "C7": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.C7", "name": "C7", "setter_type": null, "type": "builtins.str"}}, "PRIDE_GREEN": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_GREEN", "name": "PRIDE_GREEN", "setter_type": null, "type": "builtins.str"}}, "PRIDE_INDIGO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_INDIGO", "name": "PRIDE_INDIGO", "setter_type": null, "type": "builtins.str"}}, "PRIDE_ORANGE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_ORANGE", "name": "PRIDE_ORANGE", "setter_type": null, "type": "builtins.str"}}, "PRIDE_RED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_RED", "name": "PRIDE_RED", "setter_type": null, "type": "builtins.str"}}, "PRIDE_VIOLET": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_VIOLET", "name": "PRIDE_VIOLET", "setter_type": null, "type": "builtins.str"}}, "PRIDE_YELLOW": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.PRIDE_YELLOW", "name": "PRIDE_YELLOW", "setter_type": null, "type": "builtins.str"}}, "Parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.utils.PyColorize.Parser", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Parser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.utils.PyColorize", "mro": ["IPython.utils.PyColorize.Parser", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "toktype", "toktext", "start_pos", "end_pos", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Parser.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5], "arg_names": ["self", "out", "theme_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.utils.PyColorize.Parser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5], "arg_names": ["self", "out", "theme_name"], "arg_types": ["IPython.utils.PyColorize.Parser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Parse<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_inner_call_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "toktype", "toktext", "start_pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Parser._inner_call_", "name": "_inner_call_", "type": null}}, "_theme_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.utils.PyColorize.Parser._theme_name", "name": "_theme_name", "setter_type": null, "type": "builtins.str"}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "raw", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Parser.format", "name": "format", "type": null}}, "format2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "raw", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Parser.format2", "name": "format2", "type": null}}, "lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.lines", "name": "lines", "setter_type": null, "type": {".class": "NoneType"}}}, "out": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.out", "name": "out", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.pos", "name": "pos", "setter_type": null, "type": {".class": "NoneType"}}}, "raw": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.raw", "name": "raw", "setter_type": null, "type": {".class": "NoneType"}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "val"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.utils.PyColorize.Parser.set", "name": "set", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.set", "name": "set", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_of_any": 7}}}}, "style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "IPython.utils.PyColorize.Parser.style", "name": "style", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.style", "name": "style", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.utils.PyColorize.Parser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "style of Parser", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "theme_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "IPython.utils.PyColorize.Parser.theme_name", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "IPython.utils.PyColorize.Parser.theme_name", "name": "theme_name", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Parser.theme_name", "name": "theme_name", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["IPython.utils.PyColorize.Parser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "theme_name of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.utils.PyColorize.Parser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "theme_name of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.utils.PyColorize.Parser.theme_name", "name": "theme_name", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "theme_name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["IPython.utils.PyColorize.Parser", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "theme_name of <PERSON><PERSON><PERSON>", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": 1, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["IPython.utils.PyColorize.Parser"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "theme_name", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.utils.PyColorize.Parser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.utils.PyColorize.Parser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef", "module_public": false}, "Style": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Style", "name": "Style", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Style", "source_any": null, "type_of_any": 3}}}, "Symbols": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.utils.PyColorize.Symbols", "name": "Symbols", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Symbols", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.utils.PyColorize", "mro": ["IPython.utils.PyColorize.Symbols", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["top_line", "builtins.str"], ["arrow_body", "builtins.str"], ["arrow_head", "builtins.str"]], "readonly_keys": [], "required_keys": ["arrow_body", "arrow_head", "top_line"]}}}, "Terminal256Formatter": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Terminal256Formatter", "name": "Terminal256Formatter", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Terminal256Formatter", "source_any": null, "type_of_any": 3}}}, "Theme": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "IPython.utils.PyColorize.Theme", "name": "Theme", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Theme", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "IPython.utils.PyColorize", "mro": ["IPython.utils.PyColorize.Theme", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "name", "base", "extra_style", "symbols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.PyColorize.Theme.__init__", "name": "__init__", "type": null}}, "as_pygments_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "IPython.utils.PyColorize.Theme.as_pygments_style", "name": "as_pygments_style", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Theme.as_pygments_style", "name": "as_pygments_style", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.cache", "source_any": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.cache", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.utils.PyColorize.Theme.base", "name": "base", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "extra_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.utils.PyColorize.Theme.extra_style", "name": "extra_style", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize._TokenType", "source_any": null, "type_of_any": 3}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.utils.PyColorize.Theme.format", "name": "format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["IPython.utils.PyColorize.Theme", {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.TypeAlias", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "format of Theme", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_arrow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "width"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "IPython.utils.PyColorize.Theme.make_arrow", "name": "make_arrow", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "width"], "arg_types": ["IPython.utils.PyColorize.Theme", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "make_arrow of Theme", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.utils.PyColorize.Theme.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "symbols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "IPython.utils.PyColorize.Theme.symbols", "name": "symbols", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.utils.PyColorize.Symbols"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "IPython.utils.PyColorize.Theme.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "IPython.utils.PyColorize.Theme", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.Token", "name": "Token", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": null, "type_of_any": 3}}}, "TokenStream": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.TokenStream", "name": "TokenStream", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.TypeAlias", "source_any": null, "type_of_any": 3}}}, "TypeAlias": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.TypeAlias", "name": "TypeAlias", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.TypeAlias", "source_any": null, "type_of_any": 3}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "White": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.White", "name": "White", "setter_type": null, "type": "builtins.str"}}, "_KEYWORD": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize._KEYWORD", "name": "_KEYWORD", "setter_type": null, "type": "builtins.int"}}, "_TEXT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize._TEXT", "name": "_TEXT", "setter_type": null, "type": "builtins.int"}}, "_TokenType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize._TokenType", "name": "_TokenType", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize._TokenType", "source_any": null, "type_of_any": 3}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.PyColorize.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_default_symbols": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.utils.PyColorize._default_symbols", "name": "_default_symbols", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "IPython.utils.PyColorize.Symbols"}}}, "_pygment_token_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.utils.PyColorize._pygment_token_mapping", "name": "_pygment_token_mapping", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize._TokenType", "source_any": null, "type_of_any": 3}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cache": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.cache", "name": "cache", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.cache", "source_any": null, "type_of_any": 3}}}, "generate_tokens": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.generate_tokens", "name": "generate_tokens", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["readline"], "arg_types": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "tokenize.TokenInfo"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_style_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.get_style_by_name", "name": "get_style_by_name", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.get_style_by_name", "source_any": null, "type_of_any": 3}}}, "keyword": {".class": "SymbolTableNode", "cross_ref": "keyword", "kind": "Gdef", "module_public": false}, "lightbg_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.lightbg_theme", "name": "lightbg_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "linux_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.linux_theme", "name": "linux_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "neutral_nt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_nt", "name": "neutral_nt", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "neutral_posix": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_posix", "name": "neutral_posix", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "neutral_pygments_equiv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_pygments_equiv", "name": "neutral_pygments_equiv", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "neutral_pygments_nt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_pygments_nt", "name": "neutral_pygments_nt", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "neutral_pygments_posix": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_pygments_posix", "name": "neutral_pygments_posix", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "neutral_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.neutral_theme", "name": "neutral_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "nocolors_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.nocolors_theme", "name": "nocolors_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "pl": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.pl", "name": "pl", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.Token", "source_any": null, "type_of_any": 3}, "type_of_any": 7}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "pride_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.pride_theme", "name": "pride_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "pridel_theme": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.pridel_theme", "name": "pridel_theme", "setter_type": null, "type": "IPython.utils.PyColorize.Theme"}}, "pygments": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "IPython.utils.PyColorize.pygments", "name": "pygments", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "IPython.utils.PyColorize.pygments", "source_any": null, "type_of_any": 3}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "theme_table": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "IPython.utils.PyColorize.theme_table", "name": "theme_table", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "IPython.utils.PyColorize.Theme"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "token": {".class": "SymbolTableNode", "cross_ref": "token", "kind": "Gdef", "module_public": false}, "tokenize": {".class": "SymbolTableNode", "cross_ref": "tokenize", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\utils\\PyColorize.py"}