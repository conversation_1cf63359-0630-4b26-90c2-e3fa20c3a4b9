{"data_mtime": 1751955670, "dep_lines": [1, 2, 3, 4, 5, 6, 7, 8, 15, 1, 1, 1, 1, 1, 11, 12, 13, 14, 10], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 5, 5, 5, 5, 10], "dependencies": ["keyword", "os", "sys", "token", "tokenize", "warnings", "io", "typing", "functools", "builtins", "_frozen_importlib", "_io", "_warnings", "abc"], "hash": "9c1b31759166a591ed96c50eee9f7d2b36ed2bd4", "id": "IPython.utils.PyColorize", "ignore_all": true, "interface_hash": "91b0cf298f2d2adf56790bc907d0587e6af8e56e", "mtime": 1748947675, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\utils\\PyColorize.py", "plugin_data": null, "size": 15553, "suppressed": ["pygments.formatters.terminal256", "pygments.style", "pygments.styles", "pygments.token", "pygments"], "version_id": "1.16.1"}