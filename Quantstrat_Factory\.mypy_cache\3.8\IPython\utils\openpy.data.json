{".class": "MypyFile", "_fullname": "IPython.utils.openpy", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIO": {".class": "SymbolTableNode", "cross_ref": "_io.BytesIO", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "_io.TextIOWrapper", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "IPython.utils.openpy.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cookie_comment_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.openpy.cookie_comment_re", "name": "cookie_comment_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "cookie_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "IPython.utils.openpy.cookie_re", "name": "cookie_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "detect_encoding": {".class": "SymbolTableNode", "cross_ref": "tokenize.detect_encoding", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "open": {".class": "SymbolTableNode", "cross_ref": "tokenize.open", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "read_py_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["filename", "skip_encoding_cookie"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.openpy.read_py_file", "name": "read_py_file", "type": null}}, "read_py_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["url", "errors", "skip_encoding_cookie"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.openpy.read_py_url", "name": "read_py_url", "type": null}}, "source_to_unicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["txt", "errors", "skip_encoding_cookie"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.openpy.source_to_unicode", "name": "source_to_unicode", "type": null}}, "strip_encoding_cookie": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filelike"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "IPython.utils.openpy.strip_encoding_cookie", "name": "strip_encoding_cookie", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\utils\\openpy.py"}