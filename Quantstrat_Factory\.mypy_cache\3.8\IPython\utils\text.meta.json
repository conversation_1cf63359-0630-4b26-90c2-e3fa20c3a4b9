{"data_mtime": 1751955670, "dep_lines": [10, 11, 12, 13, 14, 15, 17, 19, 35, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["os", "re", "string", "sys", "textwrap", "warnings", "pathlib", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "genericpath"], "hash": "565128c2ac8d5916a3ab1b6b2d86fad1006a143b", "id": "IPython.utils.text", "ignore_all": true, "interface_hash": "f3c15987b05e87154da080dffad65c04a347e237", "mtime": 1748947675, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\IPython\\utils\\text.py", "plugin_data": null, "size": 18788, "suppressed": [], "version_id": "1.16.1"}