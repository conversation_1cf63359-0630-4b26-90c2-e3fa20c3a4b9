{".class": "MypyFile", "_fullname": "_sqlite3", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Connection", "kind": "Gdef"}, "Cursor": {".class": "SymbolTableNode", "cross_ref": "sqlite3.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "DataError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DataError", "kind": "Gdef"}, "DatabaseError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DatabaseError", "kind": "Gdef"}, "Error": {".class": "SymbolTableNode", "cross_ref": "sqlite3.E<PERSON>r", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.IntegrityError", "kind": "Gdef"}, "InterfaceError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InterfaceError", "kind": "Gdef"}, "InternalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InternalError", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NotSupportedError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.NotSupportedError", "kind": "Gdef"}, "OperationalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.OperationalError", "kind": "Gdef"}, "OptimizedUnicode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_sqlite3.OptimizedUnicode", "line": 312, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "builtins.str"}}, "PARSE_COLNAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.PARSE_COLNAMES", "name": "PARSE_COLNAMES", "setter_type": null, "type": "builtins.int"}}, "PARSE_DECLTYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.PARSE_DECLTYPES", "name": "PARSE_DECLTYPES", "setter_type": null, "type": "builtins.int"}}, "PrepareProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlite3.PrepareProtocol", "kind": "Gdef"}, "ProgrammingError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.ProgrammingError", "kind": "Gdef"}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Row", "kind": "Gdef"}, "SQLITE_ALTER_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ALTER_TABLE", "name": "SQLITE_ALTER_TABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_ANALYZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ANALYZE", "name": "SQLITE_ANALYZE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_ATTACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ATTACH", "name": "SQLITE_ATTACH", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_INDEX", "name": "SQLITE_CREATE_INDEX", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TABLE", "name": "SQLITE_CREATE_TABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_INDEX", "name": "SQLITE_CREATE_TEMP_INDEX", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_TABLE", "name": "SQLITE_CREATE_TEMP_TABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_TRIGGER", "name": "SQLITE_CREATE_TEMP_TRIGGER", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_VIEW", "name": "SQLITE_CREATE_TEMP_VIEW", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TRIGGER", "name": "SQLITE_CREATE_TRIGGER", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_VIEW", "name": "SQLITE_CREATE_VIEW", "setter_type": null, "type": "builtins.int"}}, "SQLITE_CREATE_VTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_VTABLE", "name": "SQLITE_CREATE_VTABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DELETE", "name": "SQLITE_DELETE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DENY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DENY", "name": "SQLITE_DENY", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DETACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DETACH", "name": "SQLITE_DETACH", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DONE", "name": "SQLITE_DONE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_INDEX", "name": "SQLITE_DROP_INDEX", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TABLE", "name": "SQLITE_DROP_TABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TEMP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_INDEX", "name": "SQLITE_DROP_TEMP_INDEX", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TEMP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_TABLE", "name": "SQLITE_DROP_TEMP_TABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TEMP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_TRIGGER", "name": "SQLITE_DROP_TEMP_TRIGGER", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TEMP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_VIEW", "name": "SQLITE_DROP_TEMP_VIEW", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TRIGGER", "name": "SQLITE_DROP_TRIGGER", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_VIEW", "name": "SQLITE_DROP_VIEW", "setter_type": null, "type": "builtins.int"}}, "SQLITE_DROP_VTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_VTABLE", "name": "SQLITE_DROP_VTABLE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_FUNCTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_FUNCTION", "name": "SQLITE_FUNCTION", "setter_type": null, "type": "builtins.int"}}, "SQLITE_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IGNORE", "name": "SQLITE_IGNORE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_INSERT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_INSERT", "name": "SQLITE_INSERT", "setter_type": null, "type": "builtins.int"}}, "SQLITE_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_OK", "name": "SQLITE_OK", "setter_type": null, "type": "builtins.int"}}, "SQLITE_PRAGMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_PRAGMA", "name": "SQLITE_PRAGMA", "setter_type": null, "type": "builtins.int"}}, "SQLITE_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READ", "name": "SQLITE_READ", "setter_type": null, "type": "builtins.int"}}, "SQLITE_RECURSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_RECURSIVE", "name": "SQLITE_RECURSIVE", "setter_type": null, "type": "builtins.int"}}, "SQLITE_REINDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_REINDEX", "name": "SQLITE_REINDEX", "setter_type": null, "type": "builtins.int"}}, "SQLITE_SAVEPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_SAVEPOINT", "name": "SQLITE_SAVEPOINT", "setter_type": null, "type": "builtins.int"}}, "SQLITE_SELECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_SELECT", "name": "SQLITE_SELECT", "setter_type": null, "type": "builtins.int"}}, "SQLITE_TRANSACTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_TRANSACTION", "name": "SQLITE_TRANSACTION", "setter_type": null, "type": "builtins.int"}}, "SQLITE_UPDATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_UPDATE", "name": "SQLITE_UPDATE", "setter_type": null, "type": "builtins.int"}}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Warning": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Warning", "kind": "Gdef"}, "_Adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": 1, "name": "_T", "namespace": "_sqlite3._Adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "_sqlite3._Adapter", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": 1, "name": "_T", "namespace": "_sqlite3._Adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._SqliteData"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ConnectionT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "name": "_ConnectionT", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "_Converter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_sqlite3._Converter", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_SqliteData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_sqlite3._SqliteData", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "typing_extensions.Buffer", "builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adapt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_sqlite3.adapt", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.adapt", "name": "adapt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.adapt", "name": "adapt", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "adapters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.adapters", "name": "adapters", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "_sqlite3._Adapter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "complete_statement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.complete_statement", "name": "complete_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["statement"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "complete_statement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_sqlite3.connect", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}]}}}, "converters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.converters", "name": "converters", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._Converter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "enable_callback_tracebacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.enable_callback_tracebacks", "name": "enable_callback_tracebacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "enable_callback_tracebacks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enable_shared_cache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["do_enable"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.enable_shared_cache", "name": "enable_shared_cache", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["do_enable"], "arg_types": ["builtins.int"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "enable_shared_cache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "register_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.register_adapter", "name": "register_adapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_sqlite3._Adapter"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_adapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "register_converter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.register_converter", "name": "register_converter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._Converter"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "register_converter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqlite_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.sqlite_version", "name": "sqlite_version", "setter_type": null, "type": "builtins.str"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.version", "name": "version", "setter_type": null, "type": "builtins.str"}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\_sqlite3.pyi"}