{".class": "MypyFile", "_fullname": "annotated_types", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "BaseMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.BaseMetadata", "name": "BaseMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.BaseMetadata", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "annotated_types.BaseMetadata.__slots__", "name": "__slots__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.BaseMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.BaseMetadata", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DocInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "annotated_types.DocInfo", "name": "DocInfo", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "annotated_types.DocInfo", "source_any": null, "type_of_any": 3}}}, "EllipsisType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "annotated_types.EllipsisType", "name": "EllipsisType", "setter_type": null, "type": {".class": "TypeType", "item": "builtins.ellipsis"}}}, "Ge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Ge", "name": "Ge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Ge", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 127, "name": "ge", "type": "annotated_types.SupportsGe"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Ge", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Ge.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ge"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Ge.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ge"], "arg_types": ["annotated_types.Ge", "annotated_types.SupportsGe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Ge", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["ge"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Ge.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["ge"], "arg_types": ["annotated_types.SupportsGe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Ge.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["ge"], "arg_types": ["annotated_types.SupportsGe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>e", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "ge": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Ge.ge", "name": "ge", "setter_type": null, "type": "annotated_types.SupportsGe"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Ge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Ge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GroupedMetadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__iter__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.GroupedMetadata", "name": "GroupedMetadata", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol", "runtime_protocol"], "fullname": "annotated_types.GroupedMetadata", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.GroupedMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__is_annotated_types_grouped_metadata__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "annotated_types.GroupedMetadata.__is_annotated_types_grouped_metadata__", "name": "__is_annotated_types_grouped_metadata__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["annotated_types.GroupedMetadata"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__is_annotated_types_grouped_metadata__ of GroupedMetadata", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "annotated_types.GroupedMetadata.__is_annotated_types_grouped_metadata__", "name": "__is_annotated_types_grouped_metadata__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["annotated_types.GroupedMetadata"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__is_annotated_types_grouped_metadata__ of GroupedMetadata", "ret_type": {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "annotated_types.GroupedMetadata.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["annotated_types.GroupedMetadata"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of GroupedMetadata", "ret_type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.GroupedMetadata.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.GroupedMetadata", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Gt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Gt", "name": "Gt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Gt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 116, "name": "gt", "type": "annotated_types.SupportsGt"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Gt", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Gt.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Gt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gt"], "arg_types": ["annotated_types.Gt", "annotated_types.SupportsGt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Gt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["gt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Gt.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["gt"], "arg_types": ["annotated_types.SupportsGt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Gt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Gt.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["gt"], "arg_types": ["annotated_types.SupportsGt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Gt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "gt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Gt.gt", "name": "gt", "setter_type": null, "type": "annotated_types.SupportsGt"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Gt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Gt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Interval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.GroupedMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Interval", "name": "Interval", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Interval", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 212, "name": "gt", "type": {".class": "UnionType", "items": ["annotated_types.SupportsGt", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 213, "name": "ge", "type": {".class": "UnionType", "items": ["annotated_types.SupportsGe", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 214, "name": "lt", "type": {".class": "UnionType", "items": ["annotated_types.SupportsLt", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 215, "name": "le", "type": {".class": "UnionType", "items": ["annotated_types.SupportsLe", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Interval", "annotated_types.GroupedMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Interval.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "gt", "ge", "lt", "le"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Interval.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "gt", "ge", "lt", "le"], "arg_types": ["annotated_types.Interval", {".class": "UnionType", "items": ["annotated_types.SupportsGt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsGe", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLe", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Interval", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "annotated_types.Interval.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["annotated_types.Interval"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of Interval", "ret_type": {".class": "Instance", "args": ["annotated_types.BaseMetadata"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["gt", "ge", "lt", "le"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Interval.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["gt", "ge", "lt", "le"], "arg_types": [{".class": "UnionType", "items": ["annotated_types.SupportsGt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsGe", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLe", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Interval", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Interval.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["gt", "ge", "lt", "le"], "arg_types": [{".class": "UnionType", "items": ["annotated_types.SupportsGt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsGe", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["annotated_types.SupportsLe", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Interval", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "ge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Interval.ge", "name": "ge", "setter_type": null, "type": {".class": "UnionType", "items": ["annotated_types.SupportsGe", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "gt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Interval.gt", "name": "gt", "setter_type": null, "type": {".class": "UnionType", "items": ["annotated_types.SupportsGt", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "le": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Interval.le", "name": "le", "setter_type": null, "type": {".class": "UnionType", "items": ["annotated_types.SupportsLe", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "lt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Interval.lt", "name": "lt", "setter_type": null, "type": {".class": "UnionType", "items": ["annotated_types.SupportsLt", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Interval.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Interval", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IsAscii": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.IsAs<PERSON>i", "upper_bound": "builtins.str", "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsAs<PERSON>i", "line": 387, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.IsAs<PERSON>i", "upper_bound": "builtins.str", "values": [], "variance": 0}}}, "IsDigit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.IsDigit", "upper_bound": "builtins.str", "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsDigit", "line": 380, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.IsDigit", "upper_bound": "builtins.str", "values": [], "variance": 0}}}, "IsDigits": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "annotated_types.IsDigits", "line": 381, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "type_ref": "annotated_types.IsDigit"}}}, "IsFinite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsFinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsFinite", "line": 395, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsFinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "IsInfinite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsInfinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsInfinite", "line": 403, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsInfinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "IsNan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNan", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsNan", "line": 399, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNan", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "IsNotFinite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotFinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsNotFinite", "line": 397, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotFinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "IsNotInfinite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotInfinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsNotInfinite", "line": 405, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotInfinite", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "IsNotNan": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotNan", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.IsNotNan", "line": 401, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "id": 1, "name": "_NumericType", "namespace": "annotated_types.IsNotNan", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "KW_ONLY": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "annotated_types.KW_ONLY", "name": "KW_ONLY", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Le": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Le", "name": "Le", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Le", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 149, "name": "le", "type": "annotated_types.SupportsLe"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Le", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Le.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "le"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Le.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "le"], "arg_types": ["annotated_types.Le", "annotated_types.SupportsLe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Le", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["le"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Le.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["le"], "arg_types": ["annotated_types.SupportsLe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Le.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["le"], "arg_types": ["annotated_types.SupportsLe"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "le": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Le.le", "name": "le", "setter_type": null, "type": "annotated_types.SupportsLe"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Le.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Le", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Len": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.GroupedMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Len", "name": "Len", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Len", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 271, "name": "min_length", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 272, "name": "max_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Len", "annotated_types.GroupedMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Len.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "min_length", "max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Len.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "min_length", "max_length"], "arg_types": ["annotated_types.Len", "builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Len", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "annotated_types.Len.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["annotated_types.Len"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of Len", "ret_type": {".class": "Instance", "args": ["annotated_types.BaseMetadata"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["min_length", "max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Len.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["min_length", "max_length"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Len.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["min_length", "max_length"], "arg_types": ["builtins.int", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "max_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Len.max_length", "name": "max_length", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "min_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "has_explicit_value"], "fullname": "annotated_types.Len.min_length", "name": "min_length", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Len.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Len", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "LowerCase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.LowerCase", "upper_bound": "builtins.str", "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.LowerCase", "line": 368, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.LowerCase", "upper_bound": "builtins.str", "values": [], "variance": 0}}}, "Lt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Lt", "name": "Lt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Lt", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 138, "name": "lt", "type": "annotated_types.SupportsLt"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Lt", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Lt.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Lt.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lt"], "arg_types": ["annotated_types.Lt", "annotated_types.SupportsLt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Lt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["lt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Lt.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["lt"], "arg_types": ["annotated_types.SupportsLt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Lt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Lt.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["lt"], "arg_types": ["annotated_types.SupportsLt"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Lt", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "lt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Lt.lt", "name": "lt", "setter_type": null, "type": "annotated_types.SupportsLt"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Lt.<PERSON>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Lt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MaxLen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.MaxLen", "name": "MaxLen", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.MaxLen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 260, "name": "max_length", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.MaxLen", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.MaxLen.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.MaxLen.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "max_length"], "arg_types": ["annotated_types.MaxLen", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MaxLen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["max_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.MaxLen.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["max_length"], "arg_types": ["builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.MaxLen.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["max_length"], "arg_types": ["builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "max_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.MaxLen.max_length", "name": "max_length", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.MaxLen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.MaxLen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MinLen": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.MinLen", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.MinLen", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 250, "name": "min_length", "type": "builtins.int"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.MinLen", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.MinLen.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "min_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.MinLen.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "min_length"], "arg_types": ["annotated_types.MinLen", "builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MinLen", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["min_length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Min<PERSON>en.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["min_length"], "arg_types": ["builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Min<PERSON>en.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["min_length"], "arg_types": ["builtins.int"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "min_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.MinLen.min_length", "name": "min_length", "setter_type": null, "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.MinLen.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.MinLen", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MultipleOf": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.MultipleOf", "name": "MultipleOf", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.MultipleOf", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 240, "name": "multiple_of", "type": {".class": "UnionType", "items": ["annotated_types.SupportsDiv", "annotated_types.SupportsMod"], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.MultipleOf", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.MultipleOf.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "multiple_of"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.MultipleOf.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "multiple_of"], "arg_types": ["annotated_types.MultipleOf", {".class": "UnionType", "items": ["annotated_types.SupportsDiv", "annotated_types.SupportsMod"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MultipleOf", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["multiple_of"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.MultipleOf.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["multiple_of"], "arg_types": [{".class": "UnionType", "items": ["annotated_types.SupportsDiv", "annotated_types.SupportsMod"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultipleOf", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.MultipleOf.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["multiple_of"], "arg_types": [{".class": "UnionType", "items": ["annotated_types.SupportsDiv", "annotated_types.SupportsMod"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of MultipleOf", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "multiple_of": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.MultipleOf.multiple_of", "name": "multiple_of", "setter_type": null, "type": {".class": "UnionType", "items": ["annotated_types.SupportsDiv", "annotated_types.SupportsMod"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.MultipleOf.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.MultipleOf", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Not": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Not", "name": "Not", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Not", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 360, "name": "func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Not", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "annotated_types.Not.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", null], "arg_types": ["annotated_types.Not", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Not", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Not.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Not.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["annotated_types.Not", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Not", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Not.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Not", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Not.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Not", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_settable_property", "is_ready"], "fullname": "annotated_types.Not.func", "name": "func", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Not.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Not", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Predicate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Predicate", "name": "Predicate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Predicate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 344, "name": "func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Predicate", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Predicate.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Predicate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "func"], "arg_types": ["annotated_types.Predicate", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Predicate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Predicate.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Predicate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Predicate.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["func"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Predicate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "annotated_types.Predicate.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["annotated_types.Predicate"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Predicate", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "annotated_types.Predicate.func", "name": "func", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Predicate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Predicate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_public": false}, "SLOTS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "annotated_types.SLOTS", "name": "SLOTS", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SupportsDiv": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__div__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsDiv", "name": "SupportsDiv", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsDiv", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsDiv", "builtins.object"], "names": {".class": "SymbolTable", "__div__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsDiv.__div__", "name": "__div__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsDiv.__div__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsDiv.__div__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__div__ of SupportsDiv", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsDiv.__div__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsDiv.__div__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsDiv.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsDiv", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsFloat": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsFloat", "kind": "Gdef", "module_public": false}, "SupportsGe": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__ge__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsGe", "name": "SupportsGe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsGe", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsGe", "builtins.object"], "names": {".class": "SymbolTable", "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsGe.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGe.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGe.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__ge__ of SupportsGe", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGe.__ge__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsGe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsGe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsGt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__gt__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsGt", "name": "SupportsGt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsGt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsGt", "builtins.object"], "names": {".class": "SymbolTable", "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsGt.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGt.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGt.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__gt__ of SupportsGt", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsGt.__gt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsGt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsGt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_public": false}, "SupportsLe": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__le__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsLe", "name": "SupportsLe", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsLe", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsLe", "builtins.object"], "names": {".class": "SymbolTable", "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsLe.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLe.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLe.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__le__ of SupportsLe", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLe.__le__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsLe.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsLe", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsLt": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__lt__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsLt", "name": "SupportsLt", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsLt", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsLt", "builtins.object"], "names": {".class": "SymbolTable", "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsLt.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLt.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLt.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__lt__ of SupportsLt", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsLt.__lt__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsLt.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsLt", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SupportsMod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__mod__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.SupportsMod", "name": "SupportsMod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "annotated_types.SupportsMod", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "annotated_types", "mro": ["annotated_types.SupportsMod", "builtins.object"], "names": {".class": "SymbolTable", "__mod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "annotated_types.SupportsMod.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsMod.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsMod.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mod__ of SupportsMod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsMod.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "id": -1, "name": "T", "namespace": "annotated_types.SupportsMod.__mod__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.SupportsMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.SupportsMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.T", "name": "T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Timezone": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Timezone", "name": "Timezone", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Timezone", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 296, "name": "tz", "type": {".class": "UnionType", "items": ["builtins.str", "datetime.tzinfo", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "EllipsisType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Timezone", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Timezone.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Timezone.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tz"], "arg_types": ["annotated_types.Timezone", {".class": "UnionType", "items": ["builtins.str", "datetime.tzinfo", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "EllipsisType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Timezone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["tz"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Timezone.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["tz"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "datetime.tzinfo", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "EllipsisType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Timezone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Timezone.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["tz"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "datetime.tzinfo", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "EllipsisType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Timezone", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "tz": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Timezone.tz", "name": "tz", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "datetime.tzinfo", {".class": "UnboundType", "args": [], "expr": null, "expr_fallback": null, "name": "EllipsisType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Timezone.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Timezone", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Unit": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["annotated_types.BaseMetadata"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "annotated_types.Unit", "name": "Unit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "annotated_types.Unit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 319, "name": "unit", "type": "builtins.str"}], "frozen": true}, "dataclass_tag": {}}, "module_name": "annotated_types", "mro": ["annotated_types.Unit", "annotated_types.BaseMetadata", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "annotated_types.Unit.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "annotated_types.Unit.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "unit"], "arg_types": ["annotated_types.Unit", "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Unit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["unit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "annotated_types.Unit.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["unit"], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Unit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "annotated_types.Unit.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["unit"], "arg_types": ["builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Unit", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "unit": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready"], "fullname": "annotated_types.Unit.unit", "name": "unit", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types.Unit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "annotated_types.Unit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UpperCase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.UpperCase", "upper_bound": "builtins.str", "values": [], "variance": 0}], "column": 0, "fullname": "annotated_types.UpperCase", "line": 374, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "id": 1, "name": "_StrType", "namespace": "annotated_types.UpperCase", "upper_bound": "builtins.str", "values": [], "variance": 0}}}, "_NumericType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._NumericType", "name": "_NumericType", "upper_bound": {".class": "UnionType", "items": ["typing.SupportsFloat", "typing.SupportsIndex"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_StrType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "annotated_types._StrType", "name": "_StrType", "upper_bound": "builtins.str", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "annotated_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "annotated_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "annotated_types.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef", "module_public": false}, "doc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "annotated_types.doc", "name": "doc", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "annotated_types.doc", "source_any": null, "type_of_any": 3}}}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef", "module_public": false}, "runtime_checkable": {".class": "SymbolTableNode", "cross_ref": "typing.runtime_checkable", "kind": "Gdef", "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef", "module_public": false}, "tzinfo": {".class": "SymbolTableNode", "cross_ref": "datetime.tzinfo", "kind": "Gdef", "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\annotated_types\\__init__.py"}