{".class": "MypyFile", "_fullname": "anyio._core._typedattr", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "T_Attr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "name": "T_Attr", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "T_Default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "name": "T_De<PERSON>ult", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedAttributeLookupError": {".class": "SymbolTableNode", "cross_ref": "anyio._core._exceptions.TypedAttributeLookupError", "kind": "Gdef"}, "TypedAttributeProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._typedattr.TypedAttributeProvider", "name": "TypedAttributeProvider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._typedattr.TypedAttributeProvider", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._typedattr", "mro": ["anyio._core._typedattr.TypedAttributeProvider", "builtins.object"], "names": {".class": "SymbolTable", "extra": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_final"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "impl": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "attribute", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_final", "is_decorated", "is_trivial_self"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attribute", "default"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_final", "is_ready", "is_inferred"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "attribute", "default"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attribute"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attribute"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "attribute", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attribute", "default"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra", "name": "extra", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attribute", "default"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attribute"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "attribute", "default"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra of TypedAttributeProvider", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Default", "id": -2, "name": "T_De<PERSON>ult", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "extra_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "name": "extra_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_attributes of TypedAttributeProvider", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "name": "extra_attributes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["anyio._core._typedattr.TypedAttributeProvider"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "extra_attributes of TypedAttributeProvider", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.T_Attr", "id": -1, "name": "T_Attr", "namespace": "anyio._core._typedattr.TypedAttributeProvider.extra_attributes", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.TypedAttributeProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._typedattr.TypedAttributeProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypedAttributeSet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "anyio._core._typedattr.TypedAttributeSet", "name": "TypedAttributeSet", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "anyio._core._typedattr.TypedAttributeSet", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "anyio._core._typedattr", "mro": ["anyio._core._typedattr.TypedAttributeSet", "builtins.object"], "names": {".class": "SymbolTable", "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_trivial_self"], "fullname": "anyio._core._typedattr.TypedAttributeSet.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "anyio._core._typedattr.TypedAttributeSet"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init_subclass__ of TypedAttributeSet", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "anyio._core._typedattr.TypedAttributeSet.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "anyio._core._typedattr.TypedAttributeSet", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "anyio._core._typedattr.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "typed_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "anyio._core._typedattr.typed_attribute", "name": "typed_attribute", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "typed_attribute", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "undefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "anyio._core._typedattr.undefined", "name": "undefined", "setter_type": null, "type": "builtins.object"}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\anyio\\_core\\_typedattr.py"}