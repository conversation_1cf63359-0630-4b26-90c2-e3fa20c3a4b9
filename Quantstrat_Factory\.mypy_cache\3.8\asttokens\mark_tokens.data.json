{".class": "MypyFile", "_fullname": "asttokens.mark_tokens", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASTTokens": {".class": "SymbolTableNode", "cross_ref": "asttokens.asttokens.ASTTokens", "kind": "Gdef"}, "AstNode": {".class": "SymbolTableNode", "cross_ref": "asttokens.util.AstNode", "kind": "Gdef"}, "AstroidBaseContainer": {".class": "SymbolTableNode", "cross_ref": "asttokens.astroid_compat.BaseContainer", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MarkTokens": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asttokens.mark_tokens.MarkTokens", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asttokens.mark_tokens.MarkTokens", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asttokens.mark_tokens", "mro": ["asttokens.mark_tokens.MarkTokens", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "code"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "code"], "arg_types": ["asttokens.mark_tokens.MarkTokens", "asttokens.asttokens.ASTTokens"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of MarkTokens", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asttokens.mark_tokens.MarkTokens._code", "name": "_code", "setter_type": null, "type": "asttokens.asttokens.ASTTokens"}}, "_expand_to_matching_pairs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "first_token", "last_token", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens._expand_to_matching_pairs", "name": "_expand_to_matching_pairs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "first_token", "last_token", "node"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_expand_to_matching_pairs of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_last_in_stmt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "start_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens._find_last_in_stmt", "name": "_find_last_in_stmt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "start_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_find_last_in_stmt of MarkTokens", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_gobble_parens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "first_token", "last_token", "include_all"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens._gobble_parens", "name": "_gobble_parens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "first_token", "last_token", "include_all"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_gobble_parens of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iter_children": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "asttokens.mark_tokens.MarkTokens._iter_children", "name": "_iter_children", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asttokens.mark_tokens.MarkTokens._methods", "name": "_methods", "setter_type": null, "type": "asttokens.util.NodeMethods"}}, "_visit_after_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "parent_token", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens._visit_after_children", "name": "_visit_after_children", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "parent_token", "token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_visit_after_children of MarkTokens", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_visit_before_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "parent_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens._visit_before_children", "name": "_visit_before_children", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "parent_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_visit_before_children of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_async", "name": "handle_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_async of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_attr", "name": "handle_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_attr of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_bare_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_bare_tuple", "name": "handle_bare_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_bare_tuple of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_comp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "open_brace", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_comp", "name": "handle_comp", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "open_brace", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_comp of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_def": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_def", "name": "handle_def", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_def of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_following_brackets": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "last_token", "opening_bracket"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_following_brackets", "name": "handle_following_brackets", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "last_token", "opening_bracket"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_following_brackets of MarkTokens", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "value", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_num", "name": "handle_num", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "node", "value", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "UnionType", "items": ["builtins.complex", "builtins.int", "numbers.Number"], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_num of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_str", "name": "handle_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_str of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_tuple_nonempty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.handle_tuple_nonempty", "name": "handle_tuple_nonempty", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_tuple_nonempty of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_assignattr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_assignattr", "name": "visit_assignattr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_assignname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_assignname", "name": "visit_assignname", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_assignname of MarkT<PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_asyncfor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_asyncfor", "name": "visit_asyncfor", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_asyncfunctiondef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_asyncfunctiondef", "name": "visit_asyncfunctiondef", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_asyncfunctiondef of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_asyncwith": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_asyncwith", "name": "visit_asyncwith", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_attribute", "name": "visit_attribute", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_bytes", "name": "visit_bytes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_bytes of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_call", "name": "visit_call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_call of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_classdef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_classdef", "name": "visit_classdef", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_comprehension": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_comprehension", "name": "visit_comprehension", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_comprehension of Mark<PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_const", "name": "visit_const", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_const of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_constant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_constant", "name": "visit_constant", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_default", "name": "visit_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_default of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_delattr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_delattr", "name": "visit_delattr", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_functiondef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_functiondef", "name": "visit_functiondef", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_if": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_if", "name": "visit_if", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_if of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_joinedstr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_joinedstr", "name": "visit_joinedstr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_joinedstr of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_keyword": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_keyword", "name": "visit_keyword", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_keyword of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_matchclass": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_matchclass", "name": "visit_matchclass", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_matchclass of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_num": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_num", "name": "visit_num", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_num of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_slice", "name": "visit_slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_slice of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_starred": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_starred", "name": "visit_starred", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_starred of <PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_str": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_str", "name": "visit_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_str of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_subscript": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_subscript", "name": "visit_subscript", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_subscript of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_tree": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_tree", "name": "visit_tree", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["asttokens.mark_tokens.MarkTokens", "ast.<PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_tree of MarkTokens", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "asttokens.mark_tokens.MarkTokens.visit_tuple", "name": "visit_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "first_token", "last_token"], "arg_types": ["asttokens.mark_tokens.MarkTokens", {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.AstNode"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "visit_tuple of MarkTokens", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}, {".class": "TypeAliasType", "args": [], "type_ref": "asttokens.util.Token"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asttokens.mark_tokens.MarkTokens.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asttokens.mark_tokens.MarkTokens", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Module": {".class": "SymbolTableNode", "cross_ref": "ast.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asttokens.mark_tokens.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_matching_pairs_left": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens._matching_pairs_left", "name": "_matching_pairs_left", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_matching_pairs_right": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "asttokens.mark_tokens._matching_pairs_right", "name": "_matching_pairs_right", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "nc": {".class": "SymbolTableNode", "cross_ref": "asttokens.astroid_compat.astroid_node_classes", "kind": "Gdef"}, "numbers": {".class": "SymbolTableNode", "cross_ref": "numbers", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "token": {".class": "SymbolTableNode", "cross_ref": "token", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "asttokens.util", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\asttokens\\mark_tokens.py"}