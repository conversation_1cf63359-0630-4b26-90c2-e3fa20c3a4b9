{"data_mtime": 1751955670, "dep_lines": [40, 15, 16, 17, 18, 19, 20, 21, 23, 24, 1, 1, 1, 1, 202], "dep_prios": [25, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 5], "dependencies": ["asttokens.astroid_compat", "ast", "collections", "io", "sys", "token", "tokenize", "abc", "functools", "typing", "builtins", "_collections_abc", "_frozen_importlib", "types"], "hash": "e37fa1fda7e1cc83ef77704078365bc20e7baced", "id": "asttokens.util", "ignore_all": true, "interface_hash": "4bad70c16200642fd800cc39480efc2f74ad1a0c", "mtime": 1748947633, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\asttokens\\util.py", "plugin_data": null, "size": 17286, "suppressed": ["astroid"], "version_id": "1.16.1"}