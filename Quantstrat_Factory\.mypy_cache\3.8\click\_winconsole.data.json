{".class": "MypyFile", "_fullname": "click._winconsole", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CommandLineToArgvW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.CommandLineToArgvW", "name": "CommandLineToArgvW", "setter_type": null, "type": "ctypes._CFunctionType"}}, "ConsoleStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click._winconsole.ConsoleStream", "name": "ConsoleStream", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click._winconsole.ConsoleStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click._winconsole", "mro": ["click._winconsole.ConsoleStream", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["click._winconsole.ConsoleStream", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ConsoleStream", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "text_stream", "byte_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "text_stream", "byte_stream"], "arg_types": ["click._winconsole.ConsoleStream", "typing.TextIO", "typing.BinaryIO"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConsoleStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole.ConsoleStream.__repr__", "name": "__repr__", "type": null}}, "_text_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click._winconsole.ConsoleStream._text_stream", "name": "_text_stream", "setter_type": null, "type": "typing.TextIO"}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click._winconsole.ConsoleStream.buffer", "name": "buffer", "setter_type": null, "type": "typing.BinaryIO"}}, "isatty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.isatty", "name": "isatty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click._winconsole.ConsoleStream"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "isatty of ConsoleStream", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click._winconsole.ConsoleStream"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of ConsoleStream", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click._winconsole.ConsoleStream.name", "name": "name", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click._winconsole.ConsoleStream"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "name of ConsoleStream", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["click._winconsole.ConsoleStream", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "t.<PERSON>", "namespace": "click._winconsole.ConsoleStream.write", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "write of <PERSON><PERSON>e<PERSON><PERSON>am", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "t.<PERSON>", "namespace": "click._winconsole.ConsoleStream.write", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}, "writelines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "click._winconsole.ConsoleStream.writelines", "name": "writelines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "arg_types": ["click._winconsole.ConsoleStream", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "t.<PERSON>", "namespace": "click._winconsole.ConsoleStream.writelines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "writelines of ConsoleStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "typing.AnyStr", "id": -1, "name": "t.<PERSON>", "namespace": "click._winconsole.ConsoleStream.writelines", "upper_bound": "builtins.object", "values": ["builtins.str", "builtins.bytes"], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click._winconsole.ConsoleStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click._winconsole.ConsoleStream", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DWORD": {".class": "SymbolTableNode", "cross_ref": "ctypes.wintypes.DWORD", "kind": "Gdef"}, "EOF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.EOF", "name": "EOF", "setter_type": null, "type": "builtins.bytes"}}, "ERROR_NOT_ENOUGH_MEMORY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.ERROR_NOT_ENOUGH_MEMORY", "name": "ERROR_NOT_ENOUGH_MEMORY", "setter_type": null, "type": "builtins.int"}}, "ERROR_OPERATION_ABORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.ERROR_OPERATION_ABORTED", "name": "ERROR_OPERATION_ABORTED", "setter_type": null, "type": "builtins.int"}}, "ERROR_SUCCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.ERROR_SUCCESS", "name": "ERROR_SUCCESS", "setter_type": null, "type": "builtins.int"}}, "GetCommandLineW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.GetCommandLineW", "name": "GetCommandLineW", "setter_type": null, "type": "ctypes._CFunctionType"}}, "GetConsoleMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.GetConsoleMode", "name": "GetConsoleMode", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "GetLastError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.GetLastError", "name": "GetLastError", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "GetStdHandle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.GetStdHandle", "name": "GetStdHandle", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "HANDLE": {".class": "SymbolTableNode", "cross_ref": "ctypes.wintypes.HANDLE", "kind": "Gdef"}, "LPCWSTR": {".class": "SymbolTableNode", "cross_ref": "ctypes.wintypes.LPCWSTR", "kind": "Gdef"}, "LPWSTR": {".class": "SymbolTableNode", "cross_ref": "ctypes.wintypes.LPWSTR", "kind": "Gdef"}, "LocalFree": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.LocalFree", "name": "LocalFree", "setter_type": null, "type": "ctypes._CFunctionType"}}, "MAX_BYTES_WRITTEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.MAX_BYTES_WRITTEN", "name": "MAX_BYTES_WRITTEN", "setter_type": null, "type": "builtins.int"}}, "POINTER": {".class": "SymbolTableNode", "cross_ref": "_ctypes.POINTER", "kind": "Gdef"}, "PyBUF_SIMPLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.PyBUF_SIMPLE", "name": "PyBUF_SIMPLE", "setter_type": null, "type": "builtins.int"}}, "PyBUF_WRITABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.PyBUF_WRITABLE", "name": "PyBUF_WRITABLE", "setter_type": null, "type": "builtins.int"}}, "PyBuffer_Release": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.PyBuffer_Release", "name": "PyBuffer_Release", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "PyObject_GetBuffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.PyObject_GetBuffer", "name": "PyObject_GetBuffer", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "Py_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_ctypes.Structure"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click._winconsole.Py_buffer", "name": "Py_buffer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click._winconsole.Py_buffer", "has_param_spec_type": false, "metaclass_type": "_ctypes._PyCStructType", "metadata": {}, "module_name": "click._winconsole", "mro": ["click._winconsole.Py_buffer", "_ctypes.Structure", "_ctypes._CData", "builtins.object"], "names": {".class": "SymbolTable", "_fields_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.Py_buffer._fields_", "name": "_fields_", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._SimpleCData"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}, {".class": "TypeType", "item": "_ctypes.CFuncPtr"}, {".class": "TypeType", "item": "_ctypes.Union"}, {".class": "TypeType", "item": "_ctypes.Structure"}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "_ctypes.Array"}}], "uses_pep604_syntax": false}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click._winconsole.Py_buffer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click._winconsole.Py_buffer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadConsoleW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.ReadConsoleW", "name": "ReadConsoleW", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "STDERR_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDERR_FILENO", "name": "STDERR_FILENO", "setter_type": null, "type": "builtins.int"}}, "STDERR_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDERR_HANDLE", "name": "STDERR_HANDLE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "STDIN_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDIN_FILENO", "name": "STDIN_FILENO", "setter_type": null, "type": "builtins.int"}}, "STDIN_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDIN_HANDLE", "name": "STDIN_HANDLE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "STDOUT_FILENO": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDOUT_FILENO", "name": "STDOUT_FILENO", "setter_type": null, "type": "builtins.int"}}, "STDOUT_HANDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.STDOUT_HANDLE", "name": "STDOUT_HANDLE", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "Structure": {".class": "SymbolTableNode", "cross_ref": "_ctypes.Structure", "kind": "Gdef"}, "WINFUNCTYPE": {".class": "SymbolTableNode", "cross_ref": "ctypes.WINFUNCTYPE", "kind": "Gdef"}, "WriteConsoleW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.WriteConsoleW", "name": "WriteConsoleW", "setter_type": null, "type": "ctypes._NamedFuncPointer"}}, "_NonClosingTextIOWrapper": {".class": "SymbolTableNode", "cross_ref": "click._compat._NonClosingTextIOWrapper", "kind": "Gdef"}, "_WindowsConsoleRawIOBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["io.RawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click._winconsole._WindowsConsoleRawIOBase", "name": "_WindowsConsoleRawIOBase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleRawIOBase", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "click._winconsole", "mro": ["click._winconsole._WindowsConsoleRawIOBase", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "handle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleRawIOBase.__init__", "name": "__init__", "type": null}}, "handle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click._winconsole._WindowsConsoleRawIOBase.handle", "name": "handle", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "isatty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleRawIOBase.isatty", "name": "isatty", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click._winconsole._WindowsConsoleRawIOBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click._winconsole._WindowsConsoleRawIOBase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WindowsConsoleReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click._winconsole._WindowsConsoleRawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click._winconsole._WindowsConsoleReader", "name": "_WindowsConsoleReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleReader", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "click._winconsole", "mro": ["click._winconsole._WindowsConsoleReader", "click._winconsole._WindowsConsoleRawIOBase", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "readable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleReader.readable", "name": "readable", "type": null}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleReader.readinto", "name": "readinto", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click._winconsole._WindowsConsoleReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click._winconsole._WindowsConsoleReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WindowsConsoleWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["click._winconsole._WindowsConsoleRawIOBase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click._winconsole._WindowsConsoleWriter", "name": "_WindowsConsoleWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleWriter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "click._winconsole", "mro": ["click._winconsole._WindowsConsoleWriter", "click._winconsole._WindowsConsoleRawIOBase", "io.RawIOBase", "_io._RawIOBase", "io.IOBase", "_io._IOBase", "builtins.object"], "names": {".class": "SymbolTable", "_get_error_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["errno"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "click._winconsole._WindowsConsoleWriter._get_error_message", "name": "_get_error_message", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "click._winconsole._WindowsConsoleWriter._get_error_message", "name": "_get_error_message", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["errno"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_error_message of _WindowsConsoleWriter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "writable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleWriter.writable", "name": "writable", "type": null}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._WindowsConsoleWriter.write", "name": "write", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click._winconsole._WindowsConsoleWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click._winconsole._WindowsConsoleWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click._winconsole.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_text_stderr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._get_text_stderr", "name": "_get_text_stderr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer_stream"], "arg_types": ["typing.BinaryIO"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_text_stderr", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_text_stdin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._get_text_stdin", "name": "_get_text_stdin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer_stream"], "arg_types": ["typing.BinaryIO"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_text_stdin", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_text_stdout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buffer_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._get_text_stdout", "name": "_get_text_stdout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buffer_stream"], "arg_types": ["typing.BinaryIO"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_text_stdout", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_windows_console_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["f", "encoding", "errors"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._get_windows_console_stream", "name": "_get_windows_console_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["f", "encoding", "errors"], "arg_types": ["typing.TextIO", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_windows_console_stream", "ret_type": {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_console": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click._winconsole._is_console", "name": "_is_console", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": ["typing.TextIO"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_console", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_stream_factories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "click._winconsole._stream_factories", "name": "_stream_factories", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["typing.BinaryIO"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "byref": {".class": "SymbolTableNode", "cross_ref": "_ctypes.byref", "kind": "Gdef"}, "c_char": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char", "kind": "Gdef"}, "c_char_p": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_char_p", "kind": "Gdef"}, "c_int": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_int", "kind": "Gdef"}, "c_ssize_p": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.c_ssize_p", "name": "c_ssize_p", "setter_type": null, "type": {".class": "TypeType", "item": {".class": "Instance", "args": ["ctypes.c_ssize_t"], "extra_attrs": null, "type_ref": "_ctypes._Pointer"}}}}, "c_ssize_t": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_ssize_t", "kind": "Gdef"}, "c_ulong": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_ulong", "kind": "Gdef"}, "c_void_p": {".class": "SymbolTableNode", "cross_ref": "ctypes.c_void_p", "kind": "Gdef"}, "get_buffer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.get_buffer", "name": "get_buffer", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["obj", "writable"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_buffer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "kernel32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "click._winconsole.kernel32", "name": "kernel32", "setter_type": null, "type": "ctypes.WinDLL"}}, "msvcrt": {".class": "SymbolTableNode", "cross_ref": "msvcrt", "kind": "Gdef"}, "py_object": {".class": "SymbolTableNode", "cross_ref": "ctypes.py_object", "kind": "Gdef"}, "pythonapi": {".class": "SymbolTableNode", "cross_ref": "ctypes.python<PERSON>i", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "windll": {".class": "SymbolTableNode", "cross_ref": "ctypes.windll", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\click\\_winconsole.py"}