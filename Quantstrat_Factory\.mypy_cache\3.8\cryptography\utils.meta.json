{"data_mtime": 1751955670, "dep_lines": [5, 7, 8, 9, 10, 11, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 5, 30, 30], "dependencies": ["__future__", "enum", "sys", "types", "typing", "warnings", "builtins", "_frozen_importlib", "abc"], "hash": "05d343cdc6b73453aa3fa5a5bc8b21b857dc3848", "id": "cryptography.utils", "ignore_all": true, "interface_hash": "7c87ad25289e924211bf97a7ab75494beaa97dae", "mtime": 1748947650, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\cryptography\\utils.py", "plugin_data": null, "size": 3925, "suppressed": [], "version_id": "1.16.1"}