{".class": "MypyFile", "_fullname": "cycler", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Cycler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "cycler.Cycler", "name": "Cycler", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "cycler.Cycler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "cycler", "mro": ["cycler.Cycler", "builtins.object"], "names": {".class": "SymbolTable", "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__add__", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__add__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__add__", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__add__", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__add__", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cycler.Cycler.__call__", "name": "__call__", "type": null}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cycler.Cycler.__contains__", "name": "__contains__", "type": null}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.object"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__eq__ of Cycler", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.slice"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getitem__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cycler.Cycler.__hash__", "name": "__hash__", "setter_type": null, "type": {".class": "NoneType"}}}, "__iadd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__iadd__", "name": "__iadd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iadd__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__imul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__imul__", "name": "__imul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__imul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "left", "right", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "left", "right", "op"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Cycler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__len__ of Cycler", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cycler.Cycler.__mul__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "cycler.Cycler.__mul__", "name": "__mul__", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.Cycler.__mul__", "name": "__mul__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.Cycler.__mul__", "name": "__mul__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__mul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of Cycler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cycler.Cycler.__rmul__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "cycler.Cycler.__rmul__", "name": "__rmul__", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.Cycler.__rmul__", "name": "__rmul__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.Cycler.__rmul__", "name": "__rmul__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "id": -1, "name": "L", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -2, "name": "U", "namespace": "cycler.Cycler.__rmul__#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__rmul__ of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_from_iter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label", "itr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler._from_iter", "name": "_from_iter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label", "itr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_iter of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "cycler.Cycler._from_iter", "name": "_from_iter", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "label", "itr"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_from_iter of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cycler.Cycler._keys", "name": "_keys", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_left": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cycler.Cycler._left", "name": "_left", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": true}}}, "_op": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cycler.Cycler._op", "name": "_op", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_repr_html_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler._repr_html_", "name": "_repr_html_", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_repr_html_ of Cycler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_right": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "cycler.Cycler._right", "name": "_right", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_transpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cycler.Cycler._transpose", "name": "_transpose", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "by_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.by_key", "name": "by_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "by_key of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "change_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "old", "new"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.change_key", "name": "change_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "old", "new"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "change_key of Cycler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "cycler.Cycler.concat", "name": "concat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["left", "right"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "cycler.Cycler.keys", "name": "keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keys of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "cycler.Cycler.keys", "name": "keys", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "keys of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "simplify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "cycler.Cycler.simplify", "name": "simplify", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "simplify of Cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.Cycler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": 1, "name": "K", "namespace": "cycler.Cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": 2, "name": "V", "namespace": "cycler.Cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["K", "V"], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "K": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "name": "K", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}}, "L": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.L", "name": "L", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "U": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "name": "U", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "V": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "name": "V", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "cycler.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "cycler.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "_cycler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cycler._cycler", "name": "_cycler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._cycler", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._cycler", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_process_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["left", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cycler._process_keys", "name": "_process_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["left", "right"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._process_keys", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._process_keys", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._process_keys", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._process_keys", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_process_keys", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler._process_keys", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler._process_keys", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "add": {".class": "SymbolTableNode", "cross_ref": "_operator.add", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "concat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["left", "right"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "cycler.concat", "name": "concat", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["left", "right"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "concat", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.concat", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.U", "id": -3, "name": "U", "namespace": "cycler.concat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "cycle": {".class": "SymbolTableNode", "cross_ref": "itertools.cycle", "kind": "Gdef"}, "cycler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "cycler.cycler", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "cycler.cycler", "name": "cycler", "type": null}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cycler.cycler", "name": "cycler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.cycler", "name": "cycler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cycler.cycler", "name": "cycler", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.cycler", "name": "cycler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "cycler.cycler", "name": "cycler", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "cycler.cycler", "name": "cycler", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#0", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#0", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -1, "name": "V", "namespace": "cycler.cycler#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["label", "itr"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cycler", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "cycler.Cycler"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.<PERSON>", "id": -1, "name": "K", "namespace": "cycler.cycler#2", "upper_bound": "<PERSON>.<PERSON>", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "cycler.V", "id": -2, "name": "V", "namespace": "cycler.cycler#2", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "mul": {".class": "SymbolTableNode", "cross_ref": "_operator.mul", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "product": {".class": "SymbolTableNode", "cross_ref": "itertools.product", "kind": "Gdef"}, "reduce": {".class": "SymbolTableNode", "cross_ref": "functools.reduce", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\cycler\\__init__.py"}