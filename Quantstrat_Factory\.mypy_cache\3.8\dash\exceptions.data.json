{".class": "MypyFile", "_fullname": "dash.exceptions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BackgroundCallbackError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.BackgroundCallbackError", "name": "BackgroundCallbackError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.BackgroundCallbackError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.BackgroundCallbackError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.BackgroundCallbackError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.BackgroundCallbackError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CallbackException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.CallbackException", "name": "CallbackException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.CallbackException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.CallbackException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.CallbackException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.DashException", "name": "DashException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.DashException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "dash.exceptions.DashException.__init__", "name": "__init__", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.DashException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.DashException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.DependencyException", "name": "DependencyException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.DependencyException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.DependencyException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.DependencyException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.DependencyException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.DuplicateCallback", "name": "Duplicate<PERSON><PERSON>back", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.DuplicateCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.DuplicateCallback", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.DuplicateCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.DuplicateCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.DuplicateIdError", "name": "DuplicateIdError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.DuplicateIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.DuplicateIdError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.DuplicateIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.DuplicateIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HookError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.HookError", "name": "Hook<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.HookError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.HookError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.HookError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.HookError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IDsCantContainPeriods": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.IDsCantContainPeriods", "name": "IDsCantContainPeriods", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.IDsCantContainPeriods", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.IDsCantContainPeriods", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.IDsCantContainPeriods.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.IDsCantContainPeriods", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImportedInsideCallbackError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.ImportedInsideCallbackError", "name": "ImportedInsideCallbackError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.ImportedInsideCallbackError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.ImportedInsideCallbackError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.ImportedInsideCallbackError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.ImportedInsideCallbackError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IncorrectTypeException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.IncorrectTypeException", "name": "IncorrectTypeException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.IncorrectTypeException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.IncorrectTypeException", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.IncorrectTypeException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.IncorrectTypeException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidCallbackReturnValue": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.InvalidCallbackReturnValue", "name": "InvalidCallbackReturnValue", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.InvalidCallbackReturnValue", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.InvalidCallbackReturnValue", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.InvalidCallbackReturnValue.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.InvalidCallbackReturnValue", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidComponentIdError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.IDsCantContainPeriods"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.InvalidComponentIdError", "name": "InvalidComponentIdError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.InvalidComponentIdError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.InvalidComponentIdError", "dash.exceptions.IDsCantContainPeriods", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.InvalidComponentIdError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.InvalidComponentIdError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.InvalidConfig", "name": "InvalidConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.InvalidConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.InvalidConfig", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.InvalidConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.InvalidConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidIndexException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.InvalidIndexException", "name": "InvalidIndexException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.InvalidIndexException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.InvalidIndexException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.InvalidIndexException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.InvalidIndexException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InvalidResourceError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.InvalidResourceError", "name": "InvalidResourceError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.InvalidResourceError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.InvalidResourceError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.InvalidResourceError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.InvalidResourceError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingCallbackContextException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.MissingCallbackContextException", "name": "MissingCallbackContextException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.MissingCallbackContextException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.MissingCallbackContextException", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.MissingCallbackContextException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.MissingCallbackContextException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MissingLongCallbackManagerError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.MissingLongCallbackManagerError", "name": "MissingLongCallbackManagerError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.MissingLongCallbackManagerError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.MissingLongCallbackManagerError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.MissingLongCallbackManagerError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.MissingLongCallbackManagerError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoLayoutException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.NoLayoutException", "name": "NoLayoutException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.NoLayoutException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.NoLayoutException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.NoLayoutException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.NoLayoutException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NonExistentEventException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.NonExistentEventException", "name": "NonExistentEventException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.NonExistentEventException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.NonExistentEventException", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.NonExistentEventException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.NonExistentEventException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObsoleteAttributeException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.ObsoleteAttributeException", "name": "ObsoleteAttributeException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.ObsoleteAttributeException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.ObsoleteAttributeException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.ObsoleteAttributeException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.ObsoleteAttributeException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ObsoleteKwargException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.ObsoleteKwargException", "name": "ObsoleteKwargException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.ObsoleteKwargException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.ObsoleteKwargException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.ObsoleteKwargException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.ObsoleteKwargException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PageError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.PageError", "name": "Page<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.PageError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.PageError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.PageError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.PageError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PreventUpdate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.PreventUpdate", "name": "PreventUpdate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.PreventUpdate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.PreventUpdate", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.PreventUpdate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.PreventUpdate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.ProxyError", "name": "ProxyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.ProxyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.ProxyError", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.ProxyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.ProxyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResourceException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.DashException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.ResourceException", "name": "ResourceException", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.ResourceException", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.ResourceException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.ResourceException.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.ResourceException", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UnsupportedRelativePath": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.UnsupportedRelativePath", "name": "UnsupportedRelativePath", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.UnsupportedRelativePath", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.UnsupportedRelativePath", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.UnsupportedRelativePath.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.UnsupportedRelativePath", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WildcardInLongCallback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["dash.exceptions.CallbackException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "dash.exceptions.WildcardInLongCallback", "name": "WildcardInLongCallback", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "dash.exceptions.WildcardInLongCallback", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "dash.exceptions", "mro": ["dash.exceptions.WildcardInLongCallback", "dash.exceptions.CallbackException", "dash.exceptions.DashException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "dash.exceptions.WildcardInLongCallback.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "dash.exceptions.WildcardInLongCallback", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "dash.exceptions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "dedent": {".class": "SymbolTableNode", "cross_ref": "textwrap.dedent", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\dash\\exceptions.py"}