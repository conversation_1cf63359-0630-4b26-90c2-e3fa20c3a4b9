{".class": "MypyFile", "_fullname": "h11._readers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "CLIENT": {".class": "SymbolTableNode", "cross_ref": "h11._state.CLIENT", "kind": "Gdef", "module_public": false}, "CLOSED": {".class": "SymbolTableNode", "cross_ref": "h11._state.CLOSED", "kind": "Gdef", "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "ChunkedReader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "h11._readers.ChunkedReader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._readers.ChunkedReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "h11._readers", "mro": ["h11._readers.ChunkedReader", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ChunkedReader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "arg_types": ["h11._readers.ChunkedReader", "h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ChunkedReader", "ret_type": {".class": "UnionType", "items": ["h11._events.Data", "h11._events.EndOfMessage", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ChunkedReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._readers.ChunkedReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChunkedReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bytes_in_chunk": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._readers.ChunkedReader._bytes_in_chunk", "name": "_bytes_in_chunk", "setter_type": null, "type": "builtins.int"}}, "_bytes_to_discard": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._readers.ChunkedReader._bytes_to_discard", "name": "_bytes_to_discard", "setter_type": null, "type": "builtins.int"}}, "_reading_trailer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._readers.ChunkedReader._reading_trailer", "name": "_reading_trailer", "setter_type": null, "type": "builtins.bool"}}, "read_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ChunkedReader.read_eof", "name": "read_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._readers.ChunkedReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_eof of ChunkedReader", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._readers.ChunkedReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._readers.ChunkedReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ContentLengthReader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "h11._readers.ContentLengthReader", "name": "ContentLengthReader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._readers.ContentLengthReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "h11._readers", "mro": ["h11._readers.ContentLengthReader", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ContentLengthReader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "arg_types": ["h11._readers.ContentLengthReader", "h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ContentLengthReader", "ret_type": {".class": "UnionType", "items": ["h11._events.Data", "h11._events.EndOfMessage", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "length"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ContentLengthReader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "length"], "arg_types": ["h11._readers.ContentLengthReader", "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ContentLengthReader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._readers.ContentLengthReader._length", "name": "_length", "setter_type": null, "type": "builtins.int"}}, "_remaining": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._readers.ContentLengthReader._remaining", "name": "_remaining", "setter_type": null, "type": "builtins.int"}}, "read_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.ContentLengthReader.read_eof", "name": "read_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._readers.ContentLengthReader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_eof of ContentLengthReader", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._readers.ContentLengthReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._readers.ContentLengthReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DONE": {".class": "SymbolTableNode", "cross_ref": "h11._state.DONE", "kind": "Gdef", "module_public": false}, "Data": {".class": "SymbolTableNode", "cross_ref": "h11._events.Data", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "EndOfMessage": {".class": "SymbolTableNode", "cross_ref": "h11._events.EndOfMessage", "kind": "Gdef", "module_public": false}, "Http10Reader": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "h11._readers.Http10Reader", "name": "Http10Reader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._readers.Http10Reader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "h11._readers", "mro": ["h11._readers.Http10Reader", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.Http10Reader.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "buf"], "arg_types": ["h11._readers.Http10Reader", "h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of Http10Reader", "ret_type": {".class": "UnionType", "items": ["h11._events.Data", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._readers.Http10Reader.read_eof", "name": "read_eof", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._readers.Http10Reader"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "read_eof of Http10Reader", "ret_type": "h11._events.EndOfMessage", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._readers.Http10Reader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._readers.Http10Reader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IDLE": {".class": "SymbolTableNode", "cross_ref": "h11._state.IDLE", "kind": "Gdef", "module_public": false}, "InformationalResponse": {".class": "SymbolTableNode", "cross_ref": "h11._events.InformationalResponse", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.LocalProtocolError", "kind": "Gdef", "module_public": false}, "MUST_CLOSE": {".class": "SymbolTableNode", "cross_ref": "h11._state.MUST_CLOSE", "kind": "Gdef", "module_public": false}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "READERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "h11._readers.READERS", "name": "READERS", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "h11._readers.ReadersType"}}}, "ReadersType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "h11._readers.ReadersType", "line": 227, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ReceiveBuffer": {".class": "SymbolTableNode", "cross_ref": "h11._receivebuffer.ReceiveBuffer", "kind": "Gdef", "module_public": false}, "RemoteProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.RemoteProtocolError", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "h11._events.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "h11._events.Response", "kind": "Gdef", "module_public": false}, "SEND_BODY": {".class": "SymbolTableNode", "cross_ref": "h11._state.SEND_BODY", "kind": "Gdef", "module_public": false}, "SEND_RESPONSE": {".class": "SymbolTableNode", "cross_ref": "h11._state.SEND_RESPONSE", "kind": "Gdef", "module_public": false}, "SERVER": {".class": "SymbolTableNode", "cross_ref": "h11._state.SERVER", "kind": "Gdef", "module_public": false}, "Sentinel": {".class": "SymbolTableNode", "cross_ref": "h11._util.Sentinel", "kind": "Gdef", "module_public": false}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._readers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_decode_header_lines": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "h11._readers._decode_header_lines", "name": "_decode_header_lines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lines"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_decode_header_lines", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_obsolete_line_fold": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "h11._readers._obsolete_line_fold", "name": "_obsolete_line_fold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lines"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_obsolete_line_fold", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_header": {".class": "SymbolTableNode", "cross_ref": "h11._abnf.chunk_header", "kind": "Gdef", "module_public": false}, "chunk_header_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.chunk_header_re", "name": "chunk_header_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "expect_nothing": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "h11._readers.expect_nothing", "name": "expect_nothing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buf"], "arg_types": ["h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "expect_nothing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "header_field": {".class": "SymbolTableNode", "cross_ref": "h11._abnf.header_field", "kind": "Gdef", "module_public": false}, "header_field_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.header_field_re", "name": "header_field_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "maybe_read_from_IDLE_client": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "h11._readers.maybe_read_from_IDLE_client", "name": "maybe_read_from_IDLE_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buf"], "arg_types": ["h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "maybe_read_from_IDLE_client", "ret_type": {".class": "UnionType", "items": ["h11._events.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "maybe_read_from_SEND_RESPONSE_server": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["buf"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "h11._readers.maybe_read_from_SEND_RESPONSE_server", "name": "maybe_read_from_SEND_RESPONSE_server", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["buf"], "arg_types": ["h11._receivebuffer.ReceiveBuffer"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "maybe_read_from_SEND_RESPONSE_server", "ret_type": {".class": "UnionType", "items": ["h11._events.InformationalResponse", "h11._events.Response", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "obs_fold_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.obs_fold_re", "name": "obs_fold_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "request_line": {".class": "SymbolTableNode", "cross_ref": "h11._abnf.request_line", "kind": "Gdef", "module_public": false}, "request_line_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.request_line_re", "name": "request_line_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "status_line": {".class": "SymbolTableNode", "cross_ref": "h11._abnf.status_line", "kind": "Gdef", "module_public": false}, "status_line_re": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._readers.status_line_re", "name": "status_line_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "validate": {".class": "SymbolTableNode", "cross_ref": "h11._util.validate", "kind": "Gdef", "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\h11\\_readers.py"}