{"data_mtime": 1751955671, "dep_lines": [22, 23, 24, 25, 35, 19, 20, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["h11._abnf", "h11._events", "h11._receivebuffer", "h11._state", "h11._util", "re", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "h11._headers", "typing_extensions"], "hash": "4e0a9c3053f356bd87bd9582217342b89ae640af", "id": "h11._readers", "ignore_all": true, "interface_hash": "b695d64db046d427ea78681e4e776182bb08ee38", "mtime": 1748947668, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\h11\\_readers.py", "plugin_data": null, "size": 8383, "suppressed": [], "version_id": "1.16.1"}