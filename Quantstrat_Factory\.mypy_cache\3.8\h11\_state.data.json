{".class": "MypyFile", "_fullname": "h11._state", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CLIENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.CLIENT", "name": "CLIENT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.CLIENT", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.CLIENT", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.CLIENT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.CLIENT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CLOSED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.CLOSED", "name": "CLOSED", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.CLOSED", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.CLOSED", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.CLOSED.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.CLOSED", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConnectionClosed": {".class": "SymbolTableNode", "cross_ref": "h11._events.ConnectionClosed", "kind": "Gdef", "module_public": false}, "ConnectionState": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "h11._state.ConnectionState", "name": "ConnectionState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.ConnectionState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.ConnectionState", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._state.ConnectionState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fire_event_triggered_transitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "role", "event_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState._fire_event_triggered_transitions", "name": "_fire_event_triggered_transitions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "role", "event_type"], "arg_types": ["h11._state.ConnectionState", {".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "h11._events.Event"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "h11._events.Event"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fire_event_triggered_transitions of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fire_state_triggered_transitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState._fire_state_triggered_transitions", "name": "_fire_state_triggered_transitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._state.ConnectionState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_fire_state_triggered_transitions of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "keep_alive": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "h11._state.ConnectionState.keep_alive", "name": "keep_alive", "setter_type": null, "type": "builtins.bool"}}, "pending_switch_proposals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "h11._state.ConnectionState.pending_switch_proposals", "name": "pending_switch_proposals", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "process_client_switch_proposal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "switch_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.process_client_switch_proposal", "name": "process_client_switch_proposal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "switch_event"], "arg_types": ["h11._state.ConnectionState", {".class": "TypeType", "item": "h11._util.Sentinel"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_client_switch_proposal of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "role"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.process_error", "name": "process_error", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "role"], "arg_types": ["h11._state.ConnectionState", {".class": "TypeType", "item": "h11._util.Sentinel"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_error of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "role", "event_type", "server_switch_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.process_event", "name": "process_event", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "role", "event_type", "server_switch_event"], "arg_types": ["h11._state.ConnectionState", {".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._events.Event"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_event of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_keep_alive_disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.process_keep_alive_disabled", "name": "process_keep_alive_disabled", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._state.ConnectionState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "process_keep_alive_disabled of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_next_cycle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "h11._state.ConnectionState.start_next_cycle", "name": "start_next_cycle", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["h11._state.ConnectionState"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "start_next_cycle of ConnectionState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "h11._state.ConnectionState.states", "name": "states", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.ConnectionState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.ConnectionState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.DONE", "name": "DONE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.DONE", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.DONE", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.DONE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.DONE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Data": {".class": "SymbolTableNode", "cross_ref": "h11._events.Data", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.ERROR", "name": "ERROR", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.ERROR", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.ERROR", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.ERROR.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.ERROR", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EVENT_TRIGGERED_TRANSITIONS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "h11._state.EVENT_TRIGGERED_TRANSITIONS", "name": "EVENT_TRIGGERED_TRANSITIONS", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "h11._state.EventTransitionType"}}}, "EndOfMessage": {".class": "SymbolTableNode", "cross_ref": "h11._events.EndOfMessage", "kind": "Gdef", "module_public": false}, "Event": {".class": "SymbolTableNode", "cross_ref": "h11._events.Event", "kind": "Gdef", "module_public": false}, "EventTransitionType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "h11._state.EventTransitionType", "line": 188, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "h11._events.Event"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "h11._events.Event"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "IDLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.IDLE", "name": "IDLE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.IDLE", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.IDLE", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.IDLE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.IDLE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InformationalResponse": {".class": "SymbolTableNode", "cross_ref": "h11._events.InformationalResponse", "kind": "Gdef", "module_public": false}, "LocalProtocolError": {".class": "SymbolTableNode", "cross_ref": "h11._util.LocalProtocolError", "kind": "Gdef", "module_public": false}, "MIGHT_SWITCH_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.MIGHT_SWITCH_PROTOCOL", "name": "MIGHT_SWITCH_PROTOCOL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.MIGHT_SWITCH_PROTOCOL", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.MIGHT_SWITCH_PROTOCOL", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.MIGHT_SWITCH_PROTOCOL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.MIGHT_SWITCH_PROTOCOL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MUST_CLOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.MUST_CLOSE", "name": "MUST_CLOSE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.MUST_CLOSE", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.MUST_CLOSE", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.MUST_CLOSE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.MUST_CLOSE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Request": {".class": "SymbolTableNode", "cross_ref": "h11._events.Request", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "h11._events.Response", "kind": "Gdef", "module_public": false}, "SEND_BODY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.SEND_BODY", "name": "SEND_BODY", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.SEND_BODY", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.SEND_BODY", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.SEND_BODY.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.SEND_BODY", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SEND_RESPONSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.SEND_RESPONSE", "name": "SEND_RESPONSE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.SEND_RESPONSE", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.SEND_RESPONSE", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.SEND_RESPONSE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.SEND_RESPONSE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SERVER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.SERVER", "name": "SERVER", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.SERVER", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.SERVER", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.SERVER.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.SERVER", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STATE_TRIGGERED_TRANSITIONS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "h11._state.STATE_TRIGGERED_TRANSITIONS", "name": "STATE_TRIGGERED_TRANSITIONS", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "h11._state.StateTransitionType"}}}, "SWITCHED_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state.SWITCHED_PROTOCOL", "name": "SWITCHED_PROTOCOL", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state.SWITCHED_PROTOCOL", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state.SWITCHED_PROTOCOL", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state.SWITCHED_PROTOCOL.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state.SWITCHED_PROTOCOL", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sentinel": {".class": "SymbolTableNode", "cross_ref": "h11._util.Sentinel", "kind": "Gdef", "module_public": false}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef", "module_public": false}, "StateTransitionType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "h11._state.StateTransitionType", "line": 229, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "Instance", "args": [{".class": "TypeType", "item": "h11._util.Sentinel"}, {".class": "TypeType", "item": "h11._util.Sentinel"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_SWITCH_CONNECT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state._SWITCH_CONNECT", "name": "_SWITCH_CONNECT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state._SWITCH_CONNECT", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state._SWITCH_CONNECT", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state._SWITCH_CONNECT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state._SWITCH_CONNECT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SWITCH_UPGRADE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["h11._util.Sentinel"], "dataclass_transform_spec": null, "declared_metaclass": "h11._util.Sentinel", "defn": {".class": "ClassDef", "fullname": "h11._state._SWITCH_UPGRADE", "name": "_SWITCH_UPGRADE", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "h11._state._SWITCH_UPGRADE", "has_param_spec_type": false, "metaclass_type": "h11._util.Sentinel", "metadata": {}, "module_name": "h11._state", "mro": ["h11._state._SWITCH_UPGRADE", "h11._util.Sentinel", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "h11._state._SWITCH_UPGRADE.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "h11._state._SWITCH_UPGRADE", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "h11._state.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "h11._state.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\h11\\_state.py"}