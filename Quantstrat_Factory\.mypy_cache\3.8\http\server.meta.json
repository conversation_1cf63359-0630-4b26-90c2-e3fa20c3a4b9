{"data_mtime": 1751955671, "dep_lines": [2, 7, 1, 2, 3, 4, 5, 6, 8, 9, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 20, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["email.message", "collections.abc", "_socket", "email", "io", "socketserver", "sys", "_typeshed", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "abc", "os", "socket"], "hash": "a6fdf84230c8c6689796181a209bea11f97114a9", "id": "http.server", "ignore_all": true, "interface_hash": "e0d13f454aee6ab2ec483109a2f7c5b963726b94", "mtime": 1751251076, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\http\\server.pyi", "plugin_data": null, "size": 3675, "suppressed": [], "version_id": "1.16.1"}