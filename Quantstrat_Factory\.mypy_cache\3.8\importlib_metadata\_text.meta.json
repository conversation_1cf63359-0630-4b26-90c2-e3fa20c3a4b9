{"data_mtime": 1751955670, "dep_lines": [3, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 30, 30, 30], "dependencies": ["importlib_metadata._functools", "re", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "5706a824d57d684b2985ee3a05a77ac152f55ebc", "id": "importlib_metadata._text", "ignore_all": true, "interface_hash": "b44aa44951e161ba60abc2d7eb70b0565d36c30d", "mtime": 1748947672, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\importlib_metadata\\_text.py", "plugin_data": null, "size": 2166, "suppressed": [], "version_id": "1.16.1"}