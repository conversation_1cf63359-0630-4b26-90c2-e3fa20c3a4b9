{"data_mtime": 1751955671, "dep_lines": [4, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "sys", "threading", "_typeshed", "io", "re", "string", "time", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "_thread", "abc", "os"], "hash": "6eb81240eb5dd119a2bdaa970c20a9fd32f733f0", "id": "logging", "ignore_all": true, "interface_hash": "6b4fc91f881f1c675fd14144d600bb62c836c3ec", "mtime": 1751251076, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\logging\\__init__.pyi", "plugin_data": null, "size": 21694, "suppressed": [], "version_id": "1.16.1"}