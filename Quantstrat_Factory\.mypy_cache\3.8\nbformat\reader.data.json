{".class": "MypyFile", "_fullname": "nbformat.reader", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NotJSONError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.reader.NotJSONError", "name": "NotJSONError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.reader.NotJSONError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.reader", "mro": ["nbformat.reader.NotJSONError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.reader.NotJSONError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.reader.NotJSONError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "nbformat.json_compat.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.reader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "get_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.reader.get_version", "name": "get_version", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "parse_json": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.reader.parse_json", "name": "parse_json", "type": null}}, "read": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["fp", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.reader.read", "name": "read", "type": null}}, "reads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.reader.reads", "name": "reads", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\reader.py"}