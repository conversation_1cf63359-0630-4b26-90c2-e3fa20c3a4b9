{".class": "MypyFile", "_fullname": "nbformat.v1.nbbase", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NotebookNode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["nbformat._struct.Struct"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v1.nbbase.NotebookNode", "name": "NotebookNode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v1.nbbase.NotebookNode", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "nbformat.v1.nbbase", "mro": ["nbformat.v1.nbbase.NotebookNode", "nbformat._struct.Struct", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v1.nbbase.NotebookNode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v1.nbbase.NotebookNode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Struct": {".class": "SymbolTableNode", "cross_ref": "nbformat._struct.Struct", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v1.nbbase.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "from_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v1.nbbase.from_dict", "name": "from_dict", "type": null}}, "new_code_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["code", "prompt_number"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v1.nbbase.new_code_cell", "name": "new_code_cell", "type": null}}, "new_notebook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["cells"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v1.nbbase.new_notebook", "name": "new_notebook", "type": null}}, "new_text_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v1.nbbase.new_text_cell", "name": "new_text_cell", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v1\\nbbase.py"}