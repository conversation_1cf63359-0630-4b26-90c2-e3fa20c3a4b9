{".class": "MypyFile", "_fullname": "nbformat.v2.convert", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v2.convert.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "downgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v2.convert.downgrade", "name": "downgrade", "type": null}}, "new_code_cell": {".class": "SymbolTableNode", "cross_ref": "nbformat.v2.nbbase.new_code_cell", "kind": "Gdef"}, "new_notebook": {".class": "SymbolTableNode", "cross_ref": "nbformat.v2.nbbase.new_notebook", "kind": "Gdef"}, "new_text_cell": {".class": "SymbolTableNode", "cross_ref": "nbformat.v2.nbbase.new_text_cell", "kind": "Gdef"}, "new_worksheet": {".class": "SymbolTableNode", "cross_ref": "nbformat.v2.nbbase.new_worksheet", "kind": "Gdef"}, "upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["nb", "from_version"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v2.convert.upgrade", "name": "upgrade", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v2\\convert.py"}