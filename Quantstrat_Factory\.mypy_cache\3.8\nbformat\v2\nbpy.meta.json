{"data_mtime": 1751955671, "dep_lines": [22, 23, 18, 20, 111, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 20, 5, 30, 30, 30, 30], "dependencies": ["nbformat.v2.nbbase", "nbformat.v2.rwbase", "__future__", "re", "ast", "builtins", "_frozen_importlib", "abc", "enum", "typing"], "hash": "7c9c59a460dbe9a92f0fef385fed1010b707f745", "id": "nbformat.v2.nbpy", "ignore_all": true, "interface_hash": "36d4b1a36e41262383f6f5eaac6fb49d2899a936", "mtime": 1748947713, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v2\\nbpy.py", "plugin_data": null, "size": 5600, "suppressed": [], "version_id": "1.16.1"}