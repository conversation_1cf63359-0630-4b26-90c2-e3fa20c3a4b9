{".class": "MypyFile", "_fullname": "nbformat.v3.convert", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.convert.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_unbytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.convert._unbytes", "name": "_unbytes", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "downgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["nb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.convert.downgrade", "name": "downgrade", "type": null}}, "heading_to_md": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.convert.heading_to_md", "name": "heading_to_md", "type": null}}, "nbformat": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.nbformat", "kind": "Gdef"}, "nbformat_minor": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.nbformat_minor", "kind": "Gdef"}, "raw_to_md": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cell"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.convert.raw_to_md", "name": "raw_to_md", "type": null}}, "upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["nb", "from_version", "from_minor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.convert.upgrade", "name": "upgrade", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v3\\convert.py"}