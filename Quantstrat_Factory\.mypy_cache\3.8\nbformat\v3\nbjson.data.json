{".class": "MypyFile", "_fullname": "nbformat.v3.nbjson", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesEncoder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["json.encoder.JSONEncoder"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbjson.BytesEncoder", "name": "BytesEncoder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.BytesEncoder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbjson", "mro": ["nbformat.v3.nbjson.BytesEncoder", "json.encoder.JSONEncoder", "builtins.object"], "names": {".class": "SymbolTable", "default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.BytesEncoder.default", "name": "default", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbjson.BytesEncoder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbjson.BytesEncoder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["nbformat.v3.rwbase.NotebookReader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbjson.JSONReader", "name": "J<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.JSONReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbjson", "mro": ["nbformat.v3.nbjson.JSONReader", "nbformat.v3.rwbase.NotebookReader", "builtins.object"], "names": {".class": "SymbolTable", "reads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.JSONReader.reads", "name": "reads", "type": null}}, "to_notebook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "d", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.JSONReader.to_notebook", "name": "to_notebook", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbjson.JSONReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbjson.JSONReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["nbformat.v3.rwbase.NotebookWriter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbjson.JSONWriter", "name": "JSONWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.JSONWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbjson", "mro": ["nbformat.v3.nbjson.JSONWriter", "nbformat.v3.rwbase.NotebookWriter", "builtins.object"], "names": {".class": "SymbolTable", "writes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "nb", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbjson.JSONWriter.writes", "name": "writes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbjson.JSONWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbjson.JSONWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NotebookReader": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.NotebookReader", "kind": "Gdef"}, "NotebookWriter": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.NotebookWriter", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbjson.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_reader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson._reader", "name": "_reader", "setter_type": null, "type": "nbformat.v3.nbjson.JSONReader"}}, "_writer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson._writer", "name": "_writer", "setter_type": null, "type": "nbformat.v3.nbjson.JSONWriter"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy", "kind": "Gdef"}, "from_dict": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.from_dict", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "read": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson.read", "name": "read", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["fp", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson.reads", "name": "reads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["s", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rejoin_lines": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.rejoin_lines", "kind": "Gdef"}, "split_lines": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.split_lines", "kind": "Gdef"}, "strip_transient": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.strip_transient", "kind": "Gdef"}, "to_notebook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson.to_notebook", "name": "to_notebook", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["d", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson.write", "name": "write", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["nb", "fp", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbjson.writes", "name": "writes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["nb", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v3\\nbjson.py"}