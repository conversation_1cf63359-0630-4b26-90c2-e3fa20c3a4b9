{".class": "MypyFile", "_fullname": "nbformat.v3.nbpy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NotebookReader": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.NotebookReader", "kind": "Gdef"}, "NotebookWriter": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.rwbase.NotebookWriter", "kind": "Gdef"}, "PyReader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["nbformat.v3.rwbase.NotebookReader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbpy.PyReader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbpy", "mro": ["nbformat.v3.nbpy.PyReader", "nbformat.v3.rwbase.NotebookReader", "builtins.object"], "names": {".class": "SymbolTable", "_remove_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader._remove_comments", "name": "_remove_comments", "type": null}}, "new_cell": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "state", "lines", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader.new_cell", "name": "new_cell", "type": null}}, "reads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader.reads", "name": "reads", "type": null}}, "split_lines_into_blocks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "lines"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader.split_lines_into_blocks", "name": "split_lines_into_blocks", "type": null}}, "to_notebook": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "s", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReader.to_notebook", "name": "to_notebook", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbpy.PyReader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbpy.PyReader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyReaderError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbpy.PyReaderError", "name": "PyReaderError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyReaderError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbpy", "mro": ["nbformat.v3.nbpy.PyReaderError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbpy.PyReaderError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbpy.PyReaderError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PyWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["nbformat.v3.rwbase.NotebookWriter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.v3.nbpy.PyWriter", "name": "PyWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.v3.nbpy", "mro": ["nbformat.v3.nbpy.PyWriter", "nbformat.v3.rwbase.NotebookWriter", "builtins.object"], "names": {".class": "SymbolTable", "writes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "nb", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v3.nbpy.PyWriter.writes", "name": "writes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.v3.nbpy.PyWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.v3.nbpy.PyWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v3.nbpy.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_encoding_declaration_re": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy._encoding_declaration_re", "name": "_encoding_declaration_re", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_reader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy._reader", "name": "_reader", "setter_type": null, "type": "nbformat.v3.nbpy.PyReader"}}, "_writer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy._writer", "name": "_writer", "setter_type": null, "type": "nbformat.v3.nbpy.PyWriter"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "nbformat": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.nbformat", "kind": "Gdef"}, "nbformat_minor": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.nbformat_minor", "kind": "Gdef"}, "new_code_cell": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.new_code_cell", "kind": "Gdef"}, "new_heading_cell": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.new_heading_cell", "kind": "Gdef"}, "new_notebook": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.new_notebook", "kind": "Gdef"}, "new_text_cell": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.new_text_cell", "kind": "Gdef"}, "new_worksheet": {".class": "SymbolTableNode", "cross_ref": "nbformat.v3.nbbase.new_worksheet", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "read": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy.read", "name": "read", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["fp", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy.reads", "name": "reads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["s", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_notebook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy.to_notebook", "name": "to_notebook", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["s", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy.write", "name": "write", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["nb", "fp", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v3.nbpy.writes", "name": "writes", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["nb", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": true, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v3\\nbpy.py"}