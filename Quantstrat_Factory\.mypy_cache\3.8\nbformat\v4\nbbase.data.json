{".class": "MypyFile", "_fullname": "nbformat.v4.nbbase", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "NotebookNode": {".class": "SymbolTableNode", "cross_ref": "nbformat.notebooknode.NotebookNode", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.v4.nbbase.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "nbformat": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "nbformat.v4.nbbase.nbformat", "name": "nbformat", "setter_type": null, "type": "builtins.int"}}, "nbformat_minor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "nbformat.v4.nbbase.nbformat_minor", "name": "nbformat_minor", "setter_type": null, "type": "builtins.int"}}, "nbformat_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.v4.nbbase.nbformat_schema", "name": "nbformat_schema", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "new_code_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 4], "arg_names": ["source", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.new_code_cell", "name": "new_code_cell", "type": null}}, "new_markdown_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 4], "arg_names": ["source", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.new_markdown_cell", "name": "new_markdown_cell", "type": null}}, "new_notebook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.new_notebook", "name": "new_notebook", "type": null}}, "new_output": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["output_type", "data", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.new_output", "name": "new_output", "type": null}}, "new_raw_cell": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 4], "arg_names": ["source", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.new_raw_cell", "name": "new_raw_cell", "type": null}}, "output_from_msg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.output_from_msg", "name": "output_from_msg", "type": null}}, "random_cell_id": {".class": "SymbolTableNode", "cross_ref": "nbformat.corpus.words.generate_corpus_id", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["node", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.v4.nbbase.validate", "name": "validate", "type": null}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\v4\\nbbase.py"}