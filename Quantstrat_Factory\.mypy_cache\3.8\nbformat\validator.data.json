{".class": "MypyFile", "_fullname": "nbformat.validator", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "DuplicateCellId": {".class": "SymbolTableNode", "cross_ref": "nbformat.warnings.DuplicateCellId", "kind": "Gdef", "module_public": false}, "MissingIDFieldWarning": {".class": "SymbolTableNode", "cross_ref": "nbformat.warnings.MissingIDFieldWarning", "kind": "Gdef", "module_public": false}, "NotebookValidationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "nbformat.validator.NotebookValidationError", "name": "NotebookValidationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "nbformat.validator.NotebookValidationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "nbformat.validator", "mro": ["nbformat.validator.NotebookValidationError", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.NotebookValidationError.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "original", "ref"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.NotebookValidationError.__init__", "name": "__init__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "nbformat.validator.NotebookValidationError.__str__", "name": "__str__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["nbformat.validator.NotebookValidationError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__unicode__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.NotebookValidationError.__unicode__", "name": "__unicode__", "type": null}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "nbformat.validator.NotebookValidationError.message", "name": "message", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "original": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "nbformat.validator.NotebookValidationError.original", "name": "original", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ref": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "nbformat.validator.NotebookValidationError.ref", "name": "ref", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "nbformat.validator.NotebookValidationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "nbformat.validator.NotebookValidationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef", "module_public": false}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "nbformat.json_compat.ValidationError", "kind": "Gdef"}, "_ITEM_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "nbformat.validator._ITEM_LIMIT", "name": "_ITEM_LIMIT", "setter_type": null, "type": "builtins.int"}}, "_STR_LIMIT": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "nbformat.validator._STR_LIMIT", "name": "_STR_LIMIT", "setter_type": null, "type": "builtins.int"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.validator.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "nbformat.validator.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_allow_undefined": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._allow_undefined", "name": "_allow_undefined", "type": null}}, "_dep_warn": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._dep_warn", "name": "_dep_warn", "type": null}}, "_deprecated": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "nbformat.validator._deprecated", "name": "_deprecated", "setter_type": null, "type": "builtins.object"}}, "_format_as_index": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["indices"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._format_as_index", "name": "_format_as_index", "type": null}}, "_get_errors": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 2], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._get_errors", "name": "_get_errors", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 2], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props", "args"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.int", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_errors", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_schema_json": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["v", "version", "version_minor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._get_schema_json", "name": "_get_schema_json", "type": null}}, "_normalize": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["nbdict", "version", "version_minor", "repair_duplicate_cell_ids", "relax_add_props", "strip_invalid_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._normalize", "name": "_normalize", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["nbdict", "version", "version_minor", "repair_duplicate_cell_ids", "relax_add_props", "strip_invalid_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_normalize", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_relax_additional_properties": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._relax_additional_properties", "name": "_relax_additional_properties", "type": null}}, "_strip_invalida_metadata": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._strip_invalida_metadata", "name": "_strip_invalida_metadata", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", "builtins.int", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_strip_invalida_metadata", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_truncate_obj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator._truncate_obj", "name": "_truncate_obj", "type": null}}, "_validator_for_name": {".class": "SymbolTableNode", "cross_ref": "nbformat.json_compat._validator_for_name", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "better_validation_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["error", "version", "version_minor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.better_validation_error", "name": "better_validation_error", "type": null}}, "dedent": {".class": "SymbolTableNode", "cross_ref": "textwrap.dedent", "kind": "Gdef", "module_public": false}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef", "module_public": false}, "generate_corpus_id": {".class": "SymbolTableNode", "cross_ref": "nbformat.corpus.words.generate_corpus_id", "kind": "Gdef", "module_public": false}, "get_current_validator": {".class": "SymbolTableNode", "cross_ref": "nbformat.json_compat.get_current_validator", "kind": "Gdef", "module_public": false}, "get_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["version", "version_minor", "relax_add_props", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.get_validator", "name": "get_validator", "type": null}}, "get_version": {".class": "SymbolTableNode", "cross_ref": "nbformat.reader.get_version", "kind": "Gdef", "module_public": false}, "import_item": {".class": "SymbolTableNode", "cross_ref": "nbformat._imports.import_item", "kind": "Gdef", "module_public": false}, "isvalid": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["nb<PERSON><PERSON>", "ref", "version", "version_minor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.isvalid", "name": "isvalid", "type": null}}, "iter_validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1], "arg_names": ["nbdict", "ref", "version", "version_minor", "relax_add_props", "nb<PERSON><PERSON>", "strip_invalid_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.iter_validate", "name": "iter_validate", "type": null}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "normalize": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props", "strip_invalid_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.normalize", "name": "normalize", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5], "arg_names": ["nbdict", "version", "version_minor", "relax_add_props", "strip_invalid_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "normalize", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pprint": {".class": "SymbolTableNode", "cross_ref": "pprint", "kind": "Gdef", "module_public": false}, "validate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["nbdict", "ref", "version", "version_minor", "relax_add_props", "nb<PERSON><PERSON>", "repair_duplicate_cell_ids", "strip_invalid_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "nbformat.validator.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["nbdict", "ref", "version", "version_minor", "relax_add_props", "nb<PERSON><PERSON>", "repair_duplicate_cell_ids", "strip_invalid_metadata"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "validate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validators": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "nbformat.validator.validators", "name": "validators", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\nbformat\\validator.py"}