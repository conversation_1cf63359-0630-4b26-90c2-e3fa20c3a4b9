{"data_mtime": 1751955671, "dep_lines": [13, 11, 12, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 30, 30, 30], "dependencies": ["numpy._utils._convertions", "functools", "warnings", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "db2102495d3cf45536fed2f0cef0e1d7fc96ab8a", "id": "numpy._utils", "ignore_all": true, "interface_hash": "c3e1e7c2cab84707f27e11c81a4ce5a9a3425668", "mtime": 1750163002, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": true, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\numpy\\_utils\\__init__.py", "plugin_data": null, "size": 3311, "suppressed": [], "version_id": "1.16.1"}