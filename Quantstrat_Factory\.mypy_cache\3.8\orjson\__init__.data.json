{".class": "MypyFile", "_fullname": "<PERSON><PERSON><PERSON>", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Fragment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "orjson.Fragment", "name": "Fragment", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "orjson.Fragment", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "<PERSON><PERSON><PERSON>", "mro": ["orjson.Fragment", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "orjson.Fragment.contents", "name": "contents", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bytes", "builtins.str"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "orjson.Fragment.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "orjson.Fragment", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONDecodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["json.decoder.JSONDecodeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "orjson.JSONDecodeError", "name": "JSONDecodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "orjson.JSONDecodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "<PERSON><PERSON><PERSON>", "mro": ["orjson.JSONDecodeError", "json.decoder.JSONDecodeError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "orjson.JSONDecodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "orjson.JSONDecodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONEncodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.TypeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "orjson.JSONEncodeError", "name": "JSONEncodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "orjson.JSONEncodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "<PERSON><PERSON><PERSON>", "mro": ["orjson.JSONEncodeError", "builtins.TypeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "orjson.JSONEncodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "orjson.JSONEncodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OPT_APPEND_NEWLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_APPEND_NEWLINE", "name": "OPT_APPEND_NEWLINE", "setter_type": null, "type": "builtins.int"}}, "OPT_INDENT_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_INDENT_2", "name": "OPT_INDENT_2", "setter_type": null, "type": "builtins.int"}}, "OPT_NAIVE_UTC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_NAIVE_UTC", "name": "OPT_NAIVE_UTC", "setter_type": null, "type": "builtins.int"}}, "OPT_NON_STR_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_NON_STR_KEYS", "name": "OPT_NON_STR_KEYS", "setter_type": null, "type": "builtins.int"}}, "OPT_OMIT_MICROSECONDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_OMIT_MICROSECONDS", "name": "OPT_OMIT_MICROSECONDS", "setter_type": null, "type": "builtins.int"}}, "OPT_PASSTHROUGH_DATACLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_PASSTHROUGH_DATACLASS", "name": "OPT_PASSTHROUGH_DATACLASS", "setter_type": null, "type": "builtins.int"}}, "OPT_PASSTHROUGH_DATETIME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_PASSTHROUGH_DATETIME", "name": "OPT_PASSTHROUGH_DATETIME", "setter_type": null, "type": "builtins.int"}}, "OPT_PASSTHROUGH_SUBCLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_PASSTHROUGH_SUBCLASS", "name": "OPT_PASSTHROUGH_SUBCLASS", "setter_type": null, "type": "builtins.int"}}, "OPT_SERIALIZE_DATACLASS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_SERIALIZE_DATACLASS", "name": "OPT_SERIALIZE_DATACLASS", "setter_type": null, "type": "builtins.int"}}, "OPT_SERIALIZE_NUMPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_SERIALIZE_NUMPY", "name": "OPT_SERIALIZE_NUMPY", "setter_type": null, "type": "builtins.int"}}, "OPT_SERIALIZE_UUID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_SERIALIZE_UUID", "name": "OPT_SERIALIZE_UUID", "setter_type": null, "type": "builtins.int"}}, "OPT_SORT_KEYS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_SORT_KEYS", "name": "OPT_SORT_KEYS", "setter_type": null, "type": "builtins.int"}}, "OPT_STRICT_INTEGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_STRICT_INTEGER", "name": "OPT_STRICT_INTEGER", "setter_type": null, "type": "builtins.int"}}, "OPT_UTC_Z": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.OPT_UTC_Z", "name": "OPT_UTC_Z", "setter_type": null, "type": "builtins.int"}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_hidden": true, "module_public": false}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "or<PERSON><PERSON>.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orj<PERSON>.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "or<PERSON><PERSON>.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "orjson.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "dumps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": [null, "default", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "orjson.dumps", "name": "dumps", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": [null, "default", "option"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "dumps", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_hidden": true, "module_public": false}, "loads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "orjson.loads", "name": "loads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "UnionType", "items": ["builtins.bytes", "builtins.bytearray", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}, "builtins.str"], "uses_pep604_syntax": false}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "loads", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\orjson\\__init__.pyi"}