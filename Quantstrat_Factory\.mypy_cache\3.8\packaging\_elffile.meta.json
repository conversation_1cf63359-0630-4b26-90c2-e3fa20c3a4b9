{"data_mtime": 1751955670, "dep_lines": [11, 13, 14, 15, 16, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["__future__", "enum", "os", "struct", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "dc3c25ff49ee9eb6a37daad8fd6f6664768d7f5a", "id": "packaging._elffile", "ignore_all": true, "interface_hash": "ff560075cd3401f053e0b1c340fcb33f44f1c0e6", "mtime": 1748947736, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\packaging\\_elffile.py", "plugin_data": null, "size": 3306, "suppressed": [], "version_id": "1.16.1"}