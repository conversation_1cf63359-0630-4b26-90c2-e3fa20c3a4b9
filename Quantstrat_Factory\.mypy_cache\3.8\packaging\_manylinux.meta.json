{"data_mtime": 1751955671, "dep_lines": [12, 1, 3, 4, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30], "dependencies": ["packaging._elffile", "__future__", "collections", "contextlib", "functools", "os", "re", "sys", "warnings", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "b1eff66f27c5033317c724bd4b9ac57a0727f271", "id": "packaging._manylinux", "ignore_all": true, "interface_hash": "c6d1b3a4e6dd80b4e1d03dc51faa73a6692d5de4", "mtime": 1748947736, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\packaging\\_manylinux.py", "plugin_data": null, "size": 9612, "suppressed": [], "version_id": "1.16.1"}