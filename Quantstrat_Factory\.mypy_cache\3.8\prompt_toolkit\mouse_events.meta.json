{"data_mtime": 1751955670, "dep_lines": [23, 19, 21, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30], "dependencies": ["prompt_toolkit.data_structures", "__future__", "enum", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "e7e34ccdae41bdeab316d8cf925b7c4945f8dc9f", "id": "prompt_toolkit.mouse_events", "ignore_all": true, "interface_hash": "b2a7e1f3f3a6d51f049d9724d9ccd49e91ac60db", "mtime": 1748947777, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\prompt_toolkit\\mouse_events.py", "plugin_data": null, "size": 2473, "suppressed": [], "version_id": "1.16.1"}