{".class": "MypyFile", "_fullname": "prompt_toolkit.styles.pygments", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PygmentsStyle": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.styles.pygments.PygmentsStyle", "name": "PygmentsStyle", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "prompt_toolkit.styles.pygments.PygmentsStyle", "source_any": null, "type_of_any": 3}}}, "Style": {".class": "SymbolTableNode", "cross_ref": "prompt_toolkit.styles.style.Style", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Token": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "prompt_toolkit.styles.pygments.Token", "name": "Token", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "prompt_toolkit.styles.pygments.Token", "source_any": null, "type_of_any": 3}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "prompt_toolkit.styles.pygments.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_toolkit.styles.pygments.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "pygments_token_to_classname": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.pygments.pygments_token_to_classname", "name": "pygments_token_to_classname", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["token"], "arg_types": [{".class": "AnyType", "missing_import_name": "prompt_toolkit.styles.pygments.Token", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pygments_token_to_classname", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_from_pygments_cls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pygments_style_cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.pygments.style_from_pygments_cls", "name": "style_from_pygments_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pygments_style_cls"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": "prompt_toolkit.styles.pygments.PygmentsStyle", "source_any": null, "type_of_any": 3}}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "style_from_pygments_cls", "ret_type": "prompt_toolkit.styles.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "style_from_pygments_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pygments_dict"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_toolkit.styles.pygments.style_from_pygments_dict", "name": "style_from_pygments_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pygments_dict"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": "prompt_toolkit.styles.pygments.Token", "source_any": null, "type_of_any": 3}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "style_from_pygments_dict", "ret_type": "prompt_toolkit.styles.style.Style", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\prompt_toolkit\\styles\\pygments.py"}