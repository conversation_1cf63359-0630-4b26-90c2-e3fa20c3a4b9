{"data_mtime": 1751955670, "dep_lines": [14, 21, 12, 5, 7, 8, 9, 10, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["prompt_toolkit.styles.base", "prompt_toolkit.styles.named_colors", "prompt_toolkit.cache", "__future__", "itertools", "re", "enum", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc"], "hash": "4b778519fd34efe10043dd70adff41f3c7724a93", "id": "prompt_toolkit.styles.style", "ignore_all": true, "interface_hash": "95cc0c3b9d49ae52a4852e91ee7c54be56c5141d", "mtime": 1748947777, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\prompt_toolkit\\styles\\style.py", "plugin_data": null, "size": 13043, "suppressed": [], "version_id": "1.16.1"}