{"data_mtime": 1751955670, "dep_lines": [1, 3, 4, 5, 6, 7, 8, 1, 1, 1, 1, 1, 1, 18], "dep_prios": [5, 10, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["__future__", "os", "signal", "sys", "threading", "collections", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "contextlib", "types"], "hash": "6514c7607155863086c1f616a4987beea9d7759e", "id": "prompt_toolkit.utils", "ignore_all": true, "interface_hash": "2a6a8a304e1a42e42b13d197a4c84b6681c2368d", "mtime": 1748947777, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\prompt_toolkit\\utils.py", "plugin_data": null, "size": 8631, "suppressed": ["wcwidth"], "version_id": "1.16.1"}