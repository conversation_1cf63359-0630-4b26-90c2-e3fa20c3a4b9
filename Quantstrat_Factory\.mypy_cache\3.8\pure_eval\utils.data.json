{".class": "MypyFile", "_fullname": "pure_eval.utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CannotEval": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pure_eval.utils.CannotEval", "name": "CannotEval", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pure_eval.utils.CannotEval", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pure_eval.utils", "mro": ["pure_eval.utils.CannotEval", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.CannotEval.__repr__", "name": "__repr__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "pure_eval.utils.CannotEval.__str__", "name": "__str__", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pure_eval.utils.CannotEval"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pure_eval.utils.CannotEval.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pure_eval.utils.CannotEval", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Fraction": {".class": "SymbolTableNode", "cross_ref": "fractions.Fraction", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "_C": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pure_eval.utils._C", "name": "_C", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pure_eval.utils._C", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pure_eval.utils", "mro": ["pure_eval.utils._C", "builtins.object"], "names": {".class": "SymbolTable", "bar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils._C.bar", "name": "bar", "type": null}}, "cm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pure_eval.utils._C.cm", "name": "cm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pure_eval.utils._C.cm", "name": "cm", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "pure_eval.utils._C"}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cm of _C", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "foo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils._C.foo", "name": "foo", "type": null}}, "sm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "pure_eval.utils._C.sm", "name": "sm", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "pure_eval.utils._C.sm", "name": "sm", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "sm of _C", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pure_eval.utils._C.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pure_eval.utils._C", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pure_eval.utils._E", "name": "_E", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "pure_eval.utils._E", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "pure_eval.utils", "mro": ["pure_eval.utils._E", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pure_eval.utils._E.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pure_eval.utils._E", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pure_eval.utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_is_standard_types_deep": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["x", "check_dict_values", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils._is_standard_types_deep", "name": "_is_standard_types_deep", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["x", "check_dict_values", "deep"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_standard_types_deep", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ast": {".class": "SymbolTableNode", "cross_ref": "ast", "kind": "Gdef"}, "ast_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.ast_name", "name": "ast_name", "type": null}}, "copy_ast_without_context": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.copy_ast_without_context", "name": "copy_ast_without_context", "type": null}}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "ensure_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.ensure_dict", "name": "ensure_dict", "type": null}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "eq_checking_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.eq_checking_types", "name": "eq_checking_types", "type": null}}, "has_ast_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["value", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.has_ast_name", "name": "has_ast_name", "type": null}}, "is_any": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["x", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.is_any", "name": "is_any", "type": null}}, "is_standard_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["x", "check_dict_values", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.is_standard_types", "name": "is_standard_types", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["x", "check_dict_values", "deep"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "is_standard_types", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "of_standard_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["x", "check_dict_values", "deep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.of_standard_types", "name": "of_standard_types", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["x", "check_dict_values", "deep"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.bool", "builtins.bool"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "of_standard_types", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "of_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["x", "types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.of_type", "name": "of_type", "type": null}}, "safe_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pure_eval.utils.safe_name", "name": "safe_name", "type": null}}, "safe_name_samples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pure_eval.utils.safe_name_samples", "name": "safe_name_samples", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "safe_name_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pure_eval.utils.safe_name_types", "name": "safe_name_types", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "typing_annotation_samples": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pure_eval.utils.typing_annotation_samples", "name": "typing_annotation_samples", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "typing_annotation_types": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pure_eval.utils.typing_annotation_types", "name": "typing_annotation_types", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\pure_eval\\utils.py"}