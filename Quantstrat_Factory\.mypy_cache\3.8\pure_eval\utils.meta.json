{"data_mtime": 1751955671, "dep_lines": [1, 2, 3, 4, 5, 6, 7, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["collections", "datetime", "decimal", "fractions", "ast", "enum", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "1efd087b077f77b1e6b42fe6f8d58cf723801b44", "id": "pure_eval.utils", "ignore_all": true, "interface_hash": "18a5438803c8d2e07592eefefe6d42113382e45d", "mtime": 1748947780, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\pure_eval\\utils.py", "plugin_data": null, "size": 4612, "suppressed": [], "version_id": "1.16.1"}