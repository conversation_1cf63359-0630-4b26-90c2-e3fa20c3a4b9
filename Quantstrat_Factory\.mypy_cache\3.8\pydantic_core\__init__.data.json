{".class": "MypyFile", "_fullname": "pydantic_core", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ArgsKwargs": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ArgsKwargs", "kind": "Gdef"}, "CoreConfig": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreConfig", "kind": "Gdef"}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "CoreSchemaType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchemaType", "kind": "Gdef"}, "ErrorDetails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.ErrorDetails", "name": "ErrorDetails", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.ErrorDetails", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core", "mro": ["pydantic_core.ErrorDetails", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", "builtins.str"], ["loc", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["msg", "builtins.str"], ["input", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["ctx", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["url", "builtins.str"]], "readonly_keys": [], "required_keys": ["input", "loc", "msg", "type"]}}}, "ErrorType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.ErrorType", "kind": "Gdef", "module_public": false}, "ErrorTypeInfo": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.ErrorTypeInfo", "name": "ErrorTypeInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.ErrorTypeInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core", "mro": ["pydantic_core.ErrorTypeInfo", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ErrorType"}], ["message_template_python", "builtins.str"], ["example_message_python", "builtins.str"], ["message_template_json", "builtins.str"], ["example_message_json", "builtins.str"], ["example_context", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["example_context", "example_message_python", "message_template_python", "type"]}}}, "InitErrorDetails": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.InitErrorDetails", "name": "InitErrorDetails", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.InitErrorDetails", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core", "mro": ["pydantic_core.InitErrorDetails", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "UnionType", "items": ["builtins.str", "pydantic_core._pydantic_core.PydanticCustomError"], "uses_pep604_syntax": true}], ["loc", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}], ["input", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["ctx", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["input", "type"]}}}, "MultiHostHost": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.MultiHostHost", "name": "MultiHostHost", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.MultiHostHost", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core", "mro": ["pydantic_core.MultiHostHost", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["username", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["password", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["host", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["port", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["host", "password", "port", "username"]}}}, "MultiHostUrl": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.MultiHostUrl", "kind": "Gdef"}, "PydanticCustomError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticCustomError", "kind": "Gdef"}, "PydanticKnownError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticKnownError", "kind": "Gdef"}, "PydanticOmit": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticOmit", "kind": "Gdef"}, "PydanticSerializationError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticSerializationError", "kind": "Gdef"}, "PydanticSerializationUnexpectedValue": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticSerializationUnexpectedValue", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUndefinedType": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefinedType", "kind": "Gdef"}, "PydanticUseDefault": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUseDefault", "kind": "Gdef"}, "SchemaError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaError", "kind": "Gdef"}, "SchemaSerializer": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaSerializer", "kind": "Gdef"}, "SchemaValidator": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.SchemaValidator", "kind": "Gdef"}, "Some": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.Some", "kind": "Gdef"}, "TzInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.TzInfo", "kind": "Gdef"}, "Url": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.Url", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.ValidationError", "kind": "Gdef"}, "_Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "_NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.NotRequired", "kind": "Gdef", "module_public": false}, "_TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic_core.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.__version__", "kind": "Gdef"}, "_sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "from_json": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.from_json", "kind": "Gdef"}, "to_json": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_json", "kind": "Gdef"}, "to_jsonable_python": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_jsonable_python", "kind": "Gdef"}, "validate_core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.validate_core_schema", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\pydantic_core\\__init__.py"}