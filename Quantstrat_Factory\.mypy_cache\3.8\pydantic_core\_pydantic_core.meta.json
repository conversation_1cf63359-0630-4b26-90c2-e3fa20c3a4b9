{"data_mtime": 1751955671, "dep_lines": [2, 9, 1, 3, 5, 6, 8, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["collections.abc", "pydantic_core.core_schema", "datetime", "typing", "_typeshed", "typing_extensions", "pydantic_core", "builtins", "_frozen_importlib", "abc"], "hash": "d324e69ed2f80d1f3a76229e8b3e658417fbfbbf", "id": "pydantic_core._pydantic_core", "ignore_all": true, "interface_hash": "09de200ed0e55e563e5618201eb4e96c67598cff", "mtime": 1748947786, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\pydantic_core\\_pydantic_core.pyi", "plugin_data": null, "size": 44398, "suppressed": [], "version_id": "1.16.1"}