{".class": "MypyFile", "_fullname": "shiboken6", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Object": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.Object", "kind": "Gdef"}, "VoidPtr": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.VoidPtr", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__maximum_python_version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "shiboken6.__maximum_python_version__", "name": "__maximum_python_version__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__minimum_python_version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "shiboken6.__minimum_python_version__", "name": "__minimum_python_version__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "shiboken6.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "shiboken6.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "__version_info__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "shiboken6.__version_info__", "name": "__version_info__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.float", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "base64": {".class": "SymbolTableNode", "cross_ref": "base64", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "createdByPython": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.createdByPython", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.delete", "kind": "Gdef"}, "dump": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.dump", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "getAllValidWrappers": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.getAllValidWrappers", "kind": "Gdef"}, "getCppPointer": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.getCppPointer", "kind": "Gdef"}, "invalidate": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.invalidate", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "isValid": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.isValid", "kind": "Gdef"}, "keyword": {".class": "SymbolTableNode", "cross_ref": "keyword", "kind": "Gdef"}, "marshal": {".class": "SymbolTableNode", "cross_ref": "marshal", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "ownedByPython": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.ownedByPython", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "struct": {".class": "SymbolTableNode", "cross_ref": "struct", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "textwrap": {".class": "SymbolTableNode", "cross_ref": "textwrap", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "wrapInstance": {".class": "SymbolTableNode", "cross_ref": "shiboken6.Shiboken.wrapInstance", "kind": "Gdef"}, "zipfile": {".class": "SymbolTableNode", "cross_ref": "zipfile", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\shiboken6\\__init__.py"}