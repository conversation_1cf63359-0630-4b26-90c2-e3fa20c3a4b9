{"data_mtime": 1751955670, "dep_lines": [27, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 30, 30], "dependencies": ["shiboken6.Shiboken", "sys", "os", "zipfile", "base64", "marshal", "io", "contextlib", "textwrap", "traceback", "types", "struct", "re", "tempfile", "keyword", "functools", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "122fa6bc656032ceb360093cfffad4f78a206469", "id": "shiboken6", "ignore_all": true, "interface_hash": "a7cbc2a679bd64458c7ce79ece1ff58b6252507c", "mtime": 1748947838, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\shiboken6\\__init__.py", "plugin_data": null, "size": 698, "suppressed": [], "version_id": "1.16.1"}