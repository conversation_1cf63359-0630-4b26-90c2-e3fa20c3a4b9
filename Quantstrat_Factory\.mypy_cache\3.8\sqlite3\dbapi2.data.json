{".class": "MypyFile", "_fullname": "sqlite3.dbapi2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Binary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Binary", "line": 241, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Connection", "kind": "Gdef"}, "Cursor": {".class": "SymbolTableNode", "cross_ref": "sqlite3.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "DataError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DataError", "kind": "Gdef"}, "DatabaseError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DatabaseError", "kind": "Gdef"}, "Date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Date", "line": 228, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.date"}}, "DateFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.DateFromTicks", "name": "DateFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "DateFromTicks", "ret_type": "datetime.date", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Error": {".class": "SymbolTableNode", "cross_ref": "sqlite3.E<PERSON>r", "kind": "Gdef"}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.IntegrityError", "kind": "Gdef"}, "InterfaceError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InterfaceError", "kind": "Gdef"}, "InternalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InternalError", "kind": "Gdef"}, "NotSupportedError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.NotSupportedError", "kind": "Gdef"}, "OperationalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.OperationalError", "kind": "Gdef"}, "OptimizedUnicode": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.OptimizedUnicode", "kind": "Gdef"}, "PARSE_COLNAMES": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.PARSE_COLNAMES", "kind": "Gdef"}, "PARSE_DECLTYPES": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.PARSE_DECLTYPES", "kind": "Gdef"}, "PrepareProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlite3.PrepareProtocol", "kind": "Gdef"}, "ProgrammingError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.ProgrammingError", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Row", "kind": "Gdef"}, "SQLITE_ALTER_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ALTER_TABLE", "kind": "Gdef"}, "SQLITE_ANALYZE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ANALYZE", "kind": "Gdef"}, "SQLITE_ATTACH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ATTACH", "kind": "Gdef"}, "SQLITE_CREATE_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_INDEX", "kind": "Gdef"}, "SQLITE_CREATE_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TABLE", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_INDEX", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_TABLE", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_TRIGGER", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_VIEW", "kind": "Gdef"}, "SQLITE_CREATE_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TRIGGER", "kind": "Gdef"}, "SQLITE_CREATE_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_VIEW", "kind": "Gdef"}, "SQLITE_CREATE_VTABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_VTABLE", "kind": "Gdef"}, "SQLITE_DELETE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DELETE", "kind": "Gdef"}, "SQLITE_DENY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DENY", "kind": "Gdef"}, "SQLITE_DETACH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DETACH", "kind": "Gdef"}, "SQLITE_DONE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DONE", "kind": "Gdef"}, "SQLITE_DROP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_INDEX", "kind": "Gdef"}, "SQLITE_DROP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TABLE", "kind": "Gdef"}, "SQLITE_DROP_TEMP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_INDEX", "kind": "Gdef"}, "SQLITE_DROP_TEMP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_TABLE", "kind": "Gdef"}, "SQLITE_DROP_TEMP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_TRIGGER", "kind": "Gdef"}, "SQLITE_DROP_TEMP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_VIEW", "kind": "Gdef"}, "SQLITE_DROP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TRIGGER", "kind": "Gdef"}, "SQLITE_DROP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_VIEW", "kind": "Gdef"}, "SQLITE_DROP_VTABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_VTABLE", "kind": "Gdef"}, "SQLITE_FUNCTION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_FUNCTION", "kind": "Gdef"}, "SQLITE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IGNORE", "kind": "Gdef"}, "SQLITE_INSERT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_INSERT", "kind": "Gdef"}, "SQLITE_OK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_OK", "kind": "Gdef"}, "SQLITE_PRAGMA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_PRAGMA", "kind": "Gdef"}, "SQLITE_READ": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READ", "kind": "Gdef"}, "SQLITE_RECURSIVE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_RECURSIVE", "kind": "Gdef"}, "SQLITE_REINDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_REINDEX", "kind": "Gdef"}, "SQLITE_SAVEPOINT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_SAVEPOINT", "kind": "Gdef"}, "SQLITE_SELECT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_SELECT", "kind": "Gdef"}, "SQLITE_TRANSACTION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_TRANSACTION", "kind": "Gdef"}, "SQLITE_UPDATE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_UPDATE", "kind": "Gdef"}, "Time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Time", "line": 229, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.time"}}, "TimeFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.TimeFromTicks", "name": "TimeFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "TimeFromTicks", "ret_type": "datetime.time", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Timestamp", "line": 230, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.datetime"}}, "TimestampFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.TimestampFromTicks", "name": "TimestampFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "TimestampFromTicks", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Warning": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Warning", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "adapt": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.adapt", "kind": "Gdef"}, "adapters": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.adapters", "kind": "Gdef"}, "apilevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.apilevel", "name": "apilevel", "setter_type": null, "type": "builtins.str"}}, "complete_statement": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.complete_statement", "kind": "Gdef"}, "connect": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.connect", "kind": "Gdef"}, "converters": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.converters", "kind": "Gdef"}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "enable_callback_tracebacks": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.enable_callback_tracebacks", "kind": "Gdef"}, "enable_shared_cache": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.enable_shared_cache", "kind": "Gdef"}, "paramstyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.paramstyle", "name": "paramstyle", "setter_type": null, "type": "builtins.str"}}, "register_adapter": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.register_adapter", "kind": "Gdef"}, "register_converter": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.register_converter", "kind": "Gdef"}, "sqlite_version": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.sqlite_version", "kind": "Gdef"}, "sqlite_version_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.sqlite_version_info", "name": "sqlite_version_info", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threadsafety": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.threadsafety", "name": "threadsafety", "setter_type": null, "type": "builtins.int"}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.version", "name": "version", "setter_type": null, "type": "builtins.str"}}, "version_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.version_info", "name": "version_info", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\sqlite3\\dbapi2.pyi"}