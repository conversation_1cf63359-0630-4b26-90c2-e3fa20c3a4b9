{".class": "MypyFile", "_fullname": "starlette.status", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTTP_100_CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_100_CONTINUE", "name": "HTTP_100_CONTINUE", "setter_type": null, "type": "builtins.int"}}, "HTTP_101_SWITCHING_PROTOCOLS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_101_SWITCHING_PROTOCOLS", "name": "HTTP_101_SWITCHING_PROTOCOLS", "setter_type": null, "type": "builtins.int"}}, "HTTP_102_PROCESSING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_102_PROCESSING", "name": "HTTP_102_PROCESSING", "setter_type": null, "type": "builtins.int"}}, "HTTP_103_EARLY_HINTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_103_EARLY_HINTS", "name": "HTTP_103_EARLY_HINTS", "setter_type": null, "type": "builtins.int"}}, "HTTP_200_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_200_OK", "name": "HTTP_200_OK", "setter_type": null, "type": "builtins.int"}}, "HTTP_201_CREATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_201_CREATED", "name": "HTTP_201_CREATED", "setter_type": null, "type": "builtins.int"}}, "HTTP_202_ACCEPTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_202_ACCEPTED", "name": "HTTP_202_ACCEPTED", "setter_type": null, "type": "builtins.int"}}, "HTTP_203_NON_AUTHORITATIVE_INFORMATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_203_NON_AUTHORITATIVE_INFORMATION", "name": "HTTP_203_NON_AUTHORITATIVE_INFORMATION", "setter_type": null, "type": "builtins.int"}}, "HTTP_204_NO_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_204_NO_CONTENT", "name": "HTTP_204_NO_CONTENT", "setter_type": null, "type": "builtins.int"}}, "HTTP_205_RESET_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_205_RESET_CONTENT", "name": "HTTP_205_RESET_CONTENT", "setter_type": null, "type": "builtins.int"}}, "HTTP_206_PARTIAL_CONTENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_206_PARTIAL_CONTENT", "name": "HTTP_206_PARTIAL_CONTENT", "setter_type": null, "type": "builtins.int"}}, "HTTP_207_MULTI_STATUS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_207_MULTI_STATUS", "name": "HTTP_207_MULTI_STATUS", "setter_type": null, "type": "builtins.int"}}, "HTTP_208_ALREADY_REPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_208_ALREADY_REPORTED", "name": "HTTP_208_ALREADY_REPORTED", "setter_type": null, "type": "builtins.int"}}, "HTTP_226_IM_USED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_226_IM_USED", "name": "HTTP_226_IM_USED", "setter_type": null, "type": "builtins.int"}}, "HTTP_300_MULTIPLE_CHOICES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_300_MULTIPLE_CHOICES", "name": "HTTP_300_MULTIPLE_CHOICES", "setter_type": null, "type": "builtins.int"}}, "HTTP_301_MOVED_PERMANENTLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_301_MOVED_PERMANENTLY", "name": "HTTP_301_MOVED_PERMANENTLY", "setter_type": null, "type": "builtins.int"}}, "HTTP_302_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_302_FOUND", "name": "HTTP_302_FOUND", "setter_type": null, "type": "builtins.int"}}, "HTTP_303_SEE_OTHER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_303_SEE_OTHER", "name": "HTTP_303_SEE_OTHER", "setter_type": null, "type": "builtins.int"}}, "HTTP_304_NOT_MODIFIED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_304_NOT_MODIFIED", "name": "HTTP_304_NOT_MODIFIED", "setter_type": null, "type": "builtins.int"}}, "HTTP_305_USE_PROXY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_305_USE_PROXY", "name": "HTTP_305_USE_PROXY", "setter_type": null, "type": "builtins.int"}}, "HTTP_306_RESERVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_306_RESERVED", "name": "HTTP_306_RESERVED", "setter_type": null, "type": "builtins.int"}}, "HTTP_307_TEMPORARY_REDIRECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_307_TEMPORARY_REDIRECT", "name": "HTTP_307_TEMPORARY_REDIRECT", "setter_type": null, "type": "builtins.int"}}, "HTTP_308_PERMANENT_REDIRECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_308_PERMANENT_REDIRECT", "name": "HTTP_308_PERMANENT_REDIRECT", "setter_type": null, "type": "builtins.int"}}, "HTTP_400_BAD_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_400_BAD_REQUEST", "name": "HTTP_400_BAD_REQUEST", "setter_type": null, "type": "builtins.int"}}, "HTTP_401_UNAUTHORIZED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_401_UNAUTHORIZED", "name": "HTTP_401_UNAUTHORIZED", "setter_type": null, "type": "builtins.int"}}, "HTTP_402_PAYMENT_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_402_PAYMENT_REQUIRED", "name": "HTTP_402_PAYMENT_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "HTTP_403_FORBIDDEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_403_FORBIDDEN", "name": "HTTP_403_FORBIDDEN", "setter_type": null, "type": "builtins.int"}}, "HTTP_404_NOT_FOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_404_NOT_FOUND", "name": "HTTP_404_NOT_FOUND", "setter_type": null, "type": "builtins.int"}}, "HTTP_405_METHOD_NOT_ALLOWED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_405_METHOD_NOT_ALLOWED", "name": "HTTP_405_METHOD_NOT_ALLOWED", "setter_type": null, "type": "builtins.int"}}, "HTTP_406_NOT_ACCEPTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_406_NOT_ACCEPTABLE", "name": "HTTP_406_NOT_ACCEPTABLE", "setter_type": null, "type": "builtins.int"}}, "HTTP_407_PROXY_AUTHENTICATION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_407_PROXY_AUTHENTICATION_REQUIRED", "name": "HTTP_407_PROXY_AUTHENTICATION_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "HTTP_408_REQUEST_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_408_REQUEST_TIMEOUT", "name": "HTTP_408_REQUEST_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "HTTP_409_CONFLICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_409_CONFLICT", "name": "HTTP_409_CONFLICT", "setter_type": null, "type": "builtins.int"}}, "HTTP_410_GONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_410_GONE", "name": "HTTP_410_GONE", "setter_type": null, "type": "builtins.int"}}, "HTTP_411_LENGTH_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_411_LENGTH_REQUIRED", "name": "HTTP_411_LENGTH_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "HTTP_412_PRECONDITION_FAILED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_412_PRECONDITION_FAILED", "name": "HTTP_412_PRECONDITION_FAILED", "setter_type": null, "type": "builtins.int"}}, "HTTP_413_REQUEST_ENTITY_TOO_LARGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_413_REQUEST_ENTITY_TOO_LARGE", "name": "HTTP_413_REQUEST_ENTITY_TOO_LARGE", "setter_type": null, "type": "builtins.int"}}, "HTTP_414_REQUEST_URI_TOO_LONG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_414_REQUEST_URI_TOO_LONG", "name": "HTTP_414_REQUEST_URI_TOO_LONG", "setter_type": null, "type": "builtins.int"}}, "HTTP_415_UNSUPPORTED_MEDIA_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_415_UNSUPPORTED_MEDIA_TYPE", "name": "HTTP_415_UNSUPPORTED_MEDIA_TYPE", "setter_type": null, "type": "builtins.int"}}, "HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE", "name": "HTTP_416_REQUESTED_RANGE_NOT_SATISFIABLE", "setter_type": null, "type": "builtins.int"}}, "HTTP_417_EXPECTATION_FAILED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_417_EXPECTATION_FAILED", "name": "HTTP_417_EXPECTATION_FAILED", "setter_type": null, "type": "builtins.int"}}, "HTTP_418_IM_A_TEAPOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_418_IM_A_TEAPOT", "name": "HTTP_418_IM_A_TEAPOT", "setter_type": null, "type": "builtins.int"}}, "HTTP_421_MISDIRECTED_REQUEST": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_421_MISDIRECTED_REQUEST", "name": "HTTP_421_MISDIRECTED_REQUEST", "setter_type": null, "type": "builtins.int"}}, "HTTP_422_UNPROCESSABLE_ENTITY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_422_UNPROCESSABLE_ENTITY", "name": "HTTP_422_UNPROCESSABLE_ENTITY", "setter_type": null, "type": "builtins.int"}}, "HTTP_423_LOCKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_423_LOCKED", "name": "HTTP_423_LOCKED", "setter_type": null, "type": "builtins.int"}}, "HTTP_424_FAILED_DEPENDENCY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_424_FAILED_DEPENDENCY", "name": "HTTP_424_FAILED_DEPENDENCY", "setter_type": null, "type": "builtins.int"}}, "HTTP_425_TOO_EARLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_425_TOO_EARLY", "name": "HTTP_425_TOO_EARLY", "setter_type": null, "type": "builtins.int"}}, "HTTP_426_UPGRADE_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_426_UPGRADE_REQUIRED", "name": "HTTP_426_UPGRADE_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "HTTP_428_PRECONDITION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_428_PRECONDITION_REQUIRED", "name": "HTTP_428_PRECONDITION_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "HTTP_429_TOO_MANY_REQUESTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_429_TOO_MANY_REQUESTS", "name": "HTTP_429_TOO_MANY_REQUESTS", "setter_type": null, "type": "builtins.int"}}, "HTTP_431_REQUEST_HEADER_FIELDS_TOO_LARGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_431_REQUEST_HEADER_FIELDS_TOO_LARGE", "name": "HTTP_431_REQUEST_HEADER_FIELDS_TOO_LARGE", "setter_type": null, "type": "builtins.int"}}, "HTTP_451_UNAVAILABLE_FOR_LEGAL_REASONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_451_UNAVAILABLE_FOR_LEGAL_REASONS", "name": "HTTP_451_UNAVAILABLE_FOR_LEGAL_REASONS", "setter_type": null, "type": "builtins.int"}}, "HTTP_500_INTERNAL_SERVER_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_500_INTERNAL_SERVER_ERROR", "name": "HTTP_500_INTERNAL_SERVER_ERROR", "setter_type": null, "type": "builtins.int"}}, "HTTP_501_NOT_IMPLEMENTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_501_NOT_IMPLEMENTED", "name": "HTTP_501_NOT_IMPLEMENTED", "setter_type": null, "type": "builtins.int"}}, "HTTP_502_BAD_GATEWAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_502_BAD_GATEWAY", "name": "HTTP_502_BAD_GATEWAY", "setter_type": null, "type": "builtins.int"}}, "HTTP_503_SERVICE_UNAVAILABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_503_SERVICE_UNAVAILABLE", "name": "HTTP_503_SERVICE_UNAVAILABLE", "setter_type": null, "type": "builtins.int"}}, "HTTP_504_GATEWAY_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_504_GATEWAY_TIMEOUT", "name": "HTTP_504_GATEWAY_TIMEOUT", "setter_type": null, "type": "builtins.int"}}, "HTTP_505_HTTP_VERSION_NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_505_HTTP_VERSION_NOT_SUPPORTED", "name": "HTTP_505_HTTP_VERSION_NOT_SUPPORTED", "setter_type": null, "type": "builtins.int"}}, "HTTP_506_VARIANT_ALSO_NEGOTIATES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_506_VARIANT_ALSO_NEGOTIATES", "name": "HTTP_506_VARIANT_ALSO_NEGOTIATES", "setter_type": null, "type": "builtins.int"}}, "HTTP_507_INSUFFICIENT_STORAGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_507_INSUFFICIENT_STORAGE", "name": "HTTP_507_INSUFFICIENT_STORAGE", "setter_type": null, "type": "builtins.int"}}, "HTTP_508_LOOP_DETECTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_508_LOOP_DETECTED", "name": "HTTP_508_LOOP_DETECTED", "setter_type": null, "type": "builtins.int"}}, "HTTP_510_NOT_EXTENDED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_510_NOT_EXTENDED", "name": "HTTP_510_NOT_EXTENDED", "setter_type": null, "type": "builtins.int"}}, "HTTP_511_NETWORK_AUTHENTICATION_REQUIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.HTTP_511_NETWORK_AUTHENTICATION_REQUIRED", "name": "HTTP_511_NETWORK_AUTHENTICATION_REQUIRED", "setter_type": null, "type": "builtins.int"}}, "WS_1000_NORMAL_CLOSURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1000_NORMAL_CLOSURE", "name": "WS_1000_NORMAL_CLOSURE", "setter_type": null, "type": "builtins.int"}}, "WS_1001_GOING_AWAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1001_GOING_AWAY", "name": "WS_1001_GOING_AWAY", "setter_type": null, "type": "builtins.int"}}, "WS_1002_PROTOCOL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1002_PROTOCOL_ERROR", "name": "WS_1002_PROTOCOL_ERROR", "setter_type": null, "type": "builtins.int"}}, "WS_1003_UNSUPPORTED_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1003_UNSUPPORTED_DATA", "name": "WS_1003_UNSUPPORTED_DATA", "setter_type": null, "type": "builtins.int"}}, "WS_1005_NO_STATUS_RCVD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1005_NO_STATUS_RCVD", "name": "WS_1005_NO_STATUS_RCVD", "setter_type": null, "type": "builtins.int"}}, "WS_1006_ABNORMAL_CLOSURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1006_ABNORMAL_CLOSURE", "name": "WS_1006_ABNORMAL_CLOSURE", "setter_type": null, "type": "builtins.int"}}, "WS_1007_INVALID_FRAME_PAYLOAD_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1007_INVALID_FRAME_PAYLOAD_DATA", "name": "WS_1007_INVALID_FRAME_PAYLOAD_DATA", "setter_type": null, "type": "builtins.int"}}, "WS_1008_POLICY_VIOLATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1008_POLICY_VIOLATION", "name": "WS_1008_POLICY_VIOLATION", "setter_type": null, "type": "builtins.int"}}, "WS_1009_MESSAGE_TOO_BIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1009_MESSAGE_TOO_BIG", "name": "WS_1009_MESSAGE_TOO_BIG", "setter_type": null, "type": "builtins.int"}}, "WS_1010_MANDATORY_EXT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1010_MANDATORY_EXT", "name": "WS_1010_MANDATORY_EXT", "setter_type": null, "type": "builtins.int"}}, "WS_1011_INTERNAL_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1011_INTERNAL_ERROR", "name": "WS_1011_INTERNAL_ERROR", "setter_type": null, "type": "builtins.int"}}, "WS_1012_SERVICE_RESTART": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1012_SERVICE_RESTART", "name": "WS_1012_SERVICE_RESTART", "setter_type": null, "type": "builtins.int"}}, "WS_1013_TRY_AGAIN_LATER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1013_TRY_AGAIN_LATER", "name": "WS_1013_TRY_AGAIN_LATER", "setter_type": null, "type": "builtins.int"}}, "WS_1014_BAD_GATEWAY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1014_BAD_GATEWAY", "name": "WS_1014_BAD_GATEWAY", "setter_type": null, "type": "builtins.int"}}, "WS_1015_TLS_HANDSHAKE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "starlette.status.WS_1015_TLS_HANDSHAKE", "name": "WS_1015_TLS_HANDSHAKE", "setter_type": null, "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "starlette.status.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\starlette\\status.py"}