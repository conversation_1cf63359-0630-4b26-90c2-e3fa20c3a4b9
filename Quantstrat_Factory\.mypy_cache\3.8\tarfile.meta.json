{"data_mtime": 1751955671, "dep_lines": [6, 1, 2, 3, 4, 5, 7, 8, 9, 10, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "bz2", "io", "sys", "_typeshed", "builtins", "gzip", "types", "typing", "typing_extensions", "_compression", "_frozen_importlib", "_io", "abc", "os"], "hash": "e1b5d61c448989e174a6fe4855882e2411855906", "id": "tarfile", "ignore_all": true, "interface_hash": "e1ce8c2a23eb86e458acd80bdf0d4c50a6029e18", "mtime": 1751251075, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\tarfile.pyi", "plugin_data": null, "size": 21030, "suppressed": [], "version_id": "1.16.1"}