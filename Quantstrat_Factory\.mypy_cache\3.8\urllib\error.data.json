{".class": "MypyFile", "_fullname": "urllib.error", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "ContentTooShortError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib.error.URLError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib.error.ContentTooShortError", "name": "ContentTooShortError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib.error.ContentTooShortError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib.error", "mro": ["urllib.error.ContentTooShortError", "urllib.error.URLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib.error.ContentTooShortError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "message", "content"], "arg_types": ["urllib.error.ContentTooShortError", "builtins.str", {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ContentTooShortError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib.error.ContentTooShortError.content", "name": "content", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib.error.ContentTooShortError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib.error.ContentTooShortError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HTTPError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["urllib.error.URLError", "urllib.response.addinfourl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib.error.HTTPError", "name": "HTTPError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib.error.HTTPError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib.error", "mro": ["urllib.error.HTTPError", "urllib.error.URLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "urllib.response.addinfourl", "urllib.response.addinfo", "urllib.response.addbase", "tempfile._TemporaryFileWrapper", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "url", "code", "msg", "hdrs", "fp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib.error.HTTPError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "url", "code", "msg", "hdrs", "fp"], "arg_types": ["urllib.error.HTTPError", "builtins.str", "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib.error.HTTPError.code", "name": "code", "setter_type": null, "type": "builtins.int"}}, "headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "urllib.error.HTTPError.headers", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated", "is_trivial_self"], "fullname": "urllib.error.HTTPError.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib.error.HTTPError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "urllib.error.HTTPError.headers", "name": "headers", "setter_type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["urllib.error.HTTPError", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib.error.HTTPError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "urllib.error.HTTPError.headers", "name": "headers", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["urllib.error.HTTPError", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "", "name": "headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "headers"], "arg_types": ["urllib.error.HTTPError", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": 1, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib.error.HTTPError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "headers of HTTPError", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "email.message.Message"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "urllib.error.HTTPError.reason", "name": "reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib.error.HTTPError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reason of HTTPError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "urllib.error.HTTPError.reason", "name": "reason", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["urllib.error.HTTPError"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "reason of HTTPError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib.error.HTTPError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib.error.HTTPError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Message": {".class": "SymbolTableNode", "cross_ref": "email.message.Message", "kind": "Gdef", "module_hidden": true, "module_public": false}, "URLError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.OSError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "urllib.error.URLError", "name": "URLError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "urllib.error.URLError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "urllib.error", "mro": ["urllib.error.URLError", "builtins.OSError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "reason", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "urllib.error.URLError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "reason", "filename"], "arg_types": ["urllib.error.URLError", {".class": "UnionType", "items": ["builtins.str", "builtins.BaseException"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of URLError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "urllib.error.URLError.reason", "name": "reason", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "builtins.BaseException"], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "urllib.error.URLError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "urllib.error.URLError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "urllib.error.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "urllib.error.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "addinfourl": {".class": "SymbolTableNode", "cross_ref": "urllib.response.addinfourl", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\mypy\\typeshed\\stdlib\\urllib\\error.pyi"}