{".class": "MypyFile", "_fullname": "uvicorn._types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASGI2Application": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGI2Application", "line": 272, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeType", "item": "uvicorn._types.ASGI2Protocol"}}}, "ASGI2Protocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2], ["__init__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.ASGI2Protocol", "name": "ASGI2Protocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "uvicorn._types.ASGI2Protocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.ASGI2Protocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0], "arg_names": ["self", "receive", "send"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_body", "is_trivial_self"], "fullname": "uvicorn._types.ASGI2Protocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "receive", "send"], "arg_types": ["uvicorn._types.ASGI2Protocol", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of ASGI2Protocol", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body", "is_trivial_self"], "fullname": "uvicorn._types.ASGI2Protocol.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "scope"], "arg_types": ["uvicorn._types.ASGI2Protocol", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Scope"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ASGI2Protocol", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "uvicorn._types.ASGI2Protocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "uvicorn._types.ASGI2Protocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ASGI3Application": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGI3Application", "line": 273, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Scope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveCallable"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendCallable"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ASGIApplication": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGIApplication", "line": 281, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI2Application"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGI3Application"}], "uses_pep604_syntax": false}}}, "ASGIReceiveCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGIReceiveCallable", "line": 262, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIReceiveEvent"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ASGIReceiveEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGIReceiveEvent", "line": 233, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPRequestEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPDisconnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketConnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketReceiveEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketDisconnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanStartupEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanShutdownEvent"}], "uses_pep604_syntax": false}}}, "ASGISendCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGISendCallable", "line": 263, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGISendEvent"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ASGISendEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ASGISendEvent", "line": 244, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPResponseStartEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPResponseBodyEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPResponseTrailersEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPServerPushEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPDisconnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketAcceptEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketSendEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketResponseStartEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketResponseBodyEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketCloseEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanStartupCompleteEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanStartupFailedEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanShutdownCompleteEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanShutdownFailedEvent"}], "uses_pep604_syntax": false}}}, "ASGIVersions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.ASGIVersions", "name": "ASGIVersions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.ASGIVersions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.ASGIVersions", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["spec_version", "builtins.str"], ["version", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "2.0"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "3.0"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["spec_version", "version"]}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Environ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.Environ", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "ExcInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.ExcInfo", "line": 45, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "HTTPDisconnectEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPDisconnectEvent", "name": "HTTPDisconnectEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPDisconnectEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPDisconnectEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.disconnect"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "HTTPRequestEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPRequestEvent", "name": "HTTPRequestEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPRequestEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPRequestEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.request"}], ["body", "builtins.bytes"], ["more_body", "builtins.bool"]], "readonly_keys": [], "required_keys": ["body", "more_body", "type"]}}}, "HTTPResponseBodyEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPResponseBodyEvent", "name": "HTTPResponseBodyEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPResponseBodyEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPResponseBodyEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.response.body"}], ["body", "builtins.bytes"], ["more_body", "builtins.bool"]], "readonly_keys": [], "required_keys": ["body", "type"]}}}, "HTTPResponseDebugEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPResponseDebugEvent", "name": "HTTPResponseDebugEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPResponseDebugEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPResponseDebugEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.response.debug"}], ["info", {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["info", "type"]}}}, "HTTPResponseStartEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPResponseStartEvent", "name": "HTTPResponseStartEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPResponseStartEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPResponseStartEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.response.start"}], ["status", "builtins.int"], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["trailers", "builtins.bool"]], "readonly_keys": [], "required_keys": ["status", "type"]}}}, "HTTPResponseTrailersEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPResponseTrailersEvent", "name": "HTTPResponseTrailersEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPResponseTrailersEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPResponseTrailersEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.response.trailers"}], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["more_trailers", "builtins.bool"]], "readonly_keys": [], "required_keys": ["headers", "more_trailers", "type"]}}}, "HTTPScope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPScope", "name": "HTTPScope", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPScope", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPScope", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http"}], ["asgi", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIVersions"}], ["http_version", "builtins.str"], ["method", "builtins.str"], ["scheme", "builtins.str"], ["path", "builtins.str"], ["raw_path", "builtins.bytes"], ["query_string", "builtins.bytes"], ["root_path", "builtins.str"], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["client", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["server", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["state", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["extensions", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["asgi", "client", "headers", "http_version", "method", "path", "query_string", "raw_path", "root_path", "scheme", "server", "type"]}}}, "HTTPServerPushEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.HTTPServerPushEvent", "name": "HTTPServerPushEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.HTTPServerPushEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.HTTPServerPushEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "http.response.push"}], ["path", "builtins.str"], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}]], "readonly_keys": [], "required_keys": ["headers", "path", "type"]}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "LifespanScope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanScope", "name": "LifespanScope", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanScope", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanScope", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan"}], ["asgi", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIVersions"}], ["state", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["asgi", "type"]}}}, "LifespanShutdownCompleteEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanShutdownCompleteEvent", "name": "LifespanShutdownCompleteEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanShutdownCompleteEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanShutdownCompleteEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.shutdown.complete"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "LifespanShutdownEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanShutdownEvent", "name": "LifespanShutdownEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanShutdownEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanShutdownEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.shutdown"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "LifespanShutdownFailedEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanShutdownFailedEvent", "name": "LifespanShutdownFailedEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanShutdownFailedEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanShutdownFailedEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.shutdown.failed"}], ["message", "builtins.str"]], "readonly_keys": [], "required_keys": ["message", "type"]}}}, "LifespanStartupCompleteEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanStartupCompleteEvent", "name": "LifespanStartupCompleteEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanStartupCompleteEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanStartupCompleteEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.startup.complete"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "LifespanStartupEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanStartupEvent", "name": "LifespanStartupEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanStartupEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanStartupEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.startup"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "LifespanStartupFailedEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.LifespanStartupFailedEvent", "name": "LifespanStartupFailedEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.LifespanStartupFailedEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.LifespanStartupFailedEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lifespan.startup.failed"}], ["message", "builtins.str"]], "readonly_keys": [], "required_keys": ["message", "type"]}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NotRequired": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.NotRequired", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "Scope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.Scope", "line": 97, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.LifespanScope"}], "uses_pep604_syntax": false}}}, "StartResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.StartResponse", "line": 46, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ExcInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WSGIApp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.WSGIApp", "line": 47, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.Environ"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.StartResponse"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.BaseException"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WWWScope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.WWWScope", "line": 96, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.HTTPScope"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketScope"}], "uses_pep604_syntax": false}}}, "WebSocketAcceptEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketAcceptEvent", "name": "WebSocketAcceptEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketAcceptEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketAcceptEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.accept"}], ["subprotocol", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "WebSocketCloseEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketCloseEvent", "name": "WebSocketCloseEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketCloseEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketCloseEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.close"}], ["code", "builtins.int"], ["reason", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["type"]}}}, "WebSocketConnectEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketConnectEvent", "name": "WebSocketConnectEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketConnectEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketConnectEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.connect"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "WebSocketDisconnectEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketDisconnectEvent", "name": "WebSocketDisconnectEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketDisconnectEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketDisconnectEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.disconnect"}], ["code", "builtins.int"], ["reason", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}]], "readonly_keys": [], "required_keys": ["code", "type"]}}}, "WebSocketEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.WebSocketEvent", "line": 230, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketReceiveEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketDisconnectEvent"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.WebSocketConnectEvent"}], "uses_pep604_syntax": false}}}, "WebSocketReceiveEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.WebSocketReceiveEvent", "line": 162, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types._WebSocketReceiveEventBytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types._WebSocketReceiveEventText"}], "uses_pep604_syntax": false}}}, "WebSocketResponseBodyEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketResponseBodyEvent", "name": "WebSocketResponseBodyEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketResponseBodyEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketResponseBodyEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.http.response.body"}], ["body", "builtins.bytes"], ["more_body", "builtins.bool"]], "readonly_keys": [], "required_keys": ["body", "type"]}}}, "WebSocketResponseStartEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketResponseStartEvent", "name": "WebSocketResponseStartEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketResponseStartEvent", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketResponseStartEvent", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.http.response.start"}], ["status", "builtins.int"], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}]], "readonly_keys": [], "required_keys": ["headers", "status", "type"]}}}, "WebSocketScope": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types.WebSocketScope", "name": "WebSocketScope", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types.WebSocketScope", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types.WebSocketScope", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket"}], ["asgi", {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types.ASGIVersions"}], ["http_version", "builtins.str"], ["scheme", "builtins.str"], ["path", "builtins.str"], ["raw_path", "builtins.bytes"], ["query_string", "builtins.bytes"], ["root_path", "builtins.str"], ["headers", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["client", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["server", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["subprotocols", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], ["state", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["extensions", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["asgi", "client", "headers", "http_version", "path", "query_string", "raw_path", "root_path", "scheme", "server", "subprotocols", "type"]}}}, "WebSocketSendEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "uvicorn._types.WebSocketSendEvent", "line": 177, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types._WebSocketSendEventBytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "uvicorn._types._WebSocketSendEventText"}], "uses_pep604_syntax": false}}}, "_WebSocketReceiveEventBytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types._WebSocketReceiveEventBytes", "name": "_WebSocketReceiveEventBytes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types._WebSocketReceiveEventBytes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types._WebSocketReceiveEventBytes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.receive"}], ["bytes", "builtins.bytes"], ["text", {".class": "NoneType"}]], "readonly_keys": [], "required_keys": ["bytes", "type"]}}}, "_WebSocketReceiveEventText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types._WebSocketReceiveEventText", "name": "_WebSocketReceiveEventText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types._WebSocketReceiveEventText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types._WebSocketReceiveEventText", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.receive"}], ["bytes", {".class": "NoneType"}], ["text", "builtins.str"]], "readonly_keys": [], "required_keys": ["text", "type"]}}}, "_WebSocketSendEventBytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types._WebSocketSendEventBytes", "name": "_WebSocketSendEventBytes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types._WebSocketSendEventBytes", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types._WebSocketSendEventBytes", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.send"}], ["bytes", "builtins.bytes"], ["text", {".class": "NoneType"}]], "readonly_keys": [], "required_keys": ["bytes", "type"]}}}, "_WebSocketSendEventText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "uvicorn._types._WebSocketSendEventText", "name": "_WebSocketSendEventText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "uvicorn._types._WebSocketSendEventText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "uvicorn._types", "mro": ["uvicorn._types._WebSocketSendEventText", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "websocket.send"}], ["bytes", {".class": "NoneType"}], ["text", "builtins.str"]], "readonly_keys": [], "required_keys": ["text", "type"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "uvicorn._types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}}, "path": "C:\\veighna_studio\\Lib\\site-packages\\uvicorn\\_types.py"}