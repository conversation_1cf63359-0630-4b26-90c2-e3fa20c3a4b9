{"data_mtime": 1751955670, "dep_lines": [35, 31, 33, 34, 36, 41, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "sys", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "0ec4baabd3a4331958bd8047d2f486b534d40b14", "id": "uvicorn._types", "ignore_all": true, "interface_hash": "69e0ac5692ac688f660c23dfbc121ba5902ff4d2", "mtime": 1749831047, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\uvicorn\\_types.py", "plugin_data": null, "size": 7775, "suppressed": [], "version_id": "1.16.1"}