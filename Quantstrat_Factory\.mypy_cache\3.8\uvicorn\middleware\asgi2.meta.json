{"data_mtime": 1751955671, "dep_lines": [1, 1, 1, 1, 1], "dep_prios": [5, 5, 30, 30, 30], "dependencies": ["uvicorn._types", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "fd08b74fac2df83c0af986702cfcf5f7d084550c", "id": "uvicorn.middleware.asgi2", "ignore_all": true, "interface_hash": "883d9d63124a630a3e522099940563f8f5c8592c", "mtime": 1749831047, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": true, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": true, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": true, "disallow_untyped_defs": true, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": true, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": true, "warn_unused_ignores": true}, "path": "C:\\veighna_studio\\Lib\\site-packages\\uvicorn\\middleware\\asgi2.py", "plugin_data": null, "size": 394, "suppressed": [], "version_id": "1.16.1"}