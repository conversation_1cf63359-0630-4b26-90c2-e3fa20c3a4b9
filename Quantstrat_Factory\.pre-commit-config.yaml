# Pre-commit hooks configuration
# See https://pre-commit.com for more information

repos:
  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: debug-statements
      - id: name-tests-test
        args: ['--pytest-test-first']

  # Python code formatting
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3
        args: ['--line-length=79']

  # Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ['--profile', 'black', '--line-length=79']

  # Linting
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-docstrings
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
        args: ['--max-line-length=79']

  # Type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies:
          - types-requests
          - pandas-stubs
        args: ['--ignore-missing-imports']

  # Security checks
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-c', 'pyproject.toml']
        additional_dependencies: ['bandit[toml]']

  # Documentation
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: ['--convention=google']

  # Jupyter notebooks
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.0
    hooks:
      - id: nbqa-black
        args: ['--line-length=79']
      - id: nbqa-isort
        args: ['--profile=black']
      - id: nbqa-flake8
        args: ['--max-line-length=79']

  # YAML formatting
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0-alpha.9-for-vscode
    hooks:
      - id: prettier
        types: [yaml]

# Configuration for specific hooks
default_language_version:
  python: python3.8

# Exclude certain files/directories
exclude: |
  (?x)^(
      \.git/|
      \.venv/|
      build/|
      dist/|
      \.eggs/|
      .*\.egg-info/|
      __pycache__/|
      \.pytest_cache/|
      \.mypy_cache/|
      \.coverage|
      htmlcov/|
      \.tox/
  )$
