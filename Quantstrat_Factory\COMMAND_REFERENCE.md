# Quantstrat Factory 命令参考手册

## 📋 概述

本文档提供了 Quantstrat Factory 项目中所有可执行模块的命令行使用说明，包括数据清洗、特征生成、因子分析、回测等核心功能的运行命令和参数说明。

---

## 🗂️ 目录结构

```
Quantstrat_Factory/
├── src/
│   ├── data/auditor/          # 数据清洗模块
│   ├── data/profiler/         # 特征生成模块  
│   ├── research/factor_lab/   # 因子实验室
│   └── trading/backtester/    # 回测模块
├── scripts/                   # 项目管理脚本
└── examples/                  # 示例代码
```

---

## 🔧 1. 数据源管理 (Data Sources)

### 1.1 数据源连接测试

**脚本位置**: `src/data/sources/cli.py`

**用途**: 测试所有配置的数据源连接状态

**命令格式**:
```bash
python src/data/sources/cli.py test
```

**输出示例**:
```
🔗 测试数据源连接...
📊 连接结果:
  local: ✅ 成功
  wind: ❌ 失败
  tonghuashun: ✅ 成功
🎯 可用数据源: ['local', 'tonghuashun']
```

### 1.2 获取股票列表

**命令格式**:
```bash
python src/data/sources/cli.py list [选项]
```

**参数说明**:
- `--market`: 市场选择，可选值 all/sh/sz，默认 all
- `--source`: 指定数据源，默认使用优先级最高的可用数据源
- `--limit`: 显示数量限制，默认 20
- `--output`: 输出文件路径，支持 CSV 格式

**使用示例**:
```bash
# 获取所有股票
python src/data/sources/cli.py list

# 获取上海股票并保存到文件
python src/data/sources/cli.py list --market sh --output stocks_sh.csv

# 从指定数据源获取
python src/data/sources/cli.py list --source wind --limit 50
```

### 1.3 获取日线数据

**命令格式**:
```bash
python src/data/sources/cli.py daily <股票代码> <开始日期> <结束日期> [选项]
```

**参数说明**:
- `股票代码`: 股票代码，多个用逗号分隔，如 sh600000,sz000001
- `开始日期`: 开始日期，格式 YYYY-MM-DD
- `结束日期`: 结束日期，格式 YYYY-MM-DD
- `--source`: 指定数据源
- `--output`: 输出文件路径

**使用示例**:
```bash
# 获取单只股票日线数据
python src/data/sources/cli.py daily sh600000 2023-01-01 2023-01-31

# 获取多只股票并保存
python src/data/sources/cli.py daily sh600000,sz000001 2023-01-01 2023-01-31 --output daily_data.csv

# 从指定数据源获取
python src/data/sources/cli.py daily sh600000 2023-01-01 2023-01-31 --source local
```

### 1.4 获取分钟数据

**命令格式**:
```bash
python src/data/sources/cli.py minute <股票代码> <开始日期> <结束日期> [选项]
```

**参数说明**:
- `股票代码`: 股票代码，多个用逗号分隔
- `开始日期`: 开始日期，格式 YYYY-MM-DD
- `结束日期`: 结束日期，格式 YYYY-MM-DD
- `--frequency`: 频率，可选值 1min/5min/15min/30min/60min，默认 1min
- `--source`: 指定数据源
- `--output`: 输出文件路径

**使用示例**:
```bash
# 获取1分钟数据
python src/data/sources/cli.py minute sh600000 2023-01-01 2023-01-01

# 获取5分钟数据并保存
python src/data/sources/cli.py minute sh600000 2023-01-01 2023-01-01 --frequency 5min --output minute_data.csv
```

### 1.5 查看数据源配置

**命令格式**:
```bash
python src/data/sources/cli.py config
```

**用途**: 显示当前数据源配置信息

## 🔧 2. 数据清洗模块 (Data Auditor)

### 2.1 原始数据清洗 + 日K生成

**脚本位置**: `src/data/auditor/run_auditor.py`

**用途**: 处理新获取的原始分钟数据，一次性完成数据清洗和日K数据生成

**命令格式**:
```bash
python src/data/auditor/run_auditor.py --start-date <开始日期> --end-date <结束日期>
```

**参数说明**:
- `--start-date`: **必需**，开始日期，格式 YYYY-MM-DD
- `--end-date`: **必需**，结束日期，格式 YYYY-MM-DD

**示例**:
```bash
# 处理2023年1月1日的原始分钟数据
python src/data/auditor/run_auditor.py --start-date 2023-01-01 --end-date 2023-01-01

# 处理2023年全年数据
python src/data/auditor/run_auditor.py --start-date 2023-01-01 --end-date 2023-12-31
```

### 1.2 从已清洗分钟数据生成日K

**脚本位置**: `src/data/auditor/generate_day_from_cleaned_min.py`

**用途**: 从已清洗的分钟数据生成日K数据，避免重复清洗

**命令格式**:
```bash
python src/data/auditor/generate_day_from_cleaned_min.py --start-date <开始日期> --end-date <结束日期>
```

**参数说明**:
- `--start-date`: **必需**，开始日期，格式 YYYY-MM-DD
- `--end-date`: **必需**，结束日期，格式 YYYY-MM-DD

**示例**:
```bash
# 为2022年全年已清洗的分钟数据生成日K数据
python src/data/auditor/generate_day_from_cleaned_min.py --start-date 2022-01-01 --end-date 2022-12-31
```

---

## 📊 2. 特征生成模块 (Feature Profiler)

### 2.1 分钟级特征计算

**脚本位置**: `src/data/profiler/run_profiler.py`

**用途**: 计算分钟级别的量化特征

**命令格式**:
```bash
python src/data/profiler/run_profiler.py --level min --feature-sets <特征集列表> [可选参数]
```

**参数说明**:
- `--level`: **必需**，计算级别，选择 `min` 或 `day`
- `--feature-sets`: **必需**，特征集列表，空格分隔
- `--start-date`: 可选，开始日期 (YYYY-MM-DD)
- `--end-date`: 可选，结束日期 (YYYY-MM-DD)
- `--symbols`: 可选，指定股票代码列表
- `--symbol-file`: 可选，从文件加载股票列表
- `--processes`: 可选，并行进程数，默认为CPU核心数
- `--verbose`: 可选，启用详细日志输出

**示例**:
```bash
# 计算所有股票的分钟级alpha特征
python src/data/profiler/run_profiler.py --level min --feature-sets alpha

# 计算指定股票的多种特征，使用4个进程
python src/data/profiler/run_profiler.py --level min --feature-sets alpha event risk --symbols 000001.SZ 000002.SZ --processes 4

# 从文件加载股票列表并计算特征
python src/data/profiler/run_profiler.py --level min --feature-sets alpha --symbol-file stock_list.txt --verbose
```

### 2.2 日级特征计算

**命令格式**:
```bash
python src/data/profiler/run_profiler.py --level day --feature-sets <特征集列表> [可选参数]
```

**可用特征集**:
- `daily_basics`: 基础日线数据
- `technical`: 技术指标 (MA, RSI等)
- `day_from_min_aggregation`: 从分钟数据聚合的日级特征
- `all`: 计算所有可用的日级特征

**示例**:
```bash
# 计算所有日级特征
python src/data/profiler/run_profiler.py --level day --feature-sets all

# 只计算技术指标
python src/data/profiler/run_profiler.py --level day --feature-sets technical --start-date 2023-01-01 --end-date 2023-12-31

# 增量计算（自动确定开始日期）
python src/data/profiler/run_profiler.py --level day --feature-sets daily_basics technical
```

---

## 🧪 3. 因子实验室 (Factor Lab)

### 3.1 Web应用启动

**脚本位置**: `src/research/factor_lab/web_app/app.py`

**用途**: 启动因子分析的Web界面

**命令格式**:
```bash
python src/research/factor_lab/web_app/app.py
```

**访问地址**: http://localhost:8051

**功能**:
- 因子有效性分析
- 因子组合构建
- 回测结果可视化
- 因子筛选和排序

### 3.2 因子分析工具

**脚本位置**: `src/features/factors/run_factor_analysis.py`

**用途**: 命令行因子计算和分析工具

**命令格式**:
```bash
python src/features/factors/run_factor_analysis.py [参数]
```

**主要参数**:
- `--symbols`: 股票代码列表，如 `000001.SZ 600000.SH`
- `--start-date`: 开始日期，格式 YYYY-MM-DD
- `--end-date`: 结束日期，格式 YYYY-MM-DD
- `--factor-types`: 因子类型，可选 `technical`, `sentiment`, `fundamental`
- `--output-dir`: 输出目录，默认为 `output/`
- `--verbose`: 启用详细输出

**示例**:
```bash
# 分析技术因子
python src/features/factors/run_factor_analysis.py --symbols 000001.SZ 600000.SH --factor-types technical

# 分析多种因子类型
python src/features/factors/run_factor_analysis.py --symbols 000001.SZ 000002.SZ --factor-types technical sentiment --start-date 2023-01-01 --end-date 2023-12-31

# 详细输出模式
python src/features/factors/run_factor_analysis.py --symbols 600000.SH --factor-types technical --verbose
```

**输出内容**:
- IC分析结果
- 因子衰减分析
- 因子换手率分析
- 因子暴露度分析
- 分析报告文件 (PKL格式)

---

## 🔄 4. 回测模块 (Backtester)

### 4.1 统一回测入口

**脚本位置**: `src/trading/backtester/run.py`

**用途**: 统一的策略回测、参数调优和信号生成入口

**命令格式**:
```bash
python src/trading/backtester/run.py --mode <运行模式>
```

**参数说明**:
- `--mode`: **必需**，运行模式
  - `backtest`: 执行单次回测
  - `sweep`: 执行参数调优
  - `signal`: 生成模拟盘交易信号

**示例**:
```bash
# 执行单次回测
python src/trading/backtester/run.py --mode backtest

# 执行参数调优
python src/trading/backtester/run.py --mode sweep

# 生成模拟盘信号
python src/trading/backtester/run.py --mode signal
```

### 4.2 实盘/回测控制器

**脚本位置**: `src/trading/backtester/controller/main_controller.py`

**用途**: 支持回测模式与实盘模式切换

**命令格式**:
```bash
python src/trading/backtester/controller/main_controller.py --mode <模式>
```

**参数说明**:
- `--mode`: 运行模式，`backtest` 或 `live`

**示例**:
```bash
# 启动回测模式
python src/trading/backtester/controller/main_controller.py --mode backtest

# 启动实盘模式
python src/trading/backtester/controller/main_controller.py --mode live
```

---

## 🛠️ 5. 项目管理脚本

### 5.1 项目结构验证

**脚本位置**: `scripts/verify_structure.py`

**用途**: 验证项目目录结构和配置文件

**命令格式**:
```bash
python scripts/verify_structure.py
```

### 5.2 代码质量检查

**脚本位置**: `scripts/check_code_quality.py`

**用途**: 运行代码质量检查工具

**命令格式**:
```bash
python scripts/check_code_quality.py [可选参数]
```

**参数说明**:
- `--fix`: 自动修复可修复的问题
- `--skip-tests`: 跳过测试运行
- `--skip-security`: 跳过安全检查
- `--output <文件>`: 将报告保存到文件

**示例**:
```bash
# 运行完整的代码质量检查
python scripts/check_code_quality.py

# 自动修复问题并保存报告
python scripts/check_code_quality.py --fix --output quality_report.txt
```

---

## 📚 6. 示例和演示

### 6.1 核心功能演示

**脚本位置**: `examples/demo_core_functions.py`

**用途**: 演示项目核心功能的使用

**命令格式**:
```bash
python examples/demo_core_functions.py
```

---

## ⚙️ 7. 配置说明

### 7.1 主配置文件

**文件位置**: `config/app.yaml`

**说明**: 包含数据路径、数据库连接等核心配置

### 7.2 环境配置

**目录位置**: `config/environments/`

**说明**: 不同环境（开发、测试、生产）的配置文件

---

## 📝 8. 使用建议

### 8.1 典型工作流程

1. **数据准备**:
   ```bash
   # 清洗原始数据
   python src/data/auditor/run_auditor.py --start-date 2023-01-01 --end-date 2023-12-31
   ```

2. **特征生成**:
   ```bash
   # 生成分钟级特征
   python src/data/profiler/run_profiler.py --level min --feature-sets alpha event risk
   
   # 生成日级特征
   python src/data/profiler/run_profiler.py --level day --feature-sets all
   ```

3. **因子分析**:
   ```bash
   # 启动Web界面进行因子分析
   python src/research/factor_lab/web_app/app.py
   ```

4. **策略回测**:
   ```bash
   # 执行回测
   python src/trading/backtester/run.py --mode backtest
   ```

### 8.2 注意事项

- 确保配置文件 `config/app.yaml` 中的路径设置正确
- 分钟级特征计算可能耗时较长，建议使用多进程并行计算
- 日级特征支持增量计算，可以不指定日期范围自动计算
- Web应用默认运行在8051端口，确保端口未被占用

---

## 🧩 9. 高级功能

### 9.1 优化器示例

**脚本位置**: `examples/optimizer_example.py`

**用途**: 演示参数优化功能

**命令格式**:
```bash
python examples/optimizer_example.py
```

### 9.2 监控示例

**脚本位置**: `examples/monitoring_example.py`

**用途**: 演示系统监控功能

**命令格式**:
```bash
python examples/monitoring_example.py
```

### 9.3 事件驱动示例

**脚本位置**: `examples/event_driven_example.py`

**用途**: 演示事件驱动架构

**命令格式**:
```bash
python examples/event_driven_example.py
```

### 9.4 插件架构示例

**脚本位置**: `examples/plugin_architecture_example.py`

**用途**: 演示插件系统的使用

**命令格式**:
```bash
python examples/plugin_architecture_example.py
```

---

## 🐳 10. Docker 部署

### 10.1 构建镜像

**命令格式**:
```bash
# 构建Docker镜像
docker build -t quantstrat-factory .

# 使用docker-compose启动
docker-compose up -d
```

### 10.2 容器运行

**命令格式**:
```bash
# 运行容器
docker run -d -p 8051:8051 -v /path/to/data:/app/data quantstrat-factory

# 进入容器
docker exec -it <container_id> bash
```

---

## 🧪 11. 测试命令

### 11.1 运行单元测试

**命令格式**:
```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试文件
python -m pytest tests/test_core_functions.py -v

# 运行特定测试类
python -m pytest tests/test_signal_generator.py::TestSignalGenerator -v
```

### 11.2 运行集成测试

**命令格式**:
```bash
# 运行集成测试
python -m pytest tests/integration/ -v

# 运行因子实验室集成测试
python -m pytest tests/test_factor_lab_integration.py -v
```

### 11.3 性能测试

**命令格式**:
```bash
# 运行性能测试
python -m pytest tests/performance/ -v --benchmark-only
```

---

## 📊 12. 数据检查工具

### 12.1 Parquet文件检查

**脚本位置**: `src/data/profiler/check_parquet.py`

**用途**: 检查Parquet文件的完整性和结构

**命令格式**:
```bash
python src/data/profiler/check_parquet.py --file <文件路径> [可选参数]
```

**参数说明**:
- `--file`: **必需**，Parquet文件路径
- `--verbose`: 可选，显示详细信息
- `--schema`: 可选，显示文件schema

**示例**:
```bash
# 检查特定Parquet文件
python src/data/profiler/check_parquet.py --file data/features/alpha/000001.SZ.parquet --verbose

# 检查文件schema
python src/data/profiler/check_parquet.py --file data/features/alpha/000001.SZ.parquet --schema
```

---

## 🔧 13. 维护工具

### 13.1 清理旧结构

**脚本位置**: `scripts/cleanup_old_structure.ps1` (Windows PowerShell)

**用途**: 清理项目重构前的旧目录结构

**命令格式**:
```powershell
# Windows PowerShell
.\scripts\cleanup_old_structure.ps1
```

### 13.2 烟雾测试

**脚本位置**: `scripts/smoke_tests.py`

**用途**: 快速验证系统基本功能

**命令格式**:
```bash
python scripts/smoke_tests.py [可选参数]
```

**参数说明**:
- `--environment`: 目标环境，选择 `local`、`staging` 或 `production`，默认 `local`
- `--base-url`: 应用基础URL（覆盖环境默认值）
- `--timeout`: 请求超时时间（秒），默认30秒

**示例**:
```bash
# 运行本地环境烟雾测试
python scripts/smoke_tests.py --environment local

# 运行生产环境测试，自定义超时时间
python scripts/smoke_tests.py --environment production --timeout 60

# 使用自定义URL
python scripts/smoke_tests.py --base-url http://localhost:8051 --timeout 30
```

---

## 🔍 16. 监控和性能工具

### 16.1 系统监控

**脚本位置**: `src/infrastructure/monitoring/metrics.py`

**用途**: 系统性能指标收集和监控

**命令格式**:
```bash
python src/infrastructure/monitoring/metrics.py
```

**功能**:
- 自动收集CPU、内存、磁盘使用率
- 记录自定义业务指标
- 性能追踪和计时
- 指标数据持久化

### 16.2 告警系统

**脚本位置**: `src/infrastructure/monitoring/alerts.py`

**用途**: 系统告警和通知管理

**命令格式**:
```bash
python src/infrastructure/monitoring/alerts.py
```

**功能**:
- 基于阈值的自动告警
- 多渠道通知（控制台、文件、Slack等）
- 告警规则配置
- 告警统计和历史记录

### 16.3 并行计算性能测试

**脚本位置**: `src/infrastructure/utils/parallel_computing.py`

**用途**: 并行计算性能基准测试

**命令格式**:
```bash
python src/infrastructure/utils/parallel_computing.py
```

**功能**:
- 多进程/多线程性能对比
- 因子计算并行化测试
- IC计算并行优化
- 性能基准报告

### 16.4 通知系统

**脚本位置**: `src/infrastructure/monitoring/notification_system.py`

**用途**: 统一的通知和消息推送系统

**使用方式**:
```python
# 在Python代码中使用
from src.infrastructure.monitoring.notification_system import create_notification_system

# 创建通知系统
notification_system = create_notification_system()

# 发送通知
notification_system.send_notification(
    message="数据处理完成",
    level="info",
    tags={"module": "data_auditor"}
)
```

**功能**:
- 多渠道通知（控制台、文件、邮件、Slack等）
- 通知级别过滤
- 通知规则配置
- 通知历史记录

---

## � 17. API服务

### 17.1 FastAPI服务启动

**脚本位置**: `src/core_platform/api/main.py`

**用途**: 启动量化策略工厂的API服务

**命令格式**:
```bash
python src/core_platform/api/main.py
```

**访问地址**:
- API服务: http://localhost:8000
- API文档: http://localhost:8000/docs
- ReDoc文档: http://localhost:8000/redoc

**功能**:
- 系统健康检查
- 数据处理任务提交
- 因子计算请求
- 回测任务管理

**使用uvicorn启动**:
```bash
# 开发模式
uvicorn src.core_platform.api.main:app --reload --host 0.0.0.0 --port 8000

# 生产模式
uvicorn src.core_platform.api.main:app --host 0.0.0.0 --port 8000 --workers 4
```

---

## 🚀 18. 部署脚本

### 18.1 自动化部署

**脚本位置**: `scripts/deploy.sh` (Linux/macOS)

**用途**: 自动化部署脚本，支持多环境部署

**命令格式**:
```bash
./scripts/deploy.sh [环境名称] [版本号]
```

**参数说明**:
- 环境名称: `development`、`staging`、`production`
- 版本号: 可选，默认使用当前Git提交哈希

**示例**:
```bash
# 部署到开发环境
./scripts/deploy.sh development

# 部署到生产环境，指定版本
./scripts/deploy.sh production v1.2.0
```

**功能**:
- 代码质量检查
- Docker镜像构建
- 容器部署
- 健康检查
- 通知发送

### 18.2 VeighNa实盘启动

**脚本位置**: `src/trading/backtester/scripts/run_strategy_veighna.bat` (Windows)

**用途**: 启动VeighNa实盘交易策略

**命令格式**:
```batch
# Windows命令行
.\src\trading\backtester\scripts\run_strategy_veighna.bat
```

**功能**:
- 连接VeighNa交易网关
- 启动实盘策略执行
- 实时信号处理

---

## �🏭 19. 主工厂类使用

### 19.1 统一工厂接口

**使用方式**:
```python
# 导入主工厂
from src import QuantstratFactory

# 创建工厂实例
factory = QuantstratFactory()

# 使用核心功能
factory.run_data_auditor()      # 数据清洗
factory.run_feature_profiler()  # 特征生成
factory.run_factor_lab()        # 因子工厂
```

**配置选项**:
```python
# 指定配置文件和环境
factory = QuantstratFactory(
    config_path="config/custom.yaml",
    environment="production"
)

# 获取系统状态
status = factory.get_status()
print(status)
```

---

## 📋 20. 常用命令组合

### 20.1 完整数据处理流程

```bash
# 1. 数据清洗（处理一周的数据）
python src/data/auditor/run_auditor.py --start-date 2023-12-25 --end-date 2023-12-31

# 2. 生成分钟级特征（使用8个进程）
python src/data/profiler/run_profiler.py --level min --feature-sets alpha event risk --processes 8 --verbose

# 3. 生成日级特征（增量计算）
python src/data/profiler/run_profiler.py --level day --feature-sets all

# 4. 启动因子分析界面
python src/research/factor_lab/web_app/app.py
```

### 20.2 快速验证流程

```bash
# 1. 验证项目结构
python scripts/verify_structure.py

# 2. 运行代码质量检查
python scripts/check_code_quality.py --skip-tests

# 3. 运行核心功能演示
python examples/demo_core_functions.py

# 4. 运行烟雾测试
python scripts/smoke_tests.py
```

### 20.3 开发调试流程

```bash
# 1. 运行单元测试
python -m pytest tests/unit/ -v

# 2. 检查代码格式
python scripts/check_code_quality.py --fix

# 3. 运行特定模块测试
python -m pytest tests/test_core_functions.py -v

# 4. 启动监控示例
python examples/monitoring_example.py
```

---

## ⚠️ 21. 故障排除

### 21.1 常见问题

**问题**: 找不到配置文件
```bash
# 解决方案：检查配置文件是否存在
ls config/app.yaml
```

**问题**: 数据路径不存在
```bash
# 解决方案：创建必要的目录
mkdir -p data/raw data/cleaned data/features
```

**问题**: 端口被占用
```bash
# 解决方案：检查端口使用情况
netstat -an | grep 8051
# 或使用其他端口启动应用
```

### 21.2 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看监控日志
tail -f logs/monitoring_report.json

# 查看简单监控日志
tail -f logs/simple_monitoring.log
```

---

## � 22. 最佳实践和使用建议

### 22.1 新项目启动流程

**第一次使用项目时的推荐步骤**:

```bash
# 1. 验证项目结构
python scripts/verify_structure.py

# 2. 检查代码质量（跳过测试以节省时间）
python scripts/check_code_quality.py --skip-tests

# 3. 运行核心功能演示，了解项目能力
python examples/demo_core_functions.py

# 4. 启动API服务，查看接口文档
python src/core_platform/api/main.py
# 然后访问 http://localhost:8000/docs
```

### 22.2 日常开发工作流

**推荐的日常开发流程**:

```bash
# 1. 每日数据更新（增量处理）
python src/data/auditor/run_auditor.py --start-date $(date -d "yesterday" +%Y-%m-%d) --end-date $(date +%Y-%m-%d)

# 2. 特征更新（自动增量计算）
python src/data/profiler/run_profiler.py --level day --feature-sets all

# 3. 因子分析（启动Web界面）
python src/research/factor_lab/web_app/app.py &

# 4. 代码质量检查（提交前）
python scripts/check_code_quality.py --fix
```

### 22.3 性能优化建议

**大数据量处理优化**:

```bash
# 1. 分钟级特征计算 - 使用多进程并行
python src/data/profiler/run_profiler.py --level min --feature-sets alpha --processes 16 --verbose

# 2. 分批处理股票列表
# 创建股票列表文件，每次处理100只股票
python src/data/profiler/run_profiler.py --level min --feature-sets alpha --symbol-file batch_100_stocks.txt

# 3. 监控系统资源使用
python src/infrastructure/monitoring/metrics.py &
python src/infrastructure/utils/parallel_computing.py  # 性能基准测试
```

### 22.4 生产环境部署

**生产环境推荐配置**:

```bash
# 1. 使用Docker部署
docker build -t quantstrat-factory:latest .
docker run -d -p 8000:8000 -p 8051:8051 \
  -v /data:/app/data \
  -v /logs:/app/logs \
  quantstrat-factory:latest

# 2. 使用自动化部署脚本
./scripts/deploy.sh production v1.0.0

# 3. 启动监控和告警
python src/infrastructure/monitoring/alerts.py &
python src/infrastructure/monitoring/notification_system.py &
```

### 22.5 故障排查流程

**遇到问题时的排查步骤**:

```bash
# 1. 运行系统诊断
python scripts/smoke_tests.py --environment local

# 2. 检查日志文件
tail -f logs/app.log
tail -f logs/monitoring_report.json

# 3. 验证数据完整性
python src/data/profiler/check_parquet.py --file data/features/alpha/000001.SZ.parquet --verbose

# 4. 重新运行核心功能测试
python examples/demo_core_functions.py
```

### 22.6 数据管理建议

**数据存储和管理最佳实践**:

- **数据备份**: 定期备份重要的特征数据和配置文件
- **增量更新**: 优先使用增量计算，避免重复处理历史数据
- **存储优化**: 使用Parquet格式存储大量数据，提高读取性能
- **监控磁盘**: 定期检查磁盘使用情况，及时清理临时文件

```bash
# 数据备份示例
tar -czf backup_$(date +%Y%m%d).tar.gz data/features/ config/

# 清理临时文件
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} +
```

---

## �🔄 更新日志

- **v1.0.0**: 初始版本，包含所有核心模块的命令说明
- **v1.1.0**: 添加高级功能、测试命令、维护工具等详细说明
- **v1.2.0**: 添加API服务、部署脚本、最佳实践等完整使用指南
- 本文档将随项目更新持续维护

---

*最后更新: 2024-12-30*
