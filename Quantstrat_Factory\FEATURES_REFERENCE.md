# 因子实验室 (Factor Lab)

## 1. 项目简介

因子实验室是一个功能强大的交互式 Web 应用，旨在帮助量化研究人员对股票因子进行快速验证和深入分析。它将宏观的市场模式识别与微观的因子有效性评估相结合，提供了一个从数据加载、因子计算、复合因子构建、因子评估到可视化分析的完整工作流。

## 2. 安装与运行

### 2.1 环境准备

确保您的 Python 环境已安装以下依赖：
- `dash`
- `dash-bootstrap-components`
- `pandas`
- `numpy`
- `plotly`
- `openpyxl` (用于读取 Excel 文件，如果数据源是 Excel)
- `pyarrow` (用于读取 Parquet 文件)

您可以使用 pip 安装这些依赖：
```bash
pip install dash dash-bootstrap-components pandas numpy plotly openpyxl pyarrow
```

### 2.2 数据准备

1.  **日线数据**: 确保在 `D:/PY/Data/cleaned/daily/daily_basics.parquet` 文件中存放了股票的日线数据。文件应包含 `datetime`, `symbol`, `open`, `high`, `low`, `close`, `volume` 等列。
2.  **股票列表**: 确保在 `D:/PY/stock_list.txt` 文件中包含了所有需要分析的股票代码，每行一个。
3.  **特征库**: 如果您使用文件型因子，请确保在 `D:/PY/Data/features/` 目录下有相应的因子数据文件（.parquet格式）。
4.  **配置文件**: 确保项目根目录下的配置文件存在，并且其中配置了正确的数据路径。

### 2.3 运行应用

在命令行中，导航到项目根目录，然后运行 Factor Lab：
```bash
cd D:/PY/Quantstrat_Factory/
python -m src.research.factor_lab.web_app.app
```
应用启动后，您可以在浏览器中访问 `http://127.0.0.1:8051/`。

## 3. 核心功能使用说明

### 3.1 控制面板

左侧是控制面板，用于配置分析参数。

*   **日期范围**: 选择分析数据的起始和结束日期。默认日期范围为 `2020-10-09` 至 `2020-10-31`。
*   **股票池**:
    *   **静态股票池**: 选择预定义的股票池，如“沪深300”、“中证500”或“全市场”。
    *   **动态筛选池**: 选择此选项后，点击下方的“配置动态池”按钮，可以打开动态股票池筛选器，根据自定义的量价模式生成股票池。
*   **选择因子**: 选择一个或多个因子进行分析。因子分为“计算型”（如 MOM, VOL）和“文件型”（从特征库加载）。
*   **自定义复合因子**: 在文本框中输入自定义的复合因子表达式，每行一个。例如：`COMP_A = MOM + VOL`。
*   **因子评估设置**:
    *   **启用因子评估**: 勾选此项以启用因子IC计算和分层回测。
    *   **IC计算周期 (天)**: 设置计算因子IC时未来收益率的周期。
    *   **分层数量**: 设置分层回测时将股票分为多少个分层。
*   **叠加股票代码**: 在“全市场”或“动态筛选池”模式下，您可以在图表中叠加显示特定股票的因子时序图，以便进行对比分析。
*   **开始分析按钮**: 配置完成后，点击此按钮开始执行因子分析。

### 3.2 动态股票池筛选器 (高级筛选)

点击“配置动态池”按钮将打开一个模态框，您可以在其中定义复杂的量价模式来筛选股票。

*   **日期范围**: 为模式筛选指定日期范围。
*   **分段 (Segment)**: 一个模式可以由多个连续的分段组成。
    *   点击“增加分段”按钮可以添加新的分段。
    *   每个分段可以设置其**天数**。
    *   每个分段内可以定义多条**规则**。
    *   **规则**: 每条规则包含：
        *   **指标**: 如“涨跌幅 (pct_chg)”、“成交量比率 (volume_ratio)”、“Aroon Up (aroon_up)”、“Aroon Down (aroon_down)”。
        *   **操作符**: 如 `>, <, >=, <=, ==, !=`。
        *   **值**: 规则的阈值。
        *   点击“增加规则”按钮可以添加新的规则。
        *   点击“删除”按钮可以删除对应的规则。
    *   **规则逻辑**: 选择分段内多条规则之间的逻辑关系（“AND”或“OR”）。
    *   点击“删除分段”按钮可以删除整个分段。
*   **生成股票池**: 配置完模式后，点击此按钮将执行筛选，并将符合模式的股票列表存储为“动态筛选池”，供主界面的因子分析使用。

### 3.3 右侧内容区

右侧区域显示分析结果。

*   **因子评估报告**: 如果启用了因子评估，这里将显示每个因子的 IC 均值、标准差、ICIR，以及分层回测的净值曲线和性能统计表（年化收益率、年化波动率、夏普比率）。
*   **可视化分析**: 显示因子时序图。在“全市场”或“动态筛选池”模式下，默认显示因子均值时序图，并支持叠加个股。
*   **详细数据**: 显示原始的分析数据表格。

## 4. 未来展望

*   **扩展因子知识库**: 将 `FACTOR_DEFINITIONS` 外部化到配置文件，方便用户自定义和扩充。
*   **更多评估指标**: 增加最大回撤、胜率、赔率等更丰富的分层回测指标。
*   **性能优化**: 针对大数据量下的加载和计算性能进行优化。
*   **更多可视化类型**: 增加因子分布图、IC时序图等。
*   **分钟级数据分析**: 扩展平台支持分钟级因子和评估。
- **应用场景**：开盘异动识别、流动性分析

#### **vol_pct_mid** - 午间成交量占比
- **用途**：衡量午间交易活跃度
- **计算公式**：午间(13:00-14:00)成交量 / 全天总成交量
- **参考范围**：0.15 ~ 0.25
- **参考参数**：午间时段=13:00-14:00
- **应用场景**：午间策略、成交量模式识别

#### **vol_pct_close30m** - 收盘30分钟成交量占比
- **用途**：衡量收盘阶段交易集中度
- **计算公式**：收盘30分钟(14:30-15:00)成交量 / 全天总成交量
- **参考范围**：0.10 ~ 0.25
- **参考参数**：收盘时段=14:30-15:00
- **应用场景**：收盘效应、尾盘策略

#### **vol_center_of_gravity** - 成交量重心
- **用途**：衡量成交量在时间上的分布重心
- **计算公式**：Σ(成交量 × 分钟序号) / 总成交量
- **参考范围**：60 ~ 180 (分钟序号)
- **应用场景**：交易时机选择、成交量模式分析

#### **vol_std_pct** - 成交量标准差占比
- **用途**：衡量成交量分布的离散程度
- **计算公式**：分钟成交量占比的标准差
- **参考范围**：0.001 ~ 0.010
- **应用场景**：波动性分析、异常检测

#### **volume_cluster_score** - 成交量聚集度
- **用途**：衡量大成交量的聚集程度
- **计算公式**：大成交量分钟总量 / 全天总成交量
- **参考范围**：0.10 ~ 0.30
- **参考参数**：大成交量阈值=平均值×3
- **应用场景**：主力资金识别、异动分析

### 2. 价格收益特征

#### **return_open_to_10am** - 开盘到10点收益率
- **用途**：衡量开盘阶段价格表现
- **计算公式**：(10点价格 - 开盘价) / 开盘价
- **参考范围**：-0.02 ~ 0.02
- **应用场景**：开盘策略、早盘动量

#### **return_last_hour** - 最后一小时收益率
- **用途**：衡量尾盘价格表现
- **计算公式**：最后一小时的价格变化率
- **参考范围**：-0.01 ~ 0.01
- **应用场景**：尾盘策略、收盘效应

#### **return_intraday_skew** - 日内收益偏度
- **用途**：衡量日内收益分布的偏斜程度
- **计算公式**：分钟收益率序列的偏度
- **参考范围**：-2.0 ~ 2.0
- **应用场景**：风险管理、收益分布分析

#### **close_to_vwap_pct_diff** - 收盘价与VWAP差异
- **用途**：衡量收盘价相对成交量加权平均价的偏离
- **计算公式**：(收盘价 - VWAP) / VWAP
- **参考范围**：-0.02 ~ 0.02
- **应用场景**：VWAP策略、价格效率分析

### 3. 波动率特征

#### **volatility_am** - 上午波动率
- **用途**：衡量上午时段的价格波动程度
- **计算公式**：上午对数收益率的标准差
- **参考范围**：0.001 ~ 0.005
- **参考参数**：上午时段=09:30-11:30
- **应用场景**：波动率策略、风险控制

#### **volatility_pm** - 下午波动率
- **用途**：衡量下午时段的价格波动程度
- **计算公式**：下午对数收益率的标准差
- **参考范围**：0.001 ~ 0.005
- **参考参数**：下午时段=13:00-15:00
- **应用场景**：时段波动率比较、风险管理

#### **amplitude_total** - 总振幅
- **用途**：衡量全天价格波动幅度
- **计算公式**：(最高价 - 最低价) / 开盘价
- **参考范围**：0.01 ~ 0.10
- **应用场景**：波动率预测、止损设置

### 4. 位置相关特征

#### **upper_tail_ratio** - 上影线比例
- **用途**：衡量上影线长度占总振幅的比例
- **计算公式**：(最高价 - max(开盘价,收盘价)) / (最高价 - 最低价)
- **参考范围**：0.0 ~ 1.0
- **应用场景**：K线形态分析、反转信号识别

#### **lower_tail_ratio** - 下影线比例
- **用途**：衡量下影线长度占总振幅的比例
- **计算公式**：(min(开盘价,收盘价) - 最低价) / (最高价 - 最低价)
- **参考范围**：0.0 ~ 1.0
- **应用场景**：支撑位识别、买入信号

#### **intraday_range_position** - 日内区间位置
- **用途**：衡量收盘价在日内价格区间中的相对位置
- **计算公式**：(收盘价 - 最低价) / (最高价 - 最低价)
- **参考范围**：0.0 ~ 1.0，>0.8偏上方，<0.2偏下方
- **应用场景**：强弱判断、趋势确认

#### **close_in_upper_quantile** - 收盘价上分位数标志
- **用途**：判断收盘价是否在价格分布的上分位数
- **计算公式**：收盘价是否 > 75%分位数价格
- **参考范围**：0 或 1 (布尔值)
- **应用场景**：强势股筛选、突破确认

#### **high_time_density** - 最高价时间密度
- **用途**：衡量最高价出现时间在全天的相对位置
- **计算公式**：最高价出现分钟序号 / 总交易分钟数
- **参考范围**：0.0 ~ 1.0
- **应用场景**：高点时机分析、卖出策略

### 5. 趋势相关特征

#### **price_volume_corr** - 价量相关性
- **用途**：衡量价格与成交量的相关程度
- **计算公式**：价格序列与成交量序列的皮尔逊相关系数
- **参考范围**：-1.0 ~ 1.0，>0.5正相关，<-0.5负相关
- **应用场景**：量价配合分析、趋势确认

#### **price_trend_consistency** - 价格趋势一致性
- **用途**：衡量价格变化方向的一致性程度
- **计算公式**：同向变化次数 / 总变化次数
- **参考范围**：0.0 ~ 1.0，>0.6趋势明确
- **应用场景**：趋势强度判断、策略选择

#### **volume_trend_consistency** - 成交量趋势一致性
- **用途**：衡量成交量变化方向的一致性程度
- **计算公式**：成交量同向变化次数 / 总变化次数
- **参考范围**：0.0 ~ 1.0，>0.6趋势明确
- **应用场景**：成交量趋势分析、资金流向

#### **pullback_amplitude** - 回撤幅度
- **用途**：衡量从高点回撤的最大幅度
- **计算公式**：最大回撤 / 最高价
- **参考范围**：0.0 ~ 1.0
- **应用场景**：风险控制、止损设置

---

## 📊 特征使用指南

### 🎯 策略应用建议

#### 1. 趋势跟踪策略
- **主要特征**：MA_5, MA_10, MA_20, aroon_up, aroon_down
- **辅助特征**：price_trend_consistency, volume_trend_consistency
- **应用场景**：中长期趋势跟踪

#### 2. 反转策略
- **主要特征**：RSI_14, upper_tail_ratio, lower_tail_ratio
- **辅助特征**：return_intraday_skew, close_in_upper_quantile
- **应用场景**：超买超卖反转

#### 3. 动量策略
- **主要特征**：pct_chg, return_open_to_10am, return_last_hour
- **辅助特征**：volume_ratio, vol_pct_open30m
- **应用场景**：短期动量捕捉

#### 4. 成交量策略
- **主要特征**：volume_ratio, volume_cluster_score, price_volume_corr
- **辅助特征**：vol_center_of_gravity, vol_std_pct
- **应用场景**：资金流向分析

#### 5. 波动率策略
- **主要特征**：volatility_am, volatility_pm, amplitude_total
- **辅助特征**：vol_std_pct, pullback_amplitude
- **应用场景**：波动率交易、风险管理

### 📈 特征组合建议

#### 强势股筛选组合
```
MA_5 > MA_10 > MA_20 AND
RSI_14 > 50 AND
close_in_upper_quantile = 1 AND
price_volume_corr > 0.3
```

#### 反转机会识别组合
```
RSI_14 < 30 AND
lower_tail_ratio > 0.3 AND
intraday_range_position < 0.3 AND
volume_ratio > 1.5
```

#### 突破确认组合
```
pct_chg > 0.03 AND
volume_ratio > 2.0 AND
vol_pct_open30m > 0.25 AND
upper_tail_ratio < 0.2
```

---

## 🔧 技术说明

### 数据更新频率
- **日级特征**：每日收盘后更新
- **分钟级特征**：每日收盘后聚合计算

### 数据存储格式
- **文件格式**：Parquet (高效列式存储)
- **数据类型**：Float64 (双精度浮点数)
- **缺失值处理**：NaN 表示无效或缺失数据

### 计算性能
- **日级特征**：约 1000 只股票/秒
- **分钟级特征**：约 100 只股票/秒
- **存储空间**：约 1MB/股票/年

---

## 📝 版本信息

- **文档版本**：v1.0
- **最后更新**：2025-01-04
- **适用版本**：Quantstrat Factory v1.0+

---

## 🔗 相关文档

- [命令参考手册](COMMAND_REFERENCE.md)
- [项目说明](README.md)
- [项目配置说明](config/app.yaml)
