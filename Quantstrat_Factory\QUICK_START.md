# Quantstrat Factory 快速入门指南

## 🚀 5分钟快速上手

### 第一步：运行演示脚本
```bash
cd D:\PY\Quantstrat_Factory
python demo_usage.py
```

这将展示所有主要功能的使用方法。

---

## 📊 常用功能快速使用

### 1. 计算技术因子
```python
from src.features.factors.technical_factors import TechnicalFactors

# 准备股票数据（需要包含 open, high, low, close, volume 列）
# stock_data = your_stock_data

# 初始化技术因子计算器
tech_factors = TechnicalFactors()

# 计算动量因子
momentum = tech_factors.calculate_momentum(stock_data, period=20)

# 计算RSI
rsi = tech_factors.calculate_rsi_14(stock_data)

# 计算波动率
volatility = tech_factors.calculate_volatility(stock_data, period=20)
```

### 2. 构建交易策略
```python
from src.strategy.builder.strategy_builder import StrategyBuilder

# 创建策略构建器
builder = StrategyBuilder()

# 添加因子
builder.add_factor({
    'name': 'momentum_20',
    'type': 'technical', 
    'weight': 0.6
})

# 添加交易信号
builder.add_signal_rule({
    'name': 'buy_signal',
    'condition': 'composite_score > 0.5',
    'action': 'buy'
})

# 构建策略
strategy = builder.build_strategy('my_strategy', '我的第一个策略')
```

### 3. 使用策略模板
```bash
# 命令行方式
python src/strategy/builder/run_strategy_builder.py list-templates
python src/strategy/builder/run_strategy_builder.py create-from-template momentum_strategy my_momentum_strategy
```

### 4. 生成图表和报告
```python
from src.visualization.chart_generator import ChartGenerator
from src.visualization.report_generator import ReportGenerator

# 创建K线图
chart_gen = ChartGenerator()
candlestick = chart_gen.create_candlestick_chart(stock_data, title='股价走势')

# 生成因子分析报告
report_gen = ReportGenerator()
report = report_gen.create_factor_analysis_report(factor_data, title='因子分析')
```

### 5. 性能优化
```python
from src.performance.data_optimizer import DataOptimizer
from src.performance.cache_manager import CacheManager

# 优化数据内存使用
optimizer = DataOptimizer()
optimized_data = optimizer.optimize_dtypes(large_dataframe)

# 使用缓存加速计算
cache = CacheManager()
cache.set('my_data', expensive_calculation_result)
cached_result = cache.get('my_data')
```

---

## 🎯 实际使用场景

### 场景1：因子研究
```python
# 1. 加载股票数据
stock_data = pd.read_csv('your_stock_data.csv')

# 2. 计算多个技术因子
tech_factors = TechnicalFactors()
momentum = tech_factors.calculate_momentum(stock_data, 20)
volatility = tech_factors.calculate_volatility(stock_data, 20)
rsi = tech_factors.calculate_rsi_14(stock_data)

# 3. 合并因子数据
factor_data = pd.concat([momentum, volatility, rsi], axis=1)

# 4. 生成因子分析报告
report_gen = ReportGenerator()
report = report_gen.create_factor_analysis_report(factor_data)
report_gen.export_report(report, 'factor_analysis.html')
```

### 场景2：策略开发
```python
# 1. 从模板开始
template_manager = StrategyTemplate()
builder = StrategyBuilder()

# 2. 应用动量策略模板
template = template_manager.get_template('momentum_strategy')
template_manager.apply_template(builder, template)

# 3. 自定义调整
builder.add_risk_control({
    'name': 'stop_loss',
    'type': 'stop_loss',
    'threshold': -0.05
})

# 4. 构建并保存策略
strategy = builder.build_strategy('my_momentum_strategy', '定制动量策略')
strategy_manager = StrategyManager()
strategy_manager.save_strategy(strategy)
```

### 场景3：批量数据处理
```python
# 1. 使用性能优化工具
from src.performance.parallel_processor import ParallelProcessor
from src.performance.data_optimizer import DataOptimizer

processor = ParallelProcessor()
optimizer = DataOptimizer()

# 2. 定义处理函数
def process_stock_data(stock_data):
    # 计算因子
    tech_factors = TechnicalFactors()
    momentum = tech_factors.calculate_momentum(stock_data, 20)
    return momentum

# 3. 并行处理多只股票
stock_list = ['000001.SZ', '000002.SZ', '600000.SH']  # 股票列表
stock_data_list = [load_stock_data(code) for code in stock_list]

# 并行计算因子
results = processor.process_with_threads(process_stock_data, stock_data_list)
```

---

## 🔧 命令行工具使用

### 策略管理
```bash
# 查看所有策略模板
python src/strategy/builder/run_strategy_builder.py list-templates

# 创建策略
python src/strategy/builder/run_strategy_builder.py create-from-template momentum_strategy my_strategy

# 查看策略列表
python src/strategy/builder/run_strategy_builder.py list-strategies

# 查看策略详情
python src/strategy/builder/run_strategy_builder.py show my_strategy

# 导出策略
python src/strategy/builder/run_strategy_builder.py export my_strategy output/strategy.json
```

### 运行测试
```bash
# 运行所有测试
pytest tests/

# 运行特定模块测试
pytest tests/unit/test_technical_factors.py -v

# 运行性能测试
pytest tests/unit/test_performance_optimization.py -v
```

---

## 📁 项目结构说明

```
Quantstrat_Factory/
├── src/                    # 源代码
│   ├── data/              # 数据处理模块
│   ├── features/          # 特征工程模块
│   │   └── factors/       # 因子计算
│   ├── strategy/          # 策略开发模块
│   │   └── builder/       # 策略构建器
│   ├── visualization/     # 可视化模块
│   └── performance/       # 性能优化模块
├── tests/                 # 测试代码
├── demo_usage.py         # 功能演示脚本
├── USER_GUIDE.md         # 详细使用指南
├── COMMAND_REFERENCE.md  # 命令参考手册
└── QUICK_START.md        # 本文件
```

---

## 💡 最佳实践提示

### 1. 数据处理
- 使用 `DataOptimizer` 优化大型数据集的内存使用
- 对于重复计算，使用 `CacheManager` 缓存结果
- 大数据集使用分块处理：`process_in_chunks()`

### 2. 因子计算
- 先检查数据完整性（是否包含必要的列）
- 使用标准化的因子命名规范
- 定期验证因子的有效性

### 3. 策略开发
- 从现有模板开始，逐步定制
- 添加适当的风险控制措施
- 使用策略验证功能确保配置正确

### 4. 性能优化
- 监控内存使用情况
- 对CPU密集型任务使用并行处理
- 定期清理临时文件和缓存

### 5. 可视化
- 选择合适的图表类型展示数据
- 使用交互式图表提升用户体验
- 定期生成报告跟踪策略表现

---

## 🆘 常见问题

### Q: 如何加载自己的股票数据？
A: 确保数据包含必要的列（open, high, low, close, volume），然后使用pandas读取：
```python
stock_data = pd.read_csv('your_data.csv')
# 或者使用项目的数据加载器
from src.data.data_loader import DataLoader
loader = DataLoader()
stock_data = loader.load_stock_data('000001.SZ', '2023-01-01', '2023-12-31')
```

### Q: 如何添加自定义因子？
A: 在相应的因子类中添加新方法，或创建新的因子类：
```python
class MyCustomFactors:
    def calculate_my_factor(self, data, period=20):
        # 自定义因子计算逻辑
        return result
```

### Q: 策略验证失败怎么办？
A: 检查策略配置，确保：
- 因子权重总和为1
- 信号条件语法正确
- 所有必要字段都已填写

### Q: 如何提升计算性能？
A: 使用性能优化工具：
- 数据类型优化：`DataOptimizer.optimize_dtypes()`
- 并行处理：`ParallelProcessor.process_with_threads()`
- 缓存机制：`CacheManager`

---

## 📞 获取帮助

1. **查看文档**：
   - `USER_GUIDE.md` - 详细使用指南
   - `COMMAND_REFERENCE.md` - 完整命令参考

2. **运行测试**：
   ```bash
   pytest tests/ -v
   ```

3. **查看日志**：
   检查 `quantstrat.log` 文件获取详细错误信息

4. **演示脚本**：
   ```bash
   python demo_usage.py
   ```

---

**开始您的量化策略开发之旅吧！** 🚀
