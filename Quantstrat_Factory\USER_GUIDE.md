# 因子实验室 (Factor Lab)

## 1. 项目简介

因子实验室是一个功能强大的交互式 Web 应用，旨在帮助量化研究人员对股票因子进行快速验证和深入分析。它将宏观的市场模式识别与微观的因子有效性评估相结合，提供了一个从数据加载、因子计算、复合因子构建、因子评估到可视化分析的完整工作流。

## 2. 安装与运行

### 2.1 环境准备

确保您的 Python 环境已安装以下依赖：
- `dash`
- `dash-bootstrap-components`
- `pandas`
- `numpy`
- `plotly`
- `openpyxl` (用于读取 Excel 文件，如果数据源是 Excel)
- `pyarrow` (用于读取 Parquet 文件)

您可以使用 pip 安装这些依赖：
```bash
pip install dash dash-bootstrap-components pandas numpy plotly openpyxl pyarrow
```

### 2.2 数据准备

1.  **日线数据**: 确保在 `D:/PY/Data/cleaned/daily/daily_basics.parquet` 文件中存放了股票的日线数据。文件应包含 `datetime`, `symbol`, `open`, `high`, `low`, `close`, `volume` 等列。
2.  **股票列表**: 确保在 `D:/PY/Data/stock_list.txt` 文件中包含了所有需要分析的股票代码，每行一个。
3.  **特征库**: 如果您使用文件型因子，请确保在 `D:/PY/Data/features/` 目录下有相应的因子数据文件（.parquet格式）。
4.  **配置文件**: 确保项目根目录下的配置文件存在，并且其中配置了正确的数据路径。

### 2.3 运行应用

在命令行中，导航到项目根目录，然后运行 Factor Lab：
```bash
cd D:/PY/Quantstrat_Factory/
python -m src.research.factor_lab.web_app.app
```
应用启动后，您可以在浏览器中访问 `http://127.0.0.1:8051/`。

## 3. 核心功能使用说明

### 3.1 控制面板

左侧是控制面板，用于配置分析参数。

*   **日期范围**: 选择分析数据的起始和结束日期。默认日期范围为 `2020-10-09` 至 `2020-10-31`。
*   **股票池**:
    *   **静态股票池**: 选择预定义的股票池，如“沪深300”、“中证500”或“全市场”。
    *   **动态筛选池**: 选择此选项后，点击下方的“配置动态池”按钮，可以打开动态股票池筛选器，根据自定义的量价模式生成股票池。
*   **选择因子**: 选择一个或多个因子进行分析。因子分为“计算型”（如 MOM, VOL）和“文件型”（从特征库加载）。
*   **自定义复合因子**: 在文本框中输入自定义的复合因子表达式，每行一个。例如：`COMP_A = MOM + VOL`。
*   **因子评估设置**:
    *   **启用因子评估**: 勾选此项以启用因子IC计算和分层回测。
    *   **IC计算周期 (天)**: 设置计算因子IC时未来收益率的周期。
    *   **分层数量**: 设置分层回测时将股票分为多少个分层。
*   **叠加股票代码**: 在“全市场”或“动态筛选池”模式下，您可以在图表中叠加显示特定股票的因子时序图，以便进行对比分析。
*   **开始分析按钮**: 配置完成后，点击此按钮开始执行因子分析。

### 3.2 动态股票池筛选器 (高级筛选)

点击“配置动态池”按钮将打开一个模态框，您可以在其中定义复杂的量价模式来筛选股票。

*   **日期范围**: 为模式筛选指定日期范围。
*   **分段 (Segment)**: 一个模式可以由多个连续的分段组成。
    *   点击“增加分段”按钮可以添加新的分段。
    *   每个分段可以设置其**天数**。
    *   每个分段内可以定义多条**规则**。
    *   **规则**: 每条规则包含：
        *   **指标**: 如“涨跌幅 (pct_chg)”、“成交量比率 (volume_ratio)”、“Aroon Up (aroon_up)”、“Aroon Down (aroon_down)”。
        *   **操作符**: 如 `>, <, >=, <=, ==, !=`。
        *   **值**: 规则的阈值。
        *   点击“增加规则”按钮可以添加新的规则。
        *   点击“删除”按钮可以删除对应的规则。
    *   **规则逻辑**: 选择分段内多条规则之间的逻辑关系（“AND”或“OR”）。
    *   点击“删除分段”按钮可以删除整个分段。
*   **生成股票池**: 配置完模式后，点击此按钮将执行筛选，并将符合模式的股票列表存储为“动态筛选池”，供主界面的因子分析使用。

### 3.3 右侧内容区

右侧区域显示分析结果。

*   **因子评估报告**: 如果启用了因子评估，这里将显示每个因子的 IC 均值、标准差、ICIR，以及分层回测的净值曲线和性能统计表（年化收益率、年化波动率、夏普比率）。
*   **可视化分析**: 显示因子时序图。在“全市场”或“动态筛选池”模式下，默认显示因子均值时序图，并支持叠加个股。
*   **详细数据**: 显示原始的分析数据表格。

## 4. 未来展望

*   **扩展因子知识库**: 将 `FACTOR_DEFINITIONS` 外部化到配置文件，方便用户自定义和扩充。
*   **更多评估指标**: 增加最大回撤、胜率、赔率等更丰富的分层回测指标。
*   **性能优化**: 针对大数据量下的加载和计算性能进行优化。
*   **更多可视化类型**: 增加因子分布图、IC时序图等。
*   **分钟级数据分析**: 扩展平台支持分钟级因子和评估。
.select_features(
    stock_data, 
    target_column='returns',
    method='correlation'
)
```

---

## 📈 因子研究

### 1. 技术面因子
```python
from src.features.factors.technical_factors import TechnicalFactors

# 初始化技术因子计算器
tech_factors = TechnicalFactors()

# 查看可用因子
available_factors = tech_factors.get_available_factors()
print("可用技术因子:", available_factors)

# 计算动量因子
momentum = tech_factors.calculate_momentum(stock_data, period=20)

# 计算波动率因子
volatility = tech_factors.calculate_volatility(stock_data, period=20)

# 计算RSI因子
rsi = tech_factors.calculate_rsi_14(stock_data)
```

### 2. 基本面因子
```python
from src.features.factors.fundamental_factors import FundamentalFactors

# 初始化基本面因子计算器
fund_factors = FundamentalFactors()

# 计算估值因子
pe_ratio = fund_factors.calculate_pe_ratio(financial_data)
pb_ratio = fund_factors.calculate_pb_ratio(financial_data)

# 计算盈利能力因子
roe = fund_factors.calculate_roe(financial_data)
roa = fund_factors.calculate_roa(financial_data)
```

### 3. 因子分析
```python
from src.features.factor_analyzer import FactorAnalyzer

# 初始化因子分析器
analyzer = FactorAnalyzer()

# 因子IC分析
ic_results = analyzer.calculate_ic(factor_data, return_data)

# 因子分层回测
layered_results = analyzer.factor_layered_backtest(factor_data, return_data)

# 因子相关性分析
correlation_matrix = analyzer.calculate_factor_correlation(factor_data)
```

---

## 🎯 策略开发

### 1. 策略构建器
```python
from src.strategy.builder.strategy_builder import StrategyBuilder

# 初始化策略构建器
builder = StrategyBuilder()

# 添加因子
builder.add_factor({
    'name': 'momentum_20',
    'type': 'technical',
    'weight': 0.6
})

builder.add_factor({
    'name': 'volatility_20',
    'type': 'technical',
    'weight': 0.4
})

# 添加信号规则
builder.add_signal_rule({
    'name': 'long_signal',
    'condition': 'composite_score > 0.5',
    'action': 'buy'
})

# 构建策略
strategy = builder.build_strategy('my_strategy', '我的测试策略')
```

### 2. 策略模板
```python
from src.strategy.builder.strategy_template import StrategyTemplate

# 初始化策略模板管理器
template_manager = StrategyTemplate()

# 查看可用模板
templates = template_manager.get_available_templates()
print("可用策略模板:", [t['name'] for t in templates])

# 从模板创建策略
template = template_manager.get_template('momentum_strategy')
template_manager.apply_template(builder, template)
strategy = builder.build_strategy('momentum_test', '基于动量模板的策略')
```

### 3. 策略管理
```python
from src.strategy.builder.strategy_manager import StrategyManager

# 初始化策略管理器
manager = StrategyManager()

# 保存策略
manager.save_strategy(strategy)

# 加载策略
loaded_strategy = manager.load_strategy('my_strategy')

# 列出所有策略
all_strategies = manager.list_strategies()
```

---

## 📊 可视化和报告

### 1. 图表生成
```python
from src.visualization.chart_generator import ChartGenerator

# 初始化图表生成器
chart_gen = ChartGenerator()

# 创建K线图
candlestick_chart = chart_gen.create_candlestick_chart(
    stock_data, 
    title='股票K线图'
)

# 创建因子分布图
hist_chart = chart_gen.create_histogram(
    factor_data, 
    column='momentum_20',
    title='动量因子分布'
)

# 创建相关性热力图
heatmap = chart_gen.create_heatmap(
    correlation_matrix,
    title='因子相关性热力图'
)

# 保存图表
chart_gen.save_chart(candlestick_chart, 'output/candlestick.html')
```

### 2. 报告生成
```python
from src.visualization.report_generator import ReportGenerator

# 初始化报告生成器
report_gen = ReportGenerator()

# 创建因子分析报告
factor_report = report_gen.create_factor_analysis_report(
    factor_data,
    title='因子分析报告'
)

# 创建策略报告
strategy_report = report_gen.create_strategy_report(
    strategy_data,
    title='策略表现报告'
)

# 导出报告
report_gen.export_report(factor_report, 'output/factor_report.html', format='html')
```

### 3. 仪表板
```python
from src.visualization.dashboard import Dashboard

# 创建仪表板
dashboard = Dashboard()

# 添加图表组件
dashboard.add_widget({
    'type': 'chart',
    'title': '股价走势',
    'chart_type': 'line',
    'data_source': 'stock_data'
})

# 添加指标组件
dashboard.add_widget({
    'type': 'metric',
    'title': '年化收益率',
    'config': {
        'metric_type': 'mean',
        'column': 'annual_return'
    }
})

# 保存仪表板
dashboard.save_dashboard('output/my_dashboard.json')
```

---

## ⚡ 性能优化

### 1. 数据优化
```python
from src.performance.data_optimizer import DataOptimizer

# 初始化数据优化器
optimizer = DataOptimizer()

# 优化DataFrame数据类型
optimized_df = optimizer.optimize_dtypes(large_dataframe)

# 分块处理大数据
def process_chunk(chunk):
    return chunk.mean()

results = optimizer.process_in_chunks(
    large_dataframe, 
    process_chunk, 
    chunk_size=10000
)
```

### 2. 缓存管理
```python
from src.performance.cache_manager import CacheManager

# 初始化缓存管理器
cache = CacheManager(max_size=1000, default_ttl=3600)

# 设置缓存
cache.set('factor_data_key', factor_data, ttl=1800)

# 获取缓存
cached_data = cache.get('factor_data_key')

# 使用装饰器缓存函数结果
from src.performance.cache_manager import cache_result

@cache_result(ttl=3600)
def expensive_calculation(data):
    # 耗时计算
    return data.rolling(252).std()
```

### 3. 并行处理
```python
from src.performance.parallel_processor import ParallelProcessor

# 初始化并行处理器
processor = ParallelProcessor()

# 并行处理数据列表
def calculate_factor(data):
    return data.rolling(20).mean()

results = processor.process_with_threads(
    calculate_factor,
    data_list,
    max_workers=4
)

# 并行分组处理
grouped_results = processor.parallel_groupby_apply(
    large_dataframe,
    ['stock_code'],
    calculate_factor
)
```

---

## 🖥️ 命令行工具

### 1. 策略构建工具
```bash
# 列出所有策略模板
python src/strategy/builder/run_strategy_builder.py list-templates

# 从模板创建策略
python src/strategy/builder/run_strategy_builder.py create-from-template momentum_strategy my_momentum_strategy

# 列出所有策略
python src/strategy/builder/run_strategy_builder.py list-strategies

# 查看策略详情
python src/strategy/builder/run_strategy_builder.py show my_momentum_strategy

# 导出策略
python src/strategy/builder/run_strategy_builder.py export my_momentum_strategy output/strategy.json
```

### 2. 数据处理工具
```bash
# 数据清洗
python src/data/run_data_processor.py clean --input data/raw/ --output data/cleaned/

# 特征计算
python src/features/run_feature_processor.py calculate --data data/cleaned/ --features momentum,volatility

# 因子分析
python src/features/run_factor_analyzer.py analyze --factor-data data/factors/ --return-data data/returns/
```

---

## 📋 配置文件示例

### 策略配置文件 (strategy_config.json)
```json
{
    "name": "multi_factor_strategy",
    "description": "多因子策略示例",
    "factors": [
        {
            "name": "momentum_20",
            "type": "technical",
            "weight": 0.4
        },
        {
            "name": "volatility_20", 
            "type": "technical",
            "weight": 0.3
        },
        {
            "name": "pe_ratio",
            "type": "fundamental",
            "weight": 0.3
        }
    ],
    "signals": [
        {
            "name": "long_signal",
            "condition": "composite_score > 0.6",
            "action": "buy"
        },
        {
            "name": "short_signal",
            "condition": "composite_score < -0.6",
            "action": "sell"
        }
    ]
}
```

### 数据源配置文件 (data_config.json)
```json
{
    "sources": {
        "wind": {
            "type": "wind",
            "config": {
                "username": "your_username",
                "password": "your_password"
            }
        },
        "local": {
            "type": "local",
            "config": {
                "data_path": "D:/PY/Data"
            }
        }
    },
    "default_source": "local"
}
```

---

## 🔍 调试和监控

### 1. 日志配置
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('quantstrat.log'),
        logging.StreamHandler()
    ]
)
```

### 2. 性能监控
```python
from src.performance.memory_manager import MemoryManager

# 内存监控装饰器
@MemoryManager().monitor_memory_usage
def memory_intensive_function():
    # 内存密集型操作
    pass

# 获取性能报告
from src.performance.response_optimizer import response_optimizer
performance_report = response_optimizer.get_performance_report()
```

---

## 📚 更多资源

- **完整命令参考**: 查看 `COMMAND_REFERENCE.md`
- **战略规划**: 查看 `STRATEGIC_ROADMAP_2025.md`
- **项目总结**: 查看 `PROJECT_COMPLETION_SUMMARY.md`
- **测试用例**: 查看 `tests/` 目录下的测试文件

---

## 💡 最佳实践

1. **数据处理**: 优先使用数据优化器处理大型数据集
2. **因子计算**: 使用缓存避免重复计算
3. **策略开发**: 从模板开始，逐步定制
4. **性能优化**: 定期监控资源使用情况
5. **测试**: 编写单元测试确保代码质量

---

**需要帮助?** 查看日志文件或联系开发团队获取支持。
