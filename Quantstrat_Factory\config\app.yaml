# Quantstrat Factory 主配置文件

# 应用基本信息
app:
  name: "Quantstrat Factory"
  version: "1.0.0"
  description: "量化策略开发和回测平台"
  author: "Quantstrat Factory Team"

# 数据路径配置 - 统一权威配置
data:
  root: "D:/PY/Data"
  raw:
    minute: "D:/PY/Data/raw/1min"
  cleaned:
    minute: "D:/PY/Data/cleaned/minute"
    daily: "D:/PY/Data/cleaned/daily"
  features:
    root: "D:/PY/Data/features"
    minute: "D:/PY/Data/features/minute"
    daily: "D:/PY/Data/features/daily"

# 数据源配置
data_sources:
  # 本地数据源（默认启用）
  local:
    enabled: true
    priority: 1
    data_paths:
      raw_minute: "d:/py/data/1min"
      cleaned_minute: "d:/py/data/min"
      daily: "d:/py/data/day"

  # Wind数据源
  wind:
    enabled: false  # 需要安装WindPy才能启用
    priority: 2
    timeout: 30
    retry_count: 3
    cache_enabled: true

  # 同花顺数据源
  tonghuashun:
    enabled: false  # 可选启用
    priority: 3
    timeout: 30
    retry_count: 3
    cache_enabled: true
    rate_limit: 60  # 每分钟最多60次请求

# 输出路径配置
output:
  factor_lab: "d:/py/Quantstrat_Factory/src/research/factor_lab/output"
  signals: "d:/py/Quantstrat_Factory/src/trading/signal_generator/output"
  backtests: "d:/py/Quantstrat_Factory/src/trading/backtester/output"
  experiments: "d:/py/Quantstrat_Factory/src/research/experiment_tracking/experiments"

# 数据库配置
database:
  type: "sqlite"
  path: "data/quantstrat.db"
  pool_size: 10
  echo: false

# 缓存配置
cache:
  type: "redis"
  host: "localhost"
  port: 6379
  db: 0
  ttl: 3600

# 日志配置
logging:
  level: "INFO"
  format: "json"
  file: "logs/quantstrat.log"
  max_size: "100MB"
  backup_count: 5

# 监控配置
monitoring:
  enabled: true
  metrics_interval: 60
  alert_thresholds:
    cpu_percent: 80
    memory_percent: 85
    disk_usage_percent: 90

# 性能配置
performance:
  parallel_workers: 4
  chunk_size: 10000
  memory_limit_mb: 4096
  cache_enabled: true

# 质量保障配置
quality:
  code_coverage_threshold: 90
  complexity_threshold: 15
  maintainability_threshold: 20
  test_timeout: 300

# API配置
api:
  host: "127.0.0.1"
  port: 8000
  debug: false
  cors_enabled: true
  rate_limit: "100/minute"

# Web界面配置
web:
  host: "127.0.0.1"
  port: 5000
  debug: false
  secret_key: "quantstrat_factory_secret_key"

# 通知配置
notifications:
  enabled: true
  channels:
    console:
      enabled: true
    file:
      enabled: true
      path: "logs/notifications.jsonl"
    email:
      enabled: false
      smtp_server: ""
      smtp_port: 587
      username: ""
      password: ""
      from_email: ""
      to_emails: []
    slack:
      enabled: false
      webhook_url: ""
      channel: "#alerts"

# 实验追踪配置
experiment_tracking:
  backend: "local"
  tracking_uri: "src/research/experiment_tracking/mlruns"
  artifact_location: "src/research/experiment_tracking/artifacts"
  auto_log: true

# 插件配置
plugins:
  enabled: true
  directories:
    - "src/platform/plugins"
    - "plugins"
  auto_discover: true

# 安全配置
security:
  secret_key: "your-secret-key-here"
  token_expiry: 3600
  max_login_attempts: 5
  session_timeout: 1800

# 开发配置
development:
  debug: true
  hot_reload: true
  profiling: false
  test_data_size: 1000
