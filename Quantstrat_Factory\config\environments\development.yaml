# 开发环境配置

# 继承主配置并覆盖特定设置
extends: "../app.yaml"

# 开发环境特定配置
app:
  debug: true
  environment: "development"

# 数据库配置（开发环境使用SQLite）
database:
  type: "sqlite"
  path: "data/dev_quantstrat.db"
  echo: true

# 日志配置（开发环境更详细的日志）
logging:
  level: "DEBUG"
  format: "text"
  console: true

# API配置（开发环境）
api:
  debug: true
  reload: true

# Web界面配置（开发环境）
web:
  debug: true
  hot_reload: true

# 性能配置（开发环境较小的数据集）
performance:
  parallel_workers: 2
  chunk_size: 1000
  memory_limit_mb: 2048

# 监控配置（开发环境降低监控频率）
monitoring:
  metrics_interval: 300
  alert_thresholds:
    cpu_percent: 90
    memory_percent: 90
    disk_usage_percent: 95

# 通知配置（开发环境只使用控制台）
notifications:
  channels:
    console:
      enabled: true
    file:
      enabled: true
    email:
      enabled: false
    slack:
      enabled: false
