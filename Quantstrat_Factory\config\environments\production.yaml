# 生产环境配置

# 继承主配置并覆盖特定设置
extends: "../app.yaml"

# 生产环境特定配置
app:
  debug: false
  environment: "production"

# 数据库配置（生产环境使用PostgreSQL）
database:
  type: "postgresql"
  host: "localhost"
  port: 5432
  database: "quantstrat_prod"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  pool_size: 20
  echo: false

# 缓存配置（生产环境使用Redis集群）
cache:
  type: "redis"
  host: "redis-cluster"
  port: 6379
  db: 0
  ttl: 7200

# 日志配置（生产环境结构化日志）
logging:
  level: "INFO"
  format: "json"
  file: "/var/log/quantstrat/app.log"
  max_size: "500MB"
  backup_count: 10
  console: false

# API配置（生产环境）
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  workers: 4

# Web界面配置（生产环境）
web:
  host: "0.0.0.0"
  port: 5000
  debug: false
  workers: 2

# 性能配置（生产环境高性能）
performance:
  parallel_workers: 8
  chunk_size: 50000
  memory_limit_mb: 16384
  cache_enabled: true

# 监控配置（生产环境严格监控）
monitoring:
  enabled: true
  metrics_interval: 30
  alert_thresholds:
    cpu_percent: 70
    memory_percent: 80
    disk_usage_percent: 85

# 通知配置（生产环境全渠道通知）
notifications:
  enabled: true
  channels:
    console:
      enabled: false
    file:
      enabled: true
      path: "/var/log/quantstrat/notifications.jsonl"
    email:
      enabled: true
      smtp_server: "${SMTP_SERVER}"
      smtp_port: 587
      username: "${SMTP_USERNAME}"
      password: "${SMTP_PASSWORD}"
      from_email: "${FROM_EMAIL}"
      to_emails: ["${ALERT_EMAIL}"]
    slack:
      enabled: true
      webhook_url: "${SLACK_WEBHOOK_URL}"
      channel: "#production-alerts"

# 安全配置（生产环境加强安全）
security:
  secret_key: "${SECRET_KEY}"
  token_expiry: 1800
  max_login_attempts: 3
  session_timeout: 900
  ssl_required: true

# 实验追踪配置（生产环境使用远程存储）
experiment_tracking:
  backend: "mlflow"
  tracking_uri: "${MLFLOW_TRACKING_URI}"
  artifact_location: "${MLFLOW_ARTIFACT_LOCATION}"
  auto_log: false
