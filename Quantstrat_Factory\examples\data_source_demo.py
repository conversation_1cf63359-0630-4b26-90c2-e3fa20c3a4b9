"""
数据源管理演示脚本。

展示如何使用新的数据源管理功能。
"""

import sys
from pathlib import Path
import yaml
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(project_root))

from src.data.sources import DataSourceManager


def load_config():
    """加载配置文件。"""
    config_path = project_root / "config" / "app.yaml"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config.get("data_sources", {})
    else:
        print(f"配置文件不存在: {config_path}")
        return {}


def demo_basic_usage():
    """演示基本用法。"""
    print("🚀 数据源管理基本用法演示")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    print(f"📋 加载配置: {list(config.keys())}")
    
    # 创建数据源管理器
    manager = DataSourceManager(config)
    
    # 连接数据源
    print("\n🔗 连接数据源...")
    results = manager.connect_all()
    for source, success in results.items():
        status = "✅" if success else "❌"
        print(f"  {source}: {status}")
    
    # 获取可用数据源
    available = manager.get_available_sources()
    print(f"\n🎯 可用数据源: {available}")
    
    if available:
        # 获取股票列表
        print("\n📋 获取股票列表...")
        stocks = manager.get_stock_list(market="all")
        print(f"  找到 {len(stocks)} 只股票")
        if stocks:
            print(f"  前5只: {stocks[:5]}")
    
    # 断开连接
    manager.disconnect_all()
    print("\n✅ 演示完成")


def demo_data_retrieval():
    """演示数据获取。"""
    print("\n📊 数据获取演示")
    print("=" * 50)
    
    config = load_config()
    
    # 使用上下文管理器自动管理连接
    with DataSourceManager(config) as manager:
        # 测试股票代码
        test_symbols = ["sh600000", "sz000001"]
        start_date = "2023-01-01"
        end_date = "2023-01-10"
        
        print(f"📈 测试股票: {test_symbols}")
        print(f"📅 日期范围: {start_date} 到 {end_date}")
        
        # 获取日线数据
        print("\n🔍 获取日线数据...")
        daily_df = manager.get_daily_data(
            symbols=test_symbols,
            start_date=start_date,
            end_date=end_date
        )
        
        if not daily_df.empty:
            print(f"  ✅ 获取到 {len(daily_df)} 条日线数据")
            print("  📋 数据预览:")
            print(daily_df.head())
        else:
            print("  ❌ 未获取到日线数据")
        
        # 获取分钟数据
        print("\n🔍 获取分钟数据...")
        minute_df = manager.get_minute_data(
            symbols=test_symbols[:1],  # 只测试一只股票
            start_date=start_date,
            end_date=start_date,  # 只获取一天的数据
            frequency="1min"
        )
        
        if not minute_df.empty:
            print(f"  ✅ 获取到 {len(minute_df)} 条分钟数据")
            print("  📋 数据预览:")
            print(minute_df.head())
        else:
            print("  ❌ 未获取到分钟数据")


def demo_cache_functionality():
    """演示缓存功能。"""
    print("\n💾 缓存功能演示")
    print("=" * 50)
    
    config = load_config()
    
    with DataSourceManager(config) as manager:
        # 第一次获取数据
        print("🔍 第一次获取股票列表...")
        import time
        start_time = time.time()
        stocks1 = manager.get_stock_list()
        time1 = time.time() - start_time
        print(f"  耗时: {time1:.2f}秒")
        
        # 第二次获取数据（应该从缓存获取）
        print("\n🔍 第二次获取股票列表（缓存）...")
        start_time = time.time()
        stocks2 = manager.get_stock_list()
        time2 = time.time() - start_time
        print(f"  耗时: {time2:.2f}秒")
        
        # 检查结果一致性
        if stocks1 == stocks2:
            print("  ✅ 缓存数据一致")
        else:
            print("  ❌ 缓存数据不一致")
        
        # 显示缓存信息
        cache_info = manager.get_cache_info()
        print(f"\n📊 缓存信息:")
        print(f"  缓存启用: {cache_info['cache_enabled']}")
        print(f"  缓存大小: {cache_info['cache_size']}")
        print(f"  缓存键: {cache_info['cache_keys']}")


def demo_fallback_mechanism():
    """演示数据源回退机制。"""
    print("\n🔄 数据源回退机制演示")
    print("=" * 50)
    
    config = load_config()
    
    with DataSourceManager(config) as manager:
        available = manager.get_available_sources()
        print(f"🎯 可用数据源: {available}")
        
        if len(available) > 1:
            # 指定一个不存在的数据源，测试回退
            print("\n🔍 指定不存在的数据源...")
            df = manager.get_daily_data(
                symbols=["sh600000"],
                start_date="2023-01-01",
                end_date="2023-01-02",
                source="nonexistent",
                fallback=True
            )
            
            if not df.empty:
                print("  ✅ 回退机制工作正常")
            else:
                print("  ❌ 回退机制失败")
        else:
            print("  ℹ️ 只有一个数据源，无法演示回退机制")


def demo_error_handling():
    """演示错误处理。"""
    print("\n⚠️ 错误处理演示")
    print("=" * 50)
    
    config = load_config()
    
    with DataSourceManager(config) as manager:
        # 测试无效股票代码
        print("🔍 测试无效股票代码...")
        df = manager.get_daily_data(
            symbols=["INVALID"],
            start_date="2023-01-01",
            end_date="2023-01-02"
        )
        
        if df.empty:
            print("  ✅ 正确处理无效股票代码")
        else:
            print("  ❌ 未正确处理无效股票代码")
        
        # 测试无效日期范围
        print("\n🔍 测试无效日期范围...")
        df = manager.get_daily_data(
            symbols=["sh600000"],
            start_date="2025-01-01",
            end_date="2025-01-02"
        )
        
        if df.empty:
            print("  ✅ 正确处理无效日期范围")
        else:
            print("  ❌ 未正确处理无效日期范围")


def main():
    """主函数。"""
    print("🎯 Quantstrat Factory 数据源管理演示")
    print("=" * 60)
    
    try:
        # 基本用法演示
        demo_basic_usage()
        
        # 数据获取演示
        demo_data_retrieval()
        
        # 缓存功能演示
        demo_cache_functionality()
        
        # 回退机制演示
        demo_fallback_mechanism()
        
        # 错误处理演示
        demo_error_handling()
        
        print("\n🎉 所有演示完成！")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
