#!/usr/bin/env python3
"""
Quantstrat Factory 核心功能演示。

展示数据清洗、特征生成和因子工厂的主要功能。
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent  # 回到项目根目录
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def demo_factory_creation():
    """演示工厂创建和基本功能。"""
    print("🏭 Quantstrat Factory 核心功能演示")
    print("=" * 50)
    
    # 导入并创建工厂
    import src
    factory = src.QuantstratFactory()
    
    print(f"✅ 工厂创建成功")
    print(f"📦 版本: {src.__version__}")
    print(f"👥 作者: {src.__author__}")
    print(f"📧 邮箱: {src.__email__}")
    
    # 获取系统状态
    status = factory.get_status()
    print(f"\n📊 系统状态:")
    print(f"  版本: {status['version']}")
    print(f"  组件状态:")
    for component, loaded in status['components'].items():
        status_icon = "✅" if loaded else "⚠️"
        print(f"    {status_icon} {component}: {'已加载' if loaded else '未加载'}")
    
    return factory

def demo_data_auditor(factory):
    """演示数据审计功能。"""
    print(f"\n🔍 数据审计器演示")
    print("-" * 30)
    
    # 尝试获取数据审计器
    auditor = factory.get_data_auditor()
    
    if auditor:
        print("✅ 数据审计器可用")
        print("📝 功能说明:")
        print("  - 数据质量检查")
        print("  - 缺失值处理")
        print("  - 异常值检测")
        print("  - 数据清洗")
        print("  - 数据验证")
        
        # 这里可以添加实际的数据审计演示
        print("\n💡 使用方法:")
        print("  result = factory.run_data_auditor()")
        
    else:
        print("⚠️  数据审计器模块未完全加载")
        print("📁 模块位置: src/data/auditor/")
        print("🔧 主要文件: run_auditor.py")

def demo_feature_profiler(factory):
    """演示特征工程功能。"""
    print(f"\n🔧 特征工程器演示")
    print("-" * 30)
    
    # 尝试获取特征分析器
    profiler = factory.get_feature_profiler()
    
    if profiler:
        print("✅ 特征工程器可用")
        print("📝 功能说明:")
        print("  - 技术指标计算")
        print("  - 特征提取")
        print("  - 特征变换")
        print("  - 特征选择")
        print("  - 特征存储")
        
        print("\n💡 使用方法:")
        print("  result = factory.run_feature_profiler()")
        
    else:
        print("⚠️  特征工程器模块未完全加载")
        print("📁 模块位置: src/data/profiler/")
        print("🔧 主要文件: run_profiler.py")
        print("📊 支持的分析器:")
        print("  - 日级别分析器 (day_level_profiler)")
        print("  - 分钟级别分析器 (min_level_profiler)")

def demo_factor_lab(factory):
    """演示因子实验室功能。"""
    print(f"\n🧪 因子实验室演示")
    print("-" * 30)
    
    # 尝试获取因子实验室
    factor_lab = factory.get_factor_lab()
    
    if factor_lab:
        print("✅ 因子实验室可用")
        print("📝 功能说明:")
        print("  - 因子计算")
        print("  - 因子分析")
        print("  - IC分析")
        print("  - 分层回测")
        print("  - 因子评估")
        
        print("\n💡 使用方法:")
        print("  factor_lab = factory.run_factor_lab()")
        
    else:
        print("⚠️  因子实验室模块未完全加载")
        print("📁 模块位置: src/research/factor_lab/")
        print("🔧 主要组件:")
        print("  - Web应用 (web_app/app.py)")
        print("  - 因子评估器 (factor_evaluator.py)")
        print("  - 复合因子引擎 (composite_factor_engine.py)")
        print("  - 数据加载器 (data_loader.py)")

def demo_configuration():
    """演示配置管理功能。"""
    print(f"\n⚙️  配置管理演示")
    print("-" * 30)
    
    import src
    factory = src.QuantstratFactory()
    
    # 获取配置
    config = factory.get_config()
    print(f"📋 配置系统状态: {'可用' if config else '简化模式'}")
    
    # 演示配置获取
    app_name = factory.get_config("app.name", "Quantstrat Factory")
    data_root = factory.get_config("data.root", "d:/py/data")
    
    print(f"📝 配置示例:")
    print(f"  应用名称: {app_name}")
    print(f"  数据根目录: {data_root}")
    
    print(f"\n📁 配置文件位置:")
    print(f"  主配置: config/app.yaml")
    print(f"  开发环境: config/environments/development.yaml")
    print(f"  生产环境: config/environments/production.yaml")

def demo_project_structure():
    """演示项目结构。"""
    print(f"\n📁 项目结构演示")
    print("-" * 30)
    
    print("🏗️  新的分层架构:")
    print("📦 src/")
    print("  ├── 📊 data/              # 数据处理层")
    print("  │   ├── auditor/          # 数据审计")
    print("  │   ├── profiler/         # 特征工程")
    print("  │   └── store/            # 特征存储")
    print("  ├── 🔬 research/          # 研究层")
    print("  │   ├── factor_lab/       # 因子实验室")
    print("  │   └── experiment_tracking/ # 实验追踪")
    print("  ├── 💹 trading/           # 交易层")
    print("  │   ├── signal_generator/ # 信号生成")
    print("  │   ├── backtester/       # 回测引擎")
    print("  │   └── optimizer/        # 参数优化")
    print("  ├── 🏛️  core_platform/    # 平台层")
    print("  │   ├── core/             # 核心架构")
    print("  │   ├── api/              # API接口")
    print("  │   ├── web/              # Web界面")
    print("  │   └── database/         # 数据库")
    print("  └── 🔧 infrastructure/    # 基础设施层")
    print("      ├── monitoring/       # 监控系统")
    print("      ├── quality/          # 质量保障")
    print("      └── utils/            # 工具模块")

def main():
    """主演示函数。"""
    try:
        # 创建工厂并演示基本功能
        factory = demo_factory_creation()
        
        # 演示各个核心模块
        demo_data_auditor(factory)
        demo_feature_profiler(factory)
        demo_factor_lab(factory)
        demo_configuration()
        demo_project_structure()
        
        print(f"\n🎯 快速开始指南")
        print("-" * 30)
        print("1️⃣  导入工厂:")
        print("   from src import QuantstratFactory")
        print("2️⃣  创建实例:")
        print("   factory = QuantstratFactory()")
        print("3️⃣  使用功能:")
        print("   factory.run_data_auditor()")
        print("   factory.run_feature_profiler()")
        print("   factory.run_factor_lab()")
        
        print(f"\n📚 更多信息")
        print("-" * 30)
        print("📖 文档: README.md")
        print("🏗️  架构: ARCHITECTURE_OPTIMIZATION.md")
        print("📋 重构: RESTRUCTURE_COMPLETION_REPORT.md")
        print("⚙️  配置: config/app.yaml")
        
        print(f"\n🎉 演示完成！")
        print("✨ Quantstrat Factory 已准备就绪，可以开始您的量化投资之旅！")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
