#!/usr/bin/env python3
"""
Quantstrat Factory 功能演示脚本

展示如何使用更新的各种功能模块
"""

import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def demo_technical_factors():
    """演示技术因子计算"""
    print("=" * 60)
    print("📈 技术因子计算演示")
    print("=" * 60)
    
    # 创建模拟股票数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 模拟价格数据
    price_base = 100
    returns = np.random.normal(0.001, 0.02, 100)
    prices = [price_base]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    stock_data = pd.DataFrame({
        'date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'volume': np.random.randint(1000000, 10000000, 100)
    })
    
    print(f"📊 生成了 {len(stock_data)} 天的模拟股票数据")
    print(f"价格范围: {stock_data['close'].min():.2f} - {stock_data['close'].max():.2f}")
    
    try:
        from src.features.factors.technical_factors import TechnicalFactors
        
        # 初始化技术因子计算器
        tech_factors = TechnicalFactors()
        
        # 查看可用因子
        available_factors = tech_factors.get_available_factors()
        print(f"\n🔧 可用技术因子: {', '.join(available_factors)}")
        
        # 计算各种因子
        print("\n📊 计算技术因子...")
        
        momentum = tech_factors.calculate_momentum(stock_data, period=20)
        print(f"✅ 动量因子 (20日): 均值={momentum.mean():.4f}, 标准差={momentum.std():.4f}")
        
        volatility = tech_factors.calculate_volatility(stock_data, period=20)
        print(f"✅ 波动率因子 (20日): 均值={volatility.mean():.4f}, 标准差={volatility.std():.4f}")
        
        rsi = tech_factors.calculate_rsi_14(stock_data)
        print(f"✅ RSI因子 (14日): 均值={rsi.mean():.2f}, 范围={rsi.min():.2f}-{rsi.max():.2f}")
        
        volume_factor = tech_factors.calculate_volume_factor(stock_data, period=20)
        print(f"✅ 成交量因子 (20日): 均值={volume_factor.mean():.4f}")
        
        return stock_data, {
            'momentum': momentum,
            'volatility': volatility,
            'rsi': rsi,
            'volume_factor': volume_factor
        }
        
    except ImportError as e:
        print(f"❌ 导入技术因子模块失败: {e}")
        return stock_data, {}


def demo_strategy_builder():
    """演示策略构建功能"""
    print("\n" + "=" * 60)
    print("🎯 策略构建演示")
    print("=" * 60)
    
    try:
        from src.strategy.builder.strategy_builder import StrategyBuilder
        from src.strategy.builder.strategy_template import StrategyTemplate
        from src.strategy.builder.strategy_manager import StrategyManager
        
        # 初始化组件
        builder = StrategyBuilder()
        template_manager = StrategyTemplate()
        strategy_manager = StrategyManager()
        
        # 查看可用模板
        templates = template_manager.get_available_templates()
        print(f"📋 可用策略模板: {len(templates)} 个")
        for template in templates:
            print(f"  - {template['display_name']}: {template['description']}")
        
        # 手动构建策略
        print("\n🔨 手动构建策略...")
        
        # 添加因子
        builder.add_factor({
            'name': 'momentum_20',
            'type': 'technical',
            'weight': 0.6,
            'description': '20日动量因子'
        })
        
        builder.add_factor({
            'name': 'volatility_20',
            'type': 'technical',
            'weight': 0.4,
            'description': '20日波动率因子'
        })
        
        # 添加信号规则
        builder.add_signal_rule({
            'name': 'long_signal',
            'condition': 'composite_score > 0.5',
            'action': 'buy',
            'description': '多头信号'
        })
        
        builder.add_signal_rule({
            'name': 'short_signal',
            'condition': 'composite_score < -0.5',
            'action': 'sell',
            'description': '空头信号'
        })
        
        # 添加风险控制
        builder.add_risk_control({
            'name': 'position_limit',
            'type': 'position',
            'max_position': 0.1,
            'description': '单只股票最大仓位限制'
        })
        
        # 构建策略
        strategy = builder.build_strategy(
            'demo_strategy',
            '演示策略 - 基于动量和波动率的多因子策略'
        )
        
        print(f"✅ 策略构建完成: {strategy['name']}")
        print(f"   因子数量: {len(strategy['factors'])}")
        print(f"   信号规则: {len(strategy['signals'])}")
        print(f"   风险控制: {len(strategy['risk_controls'])}")
        
        # 验证策略
        is_valid, errors = builder.validate_strategy(strategy)
        if is_valid:
            print("✅ 策略验证通过")
        else:
            print("❌ 策略验证失败:")
            for error in errors:
                print(f"   - {error}")
        
        # 保存策略
        if strategy_manager.save_strategy(strategy):
            print("✅ 策略保存成功")
        
        # 从模板创建策略
        print("\n📋 从模板创建策略...")
        momentum_template = template_manager.get_template('momentum_strategy')
        if momentum_template:
            new_builder = StrategyBuilder()
            if template_manager.apply_template(new_builder, momentum_template):
                template_strategy = new_builder.build_strategy(
                    'momentum_from_template',
                    '基于动量模板创建的策略'
                )
                print(f"✅ 从模板创建策略成功: {template_strategy['name']}")
        
        return strategy
        
    except ImportError as e:
        print(f"❌ 导入策略构建模块失败: {e}")
        return None


def demo_visualization():
    """演示可视化功能"""
    print("\n" + "=" * 60)
    print("📊 可视化功能演示")
    print("=" * 60)
    
    try:
        from src.visualization.chart_generator import ChartGenerator
        from src.visualization.report_generator import ReportGenerator
        
        # 创建测试数据
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        test_data = pd.DataFrame({
            'date': dates,
            'value': np.cumsum(np.random.randn(50)) + 100,
            'category': np.random.choice(['A', 'B', 'C'], 50)
        })
        
        # 初始化图表生成器
        chart_gen = ChartGenerator()
        
        print("📈 生成图表...")
        
        # 创建线图
        line_chart = chart_gen.create_line_chart(
            test_data, 
            'date', 
            'value',
            title='时间序列图'
        )
        
        if line_chart:
            print("✅ 线图创建成功")
        
        # 创建柱状图
        category_data = test_data.groupby('category')['value'].mean().reset_index()
        bar_chart = chart_gen.create_bar_chart(
            category_data,
            'category',
            'value',
            title='分类柱状图'
        )
        
        if bar_chart:
            print("✅ 柱状图创建成功")
        
        # 创建相关性矩阵
        corr_data = pd.DataFrame(np.random.randn(20, 5), columns=['A', 'B', 'C', 'D', 'E'])
        correlation_matrix = corr_data.corr()
        
        heatmap = chart_gen.create_heatmap(
            correlation_matrix,
            title='相关性热力图'
        )
        
        if heatmap:
            print("✅ 热力图创建成功")
        
        # 初始化报告生成器
        report_gen = ReportGenerator()
        
        print("\n📋 生成报告...")
        
        # 创建因子分析报告
        factor_report = report_gen.create_factor_analysis_report(
            corr_data,
            title='演示因子分析报告'
        )
        
        if factor_report:
            print(f"✅ 因子分析报告创建成功，包含 {len(factor_report['sections'])} 个章节")
        
        # 创建策略报告
        strategy_data = {
            'name': 'demo_strategy',
            'description': '演示策略',
            'factors': ['momentum', 'volatility'],
            'performance': {
                'return': 0.15,
                'volatility': 0.12,
                'sharpe': 1.25
            }
        }
        
        strategy_report = report_gen.create_strategy_report(
            strategy_data,
            title='演示策略报告'
        )
        
        if strategy_report:
            print(f"✅ 策略报告创建成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入可视化模块失败: {e}")
        return False


def demo_performance_optimization():
    """演示性能优化功能"""
    print("\n" + "=" * 60)
    print("⚡ 性能优化功能演示")
    print("=" * 60)
    
    try:
        from src.performance.data_optimizer import DataOptimizer
        from src.performance.cache_manager import CacheManager
        from src.performance.parallel_processor import ParallelProcessor
        
        # 数据优化演示
        print("🔧 数据优化演示...")
        
        # 创建测试数据
        large_data = pd.DataFrame({
            'int_col': np.random.randint(0, 1000, 10000),
            'float_col': np.random.randn(10000),
            'str_col': [f'item_{i%100}' for i in range(10000)],
            'bool_col': np.random.choice([True, False], 10000)
        })
        
        optimizer = DataOptimizer()
        
        # 记录原始内存使用
        original_memory = large_data.memory_usage(deep=True).sum() / 1024 / 1024
        print(f"📊 原始数据内存使用: {original_memory:.2f} MB")
        
        # 优化数据类型
        optimized_data = optimizer.optimize_dtypes(large_data)
        optimized_memory = optimized_data.memory_usage(deep=True).sum() / 1024 / 1024
        reduction = (original_memory - optimized_memory) / original_memory * 100
        
        print(f"✅ 优化后内存使用: {optimized_memory:.2f} MB")
        print(f"💾 内存减少: {reduction:.1f}%")
        
        # 缓存管理演示
        print("\n🗄️ 缓存管理演示...")
        
        cache = CacheManager(max_size=100)
        
        # 设置缓存
        cache.set('test_data', large_data.head(100))
        cache.set('test_result', {'value': 42})
        
        # 获取缓存
        cached_data = cache.get('test_data')
        cached_result = cache.get('test_result')
        
        if cached_data is not None and cached_result is not None:
            print("✅ 缓存设置和获取成功")
            
        # 获取缓存统计
        stats = cache.get_stats()
        print(f"📈 缓存统计: 命中率={stats['hit_rate']:.2%}, 大小={stats['cache_size']}")
        
        # 并行处理演示
        print("\n🔄 并行处理演示...")
        
        processor = ParallelProcessor()
        
        # 创建测试任务
        def simple_task(x):
            return x ** 2
        
        test_data = list(range(100))
        
        # 串行处理
        import time
        start_time = time.time()
        serial_results = [simple_task(x) for x in test_data]
        serial_time = time.time() - start_time
        
        # 并行处理
        start_time = time.time()
        parallel_results = processor.process_with_threads(simple_task, test_data, max_workers=4)
        parallel_time = time.time() - start_time
        
        speedup = serial_time / parallel_time if parallel_time > 0 else 1
        print(f"⏱️ 串行处理时间: {serial_time:.4f}s")
        print(f"⚡ 并行处理时间: {parallel_time:.4f}s")
        print(f"🚀 加速比: {speedup:.2f}x")
        
        # 验证结果一致性
        if serial_results == parallel_results:
            print("✅ 并行处理结果正确")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入性能优化模块失败: {e}")
        return False


def main():
    """主演示函数"""
    print("🚀 Quantstrat Factory 功能演示")
    print("=" * 80)
    
    # 演示各个功能模块
    stock_data, factors = demo_technical_factors()
    strategy = demo_strategy_builder()
    demo_visualization()
    demo_performance_optimization()
    
    print("\n" + "=" * 80)
    print("🎉 演示完成！")
    print("\n📚 更多使用方法请参考:")
    print("   - USER_GUIDE.md: 详细使用指南")
    print("   - COMMAND_REFERENCE.md: 命令参考手册")
    print("   - tests/ 目录: 单元测试示例")
    print("\n💡 提示: 运行 pytest tests/ 可以执行所有测试")


if __name__ == "__main__":
    main()
