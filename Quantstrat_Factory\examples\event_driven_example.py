"""
事件驱动架构示例。

展示如何使用事件驱动架构来实现模块间的解耦通信。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.events import Event, EventBus, EventTypes, publish_event, subscribe_event
from core.container import Container, service
from core.interfaces import IDataProcessor, IFactorCalculator, ISignalGenerator
import pandas as pd
import numpy as np
import time
import logging
from typing import Dict, Any


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EventDrivenDataProcessor:
    """事件驱动的数据处理器。"""
    
    def __init__(self):
        self.name = "EventDrivenDataProcessor"
        # 订阅数据加载请求事件
        subscribe_event("data.load_request", self.handle_load_request)
        subscribe_event("data.process_request", self.handle_process_request)
    
    def handle_load_request(self, event: Event):
        """处理数据加载请求。"""
        logger.info(f"[{self.name}] 收到数据加载请求: {event.data}")
        
        # 模拟数据加载
        symbols = event.data.get('symbols', ['A', 'B', 'C'])
        dates = pd.date_range('2023-01-01', periods=100)
        
        data_list = []
        for date in dates:
            for symbol in symbols:
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'close': 100 + np.random.normal(0, 5),
                    'volume': np.random.randint(1000, 10000)
                })
        
        data = pd.DataFrame(data_list)
        
        # 发布数据加载完成事件
        publish_event(
            EventTypes.DATA_LOADED,
            {
                'data_shape': data.shape,
                'symbols': symbols,
                'data_id': event.correlation_id
            },
            self.name
        )
        
        # 发布数据处理请求
        publish_event(
            "data.process_request",
            {
                'data': data,
                'request_id': event.correlation_id
            },
            self.name
        )
    
    def handle_process_request(self, event: Event):
        """处理数据处理请求。"""
        logger.info(f"[{self.name}] 收到数据处理请求")
        
        data = event.data.get('data')
        if data is not None:
            # 模拟数据处理
            processed_data = data.copy()
            processed_data['returns'] = processed_data.groupby('symbol')['close'].pct_change()
            
            # 发布数据处理完成事件
            publish_event(
                EventTypes.DATA_PROCESSED,
                {
                    'data': processed_data,
                    'request_id': event.data.get('request_id')
                },
                self.name
            )


class EventDrivenFactorCalculator:
    """事件驱动的因子计算器。"""
    
    def __init__(self):
        self.name = "EventDrivenFactorCalculator"
        # 订阅数据处理完成事件
        subscribe_event(EventTypes.DATA_PROCESSED, self.handle_data_processed)
        subscribe_event("factor.calculate_request", self.handle_calculate_request)
    
    def handle_data_processed(self, event: Event):
        """处理数据处理完成事件。"""
        logger.info(f"[{self.name}] 收到数据处理完成通知")
        
        # 自动触发因子计算
        publish_event(
            "factor.calculate_request",
            {
                'data': event.data.get('data'),
                'request_id': event.data.get('request_id')
            },
            self.name
        )
    
    def handle_calculate_request(self, event: Event):
        """处理因子计算请求。"""
        logger.info(f"[{self.name}] 开始计算因子")
        
        # 发布因子计算开始事件
        publish_event(
            EventTypes.FACTOR_CALCULATION_STARTED,
            {'request_id': event.data.get('request_id')},
            self.name
        )
        
        data = event.data.get('data')
        if data is not None:
            # 模拟因子计算
            factor_data = data.copy()
            
            # 计算动量因子
            factor_data['momentum'] = factor_data.groupby('symbol')['returns'].rolling(20).mean().reset_index(0, drop=True)
            
            # 计算波动率因子
            factor_data['volatility'] = factor_data.groupby('symbol')['returns'].rolling(20).std().reset_index(0, drop=True)
            
            # 发布因子计算完成事件
            publish_event(
                EventTypes.FACTOR_CALCULATION_COMPLETED,
                {
                    'factor_data': factor_data,
                    'factors': ['momentum', 'volatility'],
                    'request_id': event.data.get('request_id')
                },
                self.name
            )


class EventDrivenSignalGenerator:
    """事件驱动的信号生成器。"""
    
    def __init__(self):
        self.name = "EventDrivenSignalGenerator"
        # 订阅因子计算完成事件
        subscribe_event(EventTypes.FACTOR_CALCULATION_COMPLETED, self.handle_factor_completed)
    
    def handle_factor_completed(self, event: Event):
        """处理因子计算完成事件。"""
        logger.info(f"[{self.name}] 收到因子计算完成通知")
        
        factor_data = event.data.get('factor_data')
        if factor_data is not None:
            # 生成信号
            signal_data = factor_data.copy()
            
            # 基于动量因子生成信号
            signal_data['signal'] = 0
            momentum_threshold = 0.01
            
            signal_data.loc[signal_data['momentum'] > momentum_threshold, 'signal'] = 1
            signal_data.loc[signal_data['momentum'] < -momentum_threshold, 'signal'] = -1
            
            # 发布信号生成完成事件
            publish_event(
                EventTypes.SIGNAL_GENERATED,
                {
                    'signal_data': signal_data,
                    'signal_count': (signal_data['signal'] != 0).sum(),
                    'request_id': event.data.get('request_id')
                },
                self.name
            )


class EventDrivenBacktester:
    """事件驱动的回测器。"""
    
    def __init__(self):
        self.name = "EventDrivenBacktester"
        # 订阅信号生成完成事件
        subscribe_event(EventTypes.SIGNAL_GENERATED, self.handle_signal_generated)
    
    def handle_signal_generated(self, event: Event):
        """处理信号生成完成事件。"""
        logger.info(f"[{self.name}] 收到信号生成完成通知")
        
        signal_data = event.data.get('signal_data')
        signal_count = event.data.get('signal_count', 0)
        
        if signal_data is not None:
            # 发布回测开始事件
            publish_event(
                EventTypes.BACKTEST_STARTED,
                {
                    'signal_count': signal_count,
                    'request_id': event.data.get('request_id')
                },
                self.name
            )
            
            # 模拟回测计算
            time.sleep(0.1)  # 模拟计算时间
            
            # 计算简单的性能指标
            signals = signal_data[signal_data['signal'] != 0]
            performance = {
                'total_signals': len(signals),
                'long_signals': (signals['signal'] > 0).sum(),
                'short_signals': (signals['signal'] < 0).sum(),
                'avg_return': signals['returns'].mean() if len(signals) > 0 else 0
            }
            
            # 发布回测完成事件
            publish_event(
                EventTypes.BACKTEST_COMPLETED,
                {
                    'performance': performance,
                    'request_id': event.data.get('request_id')
                },
                self.name
            )


class WorkflowOrchestrator:
    """工作流编排器。"""
    
    def __init__(self):
        self.name = "WorkflowOrchestrator"
        self.active_requests = {}
        
        # 订阅关键事件来跟踪工作流进度
        subscribe_event(EventTypes.DATA_LOADED, self.track_progress)
        subscribe_event(EventTypes.DATA_PROCESSED, self.track_progress)
        subscribe_event(EventTypes.FACTOR_CALCULATION_COMPLETED, self.track_progress)
        subscribe_event(EventTypes.SIGNAL_GENERATED, self.track_progress)
        subscribe_event(EventTypes.BACKTEST_COMPLETED, self.handle_workflow_completed)
    
    def start_workflow(self, symbols: list, request_id: str = None):
        """启动工作流。"""
        if request_id is None:
            request_id = f"workflow_{int(time.time())}"
        
        self.active_requests[request_id] = {
            'start_time': time.time(),
            'symbols': symbols,
            'status': 'started'
        }
        
        logger.info(f"[{self.name}] 启动工作流: {request_id}")
        
        # 发布数据加载请求
        publish_event(
            "data.load_request",
            {
                'symbols': symbols,
                'request_id': request_id
            },
            self.name
        )
        
        return request_id
    
    def track_progress(self, event: Event):
        """跟踪工作流进度。"""
        request_id = event.data.get('request_id')
        if request_id in self.active_requests:
            logger.info(f"[{self.name}] 工作流进度 {request_id}: {event.event_type}")
    
    def handle_workflow_completed(self, event: Event):
        """处理工作流完成。"""
        request_id = event.data.get('request_id')
        if request_id in self.active_requests:
            request_info = self.active_requests[request_id]
            duration = time.time() - request_info['start_time']
            
            logger.info(f"[{self.name}] 工作流完成 {request_id}, 耗时: {duration:.2f}秒")
            logger.info(f"[{self.name}] 回测结果: {event.data.get('performance')}")
            
            del self.active_requests[request_id]


def run_event_driven_example():
    """运行事件驱动架构示例。"""
    print("=== 事件驱动架构示例 ===")
    
    # 创建组件实例
    data_processor = EventDrivenDataProcessor()
    factor_calculator = EventDrivenFactorCalculator()
    signal_generator = EventDrivenSignalGenerator()
    backtester = EventDrivenBacktester()
    orchestrator = WorkflowOrchestrator()
    
    # 启动工作流
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    request_id = orchestrator.start_workflow(symbols)
    
    # 等待工作流完成
    print("等待工作流完成...")
    time.sleep(3.0)
    
    print("✅ 事件驱动架构示例完成")


if __name__ == "__main__":
    run_event_driven_example()
