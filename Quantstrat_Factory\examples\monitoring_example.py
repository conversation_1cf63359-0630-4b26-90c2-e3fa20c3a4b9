"""
监控系统示例。

展示如何使用监控和日志系统来监控应用性能和健康状态。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from monitoring.logger import setup_logging, get_logger
from monitoring.metrics import MetricsCollector, track_performance, record_metric
from monitoring.alerts import AlertManager, AlertRule, AlertSeverity, SlackNotificationChannel
import time
import random
import threading
import json


def simulate_application_workload():
    """模拟应用工作负载。"""
    logger = get_logger("workload_simulator")
    
    @track_performance("data_processing")
    def process_data_batch(batch_size: int):
        """模拟数据处理。"""
        processing_time = random.uniform(0.1, 2.0)  # 随机处理时间
        time.sleep(processing_time)
        
        # 记录处理指标
        record_metric("batch_size", batch_size, {"operation": "data_processing"})
        record_metric("processing_rate", batch_size / processing_time, {"operation": "data_processing"})
        
        logger.info("数据批次处理完成", 
                   batch_size=batch_size, 
                   processing_time=processing_time)
        
        return f"处理了 {batch_size} 条记录"
    
    @track_performance("factor_calculation")
    def calculate_factors(symbol_count: int):
        """模拟因子计算。"""
        calculation_time = random.uniform(0.5, 3.0)
        time.sleep(calculation_time)
        
        # 模拟计算错误
        if random.random() < 0.1:  # 10% 概率出错
            logger.error("因子计算失败", 
                        symbol_count=symbol_count,
                        error_type="calculation_error")
            raise Exception("因子计算失败")
        
        record_metric("symbols_processed", symbol_count, {"operation": "factor_calculation"})
        record_metric("calculation_speed", symbol_count / calculation_time, {"operation": "factor_calculation"})
        
        logger.info("因子计算完成", 
                   symbol_count=symbol_count,
                   calculation_time=calculation_time)
        
        return f"计算了 {symbol_count} 只股票的因子"
    
    # 模拟工作负载
    for i in range(20):
        try:
            # 数据处理
            batch_size = random.randint(100, 1000)
            process_data_batch(batch_size)
            
            # 因子计算
            symbol_count = random.randint(10, 100)
            calculate_factors(symbol_count)
            
            # 记录API请求
            record_metric("api_requests", 1, {"endpoint": "/api/v1/factors"})
            
            # 模拟用户活动
            active_users = random.randint(50, 200)
            record_metric("active_users", active_users)
            
            time.sleep(random.uniform(1, 3))
            
        except Exception as e:
            logger.error("工作负载执行失败", error=str(e))
            record_metric("error_count", 1, {"error_type": "workload_error"})


def setup_monitoring_system():
    """设置监控系统。"""
    # 1. 设置日志系统
    setup_logging(
        level="INFO",
        log_file="logs/quantstrat.log",
        log_format="json"
    )
    
    # 2. 启动指标收集
    metrics_collector = MetricsCollector(
        collection_interval=30,  # 30秒收集一次
        storage_path="logs/metrics.jsonl"
    )
    metrics_collector.start()
    
    # 3. 设置告警系统
    alert_manager = AlertManager()
    
    # 添加告警规则
    alert_rules = [
        AlertRule(
            name="high_error_rate",
            description="错误率过高",
            metric_name="error_count",
            condition="gt",
            threshold=5.0,
            severity=AlertSeverity.ERROR,
            duration=60,
            cooldown=300
        ),
        AlertRule(
            name="slow_processing",
            description="数据处理速度过慢",
            metric_name="processing_rate",
            condition="lt",
            threshold=100.0,
            severity=AlertSeverity.WARNING,
            duration=120,
            cooldown=600
        ),
        AlertRule(
            name="low_active_users",
            description="活跃用户数过低",
            metric_name="active_users",
            condition="lt",
            threshold=20.0,
            severity=AlertSeverity.INFO,
            duration=300,
            cooldown=1800
        )
    ]
    
    for rule in alert_rules:
        alert_manager.add_rule(rule)
    
    # 添加通知渠道（这里使用模拟的Slack通知）
    # 在实际使用中，需要提供真实的Webhook URL
    # slack_channel = SlackNotificationChannel("https://hooks.slack.com/services/...")
    # alert_manager.add_notification_channel(slack_channel)
    
    return metrics_collector, alert_manager


def monitor_metrics(alert_manager: AlertManager):
    """监控指标并检查告警。"""
    logger = get_logger("metric_monitor")
    
    # 模拟指标监控循环
    for i in range(10):
        # 模拟一些指标值
        error_count = random.randint(0, 10)
        processing_rate = random.uniform(50, 200)
        active_users = random.randint(10, 150)
        
        # 检查告警
        alert_manager.check_metric("error_count", error_count)
        alert_manager.check_metric("processing_rate", processing_rate)
        alert_manager.check_metric("active_users", active_users)
        
        logger.info("指标检查完成",
                   error_count=error_count,
                   processing_rate=processing_rate,
                   active_users=active_users)
        
        time.sleep(30)  # 30秒检查一次


def run_monitoring_example():
    """运行监控系统示例。"""
    print("=== 监控系统示例 ===")
    
    # 设置监控系统
    print("\\n--- 设置监控系统 ---")
    metrics_collector, alert_manager = setup_monitoring_system()
    
    logger = get_logger("monitoring_example")
    logger.info("监控系统示例启动", version="1.0.0")
    
    # 启动工作负载模拟线程
    print("\\n--- 启动应用工作负载模拟 ---")
    workload_thread = threading.Thread(target=simulate_application_workload, daemon=True)
    workload_thread.start()
    
    # 启动指标监控线程
    print("\\n--- 启动指标监控 ---")
    monitor_thread = threading.Thread(target=monitor_metrics, args=(alert_manager,), daemon=True)
    monitor_thread.start()
    
    # 运行一段时间
    print("\\n--- 监控运行中... ---")
    time.sleep(60)  # 运行1分钟
    
    # 获取监控结果
    print("\\n--- 监控结果分析 ---")
    
    # 1. 指标摘要
    metrics_summary = metrics_collector.get_metrics_summary()
    print("\\n指标摘要:")
    print(f"  系统CPU使用率: {metrics_summary['system_metrics']['cpu_percent']:.1f}%")
    print(f"  系统内存使用率: {metrics_summary['system_metrics']['memory_percent']:.1f}%")
    print(f"  自定义指标数量: {len(metrics_summary['custom_metrics'])}")
    print(f"  指标队列大小: {metrics_summary['queue_size']}")
    
    # 2. 告警统计
    alert_stats = alert_manager.get_alert_statistics()
    print("\\n告警统计:")
    print(f"  活跃告警数量: {alert_stats['active_alerts_count']}")
    print(f"  24小时内告警总数: {alert_stats['total_alerts_last_24h']}")
    print(f"  严重程度分布: {alert_stats['severity_breakdown']}")
    
    # 3. 活跃告警详情
    active_alerts = alert_manager.get_active_alerts()
    if active_alerts:
        print("\\n活跃告警:")
        for alert in active_alerts:
            print(f"  - {alert.rule_name}: {alert.message}")
    else:
        print("\\n当前无活跃告警")
    
    # 4. 告警历史
    alert_history = alert_manager.get_alert_history(hours=1)
    if alert_history:
        print(f"\\n最近1小时告警历史 ({len(alert_history)} 条):")
        for alert in alert_history[-5:]:  # 显示最近5条
            print(f"  - {alert.timestamp.strftime('%H:%M:%S')} [{alert.severity.value}] {alert.rule_name}")
    
    # 停止监控
    print("\\n--- 停止监控系统 ---")
    metrics_collector.stop()
    
    logger.info("监控系统示例结束")
    print("\\n✅ 监控系统示例完成")


if __name__ == "__main__":
    run_monitoring_example()
