"""
优化器模块示例。

展示如何使用不同的优化器来优化策略参数。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

import pandas as pd
import numpy as np
import time
import logging


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_strategy_data():
    """创建示例策略数据。"""
    np.random.seed(42)
    
    dates = pd.date_range('2023-01-01', periods=252)  # 一年的交易日
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    
    data_list = []
    for date in dates:
        for symbol in symbols:
            # 模拟价格数据
            base_price = {'AAPL': 150, 'GOOGL': 2500, 'MSFT': 300}[symbol]
            price = base_price * (1 + np.random.normal(0, 0.02))
            
            data_list.append({
                'datetime': date,
                'symbol': symbol,
                'close': price,
                'volume': np.random.randint(1000000, 10000000),
                'returns': np.random.normal(0.001, 0.02)  # 模拟收益率
            })
    
    return pd.DataFrame(data_list)


def moving_average_strategy(data: pd.DataFrame, 
                          short_window: int = 10,
                          long_window: int = 30,
                          position_size: float = 0.1) -> dict:
    """
    移动平均策略示例。
    
    Args:
        data: 市场数据
        short_window: 短期移动平均窗口
        long_window: 长期移动平均窗口
        position_size: 仓位大小
        
    Returns:
        策略性能指标
    """
    results = []
    
    for symbol in data['symbol'].unique():
        symbol_data = data[data['symbol'] == symbol].copy()
        symbol_data = symbol_data.sort_values('datetime')
        
        # 计算移动平均线
        symbol_data['short_ma'] = symbol_data['close'].rolling(short_window).mean()
        symbol_data['long_ma'] = symbol_data['close'].rolling(long_window).mean()
        
        # 生成信号
        symbol_data['signal'] = 0
        symbol_data.loc[symbol_data['short_ma'] > symbol_data['long_ma'], 'signal'] = 1
        symbol_data.loc[symbol_data['short_ma'] < symbol_data['long_ma'], 'signal'] = -1
        
        # 计算策略收益
        symbol_data['strategy_return'] = symbol_data['signal'].shift(1) * symbol_data['returns'] * position_size
        
        results.append(symbol_data)
    
    # 合并结果
    all_results = pd.concat(results, ignore_index=True)
    
    # 计算性能指标
    strategy_returns = all_results.groupby('datetime')['strategy_return'].sum()
    
    total_return = strategy_returns.sum()
    volatility = strategy_returns.std()
    sharpe_ratio = strategy_returns.mean() / volatility if volatility > 0 else 0
    
    # 计算最大回撤
    cumulative_returns = (1 + strategy_returns).cumprod()
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min()
    
    return {
        'total_return': total_return,
        'sharpe_ratio': sharpe_ratio,
        'volatility': volatility,
        'max_drawdown': max_drawdown,
        'win_rate': (strategy_returns > 0).mean()
    }


def run_optimizer_example():
    """运行优化器示例。"""
    print("=== 优化器模块示例 ===")
    
    # 创建示例数据
    print("\\n--- 创建示例数据 ---")
    data = create_sample_strategy_data()
    print(f"数据形状: {data.shape}")
    print(f"日期范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
    
    # 定义目标函数
    def objective_function(params):
        """目标函数：最大化夏普比率。"""
        try:
            result = moving_average_strategy(
                data,
                short_window=params['short_window'],
                long_window=params['long_window'],
                position_size=params['position_size']
            )
            return result['sharpe_ratio']
        except Exception as e:
            logger.error(f"目标函数执行失败: {e}")
            return -999  # 返回很差的分数
    
    # 由于我们没有实现完整的优化器，这里使用简化版本
    print("\\n--- 简化版网格搜索优化 ---")
    
    # 定义参数网格
    short_windows = [5, 10, 15, 20]
    long_windows = [20, 30, 40, 50]
    position_sizes = [0.05, 0.1, 0.15]
    
    best_params = None
    best_score = float('-inf')
    all_results = []
    
    total_combinations = len(short_windows) * len(long_windows) * len(position_sizes)
    print(f"总参数组合数: {total_combinations}")
    
    start_time = time.time()
    
    for i, short_window in enumerate(short_windows):
        for j, long_window in enumerate(long_windows):
            for k, position_size in enumerate(position_sizes):
                # 确保短期窗口小于长期窗口
                if short_window >= long_window:
                    continue
                
                params = {
                    'short_window': short_window,
                    'long_window': long_window,
                    'position_size': position_size
                }
                
                # 评估参数组合
                score = objective_function(params)
                
                # 记录结果
                result_record = {
                    'params': params.copy(),
                    'score': score,
                    'short_window': short_window,
                    'long_window': long_window,
                    'position_size': position_size
                }
                all_results.append(result_record)
                
                # 更新最佳结果
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    print(f"发现更好的参数: {params}, 夏普比率: {score:.4f}")
    
    optimization_time = time.time() - start_time
    
    print(f"\\n--- 优化结果 ---")
    print(f"最佳参数: {best_params}")
    print(f"最佳夏普比率: {best_score:.4f}")
    print(f"优化耗时: {optimization_time:.2f}秒")
    print(f"评估次数: {len(all_results)}")
    
    # 分析优化结果
    print("\\n--- 结果分析 ---")
    
    # 转换为DataFrame进行分析
    results_df = pd.DataFrame(all_results)
    
    # 按参数分析
    print("\\n短期窗口对性能的影响:")
    short_window_analysis = results_df.groupby('short_window')['score'].agg(['mean', 'std', 'max'])
    print(short_window_analysis)
    
    print("\\n长期窗口对性能的影响:")
    long_window_analysis = results_df.groupby('long_window')['score'].agg(['mean', 'std', 'max'])
    print(long_window_analysis)
    
    print("\\n仓位大小对性能的影响:")
    position_analysis = results_df.groupby('position_size')['score'].agg(['mean', 'std', 'max'])
    print(position_analysis)
    
    # 获取前5个最佳参数组合
    print("\\n--- 前5个最佳参数组合 ---")
    top_5 = results_df.nlargest(5, 'score')
    for idx, row in top_5.iterrows():
        print(f"排名 {idx+1}: {row['params']}, 夏普比率: {row['score']:.4f}")
    
    # 测试最佳参数的详细性能
    print("\\n--- 最佳参数详细性能 ---")
    detailed_result = moving_average_strategy(data, **best_params)
    
    print(f"总收益率: {detailed_result['total_return']:.4f}")
    print(f"夏普比率: {detailed_result['sharpe_ratio']:.4f}")
    print(f"波动率: {detailed_result['volatility']:.4f}")
    print(f"最大回撤: {detailed_result['max_drawdown']:.4f}")
    print(f"胜率: {detailed_result['win_rate']:.2%}")
    
    # 随机搜索对比
    print("\\n--- 随机搜索对比 ---")
    
    np.random.seed(42)
    random_results = []
    n_random_trials = 50
    
    start_time = time.time()
    
    for i in range(n_random_trials):
        # 随机采样参数
        short_window = np.random.randint(5, 21)
        long_window = np.random.randint(short_window + 5, 51)
        position_size = np.random.uniform(0.01, 0.2)
        
        params = {
            'short_window': short_window,
            'long_window': long_window,
            'position_size': position_size
        }
        
        score = objective_function(params)
        random_results.append({'params': params, 'score': score})
    
    random_time = time.time() - start_time
    
    # 找到随机搜索的最佳结果
    best_random = max(random_results, key=lambda x: x['score'])
    
    print(f"随机搜索最佳参数: {best_random['params']}")
    print(f"随机搜索最佳夏普比率: {best_random['score']:.4f}")
    print(f"随机搜索耗时: {random_time:.2f}秒")
    
    # 比较两种方法
    print("\\n--- 方法比较 ---")
    print(f"网格搜索 - 最佳得分: {best_score:.4f}, 耗时: {optimization_time:.2f}秒, 评估次数: {len(all_results)}")
    print(f"随机搜索 - 最佳得分: {best_random['score']:.4f}, 耗时: {random_time:.2f}秒, 评估次数: {n_random_trials}")
    
    improvement = (best_score - best_random['score']) / abs(best_random['score']) * 100
    print(f"网格搜索相对随机搜索的改进: {improvement:.2f}%")
    
    print("\\n✅ 优化器模块示例完成")


if __name__ == "__main__":
    run_optimizer_example()
