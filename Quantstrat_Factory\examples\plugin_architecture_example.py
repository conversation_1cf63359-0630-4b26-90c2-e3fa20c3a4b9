"""
插件化架构示例。

展示如何使用插件系统来构建可扩展的量化交易系统。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.plugins import PluginManager, IFactorPlugin, IStrategyPlugin
import pandas as pd
import numpy as np
import logging


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_sample_data():
    """创建示例数据。"""
    np.random.seed(42)
    
    dates = pd.date_range('2023-01-01', periods=100)
    symbols = ['AAPL', 'GOOGL', 'MSFT']
    
    data_list = []
    for date in dates:
        for symbol in symbols:
            base_price = {'AAPL': 150, 'GOOGL': 2500, 'MSFT': 300}[symbol]
            price = base_price + np.random.normal(0, base_price * 0.02)
            
            data_list.append({
                'datetime': date,
                'symbol': symbol,
                'open': price * (1 + np.random.normal(0, 0.005)),
                'high': price * (1 + abs(np.random.normal(0, 0.01))),
                'low': price * (1 - abs(np.random.normal(0, 0.01))),
                'close': price,
                'volume': np.random.randint(1000000, 10000000)
            })
    
    return pd.DataFrame(data_list)


def run_plugin_architecture_example():
    """运行插件化架构示例。"""
    print("=== 插件化架构示例 ===")
    
    # 创建插件管理器
    plugin_manager = PluginManager()
    
    # 手动注册插件（在实际应用中可以通过discover_plugins自动发现）
    from plugins.momentum_factor_plugin import MomentumFactorPlugin, VolatilityFactorPlugin
    from plugins.mean_reversion_strategy_plugin import MeanReversionStrategyPlugin, TrendFollowingStrategyPlugin
    
    plugin_manager.registry.register_plugin(MomentumFactorPlugin)
    plugin_manager.registry.register_plugin(VolatilityFactorPlugin)
    plugin_manager.registry.register_plugin(MeanReversionStrategyPlugin)
    plugin_manager.registry.register_plugin(TrendFollowingStrategyPlugin)
    
    # 列出所有插件
    print("\\n--- 已注册的插件 ---")
    plugins = plugin_manager.registry.list_plugins()
    for name, metadata in plugins.items():
        print(f"- {name} v{metadata.version} ({metadata.category}): {metadata.description}")
    
    # 创建示例数据
    print("\\n--- 创建示例数据 ---")
    data = create_sample_data()
    print(f"数据形状: {data.shape}")
    print(f"股票代码: {data['symbol'].unique().tolist()}")
    print(f"日期范围: {data['datetime'].min()} 到 {data['datetime'].max()}")
    
    # 加载和使用因子插件
    print("\\n--- 使用因子插件 ---")
    
    # 加载动量因子插件
    momentum_config = {
        "short_window": 5,
        "long_window": 20,
        "enable_volatility_adjustment": True
    }
    momentum_plugin = plugin_manager.registry.load_plugin("momentum_factor", momentum_config)
    plugin_manager.registry.activate_plugin("momentum_factor")
    
    # 计算动量因子
    factor_data = momentum_plugin.calculate_factor(data)
    print(f"动量因子计算完成，新增列: {momentum_plugin.get_factor_names()}")
    
    # 加载波动率因子插件
    volatility_config = {
        "window": 20,
        "method": "std"
    }
    volatility_plugin = plugin_manager.registry.load_plugin("volatility_factor", volatility_config)
    plugin_manager.registry.activate_plugin("volatility_factor")
    
    # 计算波动率因子
    factor_data = volatility_plugin.calculate_factor(factor_data)
    print(f"波动率因子计算完成，新增列: {volatility_plugin.get_factor_names()}")
    
    # 使用策略插件
    print("\\n--- 使用策略插件 ---")
    
    # 加载均值回归策略插件
    mean_reversion_config = {
        "lookback_window": 20,
        "entry_threshold": 1.5,
        "exit_threshold": 0.5,
        "max_position_size": 0.1
    }
    mr_strategy = plugin_manager.registry.load_plugin("mean_reversion_strategy", mean_reversion_config)
    plugin_manager.registry.activate_plugin("mean_reversion_strategy")
    
    # 生成均值回归信号
    mr_signals = mr_strategy.generate_signals(factor_data)
    mr_signal_count = (mr_signals['signal'] != 0).sum()
    print(f"均值回归策略生成信号: {mr_signal_count} 个")
    
    # 加载趋势跟踪策略插件
    trend_config = {
        "fast_ma": 10,
        "slow_ma": 30,
        "momentum_threshold": 0.005,
        "position_size": 0.05
    }
    trend_strategy = plugin_manager.registry.load_plugin("trend_following_strategy", trend_config)
    plugin_manager.registry.activate_plugin("trend_following_strategy")
    
    # 生成趋势跟踪信号
    trend_signals = trend_strategy.generate_signals(factor_data)
    trend_signal_count = (trend_signals['signal'] != 0).sum()
    print(f"趋势跟踪策略生成信号: {trend_signal_count} 个")
    
    # 分析信号质量
    print("\\n--- 信号分析 ---")
    
    # 均值回归策略分析
    mr_long_signals = (mr_signals['signal'] > 0).sum()
    mr_short_signals = (mr_signals['signal'] < 0).sum()
    print(f"均值回归策略 - 做多信号: {mr_long_signals}, 做空信号: {mr_short_signals}")
    
    # 趋势跟踪策略分析
    trend_long_signals = (trend_signals['signal'] > 0).sum()
    trend_short_signals = (trend_signals['signal'] < 0).sum()
    print(f"趋势跟踪策略 - 做多信号: {trend_long_signals}, 做空信号: {trend_short_signals}")
    
    # 展示插件管理功能
    print("\\n--- 插件管理功能 ---")
    
    # 获取插件统计信息
    factor_plugins = plugin_manager.registry.get_plugins_by_category("factor")
    strategy_plugins = plugin_manager.registry.get_plugins_by_category("strategy")
    
    print(f"因子插件数量: {len(factor_plugins)}")
    print(f"策略插件数量: {len(strategy_plugins)}")
    
    # 获取插件参数
    mr_params = mr_strategy.get_parameters()
    trend_params = trend_strategy.get_parameters()
    
    print(f"均值回归策略参数: {mr_params}")
    print(f"趋势跟踪策略参数: {trend_params}")
    
    # 停用和卸载插件
    print("\\n--- 插件生命周期管理 ---")
    
    plugin_manager.registry.deactivate_plugin("mean_reversion_strategy")
    plugin_manager.registry.deactivate_plugin("trend_following_strategy")
    plugin_manager.registry.deactivate_plugin("momentum_factor")
    plugin_manager.registry.deactivate_plugin("volatility_factor")
    
    plugin_manager.registry.unload_plugin("mean_reversion_strategy")
    plugin_manager.registry.unload_plugin("trend_following_strategy")
    plugin_manager.registry.unload_plugin("momentum_factor")
    plugin_manager.registry.unload_plugin("volatility_factor")
    
    print("\\n--- 插件组合示例 ---")
    
    # 重新加载插件进行组合使用
    momentum_plugin = plugin_manager.registry.load_plugin("momentum_factor", momentum_config)
    plugin_manager.registry.activate_plugin("momentum_factor")
    
    volatility_plugin = plugin_manager.registry.load_plugin("volatility_factor", volatility_config)
    plugin_manager.registry.activate_plugin("volatility_factor")
    
    # 组合使用多个因子插件
    combined_data = data.copy()
    combined_data = momentum_plugin.calculate_factor(combined_data)
    combined_data = volatility_plugin.calculate_factor(combined_data)
    
    # 使用组合因子数据生成信号
    mr_strategy = plugin_manager.registry.load_plugin("mean_reversion_strategy", mean_reversion_config)
    plugin_manager.registry.activate_plugin("mean_reversion_strategy")
    
    final_signals = mr_strategy.generate_signals(combined_data)
    final_signal_count = (final_signals['signal'] != 0).sum()
    
    print(f"使用组合因子的信号数量: {final_signal_count}")
    
    # 展示最终结果
    print("\\n--- 最终结果摘要 ---")
    print(f"处理数据行数: {len(final_signals)}")
    print(f"包含因子: {momentum_plugin.get_factor_names() + volatility_plugin.get_factor_names()}")
    print(f"生成信号数量: {final_signal_count}")
    print(f"信号覆盖率: {final_signal_count / len(final_signals):.2%}")
    
    print("\\n✅ 插件化架构示例完成")


if __name__ == "__main__":
    run_plugin_architecture_example()
