"""
简化版监控系统示例。

展示基本的监控和日志功能。
"""

import logging
import time
import threading
import random
import json
import psutil
from datetime import datetime
from pathlib import Path


# 设置日志
def setup_simple_logging():
    """设置简单的日志系统。"""
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/simple_monitoring.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )


class SimpleMetricsCollector:
    """简单的指标收集器。"""
    
    def __init__(self):
        self.metrics = {}
        self.is_running = False
        self.collection_thread = None
        self.logger = logging.getLogger("MetricsCollector")
    
    def start(self):
        """启动指标收集。"""
        if self.is_running:
            return
        
        self.is_running = True
        self.collection_thread = threading.Thread(target=self._collection_loop, daemon=True)
        self.collection_thread.start()
        self.logger.info("指标收集器已启动")
    
    def stop(self):
        """停止指标收集。"""
        self.is_running = False
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        self.logger.info("指标收集器已停止")
    
    def record_metric(self, name: str, value: float):
        """记录指标。"""
        self.metrics[name] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
    
    def get_system_metrics(self):
        """获取系统指标。"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': memory.used / 1024 / 1024,
                'disk_usage_percent': (disk.used / disk.total) * 100,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"获取系统指标失败: {e}")
            return {}
    
    def _collection_loop(self):
        """指标收集循环。"""
        while self.is_running:
            try:
                # 收集系统指标
                system_metrics = self.get_system_metrics()
                for key, value in system_metrics.items():
                    if key != 'timestamp' and isinstance(value, (int, float)):
                        self.record_metric(f"system_{key}", value)
                
                time.sleep(30)  # 30秒收集一次
                
            except Exception as e:
                self.logger.error(f"指标收集失败: {e}")
                time.sleep(30)
    
    def get_metrics_summary(self):
        """获取指标摘要。"""
        return {
            'total_metrics': len(self.metrics),
            'latest_metrics': dict(list(self.metrics.items())[-10:]),  # 最近10个指标
            'system_status': self.get_system_metrics()
        }


class SimpleAlertManager:
    """简单的告警管理器。"""
    
    def __init__(self):
        self.alert_rules = {}
        self.active_alerts = {}
        self.alert_history = []
        self.logger = logging.getLogger("AlertManager")
    
    def add_rule(self, name: str, metric_name: str, condition: str, threshold: float):
        """添加告警规则。"""
        self.alert_rules[name] = {
            'metric_name': metric_name,
            'condition': condition,
            'threshold': threshold,
            'last_triggered': None
        }
        self.logger.info(f"添加告警规则: {name}")
    
    def check_metric(self, metric_name: str, value: float):
        """检查指标是否触发告警。"""
        for rule_name, rule in self.alert_rules.items():
            if rule['metric_name'] != metric_name:
                continue
            
            triggered = False
            if rule['condition'] == 'gt' and value > rule['threshold']:
                triggered = True
            elif rule['condition'] == 'lt' and value < rule['threshold']:
                triggered = True
            
            if triggered:
                self._trigger_alert(rule_name, metric_name, value, rule['threshold'])
    
    def _trigger_alert(self, rule_name: str, metric_name: str, value: float, threshold: float):
        """触发告警。"""
        alert = {
            'rule_name': rule_name,
            'metric_name': metric_name,
            'value': value,
            'threshold': threshold,
            'timestamp': datetime.now().isoformat(),
            'message': f"{rule_name}: {metric_name}={value} (阈值: {threshold})"
        }
        
        self.active_alerts[rule_name] = alert
        self.alert_history.append(alert)
        
        self.logger.warning(f"告警触发: {alert['message']}")
    
    def get_alert_summary(self):
        """获取告警摘要。"""
        return {
            'active_alerts_count': len(self.active_alerts),
            'total_alerts': len(self.alert_history),
            'recent_alerts': self.alert_history[-5:] if self.alert_history else []
        }


def simulate_workload(metrics_collector: SimpleMetricsCollector):
    """模拟应用工作负载。"""
    logger = logging.getLogger("WorkloadSimulator")
    
    for i in range(20):
        try:
            # 模拟数据处理
            batch_size = random.randint(100, 1000)
            processing_time = random.uniform(0.1, 2.0)
            
            logger.info(f"处理数据批次: {batch_size} 条记录, 耗时: {processing_time:.2f}秒")
            
            # 记录指标
            metrics_collector.record_metric("batch_size", batch_size)
            metrics_collector.record_metric("processing_time", processing_time)
            metrics_collector.record_metric("processing_rate", batch_size / processing_time)
            
            # 模拟错误
            if random.random() < 0.1:  # 10% 概率出错
                logger.error("数据处理失败")
                metrics_collector.record_metric("error_count", 1)
            
            # 模拟API请求
            api_requests = random.randint(10, 100)
            metrics_collector.record_metric("api_requests", api_requests)
            
            time.sleep(random.uniform(1, 3))
            
        except Exception as e:
            logger.error(f"工作负载执行失败: {e}")


def run_simple_monitoring_example():
    """运行简化版监控示例。"""
    print("=== 简化版监控系统示例 ===")
    
    # 设置日志
    setup_simple_logging()
    logger = logging.getLogger("MonitoringExample")
    logger.info("监控系统示例启动")
    
    # 创建监控组件
    metrics_collector = SimpleMetricsCollector()
    alert_manager = SimpleAlertManager()
    
    # 添加告警规则
    alert_manager.add_rule("high_cpu", "system_cpu_percent", "gt", 80.0)
    alert_manager.add_rule("high_memory", "system_memory_percent", "gt", 85.0)
    alert_manager.add_rule("slow_processing", "processing_rate", "lt", 100.0)
    
    # 启动指标收集
    metrics_collector.start()
    
    # 启动工作负载模拟
    workload_thread = threading.Thread(target=simulate_workload, args=(metrics_collector,), daemon=True)
    workload_thread.start()
    
    # 监控循环
    print("\\n--- 监控运行中... ---")
    for i in range(6):  # 运行3分钟（每30秒检查一次）
        time.sleep(30)
        
        # 检查指标和告警
        metrics_summary = metrics_collector.get_metrics_summary()
        
        # 检查系统指标告警
        system_status = metrics_summary['system_status']
        if system_status:
            alert_manager.check_metric("system_cpu_percent", system_status.get('cpu_percent', 0))
            alert_manager.check_metric("system_memory_percent", system_status.get('memory_percent', 0))
        
        # 检查应用指标告警
        latest_metrics = metrics_summary['latest_metrics']
        if 'processing_rate' in latest_metrics:
            processing_rate = latest_metrics['processing_rate']['value']
            alert_manager.check_metric("processing_rate", processing_rate)
        
        # 输出状态
        print(f"\\n--- 监控状态 (第 {i+1} 次检查) ---")
        print(f"总指标数: {metrics_summary['total_metrics']}")
        
        if system_status:
            print(f"CPU使用率: {system_status.get('cpu_percent', 0):.1f}%")
            print(f"内存使用率: {system_status.get('memory_percent', 0):.1f}%")
        
        alert_summary = alert_manager.get_alert_summary()
        print(f"活跃告警: {alert_summary['active_alerts_count']}")
        print(f"历史告警: {alert_summary['total_alerts']}")
        
        if alert_summary['recent_alerts']:
            print("最近告警:")
            for alert in alert_summary['recent_alerts'][-3:]:
                print(f"  - {alert['message']}")
    
    # 停止监控
    print("\\n--- 停止监控系统 ---")
    metrics_collector.stop()
    
    # 最终报告
    print("\\n--- 最终监控报告 ---")
    final_metrics = metrics_collector.get_metrics_summary()
    final_alerts = alert_manager.get_alert_summary()
    
    print(f"总收集指标数: {final_metrics['total_metrics']}")
    print(f"总触发告警数: {final_alerts['total_alerts']}")
    
    # 保存监控数据
    report_data = {
        'metrics_summary': final_metrics,
        'alert_summary': final_alerts,
        'timestamp': datetime.now().isoformat()
    }
    
    with open('logs/monitoring_report.json', 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    logger.info("监控系统示例结束")
    print("\\n✅ 简化版监控系统示例完成")
    print("监控报告已保存到: logs/monitoring_report.json")


if __name__ == "__main__":
    run_simple_monitoring_example()
