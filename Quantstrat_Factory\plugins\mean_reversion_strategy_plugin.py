"""
均值回归策略插件示例。

展示如何实现策略插件。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.plugins import IStrategyPlugin, PluginMetadata
import pandas as pd
import numpy as np
from typing import Dict, Any, List


class MeanReversionStrategyPlugin(IStrategyPlugin):
    """均值回归策略插件。"""
    
    def __init__(self):
        self.name = "mean_reversion_strategy"
        self.is_active = False
        self.config = {}
        self.parameters = {}
    
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据。"""
        return PluginMetadata(
            name="mean_reversion_strategy",
            version="1.0.0",
            description="基于均值回归的交易策略",
            author="Quantstrat Factory Team",
            category="strategy",
            dependencies=["momentum_factor"],
            config_schema={
                "lookback_window": {"type": "int", "default": 20, "description": "回看窗口"},
                "entry_threshold": {"type": "float", "default": 2.0, "description": "入场阈值（标准差倍数）"},
                "exit_threshold": {"type": "float", "default": 0.5, "description": "出场阈值（标准差倍数）"},
                "max_position_size": {"type": "float", "default": 0.1, "description": "最大仓位大小"}
            },
            entry_point="plugins.mean_reversion_strategy_plugin.MeanReversionStrategyPlugin"
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件。"""
        self.config = {
            "lookback_window": config.get("lookback_window", 20),
            "entry_threshold": config.get("entry_threshold", 2.0),
            "exit_threshold": config.get("exit_threshold", 0.5),
            "max_position_size": config.get("max_position_size", 0.1)
        }
        
        self.parameters = {
            "lookback_window": self.config["lookback_window"],
            "entry_threshold": self.config["entry_threshold"],
            "exit_threshold": self.config["exit_threshold"],
            "max_position_size": self.config["max_position_size"]
        }
        
        print(f"[{self.name}] 策略插件已初始化，配置: {self.config}")
    
    def activate(self) -> None:
        """激活插件。"""
        self.is_active = True
        print(f"[{self.name}] 策略插件已激活")
    
    def deactivate(self) -> None:
        """停用插件。"""
        self.is_active = False
        print(f"[{self.name}] 策略插件已停用")
    
    def cleanup(self) -> None:
        """清理插件资源。"""
        self.config = {}
        self.parameters = {}
        print(f"[{self.name}] 策略插件资源已清理")
    
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            包含交易信号的DataFrame
        """
        if not self.is_active:
            raise RuntimeError("策略插件未激活")
        
        result = factor_data.copy()
        result['signal'] = 0.0
        result['position'] = 0.0
        
        def generate_symbol_signals(group):
            # 计算价格的Z-Score
            prices = group['close']
            rolling_mean = prices.rolling(self.config['lookback_window']).mean()
            rolling_std = prices.rolling(self.config['lookback_window']).std()
            z_score = (prices - rolling_mean) / (rolling_std + 1e-8)
            
            # 生成信号
            signals = pd.Series(0.0, index=group.index)
            positions = pd.Series(0.0, index=group.index)
            
            current_position = 0.0
            
            for i in range(len(group)):
                current_z = z_score.iloc[i]
                
                if pd.isna(current_z):
                    continue
                
                # 入场信号
                if current_position == 0:
                    if current_z > self.config['entry_threshold']:
                        # 价格过高，做空
                        signals.iloc[i] = -1.0
                        current_position = -self.config['max_position_size']
                    elif current_z < -self.config['entry_threshold']:
                        # 价格过低，做多
                        signals.iloc[i] = 1.0
                        current_position = self.config['max_position_size']
                
                # 出场信号
                elif current_position > 0:  # 持有多头
                    if current_z > -self.config['exit_threshold']:
                        # 平仓
                        signals.iloc[i] = -1.0
                        current_position = 0.0
                
                elif current_position < 0:  # 持有空头
                    if current_z < self.config['exit_threshold']:
                        # 平仓
                        signals.iloc[i] = 1.0
                        current_position = 0.0
                
                positions.iloc[i] = current_position
            
            return pd.DataFrame({
                'z_score': z_score,
                'signal': signals,
                'position': positions
            }, index=group.index)
        
        # 按股票分组生成信号
        signal_data = result.groupby('symbol').apply(
            generate_symbol_signals, include_groups=False
        ).reset_index(level=0, drop=True)
        
        # 合并结果
        for col in signal_data.columns:
            result[col] = signal_data[col]
        
        return result
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数。"""
        return self.parameters.copy()


class TrendFollowingStrategyPlugin(IStrategyPlugin):
    """趋势跟踪策略插件。"""
    
    def __init__(self):
        self.name = "trend_following_strategy"
        self.is_active = False
        self.config = {}
        self.parameters = {}
    
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据。"""
        return PluginMetadata(
            name="trend_following_strategy",
            version="1.0.0",
            description="基于趋势跟踪的交易策略",
            author="Quantstrat Factory Team",
            category="strategy",
            dependencies=["momentum_factor"],
            config_schema={
                "fast_ma": {"type": "int", "default": 10, "description": "快速移动平均线周期"},
                "slow_ma": {"type": "int", "default": 30, "description": "慢速移动平均线周期"},
                "momentum_threshold": {"type": "float", "default": 0.01, "description": "动量阈值"},
                "position_size": {"type": "float", "default": 0.05, "description": "仓位大小"}
            },
            entry_point="plugins.mean_reversion_strategy_plugin.TrendFollowingStrategyPlugin"
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件。"""
        self.config = {
            "fast_ma": config.get("fast_ma", 10),
            "slow_ma": config.get("slow_ma", 30),
            "momentum_threshold": config.get("momentum_threshold", 0.01),
            "position_size": config.get("position_size", 0.05)
        }
        
        self.parameters = self.config.copy()
        print(f"[{self.name}] 策略插件已初始化，配置: {self.config}")
    
    def activate(self) -> None:
        """激活插件。"""
        self.is_active = True
        print(f"[{self.name}] 策略插件已激活")
    
    def deactivate(self) -> None:
        """停用插件。"""
        self.is_active = False
        print(f"[{self.name}] 策略插件已停用")
    
    def cleanup(self) -> None:
        """清理插件资源。"""
        self.config = {}
        self.parameters = {}
        print(f"[{self.name}] 策略插件资源已清理")
    
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            包含交易信号的DataFrame
        """
        if not self.is_active:
            raise RuntimeError("策略插件未激活")
        
        result = factor_data.copy()
        result['signal'] = 0.0
        
        def generate_trend_signals(group):
            prices = group['close']
            
            # 计算移动平均线
            fast_ma = prices.rolling(self.config['fast_ma']).mean()
            slow_ma = prices.rolling(self.config['slow_ma']).mean()
            
            # 计算动量
            momentum = group.get('short_momentum', prices.pct_change(5))
            
            # 生成信号
            signals = pd.Series(0.0, index=group.index)
            
            # 趋势跟踪逻辑
            trend_up = (fast_ma > slow_ma) & (momentum > self.config['momentum_threshold'])
            trend_down = (fast_ma < slow_ma) & (momentum < -self.config['momentum_threshold'])
            
            signals[trend_up] = self.config['position_size']
            signals[trend_down] = -self.config['position_size']
            
            return pd.DataFrame({
                'fast_ma': fast_ma,
                'slow_ma': slow_ma,
                'signal': signals
            }, index=group.index)
        
        # 按股票分组生成信号
        signal_data = result.groupby('symbol').apply(
            generate_trend_signals, include_groups=False
        ).reset_index(level=0, drop=True)
        
        # 合并结果
        for col in signal_data.columns:
            result[col] = signal_data[col]
        
        return result
    
    def get_parameters(self) -> Dict[str, Any]:
        """获取策略参数。"""
        return self.parameters.copy()


# 自动注册插件
if __name__ != "__main__":
    from core.plugins import register_plugin
    register_plugin(MeanReversionStrategyPlugin)
    register_plugin(TrendFollowingStrategyPlugin)
