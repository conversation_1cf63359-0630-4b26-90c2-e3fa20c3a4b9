"""
动量因子插件示例。

展示如何实现因子计算插件。
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from core.plugins import IFactorPlugin, PluginMetadata, PluginStatus
import pandas as pd
import numpy as np
from typing import Dict, Any, List


class MomentumFactorPlugin(IFactorPlugin):
    """动量因子插件。"""
    
    def __init__(self):
        self.name = "momentum_factor"
        self.is_active = False
        self.config = {}
    
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据。"""
        return PluginMetadata(
            name="momentum_factor",
            version="1.0.0",
            description="计算动量因子，包括短期和长期动量",
            author="Quantstrat Factory Team",
            category="factor",
            dependencies=[],
            config_schema={
                "short_window": {"type": "int", "default": 5, "description": "短期窗口"},
                "long_window": {"type": "int", "default": 20, "description": "长期窗口"},
                "enable_volatility_adjustment": {"type": "bool", "default": True, "description": "是否启用波动率调整"}
            },
            entry_point="plugins.momentum_factor_plugin.MomentumFactorPlugin"
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件。"""
        self.config = {
            "short_window": config.get("short_window", 5),
            "long_window": config.get("long_window", 20),
            "enable_volatility_adjustment": config.get("enable_volatility_adjustment", True)
        }
        print(f"[{self.name}] 插件已初始化，配置: {self.config}")
    
    def activate(self) -> None:
        """激活插件。"""
        self.is_active = True
        print(f"[{self.name}] 插件已激活")
    
    def deactivate(self) -> None:
        """停用插件。"""
        self.is_active = False
        print(f"[{self.name}] 插件已停用")
    
    def cleanup(self) -> None:
        """清理插件资源。"""
        self.config = {}
        print(f"[{self.name}] 插件资源已清理")
    
    def calculate_factor(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算动量因子。
        
        Args:
            data: 包含价格数据的DataFrame
            
        Returns:
            包含动量因子的DataFrame
        """
        if not self.is_active:
            raise RuntimeError("插件未激活")
        
        result = data.copy()
        
        # 按股票分组计算动量因子
        def calculate_momentum(group):
            # 计算收益率
            returns = group['close'].pct_change()
            
            # 短期动量
            short_momentum = returns.rolling(self.config['short_window']).mean()
            
            # 长期动量
            long_momentum = returns.rolling(self.config['long_window']).mean()
            
            # 动量差值
            momentum_diff = short_momentum - long_momentum
            
            # 波动率调整（可选）
            if self.config['enable_volatility_adjustment']:
                volatility = returns.rolling(self.config['long_window']).std()
                momentum_diff = momentum_diff / (volatility + 1e-8)
            
            return pd.DataFrame({
                'short_momentum': short_momentum,
                'long_momentum': long_momentum,
                'momentum_diff': momentum_diff
            }, index=group.index)
        
        # 应用计算
        momentum_factors = result.groupby('symbol').apply(
            calculate_momentum, include_groups=False
        ).reset_index(level=0, drop=True)
        
        # 合并结果
        for col in momentum_factors.columns:
            result[col] = momentum_factors[col]
        
        return result
    
    def get_factor_names(self) -> List[str]:
        """获取因子名称列表。"""
        return ['short_momentum', 'long_momentum', 'momentum_diff']


class VolatilityFactorPlugin(IFactorPlugin):
    """波动率因子插件。"""
    
    def __init__(self):
        self.name = "volatility_factor"
        self.is_active = False
        self.config = {}
    
    def get_metadata(self) -> PluginMetadata:
        """获取插件元数据。"""
        return PluginMetadata(
            name="volatility_factor",
            version="1.0.0",
            description="计算波动率相关因子",
            author="Quantstrat Factory Team",
            category="factor",
            dependencies=[],
            config_schema={
                "window": {"type": "int", "default": 20, "description": "计算窗口"},
                "method": {"type": "str", "default": "std", "description": "计算方法: std, parkinson, garman_klass"}
            },
            entry_point="plugins.momentum_factor_plugin.VolatilityFactorPlugin"
        )
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """初始化插件。"""
        self.config = {
            "window": config.get("window", 20),
            "method": config.get("method", "std")
        }
        print(f"[{self.name}] 插件已初始化，配置: {self.config}")
    
    def activate(self) -> None:
        """激活插件。"""
        self.is_active = True
        print(f"[{self.name}] 插件已激活")
    
    def deactivate(self) -> None:
        """停用插件。"""
        self.is_active = False
        print(f"[{self.name}] 插件已停用")
    
    def cleanup(self) -> None:
        """清理插件资源。"""
        self.config = {}
        print(f"[{self.name}] 插件资源已清理")
    
    def calculate_factor(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算波动率因子。
        
        Args:
            data: 包含价格数据的DataFrame
            
        Returns:
            包含波动率因子的DataFrame
        """
        if not self.is_active:
            raise RuntimeError("插件未激活")
        
        result = data.copy()
        
        def calculate_volatility(group):
            if self.config['method'] == 'std':
                # 标准差方法
                returns = group['close'].pct_change()
                volatility = returns.rolling(self.config['window']).std()
                
            elif self.config['method'] == 'parkinson':
                # Parkinson估计器
                hl_ratio = np.log(group['high'] / group['low'])
                volatility = np.sqrt(hl_ratio.rolling(self.config['window']).mean() / (4 * np.log(2)))
                
            elif self.config['method'] == 'garman_klass':
                # Garman-Klass估计器
                hl = np.log(group['high'] / group['low'])
                co = np.log(group['close'] / group['open'])
                volatility = np.sqrt(
                    (0.5 * hl**2 - (2*np.log(2) - 1) * co**2).rolling(self.config['window']).mean()
                )
            
            else:
                raise ValueError(f"不支持的波动率计算方法: {self.config['method']}")
            
            return pd.DataFrame({
                'volatility': volatility,
                'volatility_rank': volatility.rolling(252).rank(pct=True)  # 年化排名
            }, index=group.index)
        
        # 应用计算
        volatility_factors = result.groupby('symbol').apply(
            calculate_volatility, include_groups=False
        ).reset_index(level=0, drop=True)
        
        # 合并结果
        for col in volatility_factors.columns:
            result[col] = volatility_factors[col]
        
        return result
    
    def get_factor_names(self) -> List[str]:
        """获取因子名称列表。"""
        return ['volatility', 'volatility_rank']


# 自动注册插件
if __name__ != "__main__":
    from core.plugins import register_plugin
    register_plugin(MomentumFactorPlugin)
    register_plugin(VolatilityFactorPlugin)
