[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "quantstrat-factory"
description = "A comprehensive quantitative strategy development platform"
authors = [
    {name = "Quantstrat Factory Team", email = "<EMAIL>"},
]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.8"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
]
dependencies = [
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "scipy>=1.9.0",
    "scikit-learn>=1.1.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "plotly>=5.0.0",
    "dash>=2.0.0",
    "dash-bootstrap-components>=1.0.0",
]
dynamic = ["version"]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.10.0",
    "pre-commit>=2.20.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]

[project.urls]
Homepage = "https://github.com/quantstrat-factory/quantstrat-factory"
Documentation = "https://quantstrat-factory.readthedocs.io/"
Repository = "https://github.com/quantstrat-factory/quantstrat-factory.git"
"Bug Tracker" = "https://github.com/quantstrat-factory/quantstrat-factory/issues"

[tool.setuptools_scm]
write_to = "quantstrat_factory/_version.py"

[tool.black]
line-length = 79
target-version = ['py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
)/
'''

[tool.isort]
profile = "black"
line_length = 79
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

[tool.flake8]
max-line-length = 79
extend-ignore = [
    "E203",  # whitespace before ':'
    "E501",  # line too long (handled by black)
    "W503",  # line break before binary operator
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
]
per-file-ignores = [
    "__init__.py:F401",  # imported but unused
    "tests/*:F401,F811",  # test files can have unused imports
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pandas.*",
    "numpy.*",
    "scipy.*",
    "sklearn.*",
    "matplotlib.*",
    "seaborn.*",
    "plotly.*",
    "dash.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=quantstrat_factory",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "performance: Performance tests",
    "slow: Slow tests",
    "factor_lab: Factor lab related tests",
    "data_auditor: Data auditor related tests",
    "feature_profiler: Feature profiler related tests",
    "backtester: Backtester related tests",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]

[tool.coverage.run]
source = ["quantstrat_factory"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/conftest.py",
    "*/setup.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args

[tool.pylint.messages_control]
disable = [
    "C0103",  # invalid-name
    "C0114",  # missing-module-docstring
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
    "W0613",  # unused-argument
]

[tool.pylint.format]
max-line-length = 79
