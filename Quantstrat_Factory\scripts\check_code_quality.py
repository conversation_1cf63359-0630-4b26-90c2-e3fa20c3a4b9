#!/usr/bin/env python3
"""
代码质量检查脚本。

该脚本运行各种代码质量检查工具，包括格式化、linting、类型检查等。
"""

import subprocess
import sys
import argparse
from pathlib import Path
from typing import List, Tuple
import time


class CodeQualityChecker:
    """代码质量检查器。"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.results = []
    
    def run_command(self, command: List[str], description: str) -> Tuple[bool, str]:
        """
        运行命令并返回结果。
        
        Args:
            command: 要运行的命令
            description: 命令描述
            
        Returns:
            (是否成功, 输出信息)
        """
        print(f"\n{'='*60}")
        print(f"运行: {description}")
        print(f"命令: {' '.join(command)}")
        print(f"{'='*60}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            end_time = time.time()
            
            duration = end_time - start_time
            success = result.returncode == 0
            
            output = f"执行时间: {duration:.2f}秒\n"
            output += f"返回码: {result.returncode}\n"
            
            if result.stdout:
                output += f"\n标准输出:\n{result.stdout}"
            
            if result.stderr:
                output += f"\n标准错误:\n{result.stderr}"
            
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{status} - {description} ({duration:.2f}秒)")
            
            self.results.append({
                'description': description,
                'success': success,
                'duration': duration,
                'output': output
            })
            
            return success, output
            
        except subprocess.TimeoutExpired:
            error_msg = f"命令超时: {description}"
            print(f"❌ {error_msg}")
            self.results.append({
                'description': description,
                'success': False,
                'duration': 300,
                'output': error_msg
            })
            return False, error_msg
        
        except Exception as e:
            error_msg = f"运行命令时发生错误: {e}"
            print(f"❌ {error_msg}")
            self.results.append({
                'description': description,
                'success': False,
                'duration': 0,
                'output': error_msg
            })
            return False, error_msg
    
    def check_black_formatting(self) -> bool:
        """检查代码格式化。"""
        return self.run_command(
            ['python', '-m', 'black', '--check', '--diff', '.'],
            "Black 代码格式检查"
        )[0]
    
    def check_isort_imports(self) -> bool:
        """检查导入排序。"""
        return self.run_command(
            ['python', '-m', 'isort', '--check-only', '--diff', '.'],
            "isort 导入排序检查"
        )[0]
    
    def check_flake8_linting(self) -> bool:
        """运行flake8 linting。"""
        return self.run_command(
            ['python', '-m', 'flake8', '.'],
            "flake8 代码检查"
        )[0]
    
    def check_mypy_types(self) -> bool:
        """运行mypy类型检查。"""
        return self.run_command(
            ['python', '-m', 'mypy', '.'],
            "mypy 类型检查"
        )[0]
    
    def run_tests(self) -> bool:
        """运行测试套件。"""
        return self.run_command(
            ['python', '-m', 'pytest', '-v', '--tb=short'],
            "pytest 测试运行"
        )[0]
    
    def check_security(self) -> bool:
        """运行安全检查。"""
        return self.run_command(
            ['python', '-m', 'bandit', '-r', '.', '-f', 'json'],
            "bandit 安全检查"
        )[0]
    
    def fix_formatting(self) -> bool:
        """自动修复格式问题。"""
        print("\n🔧 自动修复代码格式...")
        
        # 运行black格式化
        black_success = self.run_command(
            ['python', '-m', 'black', '.'],
            "Black 自动格式化"
        )[0]
        
        # 运行isort排序
        isort_success = self.run_command(
            ['python', '-m', 'isort', '.'],
            "isort 自动排序导入"
        )[0]
        
        return black_success and isort_success
    
    def generate_report(self) -> str:
        """生成检查报告。"""
        total_checks = len(self.results)
        passed_checks = sum(1 for r in self.results if r['success'])
        failed_checks = total_checks - passed_checks
        
        report = f"""
{'='*80}
代码质量检查报告
{'='*80}

总检查项: {total_checks}
通过: {passed_checks} ✅
失败: {failed_checks} ❌
成功率: {(passed_checks/total_checks)*100:.1f}%

详细结果:
{'-'*80}
"""
        
        for result in self.results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            report += f"{result['description']}: {status} ({result['duration']:.2f}秒)\n"
        
        if failed_checks > 0:
            report += f"\n{'-'*80}\n失败详情:\n{'-'*80}\n"
            for result in self.results:
                if not result['success']:
                    report += f"\n{result['description']}:\n{result['output']}\n"
        
        report += f"\n{'='*80}\n"
        
        return report


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description="代码质量检查工具")
    parser.add_argument(
        '--fix', 
        action='store_true', 
        help='自动修复可修复的问题'
    )
    parser.add_argument(
        '--skip-tests', 
        action='store_true', 
        help='跳过测试运行'
    )
    parser.add_argument(
        '--skip-security', 
        action='store_true', 
        help='跳过安全检查'
    )
    parser.add_argument(
        '--output', 
        type=str, 
        help='将报告保存到文件'
    )
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    print(f"项目根目录: {project_root}")
    print(f"开始代码质量检查...")
    
    checker = CodeQualityChecker(project_root)
    
    # 如果需要修复，先运行修复
    if args.fix:
        checker.fix_formatting()
    
    # 运行各种检查
    all_passed = True
    
    # 格式检查
    all_passed &= checker.check_black_formatting()
    all_passed &= checker.check_isort_imports()
    
    # 代码质量检查
    all_passed &= checker.check_flake8_linting()
    
    # 类型检查（可能失败，但不影响整体结果）
    try:
        checker.check_mypy_types()
    except:
        print("⚠️  mypy 检查跳过（可能未安装）")
    
    # 测试运行
    if not args.skip_tests:
        all_passed &= checker.run_tests()
    
    # 安全检查
    if not args.skip_security:
        try:
            checker.check_security()
        except:
            print("⚠️  bandit 安全检查跳过（可能未安装）")
    
    # 生成报告
    report = checker.generate_report()
    print(report)
    
    # 保存报告到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output}")
    
    # 返回适当的退出码
    sys.exit(0 if all_passed else 1)


if __name__ == "__main__":
    main()
