#!/usr/bin/env python3
"""
缓存清理脚本

清理项目中的临时文件、缓存文件和空输出文件。
"""

import os
import shutil
from pathlib import Path
from typing import List, Tuple
import argparse


class CacheCleanup:
    """缓存清理器"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.cleaned_files = []
        self.cleaned_dirs = []
        self.total_size_freed = 0
    
    def clean_pycache(self) -> Tuple[int, int]:
        """清理 __pycache__ 目录"""
        cache_dirs = list(self.project_root.rglob("__pycache__"))
        files_removed = 0
        dirs_removed = 0
        
        for cache_dir in cache_dirs:
            if cache_dir.is_dir():
                # 计算目录大小
                dir_size = sum(f.stat().st_size for f in cache_dir.rglob('*') if f.is_file())
                self.total_size_freed += dir_size
                
                # 删除目录
                shutil.rmtree(cache_dir)
                self.cleaned_dirs.append(str(cache_dir))
                dirs_removed += 1
                
                # 计算文件数量
                files_removed += len(list(cache_dir.rglob('*.pyc')))
        
        return files_removed, dirs_removed
    
    def clean_pyc_files(self) -> int:
        """清理 .pyc 文件"""
        pyc_files = list(self.project_root.rglob("*.pyc"))
        files_removed = 0
        
        for pyc_file in pyc_files:
            if pyc_file.is_file():
                file_size = pyc_file.stat().st_size
                self.total_size_freed += file_size
                pyc_file.unlink()
                self.cleaned_files.append(str(pyc_file))
                files_removed += 1
        
        return files_removed
    
    def clean_empty_outputs(self) -> int:
        """清理空的输出文件"""
        output_dir = self.project_root / "output"
        if not output_dir.exists():
            return 0
        
        empty_files = []
        for file_path in output_dir.rglob("*"):
            if file_path.is_file():
                # 检查文件是否为空或只有标题行
                try:
                    if file_path.stat().st_size <= 50:  # 小于50字节认为是空文件
                        empty_files.append(file_path)
                    elif file_path.suffix == '.csv':
                        # 检查CSV文件是否只有标题行
                        with open(file_path, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            if len(lines) <= 1 or (len(lines) == 2 and not lines[1].strip()):
                                empty_files.append(file_path)
                except Exception:
                    continue
        
        files_removed = 0
        for empty_file in empty_files:
            file_size = empty_file.stat().st_size
            self.total_size_freed += file_size
            empty_file.unlink()
            self.cleaned_files.append(str(empty_file))
            files_removed += 1
        
        return files_removed
    
    def clean_log_files(self, keep_recent: bool = True) -> int:
        """清理日志文件"""
        logs_dir = self.project_root / "logs"
        if not logs_dir.exists():
            return 0
        
        files_removed = 0
        for log_file in logs_dir.rglob("*.log"):
            if log_file.is_file():
                # 如果保留最近的日志，检查文件大小
                if keep_recent and log_file.stat().st_size > 100:
                    continue
                
                file_size = log_file.stat().st_size
                self.total_size_freed += file_size
                log_file.unlink()
                self.cleaned_files.append(str(log_file))
                files_removed += 1
        
        return files_removed
    
    def clean_temp_files(self) -> int:
        """清理临时文件"""
        temp_patterns = ["*.tmp", "*.temp", "*~", ".DS_Store", "Thumbs.db"]
        files_removed = 0
        
        for pattern in temp_patterns:
            temp_files = list(self.project_root.rglob(pattern))
            for temp_file in temp_files:
                if temp_file.is_file():
                    file_size = temp_file.stat().st_size
                    self.total_size_freed += file_size
                    temp_file.unlink()
                    self.cleaned_files.append(str(temp_file))
                    files_removed += 1
        
        return files_removed
    
    def format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
    
    def generate_report(self) -> str:
        """生成清理报告"""
        report = f"""
{'='*60}
缓存清理报告
{'='*60}

清理统计:
- 清理的文件数: {len(self.cleaned_files)}
- 清理的目录数: {len(self.cleaned_dirs)}
- 释放的空间: {self.format_size(self.total_size_freed)}

清理的目录:
"""
        for dir_path in self.cleaned_dirs:
            report += f"  - {dir_path}\n"
        
        if self.cleaned_files:
            report += "\n清理的文件:\n"
            for file_path in self.cleaned_files[:10]:  # 只显示前10个
                report += f"  - {file_path}\n"
            
            if len(self.cleaned_files) > 10:
                report += f"  ... 还有 {len(self.cleaned_files) - 10} 个文件\n"
        
        report += f"\n{'='*60}\n"
        return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="项目缓存清理工具")
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='只显示将要清理的文件，不实际删除'
    )
    parser.add_argument(
        '--keep-logs',
        action='store_true',
        help='保留日志文件'
    )
    parser.add_argument(
        '--output',
        type=str,
        help='将报告保存到文件'
    )
    
    args = parser.parse_args()
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    
    print(f"项目根目录: {project_root}")
    print("开始缓存清理...")
    
    if args.dry_run:
        print("⚠️  DRY RUN 模式 - 不会实际删除文件")
    
    cleaner = CacheCleanup(project_root)
    
    # 执行清理
    print("\n🧹 清理 __pycache__ 目录...")
    pyc_files, cache_dirs = cleaner.clean_pycache()
    print(f"  清理了 {cache_dirs} 个缓存目录")
    
    print("\n🧹 清理 .pyc 文件...")
    pyc_count = cleaner.clean_pyc_files()
    print(f"  清理了 {pyc_count} 个 .pyc 文件")
    
    print("\n🧹 清理空输出文件...")
    empty_count = cleaner.clean_empty_outputs()
    print(f"  清理了 {empty_count} 个空输出文件")
    
    if not args.keep_logs:
        print("\n🧹 清理日志文件...")
        log_count = cleaner.clean_log_files()
        print(f"  清理了 {log_count} 个日志文件")
    
    print("\n🧹 清理临时文件...")
    temp_count = cleaner.clean_temp_files()
    print(f"  清理了 {temp_count} 个临时文件")
    
    # 生成报告
    report = cleaner.generate_report()
    print(report)
    
    # 保存报告到文件
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"报告已保存到: {args.output}")
    
    print("✅ 缓存清理完成")


if __name__ == "__main__":
    main()
