# 清理旧的项目结构文件

Write-Host "开始清理旧的项目结构..." -ForegroundColor Green

# 要删除的旧目录（已移动到新位置）
$oldDirectories = @(
    "01_data_auditor",
    "02_feature_profiler",
    "03_feature_store",
    "04_factor_lab",
    "05_signal_generator",
    "06_backtester",
    "07_optimizer",
    "core",
    "api",
    "database",
    "monitoring",
    "quality_assurance",
    "utils",
    "extensions",
    "experiment_tracking"
)

# 要删除的旧文件
$oldFiles = @(
    "config.ini",
    "Progress.txt",
    "create_structure.ps1"
)

# 删除旧目录
foreach ($dir in $oldDirectories) {
    if (Test-Path $dir) {
        Write-Host "删除目录: $dir" -ForegroundColor Yellow
        Remove-Item -Path $dir -Recurse -Force
    }
}

# 删除旧文件
foreach ($file in $oldFiles) {
    if (Test-Path $file) {
        Write-Host "删除文件: $file" -ForegroundColor Yellow
        Remove-Item -Path $file -Force
    }
}

# 创建必要的空目录
$requiredDirs = @(
    "data",
    "logs"
)

foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "创建目录: $dir" -ForegroundColor Green
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
    }
}

Write-Host "清理完成！" -ForegroundColor Green
Write-Host "项目现在使用新的目录结构。" -ForegroundColor Cyan
Write-Host "请使用以下方式导入模块:" -ForegroundColor Cyan
Write-Host "  from src import QuantstratFactory" -ForegroundColor White
