#!/bin/bash

# 部署脚本
set -e

# 配置变量
ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}
DOCKER_REGISTRY=${DOCKER_REGISTRY:-quantstrat}
APP_NAME="quantstrat-factory"

echo "🚀 开始部署 $APP_NAME 到 $ENVIRONMENT 环境..."

# 检查环境
if [[ ! "$ENVIRONMENT" =~ ^(staging|production)$ ]]; then
    echo "❌ 错误: 环境必须是 staging 或 production"
    exit 1
fi

# 设置环境特定的配置
case $ENVIRONMENT in
    staging)
        COMPOSE_FILE="docker-compose.staging.yml"
        REPLICAS=1
        ;;
    production)
        COMPOSE_FILE="docker-compose.prod.yml"
        REPLICAS=3
        ;;
esac

echo "📋 部署配置:"
echo "  环境: $ENVIRONMENT"
echo "  版本: $VERSION"
echo "  副本数: $REPLICAS"
echo "  配置文件: $COMPOSE_FILE"

# 检查必要的文件
if [[ ! -f "$COMPOSE_FILE" ]]; then
    echo "❌ 错误: 找不到配置文件 $COMPOSE_FILE"
    exit 1
fi

# 备份当前版本（仅生产环境）
if [[ "$ENVIRONMENT" == "production" ]]; then
    echo "💾 备份当前版本..."
    docker tag $DOCKER_REGISTRY/$APP_NAME:latest $DOCKER_REGISTRY/$APP_NAME:backup-$(date +%Y%m%d-%H%M%S) || true
fi

# 拉取最新镜像
echo "📥 拉取镜像 $DOCKER_REGISTRY/$APP_NAME:$VERSION..."
docker pull $DOCKER_REGISTRY/$APP_NAME:$VERSION

# 更新镜像标签
docker tag $DOCKER_REGISTRY/$APP_NAME:$VERSION $DOCKER_REGISTRY/$APP_NAME:latest

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
docker-compose -f $COMPOSE_FILE run --rm app python -m alembic upgrade head

# 滚动更新服务
echo "🔄 更新服务..."
docker-compose -f $COMPOSE_FILE up -d --scale app=$REPLICAS

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🏥 执行健康检查..."
HEALTH_CHECK_URL="http://localhost:8000/health"
MAX_RETRIES=10
RETRY_COUNT=0

while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
    if curl -f $HEALTH_CHECK_URL > /dev/null 2>&1; then
        echo "✅ 健康检查通过"
        break
    else
        echo "⏳ 健康检查失败，重试中... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
        sleep 10
        ((RETRY_COUNT++))
    fi
done

if [[ $RETRY_COUNT -eq $MAX_RETRIES ]]; then
    echo "❌ 健康检查失败，回滚部署..."
    
    # 回滚到备份版本
    if [[ "$ENVIRONMENT" == "production" ]]; then
        BACKUP_TAG=$(docker images $DOCKER_REGISTRY/$APP_NAME --format "table {{.Tag}}" | grep backup | head -1)
        if [[ -n "$BACKUP_TAG" ]]; then
            echo "🔙 回滚到版本 $BACKUP_TAG..."
            docker tag $DOCKER_REGISTRY/$APP_NAME:$BACKUP_TAG $DOCKER_REGISTRY/$APP_NAME:latest
            docker-compose -f $COMPOSE_FILE up -d --scale app=$REPLICAS
        fi
    fi
    
    exit 1
fi

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker image prune -f

# 运行冒烟测试
echo "🧪 运行冒烟测试..."
python scripts/smoke_tests.py --environment $ENVIRONMENT

echo "🎉 部署完成!"

# 发送通知
if [[ -n "$SLACK_WEBHOOK_URL" ]]; then
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"✅ $APP_NAME 已成功部署到 $ENVIRONMENT 环境 (版本: $VERSION)\"}" \
        $SLACK_WEBHOOK_URL
fi
