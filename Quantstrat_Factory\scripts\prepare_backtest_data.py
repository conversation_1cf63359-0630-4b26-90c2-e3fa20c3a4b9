#!/usr/bin/env python3
"""
准备回测数据脚本

将现有的特征数据重新组织为回测系统期望的格式
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

def prepare_daily_data():
    """准备日线数据"""
    print("📊 准备日线数据...")
    
    # 源数据路径 - 使用新的统一路径
    source_file = Path("D:/PY/Data/cleaned/daily/daily_basics.parquet")

    # 目标数据路径 - 使用临时目录
    target_dir = Path("temp/day_data")
    target_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取统一的日线数据文件
    if not source_file.exists():
        print(f"❌ 源数据文件不存在: {source_file}")
        return False

    print(f"📖 读取统一日线数据文件...")
    try:
        combined_df = pd.read_parquet(source_file)
        print(f"   数据形状: {combined_df.shape}")
        print(f"   列名: {list(combined_df.columns)}")

        # 确保有必要的列
        required_cols = ['datetime', 'symbol', 'open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in combined_df.columns]

        if missing_cols:
            print(f"   ⚠️ 缺少列: {missing_cols}")
            # 如果缺少某些列，尝试创建或重命名
            if 'amount' in combined_df.columns and 'volume' not in combined_df.columns:
                combined_df['volume'] = combined_df['amount'] / combined_df['close']  # 估算成交量

    except Exception as e:
        print(f"❌ 读取数据失败: {e}")
        return False
    print(f"✅ 合并后数据形状: {combined_df.shape}")
    
    # 转换日期格式
    if 'datetime' in combined_df.columns:
        combined_df['datetime'] = pd.to_datetime(combined_df['datetime'])
        combined_df = combined_df.sort_values(['symbol', 'datetime'])
    
    # 按股票分组保存
    print("💾 按股票保存数据...")
    
    stock_count = 0
    for symbol, group in combined_df.groupby('symbol'):
        # 清理股票代码
        clean_symbol = symbol.replace('.', '_')
        output_file = target_dir / f"{clean_symbol}.parquet"
        
        # 保存数据
        group.to_parquet(output_file, index=False)
        stock_count += 1
        
        if stock_count <= 5:  # 只显示前5个
            print(f"   ✅ {symbol}: {len(group)} 条记录 -> {output_file}")
        elif stock_count == 6:
            print(f"   ... 还有更多股票数据")
    
    print(f"✅ 总共处理了 {stock_count} 只股票的数据")
    
    # 更新配置文件
    print("🔧 更新配置文件...")
    config_path = Path("src/trading/config.ini")
    
    if config_path.exists():
        # 读取配置
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = f.read()
        
        # 更新路径
        new_path = str(target_dir.absolute()).replace('\\', '\\\\')
        config_content = config_content.replace(
            'day_data_path = D:\\PY\\Data\\cleaned\\daily',
            f'day_data_path = {new_path}'
        )
        
        # 写回配置
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ 配置文件已更新: {new_path}")
    
    return True

def create_sample_strategy_params():
    """创建示例策略参数"""
    print("\n📋 创建示例策略参数...")
    
    # 创建参数配置目录
    params_dir = Path("src/trading/backtester/configs/params")
    params_dir.mkdir(parents=True, exist_ok=True)
    
    # 示例参数配置
    sample_params = [
        {
            "name": "momentum_strategy_1",
            "lookback_period": 20,
            "entry_threshold": 0.5,
            "exit_threshold": -0.5,
            "max_holding_days": 10
        },
        {
            "name": "momentum_strategy_2", 
            "lookback_period": 30,
            "entry_threshold": 0.6,
            "exit_threshold": -0.4,
            "max_holding_days": 15
        }
    ]
    
    # 保存参数文件
    import json
    params_file = params_dir / "sample_params.json"
    with open(params_file, 'w', encoding='utf-8') as f:
        json.dump(sample_params, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 示例参数已保存: {params_file}")
    return True

def main():
    """主函数"""
    print("🚀 准备回测数据和配置")
    print("=" * 50)
    
    try:
        # 准备数据
        if prepare_daily_data():
            print("✅ 数据准备完成")
        else:
            print("❌ 数据准备失败")
            return False
        
        # 创建示例参数
        if create_sample_strategy_params():
            print("✅ 参数配置完成")
        else:
            print("❌ 参数配置失败")
            return False
        
        print("\n🎉 回测环境准备完成！")
        print("\n💡 现在可以运行:")
        print("   python src/trading/backtester/run.py --mode backtest")
        
        return True
        
    except Exception as e:
        print(f"❌ 准备过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
