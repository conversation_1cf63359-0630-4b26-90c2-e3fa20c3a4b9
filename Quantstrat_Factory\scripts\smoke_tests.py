#!/usr/bin/env python3
"""
冒烟测试脚本。

在部署后运行基本的功能测试，确保系统正常工作。
"""

import argparse
import requests
import time
import sys
import logging
from typing import Dict, List, Any


# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SmokeTest:
    """冒烟测试类。"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        """
        初始化冒烟测试。
        
        Args:
            base_url: 应用基础URL
            timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.timeout = timeout
        
        # 测试结果
        self.results: List[Dict[str, Any]] = []
        self.passed = 0
        self.failed = 0
    
    def run_test(self, test_name: str, test_func):
        """
        运行单个测试。
        
        Args:
            test_name: 测试名称
            test_func: 测试函数
        """
        logger.info(f"🧪 运行测试: {test_name}")
        
        start_time = time.time()
        try:
            test_func()
            duration = time.time() - start_time
            
            self.results.append({
                'name': test_name,
                'status': 'PASSED',
                'duration': duration,
                'error': None
            })
            self.passed += 1
            logger.info(f"✅ {test_name} - 通过 ({duration:.2f}s)")
            
        except Exception as e:
            duration = time.time() - start_time
            
            self.results.append({
                'name': test_name,
                'status': 'FAILED',
                'duration': duration,
                'error': str(e)
            })
            self.failed += 1
            logger.error(f"❌ {test_name} - 失败 ({duration:.2f}s): {e}")
    
    def test_health_check(self):
        """测试健康检查端点。"""
        response = self.session.get(f"{self.base_url}/health")
        response.raise_for_status()
        
        data = response.json()
        assert data.get('status') == 'healthy', f"健康检查失败: {data}"
    
    def test_api_version(self):
        """测试API版本端点。"""
        response = self.session.get(f"{self.base_url}/version")
        response.raise_for_status()
        
        data = response.json()
        assert 'version' in data, "版本信息缺失"
        assert 'build_time' in data, "构建时间缺失"
    
    def test_database_connection(self):
        """测试数据库连接。"""
        response = self.session.get(f"{self.base_url}/health/db")
        response.raise_for_status()
        
        data = response.json()
        assert data.get('database') == 'connected', f"数据库连接失败: {data}"
    
    def test_redis_connection(self):
        """测试Redis连接。"""
        response = self.session.get(f"{self.base_url}/health/redis")
        response.raise_for_status()
        
        data = response.json()
        assert data.get('redis') == 'connected', f"Redis连接失败: {data}"
    
    def test_basic_api_endpoints(self):
        """测试基本API端点。"""
        endpoints = [
            '/api/v1/factors',
            '/api/v1/strategies',
            '/api/v1/backtests'
        ]
        
        for endpoint in endpoints:
            response = self.session.get(f"{self.base_url}{endpoint}")
            # 允许404（端点可能不存在）和200
            assert response.status_code in [200, 404], \
                f"端点 {endpoint} 返回意外状态码: {response.status_code}"
    
    def test_authentication(self):
        """测试认证功能。"""
        # 测试无认证访问受保护端点
        response = self.session.get(f"{self.base_url}/api/v1/admin/users")
        assert response.status_code in [401, 403], \
            f"受保护端点应该返回401或403，实际返回: {response.status_code}"
    
    def test_rate_limiting(self):
        """测试速率限制。"""
        # 快速发送多个请求
        responses = []
        for _ in range(10):
            response = self.session.get(f"{self.base_url}/health")
            responses.append(response.status_code)
        
        # 检查是否有速率限制响应
        rate_limited = any(code == 429 for code in responses)
        success_responses = sum(1 for code in responses if code == 200)
        
        # 至少应该有一些成功的响应
        assert success_responses > 0, "所有请求都被速率限制"
    
    def test_data_processing_pipeline(self):
        """测试数据处理流水线。"""
        # 创建测试数据处理任务
        test_data = {
            "symbols": ["TEST"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-02"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/v1/data/process",
            json=test_data
        )
        
        # 允许404（端点可能不存在）或成功状态
        assert response.status_code in [200, 201, 202, 404], \
            f"数据处理端点返回意外状态码: {response.status_code}"
    
    def test_factor_calculation(self):
        """测试因子计算功能。"""
        test_request = {
            "factors": ["momentum", "volatility"],
            "symbols": ["TEST"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-02"
        }
        
        response = self.session.post(
            f"{self.base_url}/api/v1/factors/calculate",
            json=test_request
        )
        
        # 允许404（端点可能不存在）或成功状态
        assert response.status_code in [200, 201, 202, 404], \
            f"因子计算端点返回意外状态码: {response.status_code}"
    
    def run_all_tests(self):
        """运行所有冒烟测试。"""
        logger.info(f"🚀 开始冒烟测试，目标URL: {self.base_url}")
        
        # 定义测试列表
        tests = [
            ("健康检查", self.test_health_check),
            ("API版本", self.test_api_version),
            ("数据库连接", self.test_database_connection),
            ("Redis连接", self.test_redis_connection),
            ("基本API端点", self.test_basic_api_endpoints),
            ("认证功能", self.test_authentication),
            ("速率限制", self.test_rate_limiting),
            ("数据处理流水线", self.test_data_processing_pipeline),
            ("因子计算", self.test_factor_calculation)
        ]
        
        # 运行所有测试
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
        
        # 输出测试结果
        self.print_summary()
        
        # 如果有失败的测试，退出码为1
        if self.failed > 0:
            sys.exit(1)
    
    def print_summary(self):
        """打印测试摘要。"""
        total = self.passed + self.failed
        success_rate = (self.passed / total * 100) if total > 0 else 0
        
        logger.info("=" * 60)
        logger.info("📊 冒烟测试结果摘要")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total}")
        logger.info(f"通过: {self.passed}")
        logger.info(f"失败: {self.failed}")
        logger.info(f"成功率: {success_rate:.1f}%")
        
        if self.failed > 0:
            logger.info("\n❌ 失败的测试:")
            for result in self.results:
                if result['status'] == 'FAILED':
                    logger.info(f"  - {result['name']}: {result['error']}")
        
        logger.info("=" * 60)


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description="运行冒烟测试")
    parser.add_argument(
        "--environment",
        choices=["staging", "production", "local"],
        default="local",
        help="目标环境"
    )
    parser.add_argument(
        "--base-url",
        help="应用基础URL（覆盖环境默认值）"
    )
    parser.add_argument(
        "--timeout",
        type=int,
        default=30,
        help="请求超时时间（秒）"
    )
    
    args = parser.parse_args()
    
    # 根据环境确定基础URL
    if args.base_url:
        base_url = args.base_url
    else:
        url_map = {
            "local": "http://localhost:8000",
            "staging": "https://staging.quantstrat.com",
            "production": "https://api.quantstrat.com"
        }
        base_url = url_map[args.environment]
    
    # 运行冒烟测试
    smoke_test = SmokeTest(base_url, args.timeout)
    smoke_test.run_all_tests()


if __name__ == "__main__":
    main()
