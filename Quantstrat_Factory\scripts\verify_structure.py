#!/usr/bin/env python3
"""
验证新项目结构的完整性和功能性。
"""

import os
import sys
from pathlib import Path
import importlib.util

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def check_directory_structure():
    """检查目录结构是否正确。"""
    print("🔍 检查目录结构...")
    
    required_dirs = [
        "src/data/auditor",
        "src/data/profiler",
        "src/research/factor_lab",
        "src/research/experiment_tracking",
        "src/trading/signal_generator",
        "src/trading/backtester",
        "src/trading/optimizer",
        "src/core_platform/core",
        "src/core_platform/api",
        "src/core_platform/web",
        "src/core_platform/database",
        "src/infrastructure/monitoring",
        "src/infrastructure/quality",
        "src/infrastructure/utils",
        "config",
        "config/environments",
        "tests",
        "scripts",
        "examples"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if not full_path.exists():
            missing_dirs.append(dir_path)
        else:
            print(f"  ✅ {dir_path}")
    
    if missing_dirs:
        print(f"  ❌ 缺少目录: {missing_dirs}")
        return False
    
    print("  ✅ 目录结构检查通过")
    return True

def check_config_files():
    """检查配置文件是否存在。"""
    print("\n🔍 检查配置文件...")
    
    required_configs = [
        "config/app.yaml",
        "config/environments/development.yaml",
        "config/environments/production.yaml"
    ]
    
    missing_configs = []
    for config_path in required_configs:
        full_path = project_root / config_path
        if not full_path.exists():
            missing_configs.append(config_path)
        else:
            print(f"  ✅ {config_path}")
    
    if missing_configs:
        print(f"  ❌ 缺少配置文件: {missing_configs}")
        return False
    
    print("  ✅ 配置文件检查通过")
    return True

def check_init_files():
    """检查__init__.py文件是否存在。"""
    print("\n🔍 检查__init__.py文件...")
    
    required_inits = [
        "src/__init__.py",
        "src/data/__init__.py",
        "src/research/__init__.py",
        "src/trading/__init__.py",
        "src/core_platform/__init__.py",
        "src/infrastructure/__init__.py"
    ]
    
    missing_inits = []
    for init_path in required_inits:
        full_path = project_root / init_path
        if not full_path.exists():
            missing_inits.append(init_path)
        else:
            print(f"  ✅ {init_path}")
    
    if missing_inits:
        print(f"  ❌ 缺少__init__.py文件: {missing_inits}")
        return False
    
    print("  ✅ __init__.py文件检查通过")
    return True

def check_config_manager():
    """检查配置管理器是否正常工作。"""
    print("\n🔍 检查配置管理器...")
    
    try:
        # 尝试导入配置管理器
        from core_platform.core.config_manager import ConfigManager
        
        # 创建配置管理器实例
        config_manager = ConfigManager(str(project_root))
        
        # 尝试加载配置
        config = config_manager.load_config("development")
        
        # 检查基本配置项
        if "app" not in config:
            print("  ❌ 缺少app配置节")
            return False
        
        if "data" not in config:
            print("  ❌ 缺少data配置节")
            return False
        
        print(f"  ✅ 配置管理器正常工作")
        print(f"  ✅ 当前环境: {config_manager.environment}")
        print(f"  ✅ 应用名称: {config.get('app', {}).get('name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理器错误: {e}")
        return False

def check_main_factory():
    """检查主工厂类是否可以导入。"""
    print("\n🔍 检查主工厂类...")
    
    try:
        # 尝试导入主工厂类
        import src
        
        # 检查主要组件是否可用
        factory = src.QuantstratFactory()
        
        print(f"  ✅ QuantstratFactory导入成功")
        print(f"  ✅ 工厂版本: {src.__version__}")
        
        # 检查状态
        status = factory.get_status()
        print(f"  ✅ 系统状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 主工厂类错误: {e}")
        return False

def check_documentation():
    """检查文档文件是否存在。"""
    print("\n🔍 检查文档文件...")

    required_docs = [
        "README.md",
        "COMMAND_REFERENCE.md",
        "FEATURES_REFERENCE.md"
    ]

    missing_docs = []
    for doc_path in required_docs:
        full_path = project_root / doc_path
        if not full_path.exists():
            missing_docs.append(doc_path)
        else:
            print(f"  ✅ {doc_path}")

    if missing_docs:
        print(f"  ❌ 缺少文档文件: {missing_docs}")
        return False

    print("  ✅ 文档文件检查通过")
    return True

def main():
    """主验证函数。"""
    print("🚀 Quantstrat Factory 项目结构验证")
    print("=" * 50)
    
    checks = [
        check_directory_structure,
        check_config_files,
        check_init_files,
        check_config_manager,
        check_main_factory,
        check_documentation
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
        except Exception as e:
            print(f"  ❌ 检查失败: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 验证结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！项目结构重构成功！")
        print("\n📝 使用指南:")
        print("  1. 导入主工厂: from src import QuantstratFactory")
        print("  2. 创建实例: factory = QuantstratFactory()")
        print("  3. 使用功能: factory.factor_lab, factory.backtester 等")
        return True
    else:
        print("⚠️  部分检查未通过，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
