"""
Quantstrat Factory - 量化策略开发和回测平台

这是一个企业级的量化投资解决方案，提供完整的策略开发、回测和风险管理功能。
"""

__version__ = "1.0.0"
__author__ = "Quantstrat Factory Team"
__email__ = "<EMAIL>"
__description__ = "量化策略开发和回测平台"

# 简化配置管理
config_manager = None

def get_config():
    return {}

def get_config_value(key, default=None):
    return default

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__description__",

    # 核心组件
    "config_manager",
    "get_config",
    "get_config_value",

    # 主工厂类
    "QuantstratFactory"
]


class QuantstratFactory:
    """
    Quantstrat Factory 主工厂类。

    提供统一的接口来访问所有功能模块。
    """

    def __init__(self, config_path: str = None, environment: str = None):
        """
        初始化工厂。

        Args:
            config_path: 配置文件路径
            environment: 环境名称
        """
        # 加载配置
        if config_manager:
            if config_path:
                config_manager.paths.main_config = config_path

            if environment:
                config_manager.load_config(environment)
            else:
                config_manager.load_config()

        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化组件。"""
        # 数据层组件
        self._data_auditor = None
        self._feature_profiler = None
        self._factor_lab = None

    def get_data_auditor(self):
        """获取数据审计器。"""
        if self._data_auditor is None:
            try:
                from .data.auditor.run_auditor import run_auditor
                self._data_auditor = run_auditor
            except ImportError:
                self._data_auditor = None
        return self._data_auditor

    def get_feature_profiler(self):
        """获取特征分析器。"""
        if self._feature_profiler is None:
            try:
                from .data.profiler.run_profiler import run_profiler
                self._feature_profiler = run_profiler
            except ImportError:
                self._feature_profiler = None
        return self._feature_profiler

    def get_factor_lab(self):
        """获取因子实验室。"""
        if self._factor_lab is None:
            try:
                from .research.factor_lab.web_app.app import app
                self._factor_lab = app
            except ImportError:
                self._factor_lab = None
        return self._factor_lab
    
    # 核心功能方法
    def run_data_auditor(self, **kwargs):
        """运行数据审计。"""
        auditor = self.get_data_auditor()
        if auditor:
            return auditor(**kwargs)
        return None

    def run_feature_profiler(self, **kwargs):
        """运行特征分析。"""
        profiler = self.get_feature_profiler()
        if profiler:
            return profiler(**kwargs)
        return None

    def run_factor_lab(self, **kwargs):
        """运行因子实验室。"""
        factor_lab = self.get_factor_lab()
        if factor_lab:
            return factor_lab
        return None
    
    def get_config(self, key: str = None, default=None):
        """获取配置。"""
        if config_manager:
            if key is None:
                return config_manager.get_config()
            return config_manager.get(key, default)
        return default

    def get_status(self):
        """获取系统状态。"""
        return {
            "version": __version__,
            "components": {
                "data_auditor": self._data_auditor is not None,
                "feature_profiler": self._feature_profiler is not None,
                "factor_lab": self._factor_lab is not None,
            }
        }


# 创建默认工厂实例
default_factory = None

def get_factory():
    """获取默认工厂实例。"""
    global default_factory
    if default_factory is None:
        default_factory = QuantstratFactory()
    return default_factory

def create_factory(**kwargs):
    """创建新的工厂实例。"""
    return QuantstratFactory(**kwargs)
