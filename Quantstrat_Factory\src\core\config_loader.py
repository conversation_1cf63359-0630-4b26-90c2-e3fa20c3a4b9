"""
统一配置加载器

所有模块都应该通过这个模块来读取配置，确保配置的一致性
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class ConfigLoader:
    """统一配置加载器"""
    
    _instance = None
    _config = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._config is None:
            self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        # 查找配置文件
        config_paths = [
            Path(__file__).resolve().parents[2] / 'config' / 'app.yaml',  # 标准位置
            Path('config/app.yaml'),  # 相对路径
            Path('Quantstrat_Factory/config/app.yaml')  # 备用路径
        ]
        
        config_file = None
        for path in config_paths:
            if path.exists():
                config_file = path
                break
        
        if not config_file:
            logger.error("未找到配置文件 app.yaml")
            self._config = self._get_default_config()
            return
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {config_file}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'data': {
                'root': 'D:/PY/Data',
                'raw': {
                    'minute': 'D:/PY/Data/raw/1min'
                },
                'cleaned': {
                    'minute': 'D:/PY/Data/cleaned/minute',
                    'daily': 'D:/PY/Data/cleaned/daily'
                },
                'features': {
                    'root': 'D:/PY/Data/features',
                    'minute': 'D:/PY/Data/features/minute',
                    'daily': 'D:/PY/Data/features/daily'
                }
            },
            'output': {
                'root': 'D:/PY/Quantstrat_Factory/output',
                'backtests': 'D:/PY/Quantstrat_Factory/output/backtests',
                'reports': 'D:/PY/Quantstrat_Factory/output/reports'
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，用点号分隔，如 'data.raw.minute'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_path(self, path_type: str) -> str:
        """
        获取数据路径
        
        Args:
            path_type: 路径类型，如 'raw.minute', 'cleaned.daily', 'features.root'
            
        Returns:
            路径字符串
        """
        return self.get(f'data.{path_type}', '')
    
    def get_output_path(self, path_type: str) -> str:
        """
        获取输出路径
        
        Args:
            path_type: 路径类型，如 'backtests', 'reports'
            
        Returns:
            路径字符串
        """
        return self.get(f'output.{path_type}', '')
    
    def reload_config(self):
        """重新加载配置"""
        self._config = None
        self._load_config()


# 全局配置加载器实例
config_loader = ConfigLoader()


# 便捷函数
def get_config() -> Dict[str, Any]:
    """获取完整配置"""
    return config_loader.get_config()


def get_config_value(key_path: str, default: Any = None) -> Any:
    """获取配置值"""
    return config_loader.get(key_path, default)


def get_data_path(path_type: str) -> str:
    """获取数据路径"""
    return config_loader.get_data_path(path_type)


def get_output_path(path_type: str) -> str:
    """获取输出路径"""
    return config_loader.get_output_path(path_type)


# 向后兼容的路径获取函数
def get_raw_minute_path() -> str:
    """获取原始分钟数据路径"""
    return get_data_path('raw.minute')


def get_cleaned_minute_path() -> str:
    """获取清洗后分钟数据路径"""
    return get_data_path('cleaned.minute')


def get_cleaned_daily_path() -> str:
    """获取清洗后日线数据路径"""
    return get_data_path('cleaned.daily')


def get_features_path() -> str:
    """获取特征数据根路径"""
    return get_data_path('features.root')


def get_minute_features_path() -> str:
    """获取分钟级特征路径"""
    return get_data_path('features.minute')


def get_daily_features_path() -> str:
    """获取日级特征路径"""
    return get_data_path('features.daily')


if __name__ == "__main__":
    # 测试配置加载
    print("配置测试:")
    print(f"原始分钟数据路径: {get_raw_minute_path()}")
    print(f"清洗分钟数据路径: {get_cleaned_minute_path()}")
    print(f"清洗日线数据路径: {get_cleaned_daily_path()}")
    print(f"特征数据根路径: {get_features_path()}")
    print(f"分钟特征路径: {get_minute_features_path()}")
    print(f"日线特征路径: {get_daily_features_path()}")
