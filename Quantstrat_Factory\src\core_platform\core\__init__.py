"""
核心架构模块。

提供项目的核心基础设施，包括：
- 接口定义
- 依赖注入容器
- 事件系统
- 配置管理
"""

from .interfaces import (
    IDataProcessor,
    IFactorCalculator,
    ISignalGenerator,
    IBacktester,
    IEventBus
)

from .container import Container, container
from .events import Event, EventBus
from .config import ConfigManager, AppConfig

__version__ = "1.0.0"
__author__ = "Quantstrat Factory Team"

__all__ = [
    "IDataProcessor",
    "IFactorCalculator", 
    "ISignalGenerator",
    "IBacktester",
    "IEventBus",
    "Container",
    "container",
    "Event",
    "EventBus",
    "ConfigManager",
    "AppConfig"
]
