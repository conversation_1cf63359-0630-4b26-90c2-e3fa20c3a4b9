"""
配置管理系统。

提供统一的配置管理功能，支持多环境配置和动态配置更新。
"""

from typing import Any, Dict, Optional, List, Union
from pathlib import Path
from dataclasses import dataclass, field
import yaml
import json
import os
import logging
from abc import ABC, abstractmethod


@dataclass
class DatabaseConfig:
    """数据库配置。"""
    host: str = "localhost"
    port: int = 5432
    database: str = "quantstrat"
    username: str = "user"
    password: str = "password"
    driver: str = "postgresql"
    pool_size: int = 10
    max_overflow: int = 20


@dataclass
class ProcessingConfig:
    """处理配置。"""
    batch_size: int = 1000
    max_workers: int = 4
    memory_limit_mb: int = 2048
    enable_parallel: bool = True
    chunk_size: int = 10000


@dataclass
class TradingConfig:
    """交易配置。"""
    commission_rate: float = 0.0003
    stamp_tax_rate: float = 0.001
    slippage_bps: float = 5.0
    min_commission: float = 5.0
    max_position_size: float = 0.1
    max_leverage: float = 1.0


@dataclass
class RiskConfig:
    """风险配置。"""
    max_drawdown: float = 0.15
    stop_loss_pct: float = 0.05
    max_sector_exposure: float = 0.3
    var_confidence: float = 0.95
    enable_risk_check: bool = True


@dataclass
class LoggingConfig:
    """日志配置。"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size_mb: int = 100
    backup_count: int = 5


@dataclass
class AppConfig:
    """应用配置。"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    risk: RiskConfig = field(default_factory=RiskConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    data_paths: Dict[str, str] = field(default_factory=dict)
    environment: str = "development"
    debug: bool = False
    
    def __post_init__(self):
        """后处理初始化。"""
        if not self.data_paths:
            self.data_paths = {
                'data_root': 'D:/PY/Data',
                'feature_store': 'D:/PY/Data/features',
                'output': 'D:/PY/Quantstrat_Factory/output',
                'logs': 'D:/PY/Quantstrat_Factory/logs'
            }


class IConfigProvider(ABC):
    """配置提供者接口。"""
    
    @abstractmethod
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载配置。
        
        Args:
            config_path: 配置路径
            
        Returns:
            配置字典
        """
        pass
    
    @abstractmethod
    def save_config(self, config: Dict[str, Any], config_path: str) -> None:
        """
        保存配置。
        
        Args:
            config: 配置字典
            config_path: 配置路径
        """
        pass


class YamlConfigProvider(IConfigProvider):
    """YAML配置提供者。"""
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载YAML配置。"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            return {}
        
        with open(config_file, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    
    def save_config(self, config: Dict[str, Any], config_path: str) -> None:
        """保存YAML配置。"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)


class JsonConfigProvider(IConfigProvider):
    """JSON配置提供者。"""
    
    def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载JSON配置。"""
        config_file = Path(config_path)
        
        if not config_file.exists():
            return {}
        
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_config(self, config: Dict[str, Any], config_path: str) -> None:
        """保存JSON配置。"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)


class ConfigManager:
    """配置管理器。"""
    
    def __init__(self, 
                 config_path: Optional[str] = None,
                 environment: Optional[str] = None,
                 provider: Optional[IConfigProvider] = None):
        """
        初始化配置管理器。
        
        Args:
            config_path: 配置文件路径
            environment: 环境名称
            provider: 配置提供者
        """
        self.environment = environment or os.getenv('QUANTSTRAT_ENV', 'development')
        self.config_path = config_path or self._get_default_config_path()
        self.provider = provider or self._get_default_provider()
        
        self._config: Optional[AppConfig] = None
        self._config_cache: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)
    
    def load_config(self) -> AppConfig:
        """
        加载配置。
        
        Returns:
            应用配置对象
        """
        if self._config is None:
            self._config = self._load_from_files()
        
        return self._config
    
    def reload_config(self) -> AppConfig:
        """
        重新加载配置。
        
        Returns:
            应用配置对象
        """
        self._config = None
        self._config_cache.clear()
        return self.load_config()
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值。
        
        Args:
            key: 配置键（支持点分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        if key in self._config_cache:
            return self._config_cache[key]
        
        config = self.load_config()
        value = self._get_nested_value(config.__dict__, key, default)
        
        self._config_cache[key] = value
        return value
    
    def set_config_value(self, key: str, value: Any) -> None:
        """
        设置配置值。
        
        Args:
            key: 配置键
            value: 配置值
        """
        config = self.load_config()
        self._set_nested_value(config.__dict__, key, value)
        self._config_cache[key] = value
    
    def save_config(self, config: Optional[AppConfig] = None) -> None:
        """
        保存配置。
        
        Args:
            config: 配置对象，如果为None则保存当前配置
        """
        if config is None:
            config = self.load_config()
        
        config_dict = self._config_to_dict(config)
        self.provider.save_config(config_dict, self.config_path)
        self.logger.info(f"配置已保存到: {self.config_path}")
    
    def get_environment_config_path(self, env: str) -> str:
        """
        获取环境特定的配置路径。
        
        Args:
            env: 环境名称
            
        Returns:
            配置文件路径
        """
        base_path = Path(self.config_path)
        env_path = base_path.parent / f"config.{env}{base_path.suffix}"
        return str(env_path)
    
    def _get_default_config_path(self) -> str:
        """获取默认配置路径。"""
        return f"config/config.{self.environment}.yaml"
    
    def _get_default_provider(self) -> IConfigProvider:
        """获取默认配置提供者。"""
        if self.config_path.endswith('.json'):
            return JsonConfigProvider()
        else:
            return YamlConfigProvider()
    
    def _load_from_files(self) -> AppConfig:
        """从文件加载配置。"""
        # 加载基础配置
        base_config = self.provider.load_config("config/config.yaml")
        
        # 加载环境特定配置
        env_config_path = self.get_environment_config_path(self.environment)
        env_config = self.provider.load_config(env_config_path)
        
        # 合并配置
        merged_config = self._merge_configs(base_config, env_config)
        
        # 应用环境变量覆盖
        merged_config = self._apply_env_overrides(merged_config)
        
        return self._dict_to_config(merged_config)
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置字典。"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _apply_env_overrides(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """应用环境变量覆盖。"""
        # 支持通过环境变量覆盖配置
        # 格式: QUANTSTRAT_DATABASE_HOST=localhost
        prefix = "QUANTSTRAT_"
        
        for env_key, env_value in os.environ.items():
            if env_key.startswith(prefix):
                config_key = env_key[len(prefix):].lower().replace('_', '.')
                self._set_nested_value(config, config_key, env_value)
        
        return config
    
    def _get_nested_value(self, data: Dict[str, Any], key: str, default: Any = None) -> Any:
        """获取嵌套字典值。"""
        keys = key.split('.')
        current = data
        
        for k in keys:
            if isinstance(current, dict) and k in current:
                current = current[k]
            else:
                return default
        
        return current
    
    def _set_nested_value(self, data: Dict[str, Any], key: str, value: Any) -> None:
        """设置嵌套字典值。"""
        keys = key.split('.')
        current = data
        
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        current[keys[-1]] = value
    
    def _dict_to_config(self, data: Dict[str, Any]) -> AppConfig:
        """字典转配置对象。"""
        return AppConfig(
            database=DatabaseConfig(**data.get('database', {})),
            processing=ProcessingConfig(**data.get('processing', {})),
            trading=TradingConfig(**data.get('trading', {})),
            risk=RiskConfig(**data.get('risk', {})),
            logging=LoggingConfig(**data.get('logging', {})),
            data_paths=data.get('data_paths', {}),
            environment=data.get('environment', self.environment),
            debug=data.get('debug', False)
        )
    
    def _config_to_dict(self, config: AppConfig) -> Dict[str, Any]:
        """配置对象转字典。"""
        return {
            'database': config.database.__dict__,
            'processing': config.processing.__dict__,
            'trading': config.trading.__dict__,
            'risk': config.risk.__dict__,
            'logging': config.logging.__dict__,
            'data_paths': config.data_paths,
            'environment': config.environment,
            'debug': config.debug
        }


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """
    获取应用配置的便捷函数。
    
    Returns:
        应用配置对象
    """
    return config_manager.load_config()


def get_config_value(key: str, default: Any = None) -> Any:
    """
    获取配置值的便捷函数。
    
    Args:
        key: 配置键
        default: 默认值
        
    Returns:
        配置值
    """
    return config_manager.get_config_value(key, default)
