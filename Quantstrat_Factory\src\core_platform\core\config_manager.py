"""
统一配置管理器。

提供配置文件加载、环境变量替换和配置验证功能。
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dataclasses import dataclass


logger = logging.getLogger(__name__)


@dataclass
class ConfigPaths:
    """配置路径。"""
    root: Path
    main_config: Path
    environments: Path
    
    @classmethod
    def from_project_root(cls, project_root: str = None):
        """从项目根目录创建配置路径。"""
        if project_root is None:
            # 自动检测项目根目录
            current_file = Path(__file__)
            project_root = current_file.parent.parent.parent.parent
        else:
            project_root = Path(project_root)
        
        config_root = project_root / "config"
        
        return cls(
            root=config_root,
            main_config=config_root / "app.yaml",
            environments=config_root / "environments"
        )


class ConfigManager:
    """配置管理器。"""
    
    def __init__(self, project_root: str = None):
        """
        初始化配置管理器。
        
        Args:
            project_root: 项目根目录路径
        """
        self.paths = ConfigPaths.from_project_root(project_root)
        self._config: Optional[Dict[str, Any]] = None
        self._environment = os.getenv("QUANTSTRAT_ENV", "development")
    
    def load_config(self, environment: str = None) -> Dict[str, Any]:
        """
        加载配置。
        
        Args:
            environment: 环境名称（development, production等）
            
        Returns:
            配置字典
        """
        if environment:
            self._environment = environment
        
        # 加载主配置
        main_config = self._load_yaml_file(self.paths.main_config)
        
        # 加载环境特定配置
        env_config_path = self.paths.environments / f"{self._environment}.yaml"
        if env_config_path.exists():
            env_config = self._load_yaml_file(env_config_path)
            # 合并配置（环境配置覆盖主配置）
            config = self._merge_configs(main_config, env_config)
        else:
            logger.warning(f"环境配置文件不存在: {env_config_path}")
            config = main_config
        
        # 替换环境变量
        config = self._replace_env_vars(config)
        
        # 验证配置
        self._validate_config(config)
        
        self._config = config
        logger.info(f"配置加载完成，环境: {self._environment}")
        
        return config
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置。"""
        if self._config is None:
            return self.load_config()
        return self._config
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值。
        
        Args:
            key: 配置键（支持点号分隔的嵌套键）
            default: 默认值
            
        Returns:
            配置值
        """
        config = self.get_config()
        
        # 支持嵌套键访问，如 "database.host"
        keys = key.split('.')
        value = config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_data_paths(self) -> Dict[str, str]:
        """获取数据路径配置。"""
        return self.get("data", {})
    
    def get_output_paths(self) -> Dict[str, str]:
        """获取输出路径配置。"""
        return self.get("output", {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置。"""
        return self.get("database", {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置。"""
        return self.get("logging", {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置。"""
        return self.get("performance", {})
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML文件。"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            logger.error(f"加载配置文件失败 {file_path}: {e}")
            return {}
    
    def _merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并配置字典。
        
        Args:
            base: 基础配置
            override: 覆盖配置
            
        Returns:
            合并后的配置
        """
        result = base.copy()
        
        for key, value in override.items():
            if key == "extends":
                # 跳过extends字段
                continue
            
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                # 递归合并嵌套字典
                result[key] = self._merge_configs(result[key], value)
            else:
                # 直接覆盖
                result[key] = value
        
        return result
    
    def _replace_env_vars(self, config: Any) -> Any:
        """
        替换配置中的环境变量。
        
        支持 ${VAR_NAME} 和 ${VAR_NAME:default_value} 格式
        """
        if isinstance(config, dict):
            return {k: self._replace_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._replace_env_vars(item) for item in config]
        elif isinstance(config, str):
            return self._replace_env_var_string(config)
        else:
            return config
    
    def _replace_env_var_string(self, value: str) -> str:
        """替换字符串中的环境变量。"""
        import re
        
        # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default}
        pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
        
        def replace_match(match):
            var_name = match.group(1)
            default_value = match.group(2) if match.group(2) is not None else ""
            return os.getenv(var_name, default_value)
        
        return re.sub(pattern, replace_match, value)
    
    def _validate_config(self, config: Dict[str, Any]):
        """验证配置的完整性。"""
        required_sections = ["app", "data", "output"]
        
        for section in required_sections:
            if section not in config:
                logger.warning(f"缺少必需的配置节: {section}")
        
        # 验证路径是否存在
        data_paths = config.get("data", {})
        for path_name, path_value in data_paths.items():
            if isinstance(path_value, str) and path_value:
                path = Path(path_value)
                if not path.exists() and path_name != "feature_store":  # feature_store可能不存在
                    logger.warning(f"数据路径不存在: {path_name} = {path_value}")
    
    @property
    def environment(self) -> str:
        """当前环境。"""
        return self._environment
    
    @property
    def is_development(self) -> bool:
        """是否为开发环境。"""
        return self._environment == "development"
    
    @property
    def is_production(self) -> bool:
        """是否为生产环境。"""
        return self._environment == "production"


# 全局配置管理器实例
config_manager = ConfigManager()


# 便捷函数
def get_config() -> Dict[str, Any]:
    """获取配置。"""
    return config_manager.get_config()


def get_config_value(key: str, default: Any = None) -> Any:
    """获取配置值。"""
    return config_manager.get(key, default)


def get_data_path(key: str) -> str:
    """获取数据路径。"""
    data_paths = config_manager.get_data_paths()
    return data_paths.get(key, "")


def get_output_path(key: str) -> str:
    """获取输出路径。"""
    output_paths = config_manager.get_output_paths()
    return output_paths.get(key, "")


# 示例使用
if __name__ == "__main__":
    # 加载配置
    config = config_manager.load_config("development")
    
    # 获取配置值
    app_name = config_manager.get("app.name")
    data_root = config_manager.get("data.root")
    
    print(f"应用名称: {app_name}")
    print(f"数据根目录: {data_root}")
    print(f"当前环境: {config_manager.environment}")
    
    # 获取特定配置
    db_config = config_manager.get_database_config()
    print(f"数据库配置: {db_config}")
    
    print("✅ 配置管理器测试完成")
