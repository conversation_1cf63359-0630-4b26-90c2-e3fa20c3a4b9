"""
依赖注入容器实现。

提供依赖注入功能，支持自动依赖解析和生命周期管理。
"""

from typing import Any, Dict, Type, TypeVar, Callable, Optional, List
import inspect
import threading
from abc import ABC


T = TypeVar('T')


class ServiceLifetime:
    """服务生命周期枚举。"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


class ServiceDescriptor:
    """服务描述符。"""
    
    def __init__(self, 
                 interface: Type,
                 implementation: Type,
                 lifetime: str = ServiceLifetime.SINGLETON,
                 factory: Optional[Callable] = None):
        self.interface = interface
        self.implementation = implementation
        self.lifetime = lifetime
        self.factory = factory


class Container:
    """依赖注入容器。"""
    
    def __init__(self):
        self._services: Dict[str, ServiceDescriptor] = {}
        self._instances: Dict[str, Any] = {}
        self._lock = threading.Lock()
        self._scoped_instances: Dict[str, Dict[str, Any]] = {}
        self._current_scope: Optional[str] = None
    
    def register(self, 
                interface: Type[T], 
                implementation: Type[T],
                lifetime: str = ServiceLifetime.SINGLETON) -> 'Container':
        """
        注册服务。
        
        Args:
            interface: 接口类型
            implementation: 实现类型
            lifetime: 生命周期
            
        Returns:
            容器实例（支持链式调用）
        """
        key = self._get_service_key(interface)
        descriptor = ServiceDescriptor(interface, implementation, lifetime)
        
        with self._lock:
            self._services[key] = descriptor
        
        return self
    
    def register_factory(self,
                        interface: Type[T],
                        factory: Callable[[], T],
                        lifetime: str = ServiceLifetime.SINGLETON) -> 'Container':
        """
        注册工厂函数。
        
        Args:
            interface: 接口类型
            factory: 工厂函数
            lifetime: 生命周期
            
        Returns:
            容器实例
        """
        key = self._get_service_key(interface)
        descriptor = ServiceDescriptor(interface, None, lifetime, factory)
        
        with self._lock:
            self._services[key] = descriptor
        
        return self
    
    def register_instance(self, interface: Type[T], instance: T) -> 'Container':
        """
        注册实例。
        
        Args:
            interface: 接口类型
            instance: 实例对象
            
        Returns:
            容器实例
        """
        key = self._get_service_key(interface)
        
        with self._lock:
            self._instances[key] = instance
        
        return self
    
    def resolve(self, interface: Type[T]) -> T:
        """
        解析依赖。
        
        Args:
            interface: 接口类型
            
        Returns:
            服务实例
        """
        key = self._get_service_key(interface)
        
        # 检查已注册的实例
        if key in self._instances:
            return self._instances[key]
        
        # 检查服务描述符
        if key not in self._services:
            raise ValueError(f"Service {interface.__name__} not registered")
        
        descriptor = self._services[key]
        
        # 根据生命周期创建实例
        if descriptor.lifetime == ServiceLifetime.SINGLETON:
            return self._get_singleton_instance(key, descriptor)
        elif descriptor.lifetime == ServiceLifetime.SCOPED:
            return self._get_scoped_instance(key, descriptor)
        else:  # TRANSIENT
            return self._create_instance(descriptor)
    
    def resolve_all(self, interface: Type[T]) -> List[T]:
        """
        解析所有实现。
        
        Args:
            interface: 接口类型
            
        Returns:
            实现列表
        """
        implementations = []
        interface_name = interface.__name__
        
        for key, descriptor in self._services.items():
            if descriptor.interface.__name__ == interface_name:
                instance = self._create_instance(descriptor)
                implementations.append(instance)
        
        return implementations
    
    def create_scope(self, scope_id: str) -> 'Container':
        """
        创建作用域。
        
        Args:
            scope_id: 作用域ID
            
        Returns:
            容器实例
        """
        with self._lock:
            if scope_id not in self._scoped_instances:
                self._scoped_instances[scope_id] = {}
            self._current_scope = scope_id
        
        return self
    
    def dispose_scope(self, scope_id: str) -> None:
        """
        释放作用域。
        
        Args:
            scope_id: 作用域ID
        """
        with self._lock:
            if scope_id in self._scoped_instances:
                # 释放作用域内的实例
                for instance in self._scoped_instances[scope_id].values():
                    if hasattr(instance, 'dispose'):
                        instance.dispose()
                
                del self._scoped_instances[scope_id]
            
            if self._current_scope == scope_id:
                self._current_scope = None
    
    def is_registered(self, interface: Type) -> bool:
        """
        检查服务是否已注册。
        
        Args:
            interface: 接口类型
            
        Returns:
            是否已注册
        """
        key = self._get_service_key(interface)
        return key in self._services or key in self._instances
    
    def clear(self) -> None:
        """清空容器。"""
        with self._lock:
            self._services.clear()
            self._instances.clear()
            self._scoped_instances.clear()
            self._current_scope = None
    
    def _get_service_key(self, interface: Type) -> str:
        """获取服务键。"""
        return f"{interface.__module__}.{interface.__name__}"
    
    def _get_singleton_instance(self, key: str, descriptor: ServiceDescriptor) -> Any:
        """获取单例实例。"""
        if key not in self._instances:
            with self._lock:
                if key not in self._instances:  # 双重检查
                    self._instances[key] = self._create_instance(descriptor)
        
        return self._instances[key]
    
    def _get_scoped_instance(self, key: str, descriptor: ServiceDescriptor) -> Any:
        """获取作用域实例。"""
        if self._current_scope is None:
            raise ValueError("No active scope for scoped service")
        
        scope_instances = self._scoped_instances[self._current_scope]
        
        if key not in scope_instances:
            scope_instances[key] = self._create_instance(descriptor)
        
        return scope_instances[key]
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """创建实例。"""
        if descriptor.factory:
            return descriptor.factory()
        
        if descriptor.implementation is None:
            raise ValueError("No implementation or factory provided")
        
        return self._create_instance_with_injection(descriptor.implementation)
    
    def _create_instance_with_injection(self, cls: Type) -> Any:
        """创建实例并注入依赖。"""
        try:
            sig = inspect.signature(cls.__init__)
            kwargs = {}
            
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue
                
                # 检查类型注解
                if param.annotation != inspect.Parameter.empty:
                    param_type = param.annotation
                    
                    # 处理Optional类型
                    if hasattr(param_type, '__origin__') and param_type.__origin__ is type(Optional[int]).__origin__:
                        param_type = param_type.__args__[0]
                    
                    # 尝试解析依赖
                    if self.is_registered(param_type):
                        kwargs[param_name] = self.resolve(param_type)
                    elif param.default != inspect.Parameter.empty:
                        # 使用默认值
                        kwargs[param_name] = param.default
                    else:
                        # 尝试创建实例（如果是具体类）
                        if inspect.isclass(param_type) and not inspect.isabstract(param_type):
                            kwargs[param_name] = self._create_instance_with_injection(param_type)
            
            return cls(**kwargs)
            
        except Exception as e:
            raise RuntimeError(f"Failed to create instance of {cls.__name__}: {e}")


# 全局容器实例
container = Container()


def inject(interface: Type[T]) -> T:
    """
    依赖注入装饰器辅助函数。
    
    Args:
        interface: 接口类型
        
    Returns:
        服务实例
    """
    return container.resolve(interface)


def service(interface: Type[T], 
           lifetime: str = ServiceLifetime.SINGLETON):
    """
    服务注册装饰器。
    
    Args:
        interface: 接口类型
        lifetime: 生命周期
        
    Returns:
        装饰器函数
    """
    def decorator(cls: Type[T]) -> Type[T]:
        container.register(interface, cls, lifetime)
        return cls
    
    return decorator
