"""
事件系统实现。

提供事件驱动架构的基础设施，支持模块间的异步通信。
"""

from typing import Any, Dict, List, Callable, Optional
from dataclasses import dataclass, field
from datetime import datetime
from abc import ABC, abstractmethod
import threading
import queue
import logging
from enum import Enum


class EventPriority(Enum):
    """事件优先级。"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """事件基类。"""
    event_type: str
    timestamp: datetime = field(default_factory=datetime.now)
    data: Dict[str, Any] = field(default_factory=dict)
    source: str = ""
    priority: EventPriority = EventPriority.NORMAL
    correlation_id: Optional[str] = None

    def __post_init__(self):
        """后处理初始化。"""
        if not self.correlation_id:
            self.correlation_id = f"{self.event_type}_{self.timestamp.timestamp()}"

    def __lt__(self, other):
        """支持优先级队列比较。"""
        if not isinstance(other, Event):
            return NotImplemented
        return self.timestamp < other.timestamp


class IEventHandler(ABC):
    """事件处理器接口。"""
    
    @abstractmethod
    def handle(self, event: Event) -> None:
        """
        处理事件。
        
        Args:
            event: 事件对象
        """
        pass
    
    @abstractmethod
    def can_handle(self, event_type: str) -> bool:
        """
        检查是否能处理指定类型的事件。
        
        Args:
            event_type: 事件类型
            
        Returns:
            是否能处理
        """
        pass


class EventBus:
    """事件总线实现。"""
    
    def __init__(self, max_queue_size: int = 1000):
        """
        初始化事件总线。
        
        Args:
            max_queue_size: 最大队列大小
        """
        self._handlers: Dict[str, List[Callable]] = {}
        self._event_queue = queue.PriorityQueue(maxsize=max_queue_size)
        self._lock = threading.Lock()
        self._running = False
        self._worker_thread: Optional[threading.Thread] = None
        self._logger = logging.getLogger(__name__)
        
        # 事件统计
        self._event_stats: Dict[str, int] = {}
        self._error_count = 0
    
    def start(self) -> None:
        """启动事件总线。"""
        if self._running:
            return
        
        self._running = True
        self._worker_thread = threading.Thread(target=self._process_events, daemon=True)
        self._worker_thread.start()
        self._logger.info("事件总线已启动")
    
    def stop(self) -> None:
        """停止事件总线。"""
        if not self._running:
            return
        
        self._running = False
        
        # 发送停止信号
        stop_event = Event("__STOP__", source="EventBus")
        self._event_queue.put((EventPriority.CRITICAL.value, stop_event))
        
        if self._worker_thread:
            self._worker_thread.join(timeout=5.0)
        
        self._logger.info("事件总线已停止")
    
    def publish(self, event: Event) -> None:
        """
        发布事件。
        
        Args:
            event: 事件对象
        """
        if not self._running:
            self.start()
        
        try:
            # 使用负优先级值，因为PriorityQueue是最小堆
            priority = -event.priority.value
            self._event_queue.put((priority, event), timeout=1.0)
            
            # 更新统计
            with self._lock:
                self._event_stats[event.event_type] = self._event_stats.get(event.event_type, 0) + 1
            
            self._logger.debug(f"事件已发布: {event.event_type}")
            
        except queue.Full:
            self._logger.error(f"事件队列已满，丢弃事件: {event.event_type}")
    
    def subscribe(self, event_type: str, handler: Callable[[Event], None]) -> None:
        """
        订阅事件。
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        with self._lock:
            if event_type not in self._handlers:
                self._handlers[event_type] = []
            
            if handler not in self._handlers[event_type]:
                self._handlers[event_type].append(handler)
                self._logger.info(f"已订阅事件: {event_type}")
    
    def unsubscribe(self, event_type: str, handler: Callable[[Event], None]) -> None:
        """
        取消订阅事件。
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        with self._lock:
            if event_type in self._handlers:
                try:
                    self._handlers[event_type].remove(handler)
                    if not self._handlers[event_type]:
                        del self._handlers[event_type]
                    self._logger.info(f"已取消订阅事件: {event_type}")
                except ValueError:
                    self._logger.warning(f"处理器未找到: {event_type}")
    
    def subscribe_handler(self, handler: IEventHandler) -> None:
        """
        订阅事件处理器。
        
        Args:
            handler: 事件处理器
        """
        # 这里可以根据处理器的能力自动订阅相关事件
        # 简化实现，需要手动指定事件类型
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取事件统计信息。
        
        Returns:
            统计信息字典
        """
        with self._lock:
            return {
                "event_counts": self._event_stats.copy(),
                "error_count": self._error_count,
                "queue_size": self._event_queue.qsize(),
                "handler_count": sum(len(handlers) for handlers in self._handlers.values())
            }
    
    def _process_events(self) -> None:
        """处理事件的工作线程。"""
        while self._running:
            try:
                # 获取事件（阻塞等待）
                priority, event = self._event_queue.get(timeout=1.0)
                
                # 检查停止信号
                if event.event_type == "__STOP__":
                    break
                
                # 处理事件
                self._handle_event(event)
                
            except queue.Empty:
                continue
            except Exception as e:
                self._logger.error(f"处理事件时发生错误: {e}")
                with self._lock:
                    self._error_count += 1
    
    def _handle_event(self, event: Event) -> None:
        """
        处理单个事件。
        
        Args:
            event: 事件对象
        """
        handlers = []
        
        with self._lock:
            # 获取精确匹配的处理器
            if event.event_type in self._handlers:
                handlers.extend(self._handlers[event.event_type])
            
            # 获取通配符处理器
            if "*" in self._handlers:
                handlers.extend(self._handlers["*"])
        
        # 执行处理器
        for handler in handlers:
            try:
                handler(event)
            except Exception as e:
                self._logger.error(f"事件处理器执行失败: {e}")
                with self._lock:
                    self._error_count += 1


# 预定义的事件类型
class EventTypes:
    """预定义的事件类型。"""
    
    # 数据相关事件
    DATA_LOADED = "data.loaded"
    DATA_PROCESSED = "data.processed"
    DATA_VALIDATION_FAILED = "data.validation_failed"
    
    # 因子相关事件
    FACTOR_CALCULATION_STARTED = "factor.calculation_started"
    FACTOR_CALCULATION_COMPLETED = "factor.calculation_completed"
    FACTOR_CALCULATION_FAILED = "factor.calculation_failed"
    
    # 信号相关事件
    SIGNAL_GENERATED = "signal.generated"
    SIGNAL_FILTERED = "signal.filtered"
    
    # 回测相关事件
    BACKTEST_STARTED = "backtest.started"
    BACKTEST_COMPLETED = "backtest.completed"
    BACKTEST_FAILED = "backtest.failed"
    
    # 交易相关事件
    TRADE_EXECUTED = "trade.executed"
    POSITION_UPDATED = "position.updated"
    
    # 风险相关事件
    RISK_LIMIT_EXCEEDED = "risk.limit_exceeded"
    DRAWDOWN_WARNING = "risk.drawdown_warning"
    
    # 系统相关事件
    SYSTEM_STARTED = "system.started"
    SYSTEM_STOPPED = "system.stopped"
    ERROR_OCCURRED = "system.error"


# 全局事件总线实例
event_bus = EventBus()


def publish_event(event_type: str, 
                 data: Optional[Dict[str, Any]] = None,
                 source: str = "",
                 priority: EventPriority = EventPriority.NORMAL) -> None:
    """
    发布事件的便捷函数。
    
    Args:
        event_type: 事件类型
        data: 事件数据
        source: 事件源
        priority: 事件优先级
    """
    event = Event(
        event_type=event_type,
        data=data or {},
        source=source,
        priority=priority
    )
    event_bus.publish(event)


def subscribe_event(event_type: str, handler: Callable[[Event], None]) -> None:
    """
    订阅事件的便捷函数。
    
    Args:
        event_type: 事件类型
        handler: 事件处理函数
    """
    event_bus.subscribe(event_type, handler)


# 示例事件处理器
class LoggingEventHandler(IEventHandler):
    """日志事件处理器。"""
    
    def __init__(self):
        self.logger = logging.getLogger("EventHandler")
    
    def handle(self, event: Event) -> None:
        """处理事件。"""
        self.logger.info(f"事件: {event.event_type}, 来源: {event.source}, 数据: {event.data}")
    
    def can_handle(self, event_type: str) -> bool:
        """检查是否能处理事件。"""
        return True  # 记录所有事件


# 自动启动事件总线
event_bus.start()
