"""
核心接口定义。

定义项目中各个模块的标准接口，确保模块间的解耦和可扩展性。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable
import pandas as pd


class IDataProcessor(ABC):
    """数据处理器接口。"""
    
    @abstractmethod
    def process(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        处理数据。
        
        Args:
            data: 输入数据
            
        Returns:
            处理后的数据
        """
        pass
    
    @abstractmethod
    def validate(self, data: pd.DataFrame) -> bool:
        """
        验证数据。
        
        Args:
            data: 待验证的数据
            
        Returns:
            验证是否通过
        """
        pass


class IFactorCalculator(ABC):
    """因子计算器接口。"""
    
    @abstractmethod
    def calculate(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        计算因子。
        
        Args:
            data: 输入数据
            
        Returns:
            包含因子值的数据
        """
        pass
    
    @abstractmethod
    def get_dependencies(self) -> List[str]:
        """
        获取计算依赖的列名。
        
        Returns:
            依赖的列名列表
        """
        pass
    
    @abstractmethod
    def get_factor_names(self) -> List[str]:
        """
        获取计算的因子名称。
        
        Returns:
            因子名称列表
        """
        pass


class ISignalGenerator(ABC):
    """信号生成器接口。"""
    
    @abstractmethod
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            包含信号的数据
        """
        pass
    
    @abstractmethod
    def get_signal_range(self) -> tuple:
        """
        获取信号值范围。
        
        Returns:
            (最小值, 最大值)
        """
        pass


class ISignalFilter(ABC):
    """信号过滤器接口。"""
    
    @abstractmethod
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        过滤信号。
        
        Args:
            signal_data: 信号数据
            
        Returns:
            过滤后的信号数据
        """
        pass


class IBacktester(ABC):
    """回测器接口。"""
    
    @abstractmethod
    def run_backtest(self, 
                    signals: pd.DataFrame, 
                    market_data: pd.DataFrame,
                    **kwargs) -> Dict[str, Any]:
        """
        运行回测。
        
        Args:
            signals: 信号数据
            market_data: 市场数据
            **kwargs: 其他参数
            
        Returns:
            回测结果
        """
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> List[str]:
        """
        获取支持的性能指标。
        
        Returns:
            性能指标名称列表
        """
        pass


class IPortfolioManager(ABC):
    """投资组合管理器接口。"""
    
    @abstractmethod
    def update_positions(self, signals: pd.DataFrame, 
                        market_data: pd.DataFrame) -> Dict[str, float]:
        """
        更新持仓。
        
        Args:
            signals: 信号数据
            market_data: 市场数据
            
        Returns:
            更新后的持仓字典
        """
        pass
    
    @abstractmethod
    def get_current_positions(self) -> Dict[str, float]:
        """
        获取当前持仓。
        
        Returns:
            当前持仓字典
        """
        pass


class IRiskManager(ABC):
    """风险管理器接口。"""
    
    @abstractmethod
    def check_risk_limits(self, 
                         proposed_positions: Dict[str, float],
                         market_data: pd.DataFrame) -> bool:
        """
        检查风险限制。
        
        Args:
            proposed_positions: 拟议持仓
            market_data: 市场数据
            
        Returns:
            是否通过风险检查
        """
        pass
    
    @abstractmethod
    def get_risk_metrics(self) -> Dict[str, float]:
        """
        获取风险指标。
        
        Returns:
            风险指标字典
        """
        pass


class IEventBus(ABC):
    """事件总线接口。"""
    
    @abstractmethod
    def publish(self, event: 'Event') -> None:
        """
        发布事件。
        
        Args:
            event: 事件对象
        """
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, handler: Callable) -> None:
        """
        订阅事件。
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: str, handler: Callable) -> None:
        """
        取消订阅事件。
        
        Args:
            event_type: 事件类型
            handler: 事件处理函数
        """
        pass


class IDataSource(ABC):
    """数据源接口。"""
    
    @abstractmethod
    def load_data(self, 
                 symbols: List[str], 
                 start_date: str, 
                 end_date: str,
                 **kwargs) -> pd.DataFrame:
        """
        加载数据。
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            **kwargs: 其他参数
            
        Returns:
            数据DataFrame
        """
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """
        获取可用的股票代码。
        
        Returns:
            股票代码列表
        """
        pass


class IDataStorage(ABC):
    """数据存储接口。"""
    
    @abstractmethod
    def save_data(self, data: pd.DataFrame, table_name: str) -> None:
        """
        保存数据。
        
        Args:
            data: 数据
            table_name: 表名
        """
        pass
    
    @abstractmethod
    def load_data(self, table_name: str, 
                 filters: Optional[Dict] = None) -> pd.DataFrame:
        """
        加载数据。
        
        Args:
            table_name: 表名
            filters: 过滤条件
            
        Returns:
            数据DataFrame
        """
        pass
    
    @abstractmethod
    def delete_data(self, table_name: str, 
                   filters: Optional[Dict] = None) -> None:
        """
        删除数据。
        
        Args:
            table_name: 表名
            filters: 过滤条件
        """
        pass


class IOptimizer(ABC):
    """优化器接口。"""
    
    @abstractmethod
    def optimize(self, 
                objective_function: Callable,
                parameter_space: Dict[str, Any],
                **kwargs) -> Dict[str, Any]:
        """
        执行优化。
        
        Args:
            objective_function: 目标函数
            parameter_space: 参数空间
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        pass
    
    @abstractmethod
    def get_optimization_history(self) -> List[Dict[str, Any]]:
        """
        获取优化历史。
        
        Returns:
            优化历史列表
        """
        pass


class IExperimentTracker(ABC):
    """实验追踪器接口。"""
    
    @abstractmethod
    def start_experiment(self, experiment_name: str, 
                        parameters: Dict[str, Any]) -> str:
        """
        开始实验。
        
        Args:
            experiment_name: 实验名称
            parameters: 实验参数
            
        Returns:
            实验ID
        """
        pass
    
    @abstractmethod
    def log_metrics(self, experiment_id: str, 
                   metrics: Dict[str, float]) -> None:
        """
        记录指标。
        
        Args:
            experiment_id: 实验ID
            metrics: 指标字典
        """
        pass
    
    @abstractmethod
    def end_experiment(self, experiment_id: str) -> None:
        """
        结束实验。
        
        Args:
            experiment_id: 实验ID
        """
        pass
