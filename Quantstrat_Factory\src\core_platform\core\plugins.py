"""
插件化架构系统。

提供插件的注册、发现、加载和管理功能。
"""

from typing import Dict, List, Type, Any, Optional, Callable
from abc import ABC, abstractmethod
import importlib
import inspect
import os
import json
from pathlib import Path
import logging
from dataclasses import dataclass, field
from enum import Enum


class PluginStatus(Enum):
    """插件状态。"""
    REGISTERED = "registered"
    LOADED = "loaded"
    ACTIVE = "active"
    DISABLED = "disabled"
    ERROR = "error"


@dataclass
class PluginMetadata:
    """插件元数据。"""
    name: str
    version: str
    description: str
    author: str
    category: str
    dependencies: List[str] = field(default_factory=list)
    config_schema: Dict[str, Any] = field(default_factory=dict)
    entry_point: str = ""
    status: PluginStatus = PluginStatus.REGISTERED


class IPlugin(ABC):
    """插件接口。"""
    
    @abstractmethod
    def get_metadata(self) -> PluginMetadata:
        """
        获取插件元数据。
        
        Returns:
            插件元数据
        """
        pass
    
    @abstractmethod
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        初始化插件。
        
        Args:
            config: 插件配置
        """
        pass
    
    @abstractmethod
    def activate(self) -> None:
        """激活插件。"""
        pass
    
    @abstractmethod
    def deactivate(self) -> None:
        """停用插件。"""
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """清理插件资源。"""
        pass


class IFactorPlugin(IPlugin):
    """因子插件接口。"""
    
    @abstractmethod
    def calculate_factor(self, data: Any) -> Any:
        """
        计算因子。
        
        Args:
            data: 输入数据
            
        Returns:
            因子计算结果
        """
        pass
    
    @abstractmethod
    def get_factor_names(self) -> List[str]:
        """
        获取因子名称列表。
        
        Returns:
            因子名称列表
        """
        pass


class IStrategyPlugin(IPlugin):
    """策略插件接口。"""
    
    @abstractmethod
    def generate_signals(self, factor_data: Any) -> Any:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            交易信号
        """
        pass
    
    @abstractmethod
    def get_parameters(self) -> Dict[str, Any]:
        """
        获取策略参数。
        
        Returns:
            策略参数字典
        """
        pass


class IDataSourcePlugin(IPlugin):
    """数据源插件接口。"""
    
    @abstractmethod
    def load_data(self, symbols: List[str], start_date: str, end_date: str) -> Any:
        """
        加载数据。
        
        Args:
            symbols: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            数据
        """
        pass
    
    @abstractmethod
    def get_available_symbols(self) -> List[str]:
        """
        获取可用的股票代码。
        
        Returns:
            股票代码列表
        """
        pass


class PluginRegistry:
    """插件注册表。"""
    
    def __init__(self):
        self._plugins: Dict[str, PluginMetadata] = {}
        self._instances: Dict[str, IPlugin] = {}
        self._categories: Dict[str, List[str]] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_plugin(self, plugin_class: Type[IPlugin]) -> None:
        """
        注册插件类。
        
        Args:
            plugin_class: 插件类
        """
        try:
            # 创建临时实例获取元数据
            temp_instance = plugin_class()
            metadata = temp_instance.get_metadata()
            
            # 验证插件
            self._validate_plugin(plugin_class, metadata)
            
            # 注册插件
            self._plugins[metadata.name] = metadata
            
            # 按类别分组
            if metadata.category not in self._categories:
                self._categories[metadata.category] = []
            self._categories[metadata.category].append(metadata.name)
            
            self.logger.info(f"插件已注册: {metadata.name} v{metadata.version}")
            
        except Exception as e:
            self.logger.error(f"注册插件失败: {e}")
            raise
    
    def load_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> IPlugin:
        """
        加载插件。
        
        Args:
            plugin_name: 插件名称
            config: 插件配置
            
        Returns:
            插件实例
        """
        if plugin_name not in self._plugins:
            raise ValueError(f"插件未注册: {plugin_name}")
        
        if plugin_name in self._instances:
            return self._instances[plugin_name]
        
        metadata = self._plugins[plugin_name]
        
        try:
            # 检查依赖
            self._check_dependencies(metadata.dependencies)
            
            # 动态导入插件模块
            module_path, class_name = metadata.entry_point.rsplit('.', 1)
            module = importlib.import_module(module_path)
            plugin_class = getattr(module, class_name)
            
            # 创建插件实例
            instance = plugin_class()
            
            # 初始化插件
            instance.initialize(config or {})
            
            # 缓存实例
            self._instances[plugin_name] = instance
            
            # 更新状态
            metadata.status = PluginStatus.LOADED
            
            self.logger.info(f"插件已加载: {plugin_name}")
            
            return instance
            
        except Exception as e:
            metadata.status = PluginStatus.ERROR
            self.logger.error(f"加载插件失败 {plugin_name}: {e}")
            raise
    
    def activate_plugin(self, plugin_name: str) -> None:
        """
        激活插件。
        
        Args:
            plugin_name: 插件名称
        """
        if plugin_name not in self._instances:
            raise ValueError(f"插件未加载: {plugin_name}")
        
        instance = self._instances[plugin_name]
        instance.activate()
        
        metadata = self._plugins[plugin_name]
        metadata.status = PluginStatus.ACTIVE
        
        self.logger.info(f"插件已激活: {plugin_name}")
    
    def deactivate_plugin(self, plugin_name: str) -> None:
        """
        停用插件。
        
        Args:
            plugin_name: 插件名称
        """
        if plugin_name not in self._instances:
            return
        
        instance = self._instances[plugin_name]
        instance.deactivate()
        
        metadata = self._plugins[plugin_name]
        metadata.status = PluginStatus.LOADED
        
        self.logger.info(f"插件已停用: {plugin_name}")
    
    def unload_plugin(self, plugin_name: str) -> None:
        """
        卸载插件。
        
        Args:
            plugin_name: 插件名称
        """
        if plugin_name in self._instances:
            instance = self._instances[plugin_name]
            instance.cleanup()
            del self._instances[plugin_name]
        
        if plugin_name in self._plugins:
            metadata = self._plugins[plugin_name]
            metadata.status = PluginStatus.REGISTERED
        
        self.logger.info(f"插件已卸载: {plugin_name}")
    
    def get_plugins_by_category(self, category: str) -> List[str]:
        """
        按类别获取插件列表。
        
        Args:
            category: 插件类别
            
        Returns:
            插件名称列表
        """
        return self._categories.get(category, [])
    
    def get_plugin_metadata(self, plugin_name: str) -> Optional[PluginMetadata]:
        """
        获取插件元数据。
        
        Args:
            plugin_name: 插件名称
            
        Returns:
            插件元数据
        """
        return self._plugins.get(plugin_name)
    
    def list_plugins(self) -> Dict[str, PluginMetadata]:
        """
        列出所有插件。
        
        Returns:
            插件字典
        """
        return self._plugins.copy()
    
    def _validate_plugin(self, plugin_class: Type[IPlugin], metadata: PluginMetadata) -> None:
        """验证插件。"""
        # 检查是否实现了必要的接口
        if not issubclass(plugin_class, IPlugin):
            raise ValueError(f"插件必须实现IPlugin接口: {metadata.name}")
        
        # 检查元数据完整性
        required_fields = ['name', 'version', 'description', 'author', 'category']
        for field in required_fields:
            if not getattr(metadata, field):
                raise ValueError(f"插件元数据缺少必要字段 {field}: {metadata.name}")
    
    def _check_dependencies(self, dependencies: List[str]) -> None:
        """检查插件依赖。"""
        for dep in dependencies:
            if dep not in self._plugins:
                raise ValueError(f"缺少依赖插件: {dep}")
            
            dep_metadata = self._plugins[dep]
            if dep_metadata.status not in [PluginStatus.LOADED, PluginStatus.ACTIVE]:
                raise ValueError(f"依赖插件未加载: {dep}")


class PluginManager:
    """插件管理器。"""
    
    def __init__(self, plugin_dirs: Optional[List[str]] = None):
        """
        初始化插件管理器。
        
        Args:
            plugin_dirs: 插件目录列表
        """
        self.registry = PluginRegistry()
        self.plugin_dirs = plugin_dirs or ['plugins']
        self.logger = logging.getLogger(__name__)
    
    def discover_plugins(self) -> None:
        """发现并注册插件。"""
        for plugin_dir in self.plugin_dirs:
            self._scan_directory(plugin_dir)
    
    def load_plugin_config(self, config_path: str) -> Dict[str, Any]:
        """
        加载插件配置。
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        config_file = Path(config_path)
        
        if not config_file.exists():
            return {}
        
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def save_plugin_config(self, config: Dict[str, Any], config_path: str) -> None:
        """
        保存插件配置。
        
        Args:
            config: 配置字典
            config_path: 配置文件路径
        """
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def get_factor_plugins(self) -> List[IFactorPlugin]:
        """
        获取所有因子插件。
        
        Returns:
            因子插件列表
        """
        factor_plugins = []
        for plugin_name in self.registry.get_plugins_by_category('factor'):
            instance = self.registry._instances.get(plugin_name)
            if instance and isinstance(instance, IFactorPlugin):
                factor_plugins.append(instance)
        
        return factor_plugins
    
    def get_strategy_plugins(self) -> List[IStrategyPlugin]:
        """
        获取所有策略插件。
        
        Returns:
            策略插件列表
        """
        strategy_plugins = []
        for plugin_name in self.registry.get_plugins_by_category('strategy'):
            instance = self.registry._instances.get(plugin_name)
            if instance and isinstance(instance, IStrategyPlugin):
                strategy_plugins.append(instance)
        
        return strategy_plugins
    
    def _scan_directory(self, directory: str) -> None:
        """扫描目录中的插件。"""
        plugin_dir = Path(directory)
        
        if not plugin_dir.exists():
            self.logger.warning(f"插件目录不存在: {directory}")
            return
        
        # 扫描Python文件
        for py_file in plugin_dir.rglob("*.py"):
            if py_file.name.startswith('__'):
                continue
            
            try:
                self._load_plugin_from_file(py_file)
            except Exception as e:
                self.logger.error(f"加载插件文件失败 {py_file}: {e}")
    
    def _load_plugin_from_file(self, file_path: Path) -> None:
        """从文件加载插件。"""
        # 构建模块路径
        relative_path = file_path.relative_to(Path.cwd())
        module_path = str(relative_path.with_suffix('')).replace(os.sep, '.')
        
        # 导入模块
        module = importlib.import_module(module_path)
        
        # 查找插件类
        for name, obj in inspect.getmembers(module, inspect.isclass):
            if (issubclass(obj, IPlugin) and 
                obj != IPlugin and 
                obj.__module__ == module.__name__):
                
                self.registry.register_plugin(obj)


# 全局插件管理器实例
plugin_manager = PluginManager()


def register_plugin(plugin_class: Type[IPlugin]) -> None:
    """
    注册插件的便捷函数。
    
    Args:
        plugin_class: 插件类
    """
    plugin_manager.registry.register_plugin(plugin_class)


def plugin(category: str, name: str, version: str = "1.0.0", 
          description: str = "", author: str = ""):
    """
    插件装饰器。
    
    Args:
        category: 插件类别
        name: 插件名称
        version: 版本号
        description: 描述
        author: 作者
        
    Returns:
        装饰器函数
    """
    def decorator(cls: Type[IPlugin]) -> Type[IPlugin]:
        # 自动注册插件
        register_plugin(cls)
        return cls
    
    return decorator
