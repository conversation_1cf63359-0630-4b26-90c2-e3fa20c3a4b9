"""
数据库优化模块。

提供数据库性能优化功能，包括：
- 索引优化
- 查询优化
- 分区策略
- 时序数据库集成
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
import sqlite3
import time


logger = logging.getLogger(__name__)


@dataclass
class IndexRecommendation:
    """索引推荐。"""
    table_name: str
    columns: List[str]
    index_type: str  # 'btree', 'hash', 'composite'
    estimated_benefit: float
    reason: str


@dataclass
class QueryPerformance:
    """查询性能统计。"""
    query: str
    execution_time: float
    rows_examined: int
    rows_returned: int
    index_used: bool
    optimization_suggestions: List[str]


class DatabaseOptimizer(ABC):
    """数据库优化器基类。"""
    
    @abstractmethod
    def analyze_query_performance(self, query: str) -> QueryPerformance:
        """分析查询性能。"""
        pass
    
    @abstractmethod
    def recommend_indexes(self, table_name: str) -> List[IndexRecommendation]:
        """推荐索引。"""
        pass
    
    @abstractmethod
    def optimize_table_structure(self, table_name: str) -> Dict[str, Any]:
        """优化表结构。"""
        pass


class SQLiteOptimizer(DatabaseOptimizer):
    """SQLite优化器。"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
        self.connection.row_factory = sqlite3.Row
    
    def analyze_query_performance(self, query: str) -> QueryPerformance:
        """分析SQLite查询性能。"""
        cursor = self.connection.cursor()
        
        # 启用查询计划分析
        explain_query = f"EXPLAIN QUERY PLAN {query}"
        
        start_time = time.time()
        cursor.execute(query)
        results = cursor.fetchall()
        execution_time = time.time() - start_time
        
        # 获取查询计划
        cursor.execute(explain_query)
        query_plan = cursor.fetchall()
        
        # 分析查询计划
        index_used = any('USING INDEX' in str(row) for row in query_plan)
        
        # 生成优化建议
        suggestions = []
        if not index_used:
            suggestions.append("考虑为查询条件列添加索引")
        
        if execution_time > 1.0:
            suggestions.append("查询执行时间较长，考虑优化查询逻辑")
        
        return QueryPerformance(
            query=query,
            execution_time=execution_time,
            rows_examined=len(results),
            rows_returned=len(results),
            index_used=index_used,
            optimization_suggestions=suggestions
        )
    
    def recommend_indexes(self, table_name: str) -> List[IndexRecommendation]:
        """推荐SQLite索引。"""
        cursor = self.connection.cursor()
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        
        recommendations = []
        
        # 推荐常见的索引
        if 'datetime' in columns:
            recommendations.append(IndexRecommendation(
                table_name=table_name,
                columns=['datetime'],
                index_type='btree',
                estimated_benefit=0.8,
                reason="时间序列查询优化"
            ))
        
        if 'symbol' in columns:
            recommendations.append(IndexRecommendation(
                table_name=table_name,
                columns=['symbol'],
                index_type='btree',
                estimated_benefit=0.7,
                reason="股票代码查询优化"
            ))
        
        if 'datetime' in columns and 'symbol' in columns:
            recommendations.append(IndexRecommendation(
                table_name=table_name,
                columns=['datetime', 'symbol'],
                index_type='composite',
                estimated_benefit=0.9,
                reason="时间和股票复合查询优化"
            ))
        
        return recommendations
    
    def optimize_table_structure(self, table_name: str) -> Dict[str, Any]:
        """优化SQLite表结构。"""
        cursor = self.connection.cursor()
        
        # 分析表统计信息
        cursor.execute(f"ANALYZE {table_name}")
        
        # 获取表大小
        cursor.execute(f"""
            SELECT COUNT(*) as row_count,
                   SUM(LENGTH(quote(datetime)) + LENGTH(quote(symbol))) as estimated_size
            FROM {table_name}
        """)
        stats = cursor.fetchone()
        
        # 检查索引使用情况
        cursor.execute(f"PRAGMA index_list({table_name})")
        existing_indexes = cursor.fetchall()
        
        optimization_results = {
            'table_name': table_name,
            'row_count': stats[0] if stats else 0,
            'estimated_size_bytes': stats[1] if stats else 0,
            'existing_indexes': len(existing_indexes),
            'recommendations': []
        }
        
        # 生成优化建议
        if stats and stats[0] > 100000:  # 大表
            optimization_results['recommendations'].append("考虑表分区")
        
        if len(existing_indexes) == 0:
            optimization_results['recommendations'].append("添加必要的索引")
        
        return optimization_results
    
    def create_optimized_indexes(self, table_name: str):
        """创建优化索引。"""
        recommendations = self.recommend_indexes(table_name)
        cursor = self.connection.cursor()
        
        for rec in recommendations:
            index_name = f"idx_{table_name}_{'_'.join(rec.columns)}"
            columns_str = ', '.join(rec.columns)
            
            try:
                cursor.execute(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({columns_str})")
                logger.info(f"创建索引: {index_name}")
            except Exception as e:
                logger.error(f"创建索引失败 {index_name}: {e}")
        
        self.connection.commit()


class TimeSeriesDBManager:
    """时序数据库管理器。"""
    
    def __init__(self, db_type: str = 'influxdb'):
        self.db_type = db_type
        self.connection = None
    
    def connect(self, **connection_params):
        """连接时序数据库。"""
        if self.db_type == 'influxdb':
            try:
                from influxdb_client import InfluxDBClient
                self.connection = InfluxDBClient(**connection_params)
                logger.info("InfluxDB连接成功")
            except ImportError:
                logger.warning("InfluxDB客户端未安装，使用模拟连接")
                self.connection = "mock_influxdb"
        else:
            raise ValueError(f"不支持的时序数据库类型: {self.db_type}")
    
    def write_market_data(self, data: pd.DataFrame, measurement: str = "market_data"):
        """写入市场数据到时序数据库。"""
        if self.connection == "mock_influxdb":
            logger.info(f"模拟写入 {len(data)} 条记录到 {measurement}")
            return
        
        # 实际的InfluxDB写入逻辑
        try:
            from influxdb_client.client.write_api import SYNCHRONOUS
            
            write_api = self.connection.write_api(write_option=SYNCHRONOUS)
            
            # 转换数据格式
            points = []
            for _, row in data.iterrows():
                point = {
                    "measurement": measurement,
                    "tags": {"symbol": row.get("symbol", "")},
                    "fields": {
                        "open": float(row.get("open", 0)),
                        "high": float(row.get("high", 0)),
                        "low": float(row.get("low", 0)),
                        "close": float(row.get("close", 0)),
                        "volume": int(row.get("volume", 0))
                    },
                    "time": row.get("datetime")
                }
                points.append(point)
            
            write_api.write(bucket="market_data", record=points)
            logger.info(f"成功写入 {len(points)} 条记录")
            
        except Exception as e:
            logger.error(f"写入时序数据库失败: {e}")
    
    def query_market_data(self, 
                         symbols: List[str],
                         start_time: datetime,
                         end_time: datetime,
                         measurement: str = "market_data") -> pd.DataFrame:
        """从时序数据库查询市场数据。"""
        if self.connection == "mock_influxdb":
            # 返回模拟数据
            dates = pd.date_range(start_time, end_time, freq='D')
            data_list = []
            
            for date in dates:
                for symbol in symbols:
                    data_list.append({
                        'datetime': date,
                        'symbol': symbol,
                        'open': 100 + np.random.normal(0, 5),
                        'high': 105 + np.random.normal(0, 5),
                        'low': 95 + np.random.normal(0, 5),
                        'close': 100 + np.random.normal(0, 5),
                        'volume': np.random.randint(1000, 10000)
                    })
            
            return pd.DataFrame(data_list)
        
        # 实际的InfluxDB查询逻辑
        try:
            query_api = self.connection.query_api()
            
            symbols_filter = '|'.join(symbols)
            flux_query = f'''
                from(bucket: "market_data")
                |> range(start: {start_time.isoformat()}, stop: {end_time.isoformat()})
                |> filter(fn: (r) => r._measurement == "{measurement}")
                |> filter(fn: (r) => r.symbol =~ /{symbols_filter}/)
                |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            '''
            
            result = query_api.query_data_frame(flux_query)
            return result
            
        except Exception as e:
            logger.error(f"查询时序数据库失败: {e}")
            return pd.DataFrame()


class DatabasePerformanceAnalyzer:
    """数据库性能分析器。"""
    
    def __init__(self, optimizer: DatabaseOptimizer):
        self.optimizer = optimizer
        self.query_log = []
    
    def log_query(self, query: str):
        """记录查询。"""
        performance = self.optimizer.analyze_query_performance(query)
        self.query_log.append(performance)
        
        if performance.execution_time > 1.0:
            logger.warning(f"慢查询检测: {query[:100]}... (耗时: {performance.execution_time:.2f}s)")
    
    def get_slow_queries(self, threshold: float = 1.0) -> List[QueryPerformance]:
        """获取慢查询。"""
        return [q for q in self.query_log if q.execution_time > threshold]
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """生成优化报告。"""
        if not self.query_log:
            return {"message": "没有查询记录"}
        
        total_queries = len(self.query_log)
        slow_queries = self.get_slow_queries()
        avg_execution_time = np.mean([q.execution_time for q in self.query_log])
        
        # 统计索引使用情况
        queries_with_index = [q for q in self.query_log if q.index_used]
        index_usage_rate = len(queries_with_index) / total_queries if total_queries > 0 else 0
        
        return {
            'total_queries': total_queries,
            'slow_queries_count': len(slow_queries),
            'slow_query_rate': len(slow_queries) / total_queries if total_queries > 0 else 0,
            'average_execution_time': avg_execution_time,
            'index_usage_rate': index_usage_rate,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议。"""
        recommendations = []
        
        slow_queries = self.get_slow_queries()
        if slow_queries:
            recommendations.append(f"发现 {len(slow_queries)} 个慢查询，建议优化")
        
        queries_without_index = [q for q in self.query_log if not q.index_used]
        if len(queries_without_index) > len(self.query_log) * 0.5:
            recommendations.append("超过50%的查询未使用索引，建议添加索引")
        
        if not recommendations:
            recommendations.append("数据库性能良好，无需特别优化")
        
        return recommendations


# 示例使用
def demonstrate_database_optimization():
    """演示数据库优化功能。"""
    print("=== 数据库优化示例 ===")
    
    # 1. SQLite优化
    print("\n--- SQLite优化 ---")
    
    # 创建测试数据库
    import tempfile
    import os
    
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        db_path = tmp_file.name
    
    try:
        # 创建测试表和数据
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE market_data (
                datetime TEXT,
                symbol TEXT,
                open REAL,
                high REAL,
                low REAL,
                close REAL,
                volume INTEGER
            )
        ''')
        
        # 插入测试数据
        test_data = []
        for i in range(1000):
            test_data.append((
                f"2023-01-{(i % 30) + 1:02d}",
                f"STOCK_{i % 10}",
                100 + np.random.normal(0, 5),
                105 + np.random.normal(0, 5),
                95 + np.random.normal(0, 5),
                100 + np.random.normal(0, 5),
                np.random.randint(1000, 10000)
            ))
        
        cursor.executemany(
            "INSERT INTO market_data VALUES (?, ?, ?, ?, ?, ?, ?)",
            test_data
        )
        conn.commit()
        conn.close()
        
        # 使用优化器
        optimizer = SQLiteOptimizer(db_path)
        
        # 分析表结构
        optimization_results = optimizer.optimize_table_structure('market_data')
        print(f"表分析结果: {optimization_results}")
        
        # 获取索引推荐
        recommendations = optimizer.recommend_indexes('market_data')
        print(f"索引推荐: {len(recommendations)} 个")
        for rec in recommendations:
            print(f"  - {rec.columns}: {rec.reason}")
        
        # 创建推荐的索引
        optimizer.create_optimized_indexes('market_data')
        
        # 性能分析
        analyzer = DatabasePerformanceAnalyzer(optimizer)
        
        # 模拟一些查询
        test_queries = [
            "SELECT * FROM market_data WHERE symbol = 'STOCK_1'",
            "SELECT * FROM market_data WHERE datetime = '2023-01-15'",
            "SELECT * FROM market_data WHERE symbol = 'STOCK_1' AND datetime = '2023-01-15'"
        ]
        
        for query in test_queries:
            analyzer.log_query(query)
        
        # 生成优化报告
        report = analyzer.generate_optimization_report()
        print(f"\n性能分析报告:")
        print(f"  总查询数: {report['total_queries']}")
        print(f"  平均执行时间: {report['average_execution_time']:.4f}s")
        print(f"  索引使用率: {report['index_usage_rate']:.2%}")
        
    finally:
        # 清理测试文件
        os.unlink(db_path)
    
    # 2. 时序数据库示例
    print("\n--- 时序数据库示例 ---")
    
    ts_manager = TimeSeriesDBManager('influxdb')
    ts_manager.connect()  # 使用模拟连接
    
    # 创建测试数据
    ts_data = pd.DataFrame({
        'datetime': pd.date_range('2023-01-01', periods=100),
        'symbol': np.random.choice(['A', 'B', 'C'], 100),
        'open': np.random.normal(100, 5, 100),
        'high': np.random.normal(105, 5, 100),
        'low': np.random.normal(95, 5, 100),
        'close': np.random.normal(100, 5, 100),
        'volume': np.random.randint(1000, 10000, 100)
    })
    
    # 写入数据
    ts_manager.write_market_data(ts_data)
    
    # 查询数据
    query_result = ts_manager.query_market_data(
        symbols=['A', 'B'],
        start_time=datetime(2023, 1, 1),
        end_time=datetime(2023, 1, 10)
    )
    print(f"查询结果: {len(query_result)} 条记录")
    
    print("\n✅ 数据库优化示例完成")


if __name__ == "__main__":
    demonstrate_database_optimization()
