"""
Web界面模块。

提供基于Flask的Web界面，用于策略管理和结果展示。
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path
import pandas as pd


logger = logging.getLogger(__name__)


class WebInterface:
    """Web界面管理器。"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 5000, debug: bool = False):
        """
        初始化Web界面。
        
        Args:
            host: 主机地址
            port: 端口号
            debug: 调试模式
        """
        self.host = host
        self.port = port
        self.debug = debug
        self.app = None
        self.data_cache = {}
        
        # 尝试导入Flask
        try:
            from flask import Flask, render_template, jsonify, request, send_from_directory
            self.Flask = Flask
            self.render_template = render_template
            self.jsonify = jsonify
            self.request = request
            self.send_from_directory = send_from_directory
            self.flask_available = True
        except ImportError:
            logger.warning("Flask未安装，Web界面功能不可用")
            self.flask_available = False
    
    def create_app(self):
        """创建Flask应用。"""
        if not self.flask_available:
            raise ImportError("Flask未安装，无法创建Web应用")
        
        self.app = self.Flask(__name__)
        self.app.secret_key = 'quantstrat_factory_secret_key'
        
        # 注册路由
        self._register_routes()
        
        return self.app
    
    def _register_routes(self):
        """注册路由。"""
        
        @self.app.route('/')
        def index():
            """主页。"""
            return self._render_or_json('index.html', {
                'title': 'Quantstrat Factory',
                'version': '1.0.0'
            })
        
        @self.app.route('/api/health')
        def health_check():
            """健康检查API。"""
            return self.jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0'
            })
        
        @self.app.route('/api/strategies')
        def list_strategies():
            """列出策略。"""
            strategies = self._get_strategies()
            return self.jsonify({
                'strategies': strategies,
                'count': len(strategies)
            })
        
        @self.app.route('/api/strategies/<strategy_id>')
        def get_strategy(strategy_id):
            """获取策略详情。"""
            strategy = self._get_strategy_details(strategy_id)
            if strategy:
                return self.jsonify(strategy)
            else:
                return self.jsonify({'error': '策略不存在'}), 404
        
        @self.app.route('/api/backtests')
        def list_backtests():
            """列出回测结果。"""
            backtests = self._get_backtests()
            return self.jsonify({
                'backtests': backtests,
                'count': len(backtests)
            })
        
        @self.app.route('/api/backtests/<backtest_id>')
        def get_backtest(backtest_id):
            """获取回测详情。"""
            backtest = self._get_backtest_details(backtest_id)
            if backtest:
                return self.jsonify(backtest)
            else:
                return self.jsonify({'error': '回测不存在'}), 404
        
        @self.app.route('/api/factors')
        def list_factors():
            """列出因子。"""
            factors = self._get_factors()
            return self.jsonify({
                'factors': factors,
                'count': len(factors)
            })
        
        @self.app.route('/api/factors/<factor_name>/analysis')
        def get_factor_analysis(factor_name):
            """获取因子分析结果。"""
            analysis = self._get_factor_analysis(factor_name)
            if analysis:
                return self.jsonify(analysis)
            else:
                return self.jsonify({'error': '因子分析不存在'}), 404
        
        @self.app.route('/api/performance/summary')
        def get_performance_summary():
            """获取性能摘要。"""
            summary = self._get_performance_summary()
            return self.jsonify(summary)
        
        @self.app.route('/dashboard')
        def dashboard():
            """仪表板页面。"""
            return self._render_or_json('dashboard.html', {
                'title': '仪表板',
                'strategies_count': len(self._get_strategies()),
                'backtests_count': len(self._get_backtests()),
                'factors_count': len(self._get_factors())
            })
        
        @self.app.route('/strategies')
        def strategies_page():
            """策略页面。"""
            strategies = self._get_strategies()
            return self._render_or_json('strategies.html', {
                'title': '策略管理',
                'strategies': strategies
            })
        
        @self.app.route('/backtests')
        def backtests_page():
            """回测页面。"""
            backtests = self._get_backtests()
            return self._render_or_json('backtests.html', {
                'title': '回测结果',
                'backtests': backtests
            })
        
        @self.app.route('/factors')
        def factors_page():
            """因子页面。"""
            factors = self._get_factors()
            return self._render_or_json('factors.html', {
                'title': '因子分析',
                'factors': factors
            })
    
    def _render_or_json(self, template: str, data: Dict[str, Any]):
        """根据请求类型返回HTML或JSON。"""
        if self.request.headers.get('Accept', '').startswith('application/json'):
            return self.jsonify(data)
        else:
            # 如果没有模板文件，返回JSON
            return self.jsonify(data)
    
    def _get_strategies(self) -> List[Dict[str, Any]]:
        """获取策略列表。"""
        # 模拟策略数据
        return [
            {
                'id': 'strategy_001',
                'name': '均值回归策略',
                'description': '基于价格均值回归的交易策略',
                'status': 'active',
                'created_at': '2024-01-01T00:00:00Z',
                'performance': {
                    'total_return': 0.15,
                    'sharpe_ratio': 1.2,
                    'max_drawdown': 0.08
                }
            },
            {
                'id': 'strategy_002',
                'name': '动量策略',
                'description': '基于价格动量的交易策略',
                'status': 'active',
                'created_at': '2024-01-02T00:00:00Z',
                'performance': {
                    'total_return': 0.22,
                    'sharpe_ratio': 1.5,
                    'max_drawdown': 0.12
                }
            }
        ]
    
    def _get_strategy_details(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略详情。"""
        strategies = self._get_strategies()
        for strategy in strategies:
            if strategy['id'] == strategy_id:
                # 添加详细信息
                strategy['parameters'] = {
                    'lookback_window': 20,
                    'threshold': 2.0,
                    'position_size': 0.1
                }
                strategy['signals'] = [
                    {'date': '2024-01-01', 'symbol': 'AAPL', 'signal': 'buy', 'price': 150.0},
                    {'date': '2024-01-02', 'symbol': 'AAPL', 'signal': 'sell', 'price': 155.0}
                ]
                return strategy
        return None
    
    def _get_backtests(self) -> List[Dict[str, Any]]:
        """获取回测列表。"""
        return [
            {
                'id': 'backtest_001',
                'strategy_id': 'strategy_001',
                'strategy_name': '均值回归策略',
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'status': 'completed',
                'created_at': '2024-01-01T00:00:00Z',
                'metrics': {
                    'total_return': 0.15,
                    'sharpe_ratio': 1.2,
                    'max_drawdown': 0.08,
                    'win_rate': 0.65
                }
            },
            {
                'id': 'backtest_002',
                'strategy_id': 'strategy_002',
                'strategy_name': '动量策略',
                'start_date': '2023-01-01',
                'end_date': '2023-12-31',
                'status': 'completed',
                'created_at': '2024-01-02T00:00:00Z',
                'metrics': {
                    'total_return': 0.22,
                    'sharpe_ratio': 1.5,
                    'max_drawdown': 0.12,
                    'win_rate': 0.58
                }
            }
        ]
    
    def _get_backtest_details(self, backtest_id: str) -> Optional[Dict[str, Any]]:
        """获取回测详情。"""
        backtests = self._get_backtests()
        for backtest in backtests:
            if backtest['id'] == backtest_id:
                # 添加详细信息
                backtest['equity_curve'] = [
                    {'date': '2023-01-01', 'value': 100000},
                    {'date': '2023-06-01', 'value': 110000},
                    {'date': '2023-12-31', 'value': 115000}
                ]
                backtest['trades'] = [
                    {'date': '2023-01-15', 'symbol': 'AAPL', 'side': 'buy', 'quantity': 100, 'price': 150.0},
                    {'date': '2023-01-20', 'symbol': 'AAPL', 'side': 'sell', 'quantity': 100, 'price': 155.0}
                ]
                return backtest
        return None
    
    def _get_factors(self) -> List[Dict[str, Any]]:
        """获取因子列表。"""
        return [
            {
                'name': 'momentum',
                'description': '动量因子',
                'category': 'technical',
                'status': 'active',
                'ic_mean': 0.05,
                'ic_std': 0.15,
                'ic_ir': 0.33
            },
            {
                'name': 'volatility',
                'description': '波动率因子',
                'category': 'risk',
                'status': 'active',
                'ic_mean': -0.03,
                'ic_std': 0.12,
                'ic_ir': -0.25
            },
            {
                'name': 'value',
                'description': '价值因子',
                'category': 'fundamental',
                'status': 'active',
                'ic_mean': 0.02,
                'ic_std': 0.10,
                'ic_ir': 0.20
            }
        ]
    
    def _get_factor_analysis(self, factor_name: str) -> Optional[Dict[str, Any]]:
        """获取因子分析结果。"""
        factors = self._get_factors()
        for factor in factors:
            if factor['name'] == factor_name:
                # 添加分析结果
                factor['ic_series'] = [
                    {'date': '2023-01-01', 'ic': 0.05},
                    {'date': '2023-02-01', 'ic': 0.03},
                    {'date': '2023-03-01', 'ic': 0.07}
                ]
                factor['quantile_returns'] = {
                    'Q1': 0.02,
                    'Q2': 0.05,
                    'Q3': 0.08,
                    'Q4': 0.12,
                    'Q5': 0.15
                }
                return factor
        return None
    
    def _get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要。"""
        return {
            'system_status': 'healthy',
            'active_strategies': 2,
            'total_backtests': 2,
            'avg_sharpe_ratio': 1.35,
            'best_strategy': {
                'name': '动量策略',
                'return': 0.22
            },
            'recent_activity': [
                {'time': '2024-01-01T10:00:00Z', 'event': '策略001执行买入信号'},
                {'time': '2024-01-01T11:00:00Z', 'event': '回测002完成'},
                {'time': '2024-01-01T12:00:00Z', 'event': '因子分析更新'}
            ]
        }
    
    def run(self):
        """运行Web服务器。"""
        if not self.flask_available:
            logger.error("Flask未安装，无法启动Web服务器")
            return
        
        if not self.app:
            self.create_app()
        
        logger.info(f"启动Web服务器: http://{self.host}:{self.port}")
        
        try:
            self.app.run(
                host=self.host,
                port=self.port,
                debug=self.debug,
                threaded=True
            )
        except Exception as e:
            logger.error(f"Web服务器启动失败: {e}")
    
    def create_simple_html_templates(self):
        """创建简单的HTML模板。"""
        templates_dir = Path("templates")
        templates_dir.mkdir(exist_ok=True)
        
        # 基础模板
        base_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Quantstrat Factory</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; margin: -20px -20px 20px -20px; }
        .nav { margin: 20px 0; }
        .nav a { margin-right: 20px; text-decoration: none; color: #3498db; }
        .card { border: 1px solid #ddd; padding: 20px; margin: 10px 0; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Quantstrat Factory</h1>
        <div class="nav">
            <a href="/">首页</a>
            <a href="/dashboard">仪表板</a>
            <a href="/strategies">策略</a>
            <a href="/backtests">回测</a>
            <a href="/factors">因子</a>
        </div>
    </div>
    <div class="content">
        <h2>{{ title }}</h2>
        {% block content %}{% endblock %}
    </div>
</body>
</html>
        """
        
        with open(templates_dir / "base.html", "w", encoding="utf-8") as f:
            f.write(base_template)
        
        # 主页模板
        index_template = """
{% extends "base.html" %}
{% block content %}
<div class="card">
    <h3>欢迎使用Quantstrat Factory</h3>
    <p>这是一个量化策略开发和回测平台。</p>
    <p>版本: {{ version }}</p>
</div>
{% endblock %}
        """
        
        with open(templates_dir / "index.html", "w", encoding="utf-8") as f:
            f.write(index_template)
        
        logger.info("HTML模板已创建")


# 示例使用
if __name__ == "__main__":
    # 创建Web界面
    web_interface = WebInterface(host="127.0.0.1", port=5000, debug=True)
    
    # 创建简单模板
    web_interface.create_simple_html_templates()
    
    # 创建应用
    app = web_interface.create_app()
    
    print("Web界面已创建，可以通过以下方式启动:")
    print("web_interface.run()")
    print("或者使用: python -m flask run")
    
    # 如果直接运行，启动服务器
    # web_interface.run()
