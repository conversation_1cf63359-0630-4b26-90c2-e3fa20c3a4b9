# 模块 B: 数据质量审计器 (Data Auditor)

## 核心职责

本模块是整个策略工厂数据流的起点，负责确保所有输入数据的质量、准确性和一致性。它提供了两个主要脚本来处理不同的数据准备任务。

---

## 脚本使用说明

### 1. `run_auditor.py`

**用途**:
此脚本是处理 **新获取的原始数据** 的主要入口。当您有新的原始分钟数据（通常是 `.zip` 格式）时，应使用此脚本。

**核心功能**:
- **一次性处理**: 一次性完成 **原始分钟数据的清洗** 和 **日K数据的生成**。
- **数据源**: 读取 `config.ini` 中 `min_data_path` 指定的原始分钟数据。
- **输出**:
    - 清洗后的分钟数据保存到 `cleaned_min_data_output_path`。
    - 聚合生成的日K数据保存到 `day_data_path`。

**如何运行**:
```bash
python run_auditor.py --data-type <数据类型> --start-date <开始日期> --end-date <结束日期>
```

**参数说明**:
*   `--data-type`: **必需**。目前主要用于 `min` 数据的清洗。
    *   可选值: `min` (分钟数据), `day` (日线数据，功能待完善)。
*   `--start-date`: **必需**。指定数据处理的开始日期。
    *   格式: `YYYY-MM-DD` (例如: `2023-01-01`)。
*   `--end-date`: **必需**。指定数据处理的结束日期。
    *   格式: `YYYY-MM-DD` (例如: `2023-12-31`)。

**示例**:
处理 2023 年 1 月 1 日的原始分钟数据，并同时生成当日的日K数据：
```bash
python run_auditor.py --data-type min --start-date 2023-01-01 --end-date 2023-01-01
```

---

### 2. `generate_day_from_cleaned_min.py`

**用途**:
此脚本用于 **补全历史日K数据**。当您已经有清洗过的分钟数据，但缺少对应的日K数据时（例如，在旧流程中只清洗了分钟数据），应使用此脚本。

**核心功能**:
- **避免重复清洗**: 直接读取 **已清洗** 的分钟数据来生成日K数据，避免了重复清洗原始数据的耗时操作。
- **数据源**: 读取 `config.ini` 中 `cleaned_min_data_output_path` 指定的已清洗分钟数据。
- **输出**:
    - 生成的日K数据保存到 `day_data_path`。

**如何运行**:
```bash
python generate_day_from_cleaned_min.py --start-date <开始日期> --end-date <结束日期>
```

**参数说明**:
*   `--start-date`: **必需**。指定要生成日K数据的开始日期。
    *   格式: `YYYY-MM-DD`。
*   `--end-date`: **必需**。指定要生成日K数据的结束日期。
    *   格式: `YYYY-MM-DD`。

**示例**:
为 2022 年全年已清洗的分钟数据生成对应的日K数据：
```bash
python generate_day_from_cleaned_min.py --start-date 2022-01-01 --end-date 2022-12-31
