"""
数据审计器类。

提供数据质量检查、清洗和验证的统一接口。
"""

import pandas as pd
from pathlib import Path
from typing import Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)


class DataAuditor:
    """数据审计器。"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据审计器。
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
    def audit_and_clean_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        执行数据审计和清洗。
        
        Args:
            df: 输入数据DataFrame
            
        Returns:
            清洗后的DataFrame和审计报告
        """
        if df.empty:
            return df, {"status": "No data to process."}

        report = {'initial_rows': len(df)}
        
        # 删除空值
        df.dropna(inplace=True)
        report['rows_after_na_drop'] = len(df)
        
        # 删除重复值
        duplicates = df.duplicated().sum()
        report['duplicate_rows'] = int(duplicates)
        df.drop_duplicates(inplace=True)
        
        # 检查负价格
        price_cols = ['open', 'high', 'low', 'close']
        existing_price_cols = [col for col in price_cols if col in df.columns]
        
        if existing_price_cols:
            negative_prices = (df[existing_price_cols] < 0).any().any()
            report['has_negative_prices'] = bool(negative_prices)
            df = df[(df[existing_price_cols] >= 0).all(axis=1)]
        
        report['final_rows'] = len(df)
        report['status'] = "Audit complete."
        
        return df, report
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        验证数据质量。
        
        Args:
            df: 数据DataFrame
            
        Returns:
            质量检查报告
        """
        report = {}
        
        if df.empty:
            report['status'] = 'empty'
            return report
        
        # 基本统计
        report['total_rows'] = len(df)
        report['total_columns'] = len(df.columns)
        
        # 空值检查
        null_counts = df.isnull().sum()
        report['null_counts'] = null_counts.to_dict()
        report['null_percentage'] = (null_counts / len(df) * 100).to_dict()
        
        # 重复值检查
        report['duplicate_rows'] = df.duplicated().sum()
        
        # 数据类型检查
        report['data_types'] = df.dtypes.astype(str).to_dict()
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            report['numeric_stats'] = df[numeric_cols].describe().to_dict()
        
        report['status'] = 'completed'
        
        return report
    
    def clean_price_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗价格数据。
        
        Args:
            df: 价格数据DataFrame
            
        Returns:
            清洗后的DataFrame
        """
        if df.empty:
            return df
        
        # 确保必要的列存在
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                df[col] = 0.0
        
        # 删除异常价格数据
        price_cols = ['open', 'high', 'low', 'close']
        
        # 删除负价格
        for col in price_cols:
            if col in df.columns:
                df = df[df[col] >= 0]
        
        # 删除异常的高低价关系
        if all(col in df.columns for col in ['high', 'low']):
            df = df[df['high'] >= df['low']]
        
        # 删除异常的开盘收盘价
        if all(col in df.columns for col in ['high', 'low', 'open', 'close']):
            df = df[
                (df['open'] >= df['low']) & (df['open'] <= df['high']) &
                (df['close'] >= df['low']) & (df['close'] <= df['high'])
            ]
        
        # 删除异常成交量
        if 'volume' in df.columns:
            df = df[df['volume'] >= 0]
        
        return df
    
    def generate_audit_report(self, df: pd.DataFrame, output_path: str = None) -> Dict[str, Any]:
        """
        生成详细的审计报告。
        
        Args:
            df: 数据DataFrame
            output_path: 报告输出路径
            
        Returns:
            审计报告字典
        """
        report = {
            'timestamp': pd.Timestamp.now().isoformat(),
            'data_shape': df.shape,
            'quality_check': self.validate_data_quality(df)
        }
        
        # 如果指定了输出路径，保存报告
        if output_path:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存为JSON格式
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"审计报告已保存到: {output_path}")
        
        return report
