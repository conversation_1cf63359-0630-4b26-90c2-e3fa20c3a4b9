"""
特征存储客户端。

简化版本的特征存储客户端，用于数据审计器。
"""

import pandas as pd
from pathlib import Path


class FeatureStoreClient:
    """特征存储客户端。"""
    
    def __init__(self, base_path: str = None):
        """
        初始化特征存储客户端。
        
        Args:
            base_path: 基础路径
        """
        self.base_path = Path(base_path) if base_path else Path("data")
    
    def save_features(self, features: pd.DataFrame, feature_name: str, **kwargs):
        """
        保存特征数据。

        Args:
            features: 特征数据
            feature_name: 特征名称
            **kwargs: 其他参数，包括 symbol, level 等
        """
        # 检查是否是分钟级特征，需要按股票和年份分层存储
        if feature_name.startswith("minute_features_") and 'symbol' in kwargs:
            symbol = kwargs['symbol']
            symbol_safe = self.get_symbol_path(symbol)

            # 按年份分组保存
            if 'datetime' in features.columns:
                features['datetime'] = pd.to_datetime(features['datetime'])
                for year, year_df in features.groupby(features['datetime'].dt.year):
                    year_dir = self.base_path / 'minute_features' / symbol_safe / f'year={year}'
                    year_dir.mkdir(parents=True, exist_ok=True)
                    output_path = year_dir / 'feature_data.parquet'

                    # 如果文件已存在，合并数据
                    if output_path.exists():
                        try:
                            existing_df = pd.read_parquet(output_path)
                            combined_df = pd.concat([existing_df, year_df], ignore_index=True)
                            # 去重并排序
                            combined_df = combined_df.drop_duplicates(subset=['datetime', 'symbol'], keep='last')
                            combined_df = combined_df.sort_values('datetime')
                            combined_df.to_parquet(output_path, index=False)
                        except Exception as e:
                            print(f"合并现有特征数据失败: {e}，将覆盖保存")
                            year_df.to_parquet(output_path, index=False)
                    else:
                        year_df.to_parquet(output_path, index=False)

                    print(f"分钟特征已保存到: {output_path}")
        else:
            # 其他特征使用原来的保存方式
            output_path = self.base_path / f"{feature_name}.parquet"
            output_path.parent.mkdir(parents=True, exist_ok=True)

            try:
                features.to_parquet(output_path)
                print(f"特征已保存到: {output_path}")
            except Exception as e:
                print(f"保存特征失败: {e}")
    
    def get_symbol_path(self, symbol: str) -> str:
        """
        获取股票代码对应的路径。

        Args:
            symbol: 股票代码

        Returns:
            路径字符串
        """
        # 简单的路径转换，将特殊字符替换为下划线
        return symbol.replace('.', '_').replace(':', '_')

    def get_feature(self, feature_name: str, category: str = None, **kwargs) -> pd.DataFrame:
        """
        获取特征数据。支持分层存储结构。

        Args:
            feature_name: 特征名称
            category: 特征类别
            **kwargs: 其他参数

        Returns:
            特征数据
        """
        # 优先尝试从日级分层结构加载（历史数据）
        if category:
            # 尝试日级分层结构: day/category/feature_name/year=YYYY/feature_data.parquet
            day_feature_dir = self.base_path / 'day' / category / feature_name
            if day_feature_dir.exists():
                all_data = []
                # 加载所有年份的数据
                for year_dir in sorted(day_feature_dir.glob('year=*')):
                    feature_file = year_dir / 'feature_data.parquet'
                    if feature_file.exists():
                        try:
                            year_df = pd.read_parquet(feature_file)
                            all_data.append(year_df)
                            print(f"加载历史数据: {feature_file}")
                        except Exception as e:
                            print(f"读取年份数据失败 {year_dir}: {e}")

                if all_data:
                    combined_df = pd.concat(all_data, ignore_index=True)
                    # 排序并去重
                    if 'datetime' in combined_df.columns:
                        combined_df['datetime'] = pd.to_datetime(combined_df['datetime'])
                        combined_df = combined_df.sort_values(['symbol', 'datetime'])
                        combined_df = combined_df.drop_duplicates(subset=['datetime', 'symbol'], keep='last')
                    print(f"成功加载历史特征 {feature_name}: {len(combined_df)} 条记录")
                    return combined_df

        # 如果分层结构不存在，尝试从新的单文件结构加载
        if category:
            feature_path = self.base_path / category / f"{feature_name}.parquet"
        else:
            feature_path = self.base_path / f"{feature_name}.parquet"

        if feature_path.exists():
            try:
                df = pd.read_parquet(feature_path)
                print(f"从单文件加载特征 {feature_name}: {len(df)} 条记录")
                return df
            except Exception as e:
                print(f"加载特征失败: {e}")

        print(f"特征文件不存在: {feature_path}")
        return pd.DataFrame()

    def load_features(self, feature_name: str, **kwargs) -> pd.DataFrame:
        """
        加载特征数据。

        Args:
            feature_name: 特征名称
            **kwargs: 其他参数

        Returns:
            特征数据
        """
        feature_path = self.base_path / f"{feature_name}.parquet"
        
        if feature_path.exists():
            try:
                return pd.read_parquet(feature_path)
            except Exception as e:
                print(f"加载特征失败: {e}")
                return pd.DataFrame()
        else:
            print(f"特征文件不存在: {feature_path}")
            return pd.DataFrame()
    
    def list_features(self) -> list:
        """
        列出所有特征。
        
        Returns:
            特征名称列表
        """
        if not self.base_path.exists():
            return []
        
        feature_files = list(self.base_path.glob("*.parquet"))
        return [f.stem for f in feature_files]
    
    def delete_features(self, feature_name: str):
        """
        删除特征数据。
        
        Args:
            feature_name: 特征名称
        """
        feature_path = self.base_path / f"{feature_name}.parquet"
        
        if feature_path.exists():
            try:
                feature_path.unlink()
                print(f"特征已删除: {feature_path}")
            except Exception as e:
                print(f"删除特征失败: {e}")
        else:
            print(f"特征文件不存在: {feature_path}")
