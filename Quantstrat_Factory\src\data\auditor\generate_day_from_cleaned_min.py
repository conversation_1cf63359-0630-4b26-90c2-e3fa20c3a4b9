import argparse
import configparser
import pandas as pd
from pathlib import Path
import concurrent.futures
from tqdm import tqdm
from tqdm.contrib.concurrent import process_map # 导入多进程友好的 tqdm map

def get_config():
    """Reads the main configuration file."""
    config_path = Path(__file__).resolve().parents[1] / 'config.ini'
    config = configparser.ConfigParser()
    config.read(config_path, encoding='utf-8')
    return config

def generate_daily_data_from_cleaned_minute(cleaned_min_file: Path, day_data_output_path: Path):
    """
    从单个清洗后的分钟数据文件生成日K数据并保存。
    """
    try:
        date_str = cleaned_min_file.stem.replace('_cleaned', '')
        symbol = cleaned_min_file.parent.name

        # 检查目标文件是否已存在
        target_dir = day_data_output_path / symbol
        target_file = target_dir / f"{date_str}.parquet"
        if target_file.exists():
            # print(f"Skipped {symbol} on {date_str}, target file already exists.") # 避免过多输出
            return f"Skipped {symbol} on {date_str}"

        df_min = pd.read_parquet(cleaned_min_file)
        if df_min.empty:
            # print(f"Skipped {symbol} on {date_str}, cleaned minute data is empty.") # 避免过多输出
            return f"Skipped {symbol} on {date_str}"
        
        # print(f"Processing {symbol} on {date_str}...") # 增加处理日志

        # 计算日K数据
        daily_open = df_min['open'].iloc[0]
        daily_high = df_min['high'].max()
        daily_low = df_min['low'].min()
        daily_close = df_min['close'].iloc[-1]
        daily_volume = df_min['volume'].sum()
        daily_amount = df_min['amount'].sum() if 'amount' in df_min.columns else 0

        # 创建日K数据 DataFrame
        daily_data = {
            'datetime': pd.to_datetime(date_str),
            'symbol': symbol,
            'open': daily_open,
            'high': daily_high,
            'low': daily_low,
            'close': daily_close,
            'volume': daily_volume,
            'amount': daily_amount
        }
        df_day = pd.DataFrame([daily_data])

        # 保存日K数据
        target_dir.mkdir(parents=True, exist_ok=True)
        df_day.to_parquet(target_file, index=False)
        return f"Successfully generated daily data for {symbol} on {date_str}."

    except Exception as e:
        return f"Failed to process {cleaned_min_file}: {e}"

def main():
    """Main function to generate daily data from cleaned minute data."""
    parser = argparse.ArgumentParser(description="Generate Daily Data from Cleaned Minute Data")
    parser.add_argument('--start-date', type=str, required=True, help="Start date in YYYY-MM-DD format.")
    parser.add_argument('--end-date', type=str, required=True, help="End date in YYYY-MM-DD format.")
    args = parser.parse_args()

    print("--- Starting Daily Data Generation from Cleaned Minute Data ---")
    
    config = get_config()
    cleaned_min_path = Path(config['Paths']['cleaned_min_data_output_path'])
    day_data_output_path = Path(config['Paths']['day_data_path'])

    if not cleaned_min_path.exists():
        print(f"Error: Cleaned minute data path does not exist: {cleaned_min_path}")
        return

    day_data_output_path.mkdir(parents=True, exist_ok=True)

    start_dt = pd.to_datetime(args.start_date)
    end_dt = pd.to_datetime(args.end_date)
    
    print(f"Optimized searching for cleaned minute files from {args.start_date} to {args.end_date} in {cleaned_min_path}...")

    # 优化文件搜集逻辑
    all_files = cleaned_min_path.glob('**/*_cleaned.parquet')
    files_to_process = []
    for file in tqdm(all_files, desc="Scanning files"):
        try:
            date_str = file.stem.replace('_cleaned', '')
            file_date = pd.to_datetime(date_str)
            if start_dt <= file_date <= end_dt:
                files_to_process.append(file)
        except ValueError:
            # 文件名不是有效的日期格式，跳过
            continue

    if not files_to_process:
        print("No cleaned minute files found in the specified date range.")
        return

    print(f"Found {len(files_to_process)} files to process. Starting generation...")

    print(f"Found {len(files_to_process)} files to process. Starting generation...")

    # 使用 tqdm.contrib.concurrent.process_map 进行多进程并行处理
    # 它可以更好地处理多进程下的进度条显示
    results = process_map(
        generate_daily_data_from_cleaned_minute, 
        files_to_process, 
        [day_data_output_path] * len(files_to_process), 
        max_workers=8, # 明确设置工作进程数
        chunksize=1000, # 进一步增加 chunksize，减少进程间通信开销
        desc="Generating daily data (Multi-Process)"
    )

    # 打印处理结果，以便查看是否有失败或跳过的任务
    for res in results:
        if "Failed" in res: # 只打印失败的，跳过的太多
            print(res)

    print("--- Daily Data Generation Finished ---")

if __name__ == "__main__":
    main()
