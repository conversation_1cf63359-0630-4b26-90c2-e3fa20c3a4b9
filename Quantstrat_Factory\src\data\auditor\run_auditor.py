import argparse
import configparser
import pandas as pd
from pathlib import Path
import zipfile
import io
from datetime import datetime, timed<PERSON>ta
from typing import Tuple
import sys

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(project_root))

# 导入特征存储客户端
try:
    from feature_store_client import FeatureStoreClient
except ImportError:
    # 如果直接导入失败，尝试从当前目录导入
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    from feature_store_client import FeatureStoreClient

def normalize_symbol(symbol: str) -> str:
    """
    将股票代码标准化为 'sh600000' 或 'sz000001' 或 'bj830779' 这种格式。
    """
    if not isinstance(symbol, str):
        return str(symbol)
        
    symbol_lower = symbol.lower()
    
    if symbol_lower.startswith(('sh', 'sz', 'bj')) and len(symbol) > 2 and symbol[2:].isdigit():
        return symbol_lower
    
    if '.' in symbol:
        code, market = symbol.split('.')
        if market.upper() == 'SZ':
            return f"sz{code}"
        elif market.upper() == 'SH':
            return f"sh{code}"
        elif market.upper() == 'BJ':
            return f"bj{code}"
    
    if symbol.isdigit() and len(symbol) == 6:
        if symbol.startswith(('00', '30')):
            return f"sz{symbol}"
        elif symbol.startswith(('60', '68')):
            return f"sh{symbol}"
        elif symbol.startswith(('43', '83', '87', '92')):
            return f"bj{symbol}"
    
    return symbol_lower

def get_config():
    """Reads the main configuration file."""
    # 尝试读取 YAML 配置文件
    try:
        import yaml
        config_path = Path(__file__).resolve().parents[3] / 'config' / 'app.yaml'
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
    except ImportError:
        pass

    # 回退到 INI 配置文件
    config_path = Path(__file__).resolve().parents[3] / 'config.ini'
    if config_path.exists():
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        return config

    # 如果都没有，返回默认配置
    return {
        'paths': {
            'min_data_path': 'D:/PY/Data/min',
            'cleaned_min_data_output_path': 'D:/PY/Data/cleaned/min',
            'day_data_path': 'D:/PY/Data/day',
            'feature_store_path': 'D:/PY/Data/features'
        }
    }

def audit_and_clean_data(df: pd.DataFrame) -> Tuple[pd.DataFrame, dict]:
    """
    Performs cleaning and auditing on the dataframe.
    """
    if df.empty:
        return df, {"status": "No data to process."}

    report = {'initial_rows': len(df)}
    df.dropna(inplace=True)
    report['rows_after_na_drop'] = len(df)
    duplicates = df.duplicated().sum()
    report['duplicate_rows'] = int(duplicates)
    df.drop_duplicates(inplace=True)
    negative_prices = (df[['open', 'high', 'low', 'close']] < 0).any().any()
    report['has_negative_prices'] = bool(negative_prices)
    df = df[(df[['open', 'high', 'low', 'close']] >= 0).all(axis=1)]
    report['final_rows'] = len(df)
    report['status'] = "Audit complete."
    return df, report

def save_cleaned_min_data(df: pd.DataFrame, output_base_path: Path, symbol: str, date_str: str):
    """
    Saves the cleaned minute data, partitioning by year.
    """
    if df.empty:
        return
    try:
        year = pd.to_datetime(date_str).year
        symbol_path_safe = normalize_symbol(symbol)
        partition_dir = output_base_path / symbol_path_safe / f"year={year}"
        partition_dir.mkdir(parents=True, exist_ok=True)
        partition_file = partition_dir / "cleaned_data.parquet"
        
        existing_df = pd.DataFrame()
        if partition_file.exists():
            try:
                existing_df = pd.read_parquet(partition_file)
            except Exception as e:
                print(f"警告: 读取现有清洗后分钟数据分区文件 {partition_file} 失败: {e}。将视为新文件。")
        
        combined_df = pd.concat([existing_df, df], ignore_index=True)
        combined_df.drop_duplicates(subset=['datetime', 'symbol'], keep='last', inplace=True)
        combined_df.sort_values(by=['datetime'], inplace=True)
        combined_df.to_parquet(partition_file, index=False)
    except Exception as e:
        print(f"保存清洗后分钟数据到 {partition_file} 失败: {e}")

def generate_daily_data(df_min: pd.DataFrame, symbol: str, date_str: str) -> pd.DataFrame:
    """
    Generates a daily data DataFrame from minute data.
    """
    if df_min.empty:
        return pd.DataFrame()
    
    daily_open = df_min['open'].iloc[0]
    daily_high = df_min['high'].max()
    daily_low = df_min['low'].min()
    daily_close = df_min['close'].iloc[-1]
    daily_volume = df_min['volume'].sum()
    daily_amount = df_min.get('amount', pd.Series(0)).sum()

    daily_data = {
        'datetime': pd.to_datetime(date_str),
        'symbol': symbol,
        'open': daily_open,
        'high': daily_high,
        'low': daily_low,
        'close': daily_close,
        'volume': daily_volume,
        'amount': daily_amount
    }
    return pd.DataFrame([daily_data])

def process_raw_data(start_date_str: str, end_date_str: str, raw_data_path: Path, cleaned_min_output_path: Path, client: FeatureStoreClient, config: dict):
    """
    Loads raw data for a specific date range, cleans it, and saves both minute and daily data.
    """
    print(f"开始处理原始数据，路径: {raw_data_path}, 日期范围: {start_date_str} to {end_date_str}")
    
    start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
    
    current_dt = start_date
    while current_dt <= end_date:
        date_str_for_zip = current_dt.strftime('%Y%m%d')
        zip_file_name = f"{date_str_for_zip}_1min.zip"
        zip_file_path = raw_data_path / zip_file_name
        
        daily_data_batch = [] # 初始化每日日K数据的批处理列表

        if zip_file_path.exists():
            print(f"  正在处理文件: {zip_file_path.name}")
            try:
                with zipfile.ZipFile(zip_file_path, 'r') as zf:
                    for csv_filename in zf.namelist():
                        if not csv_filename.lower().endswith('.csv'):
                            continue
                        try:
                            csv_content = zf.read(csv_filename)
                            raw_symbol = Path(csv_filename).stem
                            df = pd.read_csv(io.BytesIO(csv_content))
                            
                            column_mapping = {
                                '时间': 'datetime', '代码': 'symbol', '开盘价': 'open',
                                '收盘价': 'close', '最高价': 'high', '最低价': 'low',
                                '成交量': 'volume', '成交额': 'amount'
                            }
                            df.rename(columns=column_mapping, inplace=True)

                            if 'datetime' not in df.columns:
                                continue
                            df['datetime'] = pd.to_datetime(df['datetime'])
                            
                            if 'symbol' not in df.columns:
                                df['symbol'] = raw_symbol
                            
                            normalized_symbol = normalize_symbol(df['symbol'].iloc[0])
                            df['symbol'] = normalized_symbol
                            
                            required_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume', 'symbol']
                            if not all(col in df.columns for col in required_cols):
                                continue
                            
                            df = df[required_cols]
                            
                            cleaned_df, _ = audit_and_clean_data(df)
                            if not cleaned_df.empty:
                                date_str = current_dt.strftime('%Y-%m-%d')
                                # 保存清洗后的分钟数据
                                save_cleaned_min_data(cleaned_df, cleaned_min_output_path, normalized_symbol, date_str)
                                # 生成日K数据并添加到批处理列表
                                df_day = generate_daily_data(cleaned_df, normalized_symbol, date_str)
                                if not df_day.empty:
                                    daily_data_batch.append(df_day)
                        except Exception as e_csv:
                            print(f"    处理CSV文件 {csv_filename} 失败: {e_csv}")
            except Exception as e_zip:
                print(f"  处理zip文件 {zip_file_path.name} 失败: {e_zip}")
            
            # 在处理完一天所有的股票后，批量保存日K数据
            if daily_data_batch:
                print(f"  正在批量保存 {current_dt.strftime('%Y-%m-%d')} 的 {len(daily_data_batch)} 条日K数据...")
                all_day_df = pd.concat(daily_data_batch, ignore_index=True)

                # 保存到统一的日K数据路径
                daily_data_config = config.get('data', {}).get('cleaned', {})
                daily_path = Path(daily_data_config.get('daily', 'D:/PY/Data/cleaned/daily'))
                daily_path.mkdir(parents=True, exist_ok=True)
                daily_basics_path = daily_path / 'daily_basics.parquet'

                # 如果已有历史数据，则加载并合并，同时计算涨跌幅
                if daily_basics_path.exists():
                    try:
                        # 尝试读取现有文件
                        existing_df = pd.read_parquet(daily_basics_path)
                        print(f"  成功读取现有数据: {len(existing_df)} 行")

                        # 合并新旧数据，去除重复
                        combined_df = pd.concat([existing_df, all_day_df], ignore_index=True)
                        # 去除重复记录（基于日期和股票代码）
                        combined_df = combined_df.drop_duplicates(subset=['datetime', 'symbol'], keep='last')
                        # 按日期和股票代码排序
                        combined_df = combined_df.sort_values(['symbol', 'datetime'])
                        # 计算涨跌幅
                        combined_df['pct_chg'] = combined_df.groupby('symbol')['close'].pct_change() * 100

                        # 安全保存：先保存到临时文件
                        temp_path = daily_basics_path.with_suffix('.parquet.tmp')
                        combined_df.to_parquet(temp_path)

                        # 验证临时文件
                        test_df = pd.read_parquet(temp_path)
                        if len(test_df) > 0:
                            # 备份原文件
                            backup_path = daily_basics_path.with_suffix('.parquet.backup')
                            if backup_path.exists():
                                backup_path.unlink()
                            daily_basics_path.rename(backup_path)

                            # 移动临时文件到正式位置
                            temp_path.rename(daily_basics_path)
                            print(f"  成功保存合并数据: {len(combined_df)} 行")

                            # 删除备份文件（可选）
                            if backup_path.exists():
                                backup_path.unlink()
                        else:
                            print(f"  警告: 临时文件验证失败，保持原文件不变")
                            temp_path.unlink()

                    except Exception as e:
                        print(f"  错误: 读取现有文件失败: {e}")
                        print(f"  尝试备份损坏文件并重新创建...")

                        # 备份损坏的文件
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        corrupted_path = daily_basics_path.with_suffix(f'.corrupted_{timestamp}.parquet')
                        daily_basics_path.rename(corrupted_path)
                        print(f"  已备份损坏文件: {corrupted_path}")

                        # 重新创建文件
                        all_day_df['pct_chg'] = float('nan')
                        all_day_df.to_parquet(daily_basics_path)
                        print(f"  重新创建文件: {len(all_day_df)} 行")

                else:
                    # 首次保存，添加pct_chg列（首日为NaN）
                    all_day_df['pct_chg'] = float('nan')

                    # 安全保存
                    temp_path = daily_basics_path.with_suffix('.parquet.tmp')
                    all_day_df.to_parquet(temp_path)

                    # 验证并移动
                    try:
                        test_df = pd.read_parquet(temp_path)
                        if len(test_df) > 0:
                            temp_path.rename(daily_basics_path)
                            print(f"  首次保存成功: {len(all_day_df)} 行")
                        else:
                            print(f"  错误: 保存的文件为空")
                            temp_path.unlink()
                    except Exception as e:
                        print(f"  错误: 文件验证失败: {e}")
                        if temp_path.exists():
                            temp_path.unlink()

                print(f"日K数据已保存到: {daily_basics_path}")

        current_dt += timedelta(days=1)
    
    print("数据清洗和日K生成完成。")

def main():
    """Main function to run the data auditor."""
    parser = argparse.ArgumentParser(description="Data Auditor and Daily Data Generator for Quantstrat Factory")
    parser.add_argument('--start-date', type=str, required=True, help="Start date in YYYY-MM-DD format.")
    parser.add_argument('--end-date', type=str, required=True, help="End date in YYYY-MM-DD format.")
    args = parser.parse_args()

    print("--- 启动数据审计和日K生成 ---")
    
    config = get_config()

    # 从配置中获取路径，使用新的统一路径结构
    if isinstance(config, dict):
        # YAML 配置格式
        data_config = config.get('data', {})
        feature_store_path = data_config.get('features', {}).get('root', 'D:/PY/Data/features')
        raw_path = Path(data_config.get('raw', {}).get('minute', 'D:/PY/Data/raw/1min'))
        cleaned_min_output_path = Path(data_config.get('cleaned', {}).get('minute', 'D:/PY/Data/cleaned/minute'))
    else:
        # INI 配置格式（向后兼容）
        feature_store_path = config.get('Paths', 'feature_store_path', fallback='D:/PY/Data/features')
        raw_path = Path(config.get('Paths', 'min_data_path', fallback='D:/PY/Data/raw/1min'))
        cleaned_min_output_path = Path(config.get('Paths', 'cleaned_min_data_output_path', fallback='D:/PY/Data/cleaned/minute'))

    client = FeatureStoreClient(base_path=feature_store_path)
    
    cleaned_min_output_path.mkdir(parents=True, exist_ok=True)

    process_raw_data(args.start_date, args.end_date, raw_path, cleaned_min_output_path, client, config)
    
    print("--- 数据审计和日K生成结束 ---")

if __name__ == "__main__":
    main()
