"""
增强数据清洗模块

提供高级数据清洗功能，包括智能异常检测、数据质量评估等
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
import warnings

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class EnhancedDataCleaner:
    """增强数据清洗器"""
    
    def __init__(self):
        """初始化增强数据清洗器"""
        self.cleaning_report = {}
        self.quality_metrics = {}
        
    def comprehensive_clean(
        self, 
        data: pd.DataFrame,
        config: Optional[Dict[str, Any]] = None
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        综合数据清洗
        
        Args:
            data: 原始数据
            config: 清洗配置
            
        Returns:
            (清洗后数据, 清洗报告)
        """
        if config is None:
            config = self._get_default_config()
        
        logger.info(f"开始综合数据清洗: {data.shape}")
        
        cleaned_data = data.copy()
        report = {
            'original_shape': data.shape,
            'steps': []
        }
        
        # 1. 数据类型检查和修复
        if config.get('fix_dtypes', True):
            cleaned_data, step_report = self._fix_data_types(cleaned_data)
            report['steps'].append(('fix_dtypes', step_report))
        
        # 2. 重复数据处理
        if config.get('remove_duplicates', True):
            cleaned_data, step_report = self._remove_duplicates(cleaned_data)
            report['steps'].append(('remove_duplicates', step_report))
        
        # 3. 缺失值处理
        if config.get('handle_missing', True):
            cleaned_data, step_report = self._handle_missing_values_advanced(
                cleaned_data, 
                config.get('missing_strategy', {})
            )
            report['steps'].append(('handle_missing', step_report))
        
        # 4. 异常值检测和处理
        if config.get('handle_outliers', True):
            cleaned_data, step_report = self._handle_outliers_advanced(
                cleaned_data,
                config.get('outlier_strategy', {})
            )
            report['steps'].append(('handle_outliers', step_report))
        
        # 5. 数据一致性检查
        if config.get('consistency_check', True):
            cleaned_data, step_report = self._consistency_check(cleaned_data)
            report['steps'].append(('consistency_check', step_report))
        
        # 6. 数据质量评估
        quality_metrics = self._assess_data_quality(cleaned_data)
        report['quality_metrics'] = quality_metrics
        
        report['final_shape'] = cleaned_data.shape
        report['data_reduction'] = {
            'rows_removed': data.shape[0] - cleaned_data.shape[0],
            'reduction_rate': (data.shape[0] - cleaned_data.shape[0]) / data.shape[0] * 100
        }
        
        self.cleaning_report = report
        
        logger.info(f"数据清洗完成: {data.shape} -> {cleaned_data.shape}")
        
        return cleaned_data, report
    
    def _fix_data_types(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """修复数据类型"""
        fixed_data = data.copy()
        report = {'conversions': {}, 'errors': []}
        
        for col in fixed_data.columns:
            original_dtype = str(fixed_data[col].dtype)
            
            try:
                # 尝试智能类型转换
                if fixed_data[col].dtype == 'object':
                    # 尝试转换为数值
                    if self._is_numeric_string(fixed_data[col]):
                        fixed_data[col] = pd.to_numeric(fixed_data[col], errors='coerce')
                        report['conversions'][col] = f"{original_dtype} -> {fixed_data[col].dtype}"
                    
                    # 尝试转换为日期
                    elif self._is_date_string(fixed_data[col]):
                        fixed_data[col] = pd.to_datetime(fixed_data[col], errors='coerce')
                        report['conversions'][col] = f"{original_dtype} -> datetime64"
                
                # 优化数值类型
                elif fixed_data[col].dtype in ['int64', 'float64']:
                    optimized_col = self._optimize_numeric_dtype(fixed_data[col])
                    if optimized_col.dtype != fixed_data[col].dtype:
                        fixed_data[col] = optimized_col
                        report['conversions'][col] = f"{original_dtype} -> {fixed_data[col].dtype}"
                        
            except Exception as e:
                report['errors'].append(f"列 {col} 类型转换失败: {str(e)}")
        
        return fixed_data, report
    
    def _remove_duplicates(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """移除重复数据"""
        original_count = len(data)
        
        # 检测完全重复的行
        deduplicated = data.drop_duplicates()
        
        # 检测基于关键列的重复（如果有时间和股票代码列）
        key_columns = []
        for col in data.columns:
            if any(keyword in col.lower() for keyword in ['date', 'time', 'symbol', 'code']):
                key_columns.append(col)
        
        if key_columns:
            deduplicated = deduplicated.drop_duplicates(subset=key_columns, keep='last')
        
        removed_count = original_count - len(deduplicated)
        
        report = {
            'original_count': original_count,
            'removed_count': removed_count,
            'final_count': len(deduplicated),
            'removal_rate': removed_count / original_count * 100 if original_count > 0 else 0
        }
        
        return deduplicated, report
    
    def _handle_missing_values_advanced(
        self, 
        data: pd.DataFrame, 
        strategy: Dict[str, Any]
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """高级缺失值处理"""
        handled_data = data.copy()
        report = {'missing_before': {}, 'missing_after': {}, 'methods_used': {}}
        
        # 记录处理前的缺失情况
        for col in handled_data.columns:
            missing_count = handled_data[col].isnull().sum()
            report['missing_before'][col] = {
                'count': missing_count,
                'percentage': missing_count / len(handled_data) * 100
            }
        
        # 按列类型处理缺失值
        for col in handled_data.columns:
            missing_rate = handled_data[col].isnull().sum() / len(handled_data)
            
            if missing_rate == 0:
                continue
            
            # 根据缺失率选择策略
            if missing_rate > strategy.get('drop_threshold', 0.5):
                # 缺失率过高，删除列
                handled_data = handled_data.drop(columns=[col])
                report['methods_used'][col] = 'dropped_column'
                
            elif handled_data[col].dtype in ['int64', 'float64']:
                # 数值列处理
                method = strategy.get('numeric_method', 'median')
                if method == 'mean':
                    handled_data[col].fillna(handled_data[col].mean(), inplace=True)
                elif method == 'median':
                    handled_data[col].fillna(handled_data[col].median(), inplace=True)
                elif method == 'interpolate':
                    handled_data[col] = handled_data[col].interpolate()
                
                report['methods_used'][col] = f'numeric_{method}'
                
            elif handled_data[col].dtype == 'object':
                # 分类列处理
                method = strategy.get('categorical_method', 'mode')
                if method == 'mode':
                    mode_value = handled_data[col].mode()
                    if len(mode_value) > 0:
                        handled_data[col].fillna(mode_value[0], inplace=True)
                elif method == 'unknown':
                    handled_data[col].fillna('Unknown', inplace=True)
                
                report['methods_used'][col] = f'categorical_{method}'
        
        # 记录处理后的缺失情况
        for col in handled_data.columns:
            missing_count = handled_data[col].isnull().sum()
            report['missing_after'][col] = {
                'count': missing_count,
                'percentage': missing_count / len(handled_data) * 100
            }
        
        return handled_data, report
    
    def _handle_outliers_advanced(
        self, 
        data: pd.DataFrame, 
        strategy: Dict[str, Any]
    ) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """高级异常值处理"""
        handled_data = data.copy()
        report = {'outliers_detected': {}, 'methods_used': {}}
        
        numeric_columns = handled_data.select_dtypes(include=[np.number]).columns
        
        for col in numeric_columns:
            if handled_data[col].isnull().all():
                continue
            
            method = strategy.get('method', 'isolation_forest')
            outliers_mask = np.zeros(len(handled_data), dtype=bool)
            
            if method == 'iqr':
                outliers_mask = self._detect_outliers_iqr(handled_data[col])
            elif method == 'zscore':
                outliers_mask = self._detect_outliers_zscore(handled_data[col])
            elif method == 'isolation_forest':
                outliers_mask = self._detect_outliers_isolation_forest(handled_data[[col]])
            
            outliers_count = outliers_mask.sum()
            
            if outliers_count > 0:
                action = strategy.get('action', 'remove')
                
                if action == 'remove':
                    handled_data = handled_data[~outliers_mask]
                elif action == 'cap':
                    # 用分位数截断
                    lower_bound = handled_data[col].quantile(0.01)
                    upper_bound = handled_data[col].quantile(0.99)
                    handled_data[col] = handled_data[col].clip(lower_bound, upper_bound)
                elif action == 'transform':
                    # 对数变换
                    if (handled_data[col] > 0).all():
                        handled_data[col] = np.log1p(handled_data[col])
                
                report['outliers_detected'][col] = {
                    'count': outliers_count,
                    'percentage': outliers_count / len(data) * 100,
                    'action': action
                }
                report['methods_used'][col] = method
        
        return handled_data, report
    
    def _consistency_check(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """数据一致性检查"""
        consistent_data = data.copy()
        report = {'issues_found': [], 'fixes_applied': []}
        
        # 检查日期列的一致性
        date_columns = consistent_data.select_dtypes(include=['datetime64']).columns
        for col in date_columns:
            # 检查未来日期
            future_mask = consistent_data[col] > pd.Timestamp.now()
            if future_mask.any():
                report['issues_found'].append(f"列 {col} 包含未来日期")
                consistent_data = consistent_data[~future_mask]
                report['fixes_applied'].append(f"移除列 {col} 中的未来日期")
        
        # 检查数值列的合理性
        numeric_columns = consistent_data.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            # 检查负价格（如果列名包含price, close等）
            if any(keyword in col.lower() for keyword in ['price', 'close', 'open', 'high', 'low']):
                negative_mask = consistent_data[col] < 0
                if negative_mask.any():
                    report['issues_found'].append(f"列 {col} 包含负值")
                    consistent_data = consistent_data[~negative_mask]
                    report['fixes_applied'].append(f"移除列 {col} 中的负值")
            
            # 检查成交量的合理性
            if 'volume' in col.lower():
                zero_volume_mask = consistent_data[col] == 0
                if zero_volume_mask.any():
                    report['issues_found'].append(f"列 {col} 包含零成交量")
                    # 可以选择保留或移除，这里选择保留但记录
        
        return consistent_data, report
    
    def _assess_data_quality(self, data: pd.DataFrame) -> Dict[str, Any]:
        """评估数据质量"""
        quality_metrics = {
            'completeness': {},
            'consistency': {},
            'accuracy': {},
            'overall_score': 0
        }
        
        # 完整性评估
        total_cells = data.shape[0] * data.shape[1]
        missing_cells = data.isnull().sum().sum()
        completeness_score = (total_cells - missing_cells) / total_cells * 100
        
        quality_metrics['completeness'] = {
            'score': completeness_score,
            'missing_cells': missing_cells,
            'total_cells': total_cells
        }
        
        # 一致性评估（基于数据类型一致性）
        consistency_issues = 0
        for col in data.columns:
            if data[col].dtype == 'object':
                # 检查字符串列的一致性
                unique_values = data[col].dropna().unique()
                if len(unique_values) > 0:
                    # 简单的一致性检查：检查是否有明显的格式不一致
                    pass
        
        consistency_score = max(0, 100 - consistency_issues * 10)
        quality_metrics['consistency']['score'] = consistency_score
        
        # 准确性评估（基于异常值比例）
        numeric_data = data.select_dtypes(include=[np.number])
        if not numeric_data.empty:
            outlier_ratio = 0
            for col in numeric_data.columns:
                outliers = self._detect_outliers_iqr(numeric_data[col])
                outlier_ratio += outliers.sum() / len(numeric_data)
            
            outlier_ratio /= len(numeric_data.columns)
            accuracy_score = max(0, 100 - outlier_ratio * 100)
        else:
            accuracy_score = 100
        
        quality_metrics['accuracy']['score'] = accuracy_score
        
        # 总体评分
        overall_score = (completeness_score + consistency_score + accuracy_score) / 3
        quality_metrics['overall_score'] = overall_score
        
        return quality_metrics
    
    def _is_numeric_string(self, series: pd.Series) -> bool:
        """检查字符串列是否可以转换为数值"""
        try:
            pd.to_numeric(series.dropna().head(100), errors='raise')
            return True
        except:
            return False
    
    def _is_date_string(self, series: pd.Series) -> bool:
        """检查字符串列是否可以转换为日期"""
        try:
            pd.to_datetime(series.dropna().head(100), errors='raise')
            return True
        except:
            return False
    
    def _optimize_numeric_dtype(self, series: pd.Series) -> pd.Series:
        """优化数值类型"""
        if series.dtype == 'int64':
            if series.min() >= 0:
                if series.max() <= 255:
                    return series.astype('uint8')
                elif series.max() <= 65535:
                    return series.astype('uint16')
                elif series.max() <= 4294967295:
                    return series.astype('uint32')
            else:
                if series.min() >= -128 and series.max() <= 127:
                    return series.astype('int8')
                elif series.min() >= -32768 and series.max() <= 32767:
                    return series.astype('int16')
                elif series.min() >= -2147483648 and series.max() <= 2147483647:
                    return series.astype('int32')
        
        elif series.dtype == 'float64':
            return pd.to_numeric(series, downcast='float')
        
        return series
    
    def _detect_outliers_iqr(self, series: pd.Series) -> np.ndarray:
        """使用IQR方法检测异常值"""
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        
        return (series < lower_bound) | (series > upper_bound)
    
    def _detect_outliers_zscore(self, series: pd.Series, threshold: float = 3) -> np.ndarray:
        """使用Z-score方法检测异常值"""
        z_scores = np.abs(stats.zscore(series.dropna()))
        outliers = np.zeros(len(series), dtype=bool)
        outliers[series.notna()] = z_scores > threshold
        return outliers
    
    def _detect_outliers_isolation_forest(self, data: pd.DataFrame) -> np.ndarray:
        """使用孤立森林检测异常值"""
        try:
            # 标准化数据
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(data.dropna())
            
            # 孤立森林
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            outliers_pred = iso_forest.fit_predict(scaled_data)
            
            # 转换为布尔掩码
            outliers = np.zeros(len(data), dtype=bool)
            outliers[data.notna().all(axis=1)] = outliers_pred == -1
            
            return outliers
        except:
            # 如果失败，回退到IQR方法
            return self._detect_outliers_iqr(data.iloc[:, 0])
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认清洗配置"""
        return {
            'fix_dtypes': True,
            'remove_duplicates': True,
            'handle_missing': True,
            'handle_outliers': True,
            'consistency_check': True,
            'missing_strategy': {
                'drop_threshold': 0.5,
                'numeric_method': 'median',
                'categorical_method': 'mode'
            },
            'outlier_strategy': {
                'method': 'iqr',
                'action': 'remove'
            }
        }
    
    def get_cleaning_report(self) -> Dict[str, Any]:
        """获取清洗报告"""
        return self.cleaning_report.copy()
    
    def get_quality_metrics(self) -> Dict[str, Any]:
        """获取质量指标"""
        return self.quality_metrics.copy()
