"""
数据处理优化模块

提供数据清洗、特征工程和存储的性能优化功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable
import logging
from pathlib import Path
import time
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from functools import partial
import gc

logger = logging.getLogger(__name__)


class DataProcessingOptimizer:
    """数据处理优化器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化数据处理优化器
        
        Args:
            max_workers: 最大工作进程数
        """
        self.max_workers = max_workers or mp.cpu_count()
        self.processing_stats = {}
        
    def optimize_data_cleaning(self, data: pd.DataFrame, rules: Dict[str, Any]) -> pd.DataFrame:
        """
        优化数据清洗过程
        
        Args:
            data: 原始数据
            rules: 清洗规则
            
        Returns:
            清洗后的数据
        """
        start_time = time.time()
        
        try:
            # 1. 数据类型优化
            optimized_data = self._optimize_dtypes(data)
            
            # 2. 缺失值处理
            if 'missing_value_strategy' in rules:
                optimized_data = self._handle_missing_values(
                    optimized_data, 
                    rules['missing_value_strategy']
                )
            
            # 3. 异常值处理
            if 'outlier_detection' in rules:
                optimized_data = self._handle_outliers(
                    optimized_data,
                    rules['outlier_detection']
                )
            
            # 4. 数据验证
            if 'validation_rules' in rules:
                optimized_data = self._validate_data(
                    optimized_data,
                    rules['validation_rules']
                )
            
            # 记录性能统计
            processing_time = time.time() - start_time
            self.processing_stats['data_cleaning'] = {
                'processing_time': processing_time,
                'input_rows': len(data),
                'output_rows': len(optimized_data),
                'memory_reduction': self._calculate_memory_reduction(data, optimized_data)
            }
            
            logger.info(f"数据清洗完成: {len(data)} -> {len(optimized_data)} 行, 用时 {processing_time:.2f}s")
            
            return optimized_data
            
        except Exception as e:
            logger.error(f"数据清洗失败: {e}")
            return data
    
    def parallel_feature_engineering(
        self, 
        data: pd.DataFrame, 
        feature_functions: List[Callable],
        chunk_size: int = 10000
    ) -> pd.DataFrame:
        """
        并行特征工程
        
        Args:
            data: 输入数据
            feature_functions: 特征计算函数列表
            chunk_size: 数据块大小
            
        Returns:
            包含新特征的数据
        """
        start_time = time.time()
        
        try:
            # 分割数据
            chunks = [data.iloc[i:i+chunk_size] for i in range(0, len(data), chunk_size)]
            
            # 并行处理每个数据块
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # 为每个特征函数创建任务
                futures = []
                for func in feature_functions:
                    chunk_futures = [
                        executor.submit(self._apply_feature_function, chunk, func)
                        for chunk in chunks
                    ]
                    futures.extend(chunk_futures)
                
                # 收集结果
                results = []
                for future in futures:
                    try:
                        result = future.result()
                        if result is not None:
                            results.append(result)
                    except Exception as e:
                        logger.error(f"特征计算失败: {e}")
            
            # 合并结果
            if results:
                feature_data = pd.concat(results, ignore_index=True)
                
                # 与原始数据合并
                if len(feature_data) == len(data):
                    enhanced_data = pd.concat([data, feature_data], axis=1)
                else:
                    logger.warning("特征数据长度不匹配，使用原始数据")
                    enhanced_data = data
            else:
                enhanced_data = data
            
            processing_time = time.time() - start_time
            self.processing_stats['feature_engineering'] = {
                'processing_time': processing_time,
                'input_features': len(data.columns),
                'output_features': len(enhanced_data.columns),
                'new_features': len(enhanced_data.columns) - len(data.columns)
            }
            
            logger.info(f"特征工程完成: {len(data.columns)} -> {len(enhanced_data.columns)} 列, "
                       f"用时 {processing_time:.2f}s")
            
            return enhanced_data
            
        except Exception as e:
            logger.error(f"并行特征工程失败: {e}")
            return data
    
    def optimize_data_storage(
        self, 
        data: pd.DataFrame, 
        output_path: str,
        storage_format: str = 'parquet',
        compression: str = 'snappy'
    ) -> bool:
        """
        优化数据存储
        
        Args:
            data: 要存储的数据
            output_path: 输出路径
            storage_format: 存储格式 ('parquet', 'hdf5', 'feather')
            compression: 压缩方式
            
        Returns:
            是否存储成功
        """
        start_time = time.time()
        
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 优化数据类型
            optimized_data = self._optimize_dtypes(data)
            
            # 根据格式存储
            if storage_format == 'parquet':
                optimized_data.to_parquet(
                    output_file, 
                    compression=compression,
                    index=False
                )
            elif storage_format == 'hdf5':
                optimized_data.to_hdf(
                    output_file,
                    key='data',
                    mode='w',
                    complib=compression
                )
            elif storage_format == 'feather':
                optimized_data.to_feather(output_file)
            else:
                # 默认使用CSV
                optimized_data.to_csv(output_file, index=False)
            
            # 计算存储统计
            file_size = output_file.stat().st_size / (1024 * 1024)  # MB
            processing_time = time.time() - start_time
            
            self.processing_stats['data_storage'] = {
                'processing_time': processing_time,
                'file_size_mb': file_size,
                'rows_stored': len(optimized_data),
                'columns_stored': len(optimized_data.columns),
                'storage_format': storage_format,
                'compression': compression
            }
            
            logger.info(f"数据存储完成: {len(optimized_data)} 行, {file_size:.2f}MB, "
                       f"用时 {processing_time:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"数据存储失败: {e}")
            return False
    
    def batch_process_files(
        self, 
        file_paths: List[str],
        processing_function: Callable,
        output_dir: str,
        **kwargs
    ) -> Dict[str, bool]:
        """
        批量处理文件
        
        Args:
            file_paths: 文件路径列表
            processing_function: 处理函数
            output_dir: 输出目录
            **kwargs: 处理函数参数
            
        Returns:
            处理结果字典
        """
        start_time = time.time()
        results = {}
        
        try:
            # 创建输出目录
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            
            # 并行处理文件
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_file = {
                    executor.submit(
                        self._process_single_file,
                        file_path,
                        processing_function,
                        output_dir,
                        **kwargs
                    ): file_path
                    for file_path in file_paths
                }
                
                # 收集结果
                for future in future_to_file:
                    file_path = future_to_file[future]
                    try:
                        result = future.result()
                        results[file_path] = result
                    except Exception as e:
                        logger.error(f"处理文件失败 {file_path}: {e}")
                        results[file_path] = False
            
            processing_time = time.time() - start_time
            success_count = sum(1 for r in results.values() if r)
            
            self.processing_stats['batch_processing'] = {
                'processing_time': processing_time,
                'total_files': len(file_paths),
                'success_files': success_count,
                'success_rate': success_count / len(file_paths) if file_paths else 0
            }
            
            logger.info(f"批量处理完成: {success_count}/{len(file_paths)} 文件成功, "
                       f"用时 {processing_time:.2f}s")
            
            return results
            
        except Exception as e:
            logger.error(f"批量处理失败: {e}")
            return {path: False for path in file_paths}
    
    def _optimize_dtypes(self, data: pd.DataFrame) -> pd.DataFrame:
        """优化数据类型"""
        optimized_data = data.copy()
        
        for col in optimized_data.columns:
            col_type = optimized_data[col].dtype
            
            if col_type == 'object':
                # 尝试转换为数值类型
                try:
                    optimized_data[col] = pd.to_numeric(optimized_data[col], downcast='integer')
                except (ValueError, TypeError):
                    # 检查是否适合分类类型
                    if optimized_data[col].nunique() / len(optimized_data) < 0.5:
                        optimized_data[col] = optimized_data[col].astype('category')
            
            elif col_type in ['int64', 'int32']:
                optimized_data[col] = pd.to_numeric(optimized_data[col], downcast='integer')
            
            elif col_type in ['float64', 'float32']:
                optimized_data[col] = pd.to_numeric(optimized_data[col], downcast='float')
        
        return optimized_data
    
    def _handle_missing_values(self, data: pd.DataFrame, strategy: Dict[str, Any]) -> pd.DataFrame:
        """处理缺失值"""
        method = strategy.get('method', 'drop')
        
        if method == 'drop':
            threshold = strategy.get('threshold', 0.5)
            return data.dropna(thresh=int(len(data.columns) * threshold))
        
        elif method == 'fill':
            fill_value = strategy.get('fill_value', 0)
            return data.fillna(fill_value)
        
        elif method == 'interpolate':
            return data.interpolate()
        
        elif method == 'forward_fill':
            return data.fillna(method='ffill')
        
        elif method == 'backward_fill':
            return data.fillna(method='bfill')
        
        else:
            return data
    
    def _handle_outliers(self, data: pd.DataFrame, config: Dict[str, Any]) -> pd.DataFrame:
        """处理异常值"""
        method = config.get('method', 'iqr')
        
        if method == 'iqr':
            # 使用IQR方法检测异常值
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 移除异常值
            mask = ~((data < lower_bound) | (data > upper_bound)).any(axis=1)
            return data[mask]
        
        elif method == 'zscore':
            # 使用Z-score方法
            threshold = config.get('threshold', 3)
            z_scores = np.abs((data - data.mean()) / data.std())
            mask = (z_scores < threshold).all(axis=1)
            return data[mask]
        
        else:
            return data
    
    def _validate_data(self, data: pd.DataFrame, rules: Dict[str, Any]) -> pd.DataFrame:
        """数据验证"""
        validated_data = data.copy()
        
        # 检查数据范围
        if 'value_ranges' in rules:
            for col, (min_val, max_val) in rules['value_ranges'].items():
                if col in validated_data.columns:
                    mask = (validated_data[col] >= min_val) & (validated_data[col] <= max_val)
                    validated_data = validated_data[mask]
        
        # 检查数据完整性
        if 'required_columns' in rules:
            missing_cols = set(rules['required_columns']) - set(validated_data.columns)
            if missing_cols:
                logger.warning(f"缺少必要列: {missing_cols}")
        
        return validated_data
    
    def _apply_feature_function(self, chunk: pd.DataFrame, func: Callable) -> Optional[pd.DataFrame]:
        """应用特征函数到数据块"""
        try:
            return func(chunk)
        except Exception as e:
            logger.error(f"特征函数应用失败: {e}")
            return None
    
    def _process_single_file(
        self, 
        file_path: str, 
        processing_function: Callable,
        output_dir: str,
        **kwargs
    ) -> bool:
        """处理单个文件"""
        try:
            # 读取文件
            if file_path.endswith('.parquet'):
                data = pd.read_parquet(file_path)
            elif file_path.endswith('.csv'):
                data = pd.read_csv(file_path)
            else:
                logger.warning(f"不支持的文件格式: {file_path}")
                return False
            
            # 应用处理函数
            processed_data = processing_function(data, **kwargs)
            
            # 保存结果
            output_file = Path(output_dir) / Path(file_path).name
            
            if file_path.endswith('.parquet'):
                processed_data.to_parquet(output_file, index=False)
            else:
                processed_data.to_csv(output_file, index=False)
            
            return True
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            return False
    
    def _calculate_memory_reduction(self, original: pd.DataFrame, optimized: pd.DataFrame) -> float:
        """计算内存减少百分比"""
        try:
            original_memory = original.memory_usage(deep=True).sum()
            optimized_memory = optimized.memory_usage(deep=True).sum()
            
            if original_memory > 0:
                return (original_memory - optimized_memory) / original_memory * 100
            else:
                return 0.0
        except:
            return 0.0
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.processing_stats.copy()
    
    def clear_stats(self):
        """清空统计信息"""
        self.processing_stats.clear()
    
    def cleanup_memory(self):
        """清理内存"""
        gc.collect()
        logger.info("内存清理完成")
