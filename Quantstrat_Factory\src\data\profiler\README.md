# 模块 A: 特征画像器 (Feature Profiler)

## 核心职责

本模块负责从经过审计的干净数据中，计算和生成用于因子研究和策略建模的各种特征。

## 功能

1.  **数据加载**: 从 `config.ini` 指定的 `cleaned_data_path` 加载干净的数据。
2.  **特征计算**:
    - **价格衍生特征**: 复权因子、对数收益率等。
    - **量价特征**: VWAP (成交量加权平均价)、Amihud非流动性因子等。
    - **波动率特征**: ATR (平均真实波幅)、已实现波动率等。
    - **盘口特征 (代理)**: 基于分钟快照计算买卖压力等。
    - **正交化**: 对特征进行行业和市值中性化处理。
3.  **特征存储**:
    - 将计算出的每个特征矩阵，以独立文件的形式存储到 `config.ini` 指定的 `feature_store_path`。
    - 采用高效的列式存储格式（如 Parquet）。
    - 遵循清晰的命名和目录规范，例如 `.../features/log_return_1d/2023.parquet`。

## 如何运行

使用以下命令运行特征画像器：

```bash
python run_profiler.py --level <计算级别> --feature-sets <特征集列表> [--start-date <开始日期>] [--end-date <结束日期>] [--symbols <股票代码列表>]
```

**参数说明：**

*   `--level`: **必需**。指定要计算特征的数据级别。
    *   可选值: `min` (分钟数据), `day` (日线数据)。
*   `--feature-sets`: **必需**。指定要计算的特征集或单个特征名称。可以指定多个，用空格分隔。
*   `--start-date`: **可选**。指定数据处理的开始日期。
    *   格式: `YYYY-MM-DD` (例如: `2023-01-01`)。
*   `--end-date`: **可选**。指定数据处理的结束日期。
    *   格式: `YYYY-MM-DD` (例如: `2023-12-31`)。
*   `--symbols`: **可选**。指定要处理的股票代码列表。可以指定多个，用空格分隔。

**示例：**

计算 2023 年所有分钟级特征：
```bash
python run_profiler.py --level min --feature-sets all --start-date 2023-01-01 --end-date 2023-12-31
```

计算特定股票的 `alpha` 和 `event` 类特征：
```bash
python run_profiler.py --level min --feature-sets alpha event --symbols sh600000 sz000001
```

## 可用特征集 (`--feature-sets`)

### 1. 分钟级别 (`--level min`)

**特殊选项:**
*   `all`: 计算所有分钟级别的 `alpha`, `event`, `risk` 特征。

**按类别:**
*   `alpha`: 计算所有阿尔法类因子。
*   `event`: 计算所有事件驱动类因子。
*   `risk`: 计算所有风险类因子。

**按具体名称 (单个特征):**

*   **Alpha 因子 (`alpha`类别):**
    *   `vol_pct_morning`: 上午成交量占比
    *   `return_open_to_10am`: 开盘到10点收益率
    *   `vol_pct_open30m`: 开盘30分钟成交量占比
    *   `vol_pct_mid`: 午盘核心时段成交量占比
    *   `vol_pct_close30m`: 尾盘30分钟成交量占比
    *   `vol_center_of_gravity`: 成交量重心
    *   `vol_std_pct`: 成交量分布标准差
    *   `return_last_hour`: 最后一小时收益率
    *   `return_intraday_skew`: 日内收益率偏度
    *   `volatility_am`: 上午波动率
    *   `volatility_pm`: 下午波动率
    *   `amplitude_total`: 总振幅
    *   `upper_tail_ratio`: 上影线比例
    *   `lower_tail_ratio`: 下影线比例
    *   `price_volume_corr`: 价量相关性
    *   `close_to_vwap_pct_diff`: 收盘价与VWAP的偏离度
    *   `volume_cluster_score`: 成交量脉冲得分
    *   `intraday_range_position`: 日内振幅位置
    *   `price_trend_consistency`: 价格趋势一致性
    *   `volume_trend_consistency`: 成交量趋势一致性
    *   `close_in_upper_quantile`: 收盘价位于高分位
    *   `pullback_amplitude`: 从高点回落幅度
    *   `high_time_density`: 最高价时间密度

*   **Event 因子 (`event`类别):**
    *   `tail_15m_spike`: 尾盘15分钟脉冲
    *   `afternoon_volume_jump`: 午后成交量跳跃
    *   `reverse_tail_tag`: 尾盘反转
    *   `is_tail_spike_rise`: 尾盘脉冲上涨
    *   `is_midday_vshape`: 午间V型反转
    *   `is_volatility_squeeze`: 布林带波动性压缩
    *   `is_bull_stretch`: 多头拉伸
    *   `high_time_density_morning`: 早盘出现最高价

*   **Risk 因子 (`risk`类别):**
    *   `amplitude_total`: 当日振幅 (风险版本)
    *   `volatility_am_pm_ratio`: 上下午波动率比
    *   `pullback_amplitude`: 从高点回落幅度 (风险版本)

### 2. 日级别 (`--level day`)

**特殊选项:**
*   `all`: 计算所有日级别特征，等同于同时指定 `daily_basics`, `technical`, 和 `day_from_min_aggregation`。

**按类别:**
*   `daily_basics`: 计算基础的日线数据，如开高低收、成交量等 (不指定 `all` 时，此项通常会自动添加)。
*   `technical`: 计算常用的技术指标，如 `MA` (移动平均线) 和 `RSI` (相对强弱指数)。
*   `day_from_min_aggregation`: 将 `--level min` 计算出的每日特征值进行聚合（例如计算移动平均），并存储为日级别特征。
