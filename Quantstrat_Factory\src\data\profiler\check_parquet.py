import pandas as pd
from pathlib import Path

file_path = Path("d:/py/Quantstrat_Factory/03_feature_store/features/minute_features/000002_SZ/2020-10-14_minute_features.parquet")

if file_path.exists():
    try:
        df = pd.read_parquet(file_path)
        print(f"文件 {file_path} 的列名：")
        print(df.columns.tolist())
        print(f"\n文件 {file_path} 的前5行数据：")
        print(df.head())
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
else:
    print(f"文件 {file_path} 不存在。")