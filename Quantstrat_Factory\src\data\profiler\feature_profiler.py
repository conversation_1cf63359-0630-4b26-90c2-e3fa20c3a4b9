"""
特征分析器类。

提供特征提取、分析和处理的统一接口。
"""

import pandas as pd
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class FeatureProfiler:
    """特征分析器。"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化特征分析器。
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        
    def extract_basic_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        提取基础特征。
        
        Args:
            df: 输入数据DataFrame
            
        Returns:
            包含特征的DataFrame
        """
        if df.empty:
            return df
        
        result_df = df.copy()
        
        # 价格相关特征
        if all(col in df.columns for col in ['open', 'high', 'low', 'close']):
            # 价格变化
            result_df['price_change'] = result_df['close'] - result_df['open']
            result_df['price_change_pct'] = result_df['price_change'] / result_df['open']
            
            # 振幅
            result_df['amplitude'] = (result_df['high'] - result_df['low']) / result_df['open']
            
            # 上影线和下影线
            result_df['upper_shadow'] = result_df['high'] - result_df[['open', 'close']].max(axis=1)
            result_df['lower_shadow'] = result_df[['open', 'close']].min(axis=1) - result_df['low']
        
        # 成交量特征
        if 'volume' in df.columns:
            # 成交量变化
            result_df['volume_change'] = result_df['volume'].pct_change()
            
            # 量价关系
            if 'price_change_pct' in result_df.columns:
                result_df['volume_price_ratio'] = result_df['volume'] * abs(result_df['price_change_pct'])
        
        return result_df
    
    def extract_technical_indicators(self, df: pd.DataFrame, indicators: List[str] = None) -> pd.DataFrame:
        """
        提取技术指标。
        
        Args:
            df: 输入数据DataFrame
            indicators: 指标列表
            
        Returns:
            包含技术指标的DataFrame
        """
        if df.empty:
            return df
        
        if indicators is None:
            indicators = ['sma_5', 'sma_20', 'rsi_14']
        
        result_df = df.copy()
        
        for indicator in indicators:
            try:
                if indicator.startswith('sma_'):
                    # 简单移动平均
                    period = int(indicator.split('_')[1])
                    if 'close' in df.columns:
                        result_df[indicator] = df['close'].rolling(window=period).mean()
                
                elif indicator.startswith('ema_'):
                    # 指数移动平均
                    period = int(indicator.split('_')[1])
                    if 'close' in df.columns:
                        result_df[indicator] = df['close'].ewm(span=period).mean()
                
                elif indicator.startswith('rsi_'):
                    # RSI指标
                    period = int(indicator.split('_')[1])
                    if 'close' in df.columns:
                        result_df[indicator] = self._calculate_rsi(df['close'], period)
                
                elif indicator == 'macd':
                    # MACD指标
                    if 'close' in df.columns:
                        macd_data = self._calculate_macd(df['close'])
                        result_df['macd'] = macd_data['macd']
                        result_df['macd_signal'] = macd_data['signal']
                        result_df['macd_histogram'] = macd_data['histogram']
                
            except Exception as e:
                logger.error(f"计算指标 {indicator} 失败: {e}")
                continue
        
        return result_df
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标。"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, pd.Series]:
        """计算MACD指标。"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        macd_histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram
        }
    
    def profile_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        分析数据特征。
        
        Args:
            df: 数据DataFrame
            
        Returns:
            数据特征分析报告
        """
        if df.empty:
            return {'status': 'empty'}
        
        profile = {
            'basic_info': {
                'shape': df.shape,
                'columns': list(df.columns),
                'data_types': df.dtypes.astype(str).to_dict()
            },
            'missing_values': df.isnull().sum().to_dict(),
            'numeric_summary': {},
            'categorical_summary': {}
        }
        
        # 数值列分析
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            profile['numeric_summary'] = df[numeric_cols].describe().to_dict()
        
        # 分类列分析
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            for col in categorical_cols:
                profile['categorical_summary'][col] = {
                    'unique_count': df[col].nunique(),
                    'top_values': df[col].value_counts().head().to_dict()
                }
        
        return profile
