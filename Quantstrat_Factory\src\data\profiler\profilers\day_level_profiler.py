import pandas as pd
from pathlib import Path
import numpy as np
import configparser
from typing import List, Dict, Any, Optional
import concurrent.futures
import datetime # 新增导入

# 假设 feature_store_client 在项目根目录
# 需要调整 sys.path 来正确导入
import sys
ROOT_DIR = Path(__file__).resolve().parents[2] # Quantstrat_Factory
sys.path.append(str(ROOT_DIR))
try:
    from feature_store_client import FeatureStoreClient
except ImportError:
    print("错误: DayLevelProfiler 无法导入 FeatureStoreClient。")
    FeatureStoreClient = None


class DayLevelProfiler:
    def __init__(self, 
                 config: configparser.ConfigParser, 
                 start_date: Optional[str] = None, 
                 end_date: Optional[str] = None,
                 symbols_to_process: Optional[List[str]] = None,
                 verbose: bool = False,
                 feature_store_client: Optional[FeatureStoreClient] = None):
        self.config = config
        self.start_date_dt = pd.to_datetime(start_date) if start_date else None
        self.end_date_dt = pd.to_datetime(end_date) if end_date else None
        self.symbols_to_process = symbols_to_process
        self.verbose = verbose
        
        if feature_store_client:
            self.feature_store_client = feature_store_client
        elif FeatureStoreClient:
            self.feature_store_client = FeatureStoreClient(config_object=self.config)
        else:
            raise ImportError("FeatureStoreClient 未能成功导入或提供，DayLevelProfiler 无法工作。")

    def _load_daily_basics_from_feature_store(self) -> pd.DataFrame:
        """
        从 FeatureStoreClient 加载指定日期范围和股票列表的 daily_basics 数据。
        """
        if not self.symbols_to_process:
            print("警告: 未提供要处理的股票列表，无法加载 daily_basics 数据。")
            return pd.DataFrame()

        if not self.start_date_dt or not self.end_date_dt:
            print("警告: 未提供有效的日期范围 (start_date_dt, end_date_dt)，无法加载 daily_basics 数据。")
            return pd.DataFrame()

        print(f"正在从统一路径加载 'daily_basics' 数据，日期范围从 {self.start_date_dt.strftime('%Y-%m-%d')} 到 {self.end_date_dt.strftime('%Y-%m-%d')}。")

        # 从统一的日K数据路径加载
        daily_basics_path = Path("D:/PY/Data/cleaned/daily/daily_basics.parquet")
        if not daily_basics_path.exists():
            print(f"错误: 日K数据文件不存在: {daily_basics_path}")
            return pd.DataFrame()

        df = pd.read_parquet(daily_basics_path)

        # 过滤日期范围
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
            mask = (df['datetime'] >= self.start_date_dt) & (df['datetime'] <= self.end_date_dt)
            df = df[mask]
        
        if df.empty:
            print("警告: 未能从 FeatureStore 加载到任何 'daily_basics' 数据。")
            return pd.DataFrame()

        # 仅筛选指定的股票
        df = df[df['symbol'].isin(self.symbols_to_process)].copy()

        if df.empty:
            print("警告: 筛选指定股票后，'daily_basics' 数据为空。")
            return pd.DataFrame()

        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.sort_values(by=['symbol', 'datetime']).reset_index(drop=True)
        return df

    def _save_feature_to_store(self, feature_df: pd.DataFrame, feature_name: str, category: str = "default"):
        """使用 FeatureStoreClient 保存特征。"""
        if not self.feature_store_client:
            print(f"错误: FeatureStoreClient 未初始化，无法保存特征 '{feature_name}'。")
            return

        if feature_df.empty:
            print(f"没有新的 '{feature_name}' 特征数据需要保存。")
            return

        # 使用简化版本的 save_features 方法
        self.feature_store_client.save_features(feature_df, feature_name, category=category, level='day')

    def _calculate_pct_chg_volume_ratio(self, all_day_data: pd.DataFrame, m_value: int = 20):
        print("正在计算 'pct_chg' 和 'volume_ratio' 特征...")
        if all_day_data.empty:
            print("无日线数据可处理 'pct_chg' 和 'volume_ratio'。")
            return

        df = all_day_data.copy()
        symbol_col = 'symbol'

        if 'close' in df.columns:
            df['pct_chg'] = df.groupby(symbol_col)['close'].pct_change() * 100
            self._save_feature_to_store(df[['datetime', symbol_col, 'pct_chg']].dropna(), 'pct_chg', category='technical')

        if 'volume' in df.columns:
            volume_mean = df.groupby(symbol_col)['volume'].transform(lambda x: x.rolling(window=m_value, min_periods=1).mean())
            df['volume_ratio'] = df['volume'] / volume_mean
            self._save_feature_to_store(df[['datetime', symbol_col, 'volume_ratio']].dropna(), 'volume_ratio', category='technical')

    def _calculate_aroon(self, all_day_data: pd.DataFrame, period: int = 25):
        print(f"正在计算 Aroon ({period}) 特征...")
        if all_day_data.empty:
            print("无日线数据可处理 Aroon。")
            return

        df = all_day_data.copy()
        symbol_col = 'symbol'

        if 'high' not in df.columns or 'low' not in df.columns:
            print("警告: 缺少 'high' 或 'low' 列，无法计算 Aroon。")
            return

        def aroon_calc(group):
            high = group['high']
            low = group['low']
            high_rolling = high.rolling(period + 1, min_periods=period)
            low_rolling = low.rolling(period + 1, min_periods=period)
            aroon_up = high_rolling.apply(lambda x: x.argmax() / period * 100 if pd.notna(x).all() else np.nan, raw=True)
            aroon_down = low_rolling.apply(lambda x: x.argmin() / period * 100 if pd.notna(x).all() else np.nan, raw=True)
            return pd.DataFrame({'aroon_up': aroon_up, 'aroon_down': aroon_down}, index=group.index)

        # 使用 groupby().apply() 并确保索引对齐
        aroon_results = df.groupby(symbol_col, group_keys=False).apply(aroon_calc)
        df = df.join(aroon_results)

        self._save_feature_to_store(df[['datetime', 'symbol', 'aroon_up']].dropna(), 'aroon_up', category='technical')
        self._save_feature_to_store(df[['datetime', 'symbol', 'aroon_down']].dropna(), 'aroon_down', category='technical')

    def _calculate_ma(self, all_day_data: pd.DataFrame, window: int):
        feature_name = f'MA_{window}'
        category = 'technical'
        print(f"正在计算 '{feature_name}' 特征...")
        
        if all_day_data.empty:
            return

        df = all_day_data.copy()
        df[feature_name] = df.groupby('symbol')['close'].transform(
            lambda x: x.rolling(window=window, min_periods=window).mean()
        )
        self._save_feature_to_store(df[['datetime', 'symbol', feature_name]].dropna(), feature_name, category=category)

    def _calculate_rsi(self, all_day_data: pd.DataFrame, window: int = 14):
        feature_name = f'RSI_{window}'
        category = 'technical'
        print(f"正在计算 '{feature_name}' 特征...")

        if all_day_data.empty:
            return

        df = all_day_data.copy()

        def rsi_calc(series):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).fillna(0)
            loss = -delta.where(delta < 0, 0).fillna(0)
            avg_gain = gain.ewm(com=window - 1, min_periods=window).mean()
            avg_loss = loss.ewm(com=window - 1, min_periods=window).mean()
            rs = avg_gain / avg_loss
            return 100 - (100 / (1 + rs))

        df[feature_name] = df.groupby('symbol')['close'].transform(rsi_calc)
        self._save_feature_to_store(df[['datetime', 'symbol', feature_name]].dropna(), feature_name, category=category)
        
    def _aggregate_single_minute_feature_to_day(
        self, 
        min_feature_name: str, 
        min_feature_category: str, 
        aggregation_methods: List[str],
        symbols: List[str],
        date_range: pd.DatetimeIndex
    ):
        print(f"开始聚合分钟特征 '{min_feature_category}/{min_feature_name}' 到日级别...")
        
        # 定义一个辅助函数，用于处理单个 (symbol, date) 对的聚合
        # 现在这个函数接收预加载的分钟数据字典
        def _process_single_symbol_date_aggregation(symbol: str, date_obj: pd.Timestamp, 
                                                    min_feature_name: str, aggregation_methods: List[str],
                                                    preloaded_minute_data_by_symbol: Dict[str, pd.DataFrame], verbose: bool) -> Dict[str, Any]:
            date_val = date_obj.date()
            result = {'datetime': pd.Timestamp(date_obj), 'symbol': symbol}
            
            for agg_method in aggregation_methods:
                result[agg_method] = np.nan

            try:
                symbol_df = preloaded_minute_data_by_symbol.get(symbol)
                if symbol_df is None or symbol_df.empty:
                    return result

                if date_val in symbol_df.index:
                    feature_value = symbol_df.loc[date_val, min_feature_name]
                    # 如果一个日期有多行，loc会返回一个Series，取第一个值
                    if isinstance(feature_value, pd.Series):
                        feature_value = feature_value.iloc[0]

                    if pd.notna(feature_value):
                        for agg_method in aggregation_methods:
                            result[agg_method] = feature_value
                elif verbose:
                    # print(f"  信息: {symbol} 日期 {date_val}：未找到分钟特征数据。") # Too verbose
                    pass

            except Exception as e:
                print(f"  处理股票 {symbol} 日期 {date_val} 的特征 '{min_feature_name}' 失败: {e}。将生成 NaN。")
            
            return result

        # --- 增量更新逻辑 ---
        # 1. 确定所有需要计算的 (symbol, date) 组合
        all_tasks_metadata = set()
        for symbol in symbols:
            for date_obj in date_range:
                all_tasks_metadata.add((symbol, date_obj.date()))

        # --- 预加载所有需要的分钟特征数据 (按股票聚合) ---
        print(f"正在为 {len(symbols)} 个股票预加载分钟特征数据...")
        preloaded_minute_data_by_symbol = {}
        
        def _load_symbol_minute_features(symbol):
            try:
                # 使用 feature_store_client 加载该股票在指定日期范围内的所有分钟特征
                df = self.feature_store_client.get_feature(
                    feature_name=None, # not used for this level
                    category=None, # not used for this level
                    level='minute_daily_bundle',
                    start_date=date_range.min(),
                    end_date=date_range.max(),
                    symbol=symbol,
                    columns=[min_feature_name, 'datetime', 'symbol'] # 只加载需要的列
                )
                if not df.empty:
                    # 按日期分组，以便快速查找
                    df['date_only'] = df['datetime'].dt.date
                    return symbol, df.set_index('date_only')
                return symbol, None
            except Exception as e:
                if self.verbose:
                    print(f"为股票 {symbol} 加载分钟特征失败: {e}")
                return symbol, None

        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(_load_symbol_minute_features, s) for s in symbols]
            for future in concurrent.futures.as_completed(futures):
                symbol, df = future.result()
                if df is not None:
                    preloaded_minute_data_by_symbol[symbol] = df
        
        print(f"预加载完成，共加载了 {len(preloaded_minute_data_by_symbol)} 个股票的分钟特征数据。")

        all_aggregated_results = []
        
        # 定义批处理大小
        batch_size = 10000 # 可以根据实际情况调整批处理大小

        # 使用 ThreadPoolExecutor 进行并行处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor: # max_workers 可调整
            for agg_method in aggregation_methods:
                output_feature_name = f"{min_feature_name}_{agg_method}_day"
                category = f"agg_{min_feature_category}"
                existing_metadata = self.feature_store_client.get_existing_feature_metadata(output_feature_name, category)
                
                metadata_to_compute = all_tasks_metadata - existing_metadata
                if not metadata_to_compute:
                    print(f"聚合特征 '{output_feature_name}' 已是最新，无需计算。")
                    continue

                print(f"需要为 {len(metadata_to_compute)} 个 (股票, 日期) 组合计算聚合特征 '{output_feature_name}'。")
                
                # 将需要计算的任务分割成批次
                tasks_for_this_agg_method = []
                for symbol, date_val in metadata_to_compute:
                    date_obj = pd.Timestamp(date_val)
                    tasks_for_this_agg_method.append((symbol, date_obj, min_feature_name, [agg_method]))

                def chunks(lst, n):
                    for i in range(0, len(lst), n):
                        yield lst[i:i + n]

                task_batches = list(chunks(tasks_for_this_agg_method, batch_size))
                print(f"将 {len(tasks_for_this_agg_method)} 个任务分割成 {len(task_batches)} 个批次，每批次 {batch_size} 个任务。")

                for i, batch in enumerate(task_batches):
                    print(f"正在处理批次 {i+1}/{len(task_batches)} for {output_feature_name}...")
                    futures = [executor.submit(_process_single_symbol_date_aggregation, *task, preloaded_minute_data_by_symbol, self.verbose) for task in batch]
                    for future in concurrent.futures.as_completed(futures):
                        result = future.result()
                        if result:
                            all_aggregated_results.append(result)
        
        # 将结果按聚合方法分类
        all_aggregated_data = {agg_method: [] for agg_method in aggregation_methods}
        for res in all_aggregated_results:
            dt = res['datetime']
            sym = res['symbol']
            for agg_method in aggregation_methods:
                if agg_method in res:
                    all_aggregated_data[agg_method].append({
                        'datetime': dt,
                        'symbol': sym,
                        'value': res[agg_method]
                    })

        for agg_method, data_list in all_aggregated_data.items():
            if not data_list:
                print(f"  特征 '{min_feature_name}' 使用方法 '{agg_method}' 聚合后无数据。")
                continue
            
            agg_df = pd.DataFrame(data_list)
            output_feature_name = f"{min_feature_name}_{agg_method}_day"
            agg_df.rename(columns={'value': output_feature_name}, inplace=True)
            
            self._save_feature_to_store(
                agg_df[['datetime', 'symbol', output_feature_name]], 
                output_feature_name, 
                category=f"agg_{min_feature_category}"
            )

    def compute_and_save_features(self, feature_sets: List[str], aggregation_rules: List[Dict[str, Any]] = None):
        # 确保日期范围和股票列表已设置
        if not self.start_date_dt or not self.end_date_dt or not self.symbols_to_process:
            print("错误: 无法进行特征计算，因为未提供有效的日期范围或股票列表。")
            return

        # 加载增量日线数据
        all_day_data = self._load_daily_basics_from_feature_store()
        
        if all_day_data.empty:
            print("警告: 未能加载任何日线数据，跳过所有日级别特征计算。")
            return

        # 确定聚合任务的日期范围和股票列表
        date_range_for_agg = pd.date_range(start=self.start_date_dt, end=self.end_date_dt, freq='B')
        symbols_for_agg = self.symbols_to_process

        for feature_set_name in feature_sets:
            print(f"\n处理日级别特征集: '{feature_set_name}'")
            if feature_set_name == 'daily_basics':
                print("daily_basics 特征集已由 run_auditor.py 处理，此处跳过。")
                continue
            
            elif feature_set_name == 'technical':
                self._calculate_pct_chg_volume_ratio(all_day_data)
                self._calculate_aroon(all_day_data)
                self._calculate_ma(all_day_data, window=5)
                self._calculate_ma(all_day_data, window=10)
                self._calculate_ma(all_day_data, window=20)
                self._calculate_rsi(all_day_data, window=14)

            elif feature_set_name == 'day_from_min_aggregation':
                if aggregation_rules and date_range_for_agg is not None and not date_range_for_agg.empty and symbols_for_agg:
                    print(f"将对 {len(symbols_for_agg)} 个股票在日期范围 "
                          f"{date_range_for_agg.min().strftime('%Y-%m-%d')} to {date_range_for_agg.max().strftime('%Y-%m-%d')} "
                          f"进行分钟特征聚合。")
                    for rule in aggregation_rules:
                        self._aggregate_single_minute_feature_to_day(
                            min_feature_name=rule['feature_name'],
                            min_feature_category=rule.get('category', 'unknown_min_cat'),
                            aggregation_methods=rule['methods'],
                            symbols=symbols_for_agg,
                            date_range=date_range_for_agg
                        )
                else: 
                    print("已跳过 'day_from_min_aggregation'，因为缺少有效聚合规则、日期范围或股票列表。")
            else: 
                print(f"警告: 未知的日级别特征集 '{feature_set_name}'。")
