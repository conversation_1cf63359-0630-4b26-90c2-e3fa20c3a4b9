import pandas as pd
from pathlib import Path
import numpy as np
import configparser
import sys
from typing import List, Dict, Any # 确保导入

ROOT_DIR = Path(__file__).resolve().parents[2] 
sys.path.append(str(ROOT_DIR))
try:
    from feature_store_client import FeatureStoreClient
except ImportError:
    print("错误: MinLevelProfiler 无法导入 FeatureStoreClient。")
    FeatureStoreClient = None

class MinLevelProfiler:
    def __init__(self, config: configparser.ConfigParser, start_date: str = None, end_date: str = None, symbol_to_process: str = None, verbose: bool = False):
        self.config = config
        self.start_date = start_date
        self.end_date = end_date
        self.symbol_to_process = symbol_to_process
        self.verbose = verbose

        if FeatureStoreClient is None:
            raise ImportError("FeatureStoreClient 未能成功导入，MinLevelProfiler 无法工作。")

        # 从配置中获取特征存储路径
        if isinstance(config, dict):
            data_config = config.get('data', {})
            feature_store_path = data_config.get('features', {}).get('root', 'D:/PY/Data/features')
            cleaned_min_path = data_config.get('cleaned', {}).get('minute', 'D:/PY/Data/cleaned/minute')
        else:
            feature_store_path = config.get('Paths', 'feature_store_path', fallback='D:/PY/Data/features')
            cleaned_min_path = config.get('Paths', 'cleaned_data_path', fallback='D:/PY/Data/cleaned/minute')

        self.feature_store_client = FeatureStoreClient(base_path=feature_store_path)
        self.cleaned_min_data_path = Path(cleaned_min_path)

    def _load_cleaned_min_data(self) -> pd.DataFrame:
        if not self.symbol_to_process:
            raise ValueError("错误: MinLevelProfiler 需要一个 symbol_to_process 来加载数据。")

        symbol_dir = self.cleaned_min_data_path / self.feature_store_client.get_symbol_path(self.symbol_to_process)

        if not symbol_dir.exists():
            if self.verbose:
                print(f"警告: 股票 {self.symbol_to_process} 的数据目录不存在: {symbol_dir}")
            return pd.DataFrame()

        all_dfs = []
        if not self.start_date or not self.end_date:
            print("错误: MinLevelProfiler 需要指定 start_date 和 end_date 来加载分钟数据。")
            return pd.DataFrame()

        start_dt_obj = pd.to_datetime(self.start_date)
        end_dt_obj = pd.to_datetime(self.end_date)
        start_year = start_dt_obj.year
        end_year = end_dt_obj.year

        if self.verbose:
            print(f"[{self.symbol_to_process}] 开始从年份分区加载数据...")
        
        for year in range(start_year, end_year + 1):
            partition_dir = symbol_dir / f"year={year}"
            partition_file = partition_dir / "cleaned_data.parquet"
            if partition_file.exists():
                try:
                    df = pd.read_parquet(partition_file)
                    all_dfs.append(df)
                except Exception as e:
                    print(f"  加载文件 {partition_file} 失败: {e}")

        if not all_dfs:
            if self.verbose:
                print(f"警告: 股票 {self.symbol_to_process} 在指定日期范围内未找到任何数据文件。")
            return pd.DataFrame()

        if self.verbose:
            print(f"[{self.symbol_to_process}] 数据加载完成，合并 {len(all_dfs)} 个年份的文件。")
        
        combined_df = pd.concat(all_dfs, ignore_index=True)
        if 'datetime' in combined_df.columns and 'symbol' in combined_df.columns:
            combined_df['datetime'] = pd.to_datetime(combined_df['datetime'])
            combined_df.drop_duplicates(subset=['datetime', 'symbol'], keep='first', inplace=True)
            combined_df = combined_df.sort_values(by=['symbol', 'datetime']).reset_index(drop=True)
            
            # 最终过滤，确保日期范围精确
            start_datetime_obj = pd.to_datetime(self.start_date)
            end_datetime_obj = pd.to_datetime(self.end_date) + pd.Timedelta(days=1) - pd.Timedelta(seconds=1) # 包含结束日期的所有时间
            
            combined_df = combined_df[
                (combined_df['datetime'] >= start_datetime_obj) & 
                (combined_df['datetime'] <= end_datetime_obj)
            ]
        return combined_df

    def _load_all_factor_calculators(self) -> list:
        all_calculators = []
        # profilers_dir = Path(__file__).parent # 指向 profilers 目录
        # minute_factors_dir = profilers_dir / "minute_factors"
        # 为了测试的灵活性，以及考虑到脚本可能从不同位置运行，
        # 最好让调用者（如run_profiler.py）来确定minute_factors的准确路径
        # 或者，如果MinLevelProfiler总是在其固定位置，可以这样：
        current_script_dir = Path(__file__).parent
        minute_factors_dir = current_script_dir / "minute_factors"

        if not minute_factors_dir.is_dir():
            print(f"错误: 分钟因子目录 {minute_factors_dir} 不存在。")
            return all_calculators

        for factor_file in minute_factors_dir.glob('*.py'):
            if factor_file.name == '__init__.py':
                continue
            
            module_name = factor_file.stem
            try:
                # 动态导入模块，相对于 profilers 目录
                # 假设 profilers 目录在PYTHONPATH中，或者 run_profiler.py 已经处理了路径
                module_path_for_import = f"minute_factors.{module_name}" 
                # 如果是从 Quantstrat_Factory 根目录运行，则需要更完整的路径
                # 例如： from Quantstrat_Factory.02_feature_profiler.profilers.minute_factors import ...
                # 鉴于 run_profiler.py 中已将 PROFILERS_DIR 添加到 sys.path, 
                # 并且 MinLevelProfiler 在 profilers 目录下，可以直接导入 minute_factors
                factor_module = __import__(module_path_for_import, fromlist=[module_name])
                
                if hasattr(factor_module, 'FACTOR_CALCULATORS') and isinstance(factor_module.FACTOR_CALCULATORS, list):
                    print(f"  从模块 {module_name} 加载了 {len(factor_module.FACTOR_CALCULATORS)} 个因子计算器。")
                    all_calculators.extend(factor_module.FACTOR_CALCULATORS)
                else:
                    print(f"警告: 因子模块 {module_name} 未定义 FACTOR_CALCULATORS 列表或格式不正确。")
            except ImportError as e:
                print(f"错误: 无法导入因子模块 {module_name} (尝试路径: {module_path_for_import}): {e}")
            except Exception as e:
                print(f"加载因子模块 {module_name} 时发生未知错误: {e}")
        
        print(f"总共加载了 {len(all_calculators)} 个分钟因子计算器。")
        return all_calculators

    def compute_and_save_features(self, requested_feature_sets_or_names: List[str]):
        # 加载单只股票的全部数据
        all_min_data = self._load_cleaned_min_data()
        if all_min_data.empty:
            print(f"[{self.symbol_to_process}] 未能加载任何分钟数据，跳过计算。")
            return

        # 加载所有可用的因子计算器
        available_calculators = self._load_all_factor_calculators()
        if not available_calculators:
            print(f"[{self.symbol_to_process}] 错误: 未能加载任何分钟因子计算器。")
            return

        # 筛选出需要运行的计算器
        calculators_to_run_map = {}
        if "all" in requested_feature_sets_or_names:
            for calc_info in available_calculators:
                calculators_to_run_map[calc_info['name']] = calc_info
        else:
            for req_name_or_set in requested_feature_sets_or_names:
                for calc_info in available_calculators:
                    if calc_info['name'] == req_name_or_set or calc_info.get('category') == req_name_or_set:
                        calculators_to_run_map[calc_info['name']] = calc_info
        
        if not calculators_to_run_map:
            print(f"[{self.symbol_to_process}] 没有有效的分钟因子需要计算。")
            return

        if self.verbose:
            print(f"[{self.symbol_to_process}] 将要计算 {len(calculators_to_run_map)} 个分钟因子: {list(calculators_to_run_map.keys())}")

        # 向量化计算：对整个DataFrame按天分组，然后应用所有计算
        daily_groups = all_min_data.groupby(all_min_data['datetime'].dt.date)
        
        all_daily_results = []

        for date_obj, one_day_data in daily_groups:
            if one_day_data.empty:
                continue

            # --- 中央预计算 ---
            # 预先计算当天所有因子都可能用到的基础指标
            daily_metrics = {}
            one_day_data_sorted = one_day_data.sort_values(by='datetime')
            daily_metrics['daily_high'] = one_day_data['high'].max()
            daily_metrics['daily_low'] = one_day_data['low'].min()
            daily_metrics['daily_open'] = one_day_data_sorted['open'].iloc[0]
            daily_metrics['daily_close'] = one_day_data_sorted['close'].iloc[-1]
            daily_metrics['total_volume'] = one_day_data['volume'].sum()
            if daily_metrics['total_volume'] > 0:
                daily_metrics['vwap'] = (one_day_data['close'] * one_day_data['volume']).sum() / daily_metrics['total_volume']
            else:
                daily_metrics['vwap'] = np.nan
            # --- 结束预计算 ---

            daily_factors = {'datetime': pd.Timestamp(date_obj), 'symbol': self.symbol_to_process}
            
            for factor_name, calc_info in calculators_to_run_map.items():
                try:
                    # 将预计算的指标传递给因子函数
                    result_df = calc_info['function'](
                        one_day_data.copy(), 
                        params={}, 
                        daily_metrics=daily_metrics
                    )
                    if not result_df.empty and factor_name in result_df.columns:
                        daily_factors[factor_name] = result_df[factor_name].iloc[0]
                    else:
                        daily_factors[factor_name] = np.nan
                except Exception as e:
                    # print(f"    为股票 {self.symbol_to_process} 日期 {date_obj} 计算因子 {factor_name} 时发生错误: {e}")
                    daily_factors[factor_name] = np.nan
            
            all_daily_results.append(daily_factors)

        if not all_daily_results:
            print(f"[{self.symbol_to_process}] 未计算出任何有效的因子值。")
            return

        # 将所有结果合并成一个DataFrame
        if not all_daily_results:
            print(f"[{self.symbol_to_process}] 未计算出任何有效的因子值，不进行保存。")
            return
            
        final_df = pd.DataFrame(all_daily_results)
        final_df.set_index('datetime', inplace=True)

        # --- 新的保存逻辑：一次性保存所有特征，由FeatureStoreClient处理按年分区 ---
        if self.verbose:
            print(f"[{self.symbol_to_process}] 开始保存 {len(final_df)} 天的特征...")
        
        try:
            # 恢复datetime列
            final_df.reset_index(inplace=True)
            # 确保有symbol列
            if 'symbol' not in final_df.columns:
                final_df['symbol'] = self.symbol_to_process

            self.feature_store_client.save_features(
                features=final_df,
                feature_name=f"minute_features_{self.symbol_to_process}",
                symbol=self.symbol_to_process,
                level='minute'
            )
            if self.verbose:
                print(f"[{self.symbol_to_process}] 完成保存。")
        except Exception as e:
            print(f"[{self.symbol_to_process}] 保存分钟特征失败: {e}")
