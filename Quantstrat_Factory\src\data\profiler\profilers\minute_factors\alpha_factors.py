# 文件名: alpha_factors.py
# 类别: Alpha 因子
import pandas as pd
import numpy as np

# -----------------------------------------------------------------------------
# 因子函数命名约定: calculate_alpha_描述性名称
# 优化：所有函数都假定输入的 df_symbol_minutes 是单只股票单日的数据，并接收一个包含预计算指标的 daily_metrics 字典。
# -----------------------------------------------------------------------------

def calculate_alpha_vol_pct_morning(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算 Alpha 因子: vol_pct_morning (上午成交量占比)
    """
    factor_name = "vol_pct_morning"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes
    
    try:
        symbol = group_df['symbol'].iloc[0]
        
        group_df['time'] = group_df['datetime'].dt.time
        morning_start_time = params.get('morning_start_time', pd.Timestamp('09:30:00').time()) if params else pd.Timestamp('09:30:00').time()
        morning_end_time = params.get('morning_end_time', pd.Timestamp('11:30:00').time()) if params else pd.Timestamp('11:30:00').time()

        daily_total_volume = daily_metrics.get('total_volume', 0)
        if daily_total_volume == 0:
            vol_pct = 0.0
        else:
            morning_volume_df = group_df[(group_df['time'] >= morning_start_time) & (group_df['time'] <= morning_end_time)]
            morning_volume = morning_volume_df['volume'].sum()
            vol_pct = morning_volume / daily_total_volume

        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: vol_pct}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])
        
    return result_df.dropna(subset=[factor_name])


def calculate_alpha_return_open_to_10am(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "return_open_to_10am"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['open', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    try:
        symbol = group_df['symbol'].iloc[0]
        date_val = group_df.index[0].date()
        
        actual_open_price = daily_metrics.get('daily_open', np.nan)
        time_10am = pd.Timestamp(f"{date_val} 10:00:00").time()
        
        price_at_10am_series = group_df[group_df.index.time <= time_10am]['close']
        if price_at_10am_series.empty:
            price_at_10am_series = group_df[group_df.index.time > time_10am]['close']
        
        if price_at_10am_series.empty or pd.isna(actual_open_price):
             return_val = np.nan
        else:
            price_at_10am = price_at_10am_series.iloc[-1]
            return_val = (price_at_10am - actual_open_price) / actual_open_price if actual_open_price > 0 else np.nan
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: return_val}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_vol_pct_open30m(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "vol_pct_open30m"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time
        open_period_start_time = params.get('open_period_start_time', pd.Timestamp('09:30:00').time()) if params else pd.Timestamp('09:30:00').time()
        open_period_end_time = params.get('open_period_end_time', pd.Timestamp('10:00:00').time()) if params else pd.Timestamp('10:00:00').time()
        
        daily_total_volume = daily_metrics.get('total_volume', 0)
        if daily_total_volume == 0:
            vol_pct = 0.0
        else:
            open30m_volume = group_df[(group_df['time'] >= open_period_start_time) & (group_df['time'] <= open_period_end_time)]['volume'].sum()
            vol_pct = open30m_volume / daily_total_volume
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: vol_pct}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_vol_pct_mid(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "vol_pct_mid"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time
        mid_period_start_time = params.get('mid_period_start_time', pd.Timestamp('13:00:00').time()) if params else pd.Timestamp('13:00:00').time()
        mid_period_end_time = params.get('mid_period_end_time', pd.Timestamp('14:00:00').time()) if params else pd.Timestamp('14:00:00').time()
        
        daily_total_volume = daily_metrics.get('total_volume', 0)
        if daily_total_volume == 0:
            vol_pct = 0.0
        else:
            mid_volume = group_df[(group_df['time'] >= mid_period_start_time) & (group_df['time'] <= mid_period_end_time)]['volume'].sum()
            vol_pct = mid_volume / daily_total_volume
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: vol_pct}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_vol_pct_close30m(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "vol_pct_close30m"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time
        close_period_start_time = params.get('close_period_start_time', pd.Timestamp('14:30:00').time()) if params else pd.Timestamp('14:30:00').time()
        close_period_end_time = params.get('close_period_end_time', pd.Timestamp('15:00:00').time()) if params else pd.Timestamp('15:00:00').time()
        
        daily_total_volume = daily_metrics.get('total_volume', 0)
        if daily_total_volume == 0:
            vol_pct = 0.0
        else:
            close30m_volume = group_df[(group_df['time'] >= close_period_start_time) & (group_df['time'] <= close_period_end_time)]['volume'].sum()
            vol_pct = close30m_volume / daily_total_volume
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: vol_pct}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_vol_center_of_gravity(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "vol_center_of_gravity"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['volume', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        total_volume = daily_metrics.get('total_volume', 0)
        if group_df.empty or total_volume == 0:
            center_of_gravity = np.nan
        else:
            group_df_sorted = group_df.sort_values(by='datetime').reset_index(drop=True)
            group_df_sorted['minute_index'] = group_df_sorted.index + 1
            weighted_sum = (group_df_sorted['volume'] * group_df_sorted['minute_index']).sum()
            center_of_gravity = weighted_sum / total_volume
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: center_of_gravity}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_vol_std_pct(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "vol_std_pct"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if group_df.empty or len(group_df) < 2:
            std_pct = np.nan
        else:
            daily_total_volume = daily_metrics.get('total_volume', 0)
            if daily_total_volume == 0:
                std_pct = 0.0
            else:
                group_df['vol_pct_of_day'] = group_df['volume'] / daily_total_volume
                std_pct = group_df['vol_pct_of_day'].std(ddof=0)
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: std_pct}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_return_last_hour(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "return_last_hour"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['open', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes.set_index('datetime')

    try:
        symbol = group_df['symbol'].iloc[0]
        date_val = group_df.index[0].date()
        
        last_hour_start_time = params.get('last_hour_start_time', pd.Timestamp(f"{date_val} 14:00:00").time()) if params else pd.Timestamp(f"{date_val} 14:00:00").time()
        last_hour_end_time = params.get('last_hour_end_time', pd.Timestamp(f"{date_val} 14:59:00").time()) if params else pd.Timestamp(f"{date_val} 14:59:00").time()
        
        price_at_1400_series = group_df[group_df.index.time == last_hour_start_time]['open']
        if price_at_1400_series.empty:
            price_at_1400_series = group_df[group_df.index.time > last_hour_start_time]['open']
        
        if price_at_1400_series.empty:
            return_val = np.nan
        else:
            price_start_last_hour = price_at_1400_series.iloc[0]
            price_at_1459_series = group_df[group_df.index.time == last_hour_end_time]['close']
            if price_at_1459_series.empty:
                price_at_1459_series = group_df[group_df.index.time < last_hour_end_time]['close']
                price_end_last_hour = price_at_1459_series.iloc[-1] if not price_at_1459_series.empty else group_df['close'].iloc[-1]
            else:
                price_end_last_hour = price_at_1459_series.iloc[0]
            return_val = (price_end_last_hour - price_start_last_hour) / price_start_last_hour if price_start_last_hour > 0 else np.nan
            
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: return_val}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_return_intraday_skew(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "return_intraday_skew"
    if df_symbol_minutes.empty or 'close' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if len(group_df) < 3:
            skewness = np.nan
        else:
            group_df_sorted = group_df.sort_values(by='datetime')
            minute_returns = group_df_sorted['close'].pct_change().dropna()
            if len(minute_returns) < 3:
                skewness = np.nan
            else:
                skewness = minute_returns.skew()
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: skewness}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_volatility_am(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "volatility_am"
    if df_symbol_minutes.empty or 'close' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time
        morning_start_time = params.get('morning_start_time', pd.Timestamp('09:30:00').time()) if params else pd.Timestamp('09:30:00').time()
        morning_end_time = params.get('morning_end_time', pd.Timestamp('11:30:00').time()) if params else pd.Timestamp('11:30:00').time()
        
        am_df = group_df[(group_df['time'] >= morning_start_time) & (group_df['time'] <= morning_end_time)]
        if len(am_df) < 2:
            volatility = np.nan
        else:
            am_df_sorted = am_df.sort_values(by='datetime')
            log_returns_am = np.log(am_df_sorted['close'] / am_df_sorted['close'].shift(1)).dropna()
            if len(log_returns_am) < 2:
                volatility = np.nan
            else:
                volatility = log_returns_am.std(ddof=0)
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: volatility}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_volatility_pm(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "volatility_pm"
    if df_symbol_minutes.empty or 'close' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time
        pm_start_time = params.get('pm_start_time', pd.Timestamp('13:00:00').time()) if params else pd.Timestamp('13:00:00').time()
        pm_end_time = params.get('pm_end_time', pd.Timestamp('15:00:00').time()) if params else pd.Timestamp('15:00:00').time()
        
        pm_df = group_df[(group_df['time'] >= pm_start_time) & (group_df['time'] <= pm_end_time)]
        if len(pm_df) < 2:
            volatility = np.nan
        else:
            pm_df_sorted = pm_df.sort_values(by='datetime')
            log_returns_pm = np.log(pm_df_sorted['close'] / pm_df_sorted['close'].shift(1)).dropna()
            if len(log_returns_pm) < 2:
                volatility = np.nan
            else:
                volatility = log_returns_pm.std(ddof=0)
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: volatility}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_amplitude_total(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "amplitude_total"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'open', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_open = daily_metrics.get('daily_open', np.nan)
        amplitude = (daily_high - daily_low) / daily_open if daily_open > 0 else np.nan
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: amplitude}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_upper_tail_ratio(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "upper_tail_ratio"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'open', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_open = daily_metrics.get('daily_open', np.nan)
        daily_close = daily_metrics.get('daily_close', np.nan)
        
        upper_shadow = daily_high - max(daily_open, daily_close)
        total_range = daily_high - daily_low
        ratio = upper_shadow / total_range if total_range > 0 else (0.0 if upper_shadow == 0 and total_range == 0 else np.nan)
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: ratio}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_lower_tail_ratio(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "lower_tail_ratio"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'open', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_open = daily_metrics.get('daily_open', np.nan)
        daily_close = daily_metrics.get('daily_close', np.nan)
        
        lower_shadow = min(daily_open, daily_close) - daily_low
        total_range = daily_high - daily_low
        ratio = lower_shadow / total_range if total_range > 0 else (0.0 if lower_shadow == 0 and total_range == 0 else np.nan)
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: ratio}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_price_volume_corr(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "price_volume_corr"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['close', 'volume', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if len(group_df) < 2:
            correlation = np.nan
        else:
            group_df_sorted = group_df.sort_values(by='datetime')
            log_returns = np.log(group_df_sorted['close'] / group_df_sorted['close'].shift(1))
            correlation = log_returns.corr(group_df_sorted['volume'])
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: correlation}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_close_to_vwap_pct_diff(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "close_to_vwap_pct_diff"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['close', 'volume', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        vwap = daily_metrics.get('vwap', np.nan)
        daily_final_close = daily_metrics.get('daily_close', np.nan)
        
        if pd.isna(vwap) or pd.isna(daily_final_close):
            bias = np.nan
        else:
            bias = (daily_final_close - vwap) / vwap if vwap > 0 else np.nan
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: bias}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_volume_cluster_score(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "volume_cluster_score"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['volume', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes
    
    try:
        symbol = group_df['symbol'].iloc[0]
        volume_spike_multiplier = params.get('volume_spike_multiplier', 3) if params else 3
        min_minutes_for_avg = params.get('min_minutes_for_avg', 10) if params else 10
        
        if len(group_df) < min_minutes_for_avg:
            cluster_score = np.nan
        else:
            daily_total_volume = daily_metrics.get('total_volume', 0)
            if daily_total_volume == 0:
                cluster_score = 0.0
            else:
                avg_minute_volume = daily_total_volume / len(group_df)
                volume_spike_threshold = avg_minute_volume * volume_spike_multiplier
                pulse_minutes_df = group_df[group_df['volume'] > volume_spike_threshold]
                pulse_total_volume = pulse_minutes_df['volume'].sum()
                cluster_score = pulse_total_volume / daily_total_volume
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: cluster_score}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_intraday_range_position(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "intraday_range_position"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_final_close = daily_metrics.get('daily_close', np.nan)
        
        total_range = daily_high - daily_low
        position_val = (daily_final_close - daily_low) / total_range if total_range > 0 else (0.5 if total_range == 0 and daily_final_close == daily_low else np.nan)
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: position_val}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_price_trend_consistency(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "price_trend_consistency"
    if df_symbol_minutes.empty or 'close' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if len(group_df) < 2:
            consistency = np.nan
        else:
            group_df_sorted = group_df.sort_values(by='datetime')
            price_diff = group_df_sorted['close'].diff()
            up_minutes_count = (price_diff > 0).sum()
            total_comparable_minutes = len(price_diff.dropna())
            consistency = up_minutes_count / total_comparable_minutes if total_comparable_minutes > 0 else np.nan
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: consistency}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_volume_trend_consistency(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "volume_trend_consistency"
    if df_symbol_minutes.empty or 'volume' not in df_symbol_minutes.columns or 'datetime' not in df_symbol_minutes.columns:
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if len(group_df) < 2:
            consistency = np.nan
        else:
            group_df_sorted = group_df.sort_values(by='datetime')
            volume_diff = group_df_sorted['volume'].diff()
            increasing_volume_minutes_count = (volume_diff > 0).sum()
            total_comparable_minutes = len(volume_diff.dropna())
            consistency = increasing_volume_minutes_count / total_comparable_minutes if total_comparable_minutes > 0 else np.nan
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: consistency}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_close_in_upper_quantile(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "close_in_upper_quantile"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes
    
    try:
        symbol = group_df['symbol'].iloc[0]
        quantile_threshold = params.get('quantile_threshold', 0.8) if params else 0.8
        
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_final_close = daily_metrics.get('daily_close', np.nan)
        
        total_range = daily_high - daily_low
        is_in_upper_quantile = np.nan
        if pd.notna(total_range):
            if total_range > 0:
                quantile_price = daily_low + total_range * quantile_threshold
                is_in_upper_quantile = 1 if daily_final_close >= quantile_price else 0
            elif total_range == 0:
                is_in_upper_quantile = 1 if daily_final_close >= daily_low else 0
            
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: is_in_upper_quantile}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_pullback_amplitude(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "pullback_amplitude"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'close', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_final_close = daily_metrics.get('daily_close', np.nan)
        
        total_range = daily_high - daily_low
        pullback_amp_val = (daily_high - daily_final_close) / total_range if total_range > 0 else (0.0 if total_range == 0 and daily_high == daily_final_close else np.nan)
        
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: pullback_amp_val}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


def calculate_alpha_high_time_density(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "high_time_density"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'datetime']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])
    
    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        if len(group_df) < 1:
            density = np.nan
        else:
            daily_high = daily_metrics.get('daily_high', np.nan)
            high_minutes_df = group_df[group_df['high'] == daily_high]
            if high_minutes_df.empty:
                density = np.nan
            else:
                first_high_time = high_minutes_df['datetime'].min()
                group_df_sorted = group_df.sort_values(by='datetime').reset_index(drop=True)
                group_df_sorted['minute_index'] = group_df_sorted.index + 1
                high_minute_index = group_df_sorted[group_df_sorted['datetime'] == first_high_time]['minute_index'].iloc[0]
                total_minutes = len(group_df_sorted)
                density = high_minute_index / total_minutes if total_minutes > 0 else np.nan
                
        result_df = pd.DataFrame([{'datetime': group_df['datetime'].iloc[-1], 'symbol': symbol, factor_name: density}])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])

    return result_df.dropna(subset=[factor_name])


FACTOR_CALCULATORS = [
    {"function": calculate_alpha_vol_pct_morning, "name": "vol_pct_morning", "category": "alpha"},
    {"function": calculate_alpha_return_open_to_10am, "name": "return_open_to_10am", "category": "alpha"},
    {"function": calculate_alpha_vol_pct_open30m, "name": "vol_pct_open30m", "category": "alpha"},
    {"function": calculate_alpha_vol_pct_mid, "name": "vol_pct_mid", "category": "alpha"},
    {"function": calculate_alpha_vol_pct_close30m, "name": "vol_pct_close30m", "category": "alpha"},
    {"function": calculate_alpha_vol_center_of_gravity, "name": "vol_center_of_gravity", "category": "alpha"},
    {"function": calculate_alpha_vol_std_pct, "name": "vol_std_pct", "category": "alpha"},
    {"function": calculate_alpha_return_last_hour, "name": "return_last_hour", "category": "alpha"},
    {"function": calculate_alpha_return_intraday_skew, "name": "return_intraday_skew", "category": "alpha"},
    {"function": calculate_alpha_volatility_am, "name": "volatility_am", "category": "alpha"},
    {"function": calculate_alpha_volatility_pm, "name": "volatility_pm", "category": "alpha"},
    {"function": calculate_alpha_amplitude_total, "name": "amplitude_total", "category": "alpha"}, 
    {"function": calculate_alpha_upper_tail_ratio, "name": "upper_tail_ratio", "category": "alpha"},
    {"function": calculate_alpha_lower_tail_ratio, "name": "lower_tail_ratio", "category": "alpha"},
    {"function": calculate_alpha_price_volume_corr, "name": "price_volume_corr", "category": "alpha"},
    {"function": calculate_alpha_close_to_vwap_pct_diff, "name": "close_to_vwap_pct_diff", "category": "alpha"}, 
    {"function": calculate_alpha_volume_cluster_score, "name": "volume_cluster_score", "category": "alpha"},
    {"function": calculate_alpha_intraday_range_position, "name": "intraday_range_position", "category": "alpha"},
    {"function": calculate_alpha_price_trend_consistency, "name": "price_trend_consistency", "category": "alpha"},
    {"function": calculate_alpha_volume_trend_consistency, "name": "volume_trend_consistency", "category": "alpha"},
    {"function": calculate_alpha_close_in_upper_quantile, "name": "close_in_upper_quantile", "category": "alpha"},
    {"function": calculate_alpha_pullback_amplitude, "name": "pullback_amplitude", "category": "alpha"},
    {"function": calculate_alpha_high_time_density, "name": "high_time_density", "category": "alpha"},
]
