# 文件名: event_factors.py
# 类别: Event 信号因子
import pandas as pd
import numpy as np

# -----------------------------------------------------------------------------
# 因子函数命名约定: calculate_event_描述性名称
# 优化：所有函数都假定输入的 df_symbol_minutes 是单只股票单日的数据，并接收一个包含预计算指标的 daily_metrics 字典。
# -----------------------------------------------------------------------------

def calculate_event_tail_15m_spike(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算 Event 因子: tail_15m_spike (尾盘15 min 涨幅 > 1% 且放量)
    """
    factor_name = "tail_15m_spike"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['close', 'volume', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    spike_threshold = p.get('spike_threshold', 0.01)
    volume_multiplier = p.get('volume_multiplier', 1.5)

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False
        
        actual_market_close_dt = group_df.index.max()
        tail_start_datetime = max(group_df.index.min(), actual_market_close_dt - pd.Timedelta(minutes=15-1))
        
        tail_period_df = group_df[group_df.index >= tail_start_datetime]

        if len(tail_period_df) >= 2: 
            price_start_tail = tail_period_df['close'].iloc[0]
            price_end_tail = tail_period_df['close'].iloc[-1]
            tail_return = (price_end_tail - price_start_tail) / price_start_tail if price_start_tail > 0 else 0
            tail_volume = tail_period_df['volume'].sum()
            
            non_tail_df = group_df[group_df.index < tail_start_datetime]
            avg_other_15min_volume = 0
            if not non_tail_df.empty and len(non_tail_df) >= 1:
                duration_non_tail_minutes = (non_tail_df.index.max() - non_tail_df.index.min()).total_seconds() / 60 + 1
                if duration_non_tail_minutes >= 15:
                    num_15min_slots = duration_non_tail_minutes / 15.0
                    avg_other_15min_volume = non_tail_df['volume'].sum() / num_15min_slots
            
            event_triggered_today = (tail_return > spike_threshold) and \
                                    (tail_volume > avg_other_15min_volume * volume_multiplier if avg_other_15min_volume > 0 else tail_volume > 0)

        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])
        
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])


def calculate_event_afternoon_volume_jump(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算 Event 因子: afternoon_volume_jump (午后成交量跳跃)
    """
    factor_name = "afternoon_volume_jump"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['volume', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    afternoon_start_str = p.get('afternoon_start_time', "13:00")
    afternoon_end_str = p.get('afternoon_end_time', "14:30") 
    morning_start_str = p.get('morning_start_time', "09:30")
    morning_end_str = p.get('morning_end_time', "11:30")
    window_minutes = p.get('window_minutes', 30) 
    volume_multiplier = p.get('volume_multiplier', 2.0)

    try:
        symbol = group_df['symbol'].iloc[0]
        date = group_df.index[0].date()
        event_triggered_today = False
        
        afternoon_start_time = pd.Timestamp(f"{date} {afternoon_start_str}").time()
        afternoon_end_time = pd.Timestamp(f"{date} {afternoon_end_str}").time()
        morning_start_time = pd.Timestamp(f"{date} {morning_start_str}").time()
        morning_end_time = pd.Timestamp(f"{date} {morning_end_str}").time()

        morning_df = group_df[(group_df.index.time >= morning_start_time) & (group_df.index.time < morning_end_time)]
        
        avg_morning_window_volume = 0
        if not morning_df.empty and len(morning_df) >= window_minutes:
            morning_window_volumes = morning_df['volume'].resample(f'{window_minutes}min').sum()
            morning_window_volumes = morning_window_volumes[morning_window_volumes > 0]
            if not morning_window_volumes.empty:
                avg_morning_window_volume = morning_window_volumes.mean()

        afternoon_df = group_df[(group_df.index.time >= afternoon_start_time) & (group_df.index.time < afternoon_end_time)]

        if not afternoon_df.empty and len(afternoon_df) >= window_minutes and avg_morning_window_volume > 0:
            afternoon_window_volumes = afternoon_df['volume'].resample(f'{window_minutes}min').sum()
            if any(vol > avg_morning_window_volume * volume_multiplier for vol in afternoon_window_volumes):
                event_triggered_today = True
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])
        
    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_reverse_tail_tag(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "reverse_tail_tag"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['open', 'high', 'low', 'close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    tail_period_minutes = p.get('tail_period_minutes', 30) 
    pre_tail_end_offset_minutes = p.get('pre_tail_end_offset_minutes', 5)
    reversal_threshold_pct = p.get('reversal_threshold_pct', 0.01) 
    trend_threshold_pct = p.get('trend_threshold_pct', 0.005)

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False

        if len(group_df) >= (tail_period_minutes + 60): 
            day_open_price = daily_metrics.get('daily_open', np.nan)
            actual_market_close_dt = group_df.index.max() 
            tail_start_datetime = actual_market_close_dt - pd.Timedelta(minutes=tail_period_minutes -1) 
            pre_tail_end_datetime = tail_start_datetime - pd.Timedelta(minutes=pre_tail_end_offset_minutes)
            min_trend_observe_dt = group_df.index.min() + pd.Timedelta(minutes=30)

            if pre_tail_end_datetime < min_trend_observe_dt:
                 pre_tail_end_datetime = min_trend_observe_dt
            
            if pre_tail_end_datetime < tail_start_datetime:
                pre_tail_df = group_df[group_df.index <= pre_tail_end_datetime]
                tail_df = group_df[group_df.index >= tail_start_datetime]

                if not (pre_tail_df.empty or tail_df.empty or len(tail_df) < 2):
                    price_at_pre_tail_end = pre_tail_df['close'].iloc[-1]
                    intraday_trend_return = (price_at_pre_tail_end - day_open_price) / day_open_price if day_open_price > 0 else 0
                    price_at_tail_start = tail_df['open'].iloc[0] 
                    price_at_tail_end = tail_df['close'].iloc[-1] 
                    tail_return = (price_at_tail_end - price_at_tail_start) / price_at_tail_start if price_at_tail_start > 0 else 0

                    if (intraday_trend_return > trend_threshold_pct and tail_return < -reversal_threshold_pct) or \
                       (intraday_trend_return < -trend_threshold_pct and tail_return > reversal_threshold_pct):
                        event_triggered_today = True
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_is_tail_spike_rise(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "is_tail_spike_rise"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['low', 'close', 'volume', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    tail_minutes = p.get('tail_minutes', 15) 
    sharp_rise_threshold = p.get('sharp_rise_threshold', 0.015) 
    volume_multiplier = p.get('volume_multiplier', 1.5) 

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False

        if len(group_df) >= tail_minutes + 15:
            actual_market_close_dt = group_df.index.max()
            tail_start_datetime = actual_market_close_dt - pd.Timedelta(minutes=tail_minutes - 1)
            tail_period_df = group_df[group_df.index >= tail_start_datetime]

            if not (tail_period_df.empty or len(tail_period_df) < 2):
                tail_low = tail_period_df['low'].min()
                tail_close = tail_period_df['close'].iloc[-1]
                rise_from_low_pct = (tail_close - tail_low) / tail_low if tail_low > 0 else 0
                
                tail_volume = tail_period_df['volume'].sum()
                non_tail_df = group_df[group_df.index < tail_start_datetime]
                avg_other_period_volume = 0
                if not non_tail_df.empty and len(non_tail_df) >= tail_minutes:
                    duration_non_tail_minutes = (non_tail_df.index.max() - non_tail_df.index.min()).total_seconds() / 60 + 1
                    if duration_non_tail_minutes >= tail_minutes:
                        num_slots = duration_non_tail_minutes / tail_minutes
                        avg_other_period_volume = non_tail_df['volume'].sum() / num_slots
                
                volume_condition_met = (tail_volume > avg_other_period_volume * volume_multiplier if avg_other_period_volume > 0 else tail_volume > 0)

                if rise_from_low_pct > sharp_rise_threshold and volume_condition_met:
                    event_triggered_today = True
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_is_midday_vshape(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "is_midday_vshape"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['open', 'high', 'low', 'close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    midday_start_str = p.get('midday_start_time', "10:30") 
    midday_end_str = p.get('midday_end_time', "14:00")   
    min_decline_pct = p.get('min_decline_pct', 0.015)    
    min_rise_pct = p.get('min_rise_pct', 0.015)          
    min_recovery_ratio = p.get('min_recovery_ratio', 0.6) 
    min_duration_leg_minutes = p.get('min_duration_leg_minutes', 30)

    try:
        symbol = group_df['symbol'].iloc[0]
        date = group_df.index[0].date()
        event_triggered_today = False

        midday_start_dt = pd.Timestamp(f"{date} {midday_start_str}")
        midday_end_dt = pd.Timestamp(f"{date} {midday_end_str}")
        midday_df = group_df[(group_df.index >= midday_start_dt) & (group_df.index <= midday_end_dt)]

        if not (midday_df.empty or len(midday_df) < (2 * min_duration_leg_minutes)):
            vertex_time = midday_df['low'].idxmin()
            vertex_price = midday_df['low'].min()
            
            left_leg_df = midday_df[midday_df.index < vertex_time]
            right_leg_df = midday_df[midday_df.index > vertex_time]

            if not (left_leg_df.empty or right_leg_df.empty or \
               len(left_leg_df) < min_duration_leg_minutes or \
               len(right_leg_df) < min_duration_leg_minutes or \
               vertex_time == midday_df.index.min() or \
               vertex_time == midday_df.index.max()):

                p_start = left_leg_df['close'].iloc[0] 
                p_end = right_leg_df['close'].iloc[-1]   

                if vertex_price > 0 and p_start > 0: 
                    decline_magnitude = p_start - vertex_price
                    rise_magnitude = p_end - vertex_price
                    decline_pct = decline_magnitude / p_start
                    rise_pct = rise_magnitude / vertex_price
                    recovery_ratio = rise_magnitude / decline_magnitude if decline_magnitude > 0 else 0
                    
                    if decline_pct >= min_decline_pct and rise_pct >= min_rise_pct and recovery_ratio >= min_recovery_ratio:
                        event_triggered_today = True
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_is_volatility_squeeze(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "is_volatility_squeeze"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    bb_window = p.get('bb_window', 20) 
    bb_std_dev = p.get('bb_std_dev', 2) 
    squeeze_lookback = p.get('squeeze_lookback_periods', 60)

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False

        if len(group_df) >= bb_window:
            middle_band = group_df['close'].rolling(window=bb_window, min_periods=bb_window).mean()
            std_dev = group_df['close'].rolling(window=bb_window, min_periods=bb_window).std()
            upper_band = middle_band + (bb_std_dev * std_dev)
            lower_band = middle_band - (bb_std_dev * std_dev)
            
            bbw = (upper_band - lower_band) / middle_band
            bbw = bbw.replace([np.inf, -np.inf], np.nan).dropna() 

            if not (bbw.empty or len(bbw) < squeeze_lookback):
                rolling_min_bbw = bbw.rolling(window=squeeze_lookback, min_periods=1).min()
                
                if not bbw.empty and not rolling_min_bbw.empty:
                    min_val_in_lookback_is_current = (bbw.rolling(window=squeeze_lookback).min() == bbw)
                    if min_val_in_lookback_is_current.any():
                        event_triggered_today = True
            
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_is_bull_stretch(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "is_bull_stretch"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['open', 'high', 'low', 'close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    stretch_window_len = p.get('stretch_window_len', 90) 
    min_total_rise_pct = p.get('min_total_rise_pct', 0.025) 
    min_positive_bars_ratio = p.get('min_positive_bars_ratio', 0.65) 
    max_drawdown_from_high_ratio = p.get('max_drawdown_from_high_ratio', 0.30) 

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False

        if not (group_df.empty or len(group_df) < stretch_window_len):
            for i in range(len(group_df) - stretch_window_len + 1):
                window_df = group_df.iloc[i : i + stretch_window_len]
                
                if window_df.empty: continue

                window_open_price = window_df['open'].iloc[0]
                window_close_price = window_df['close'].iloc[-1]
                window_high_price = window_df['high'].max()
                window_low_price = window_df['low'].min()

                if window_open_price <= 0 or window_low_price <=0 or window_high_price <= window_low_price: 
                    continue

                total_rise_pct = (window_close_price - window_open_price) / window_open_price
                if total_rise_pct < min_total_rise_pct:
                    continue

                positive_bars = (window_df['close'] > window_df['open']).sum()
                positive_bars_ratio = positive_bars / len(window_df)
                if positive_bars_ratio < min_positive_bars_ratio:
                    continue
                
                window_amplitude = window_high_price - window_low_price
                drawdown_from_high = window_high_price - window_close_price
                drawdown_ratio = drawdown_from_high / window_amplitude if window_amplitude > 0 else 1.0
                
                if drawdown_ratio > max_drawdown_from_high_ratio:
                    continue
                
                event_triggered_today = True
                break 

        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

def calculate_event_high_time_density_morning(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    factor_name = "high_time_density_morning"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes.set_index('datetime')

    p = params if params else {}
    morning_period_minutes = p.get('morning_period_minutes', 60) 

    try:
        symbol = group_df['symbol'].iloc[0]
        event_triggered_today = False

        if not group_df.empty:
            time_of_day_high = group_df['high'].idxmax()
            market_open_time_today = group_df.index.min()
            morning_period_end_dt = market_open_time_today + pd.Timedelta(minutes=morning_period_minutes)
            
            if time_of_day_high < morning_period_end_dt:
                event_triggered_today = True
        
        result_df = pd.DataFrame([{'datetime': group_df.index[-1], 'symbol': symbol, factor_name: 1 if event_triggered_today else 0}])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: 0}])

    return result_df.dropna(subset=['datetime', 'symbol'])

FACTOR_CALCULATORS = [
    {"function": calculate_event_tail_15m_spike, "name": "tail_15m_spike", "category": "event"},
    {"function": calculate_event_afternoon_volume_jump, "name": "afternoon_volume_jump", "category": "event"},
    {"function": calculate_event_reverse_tail_tag, "name": "reverse_tail_tag", "category": "event"},
    {"function": calculate_event_is_tail_spike_rise, "name": "is_tail_spike_rise", "category": "event"},
    {"function": calculate_event_is_midday_vshape, "name": "is_midday_vshape", "category": "event"},
    {"function": calculate_event_is_volatility_squeeze, "name": "is_volatility_squeeze", "category": "event"},
    {"function": calculate_event_is_bull_stretch, "name": "is_bull_stretch", "category": "event"},
    {"function": calculate_event_high_time_density_morning, "name": "high_time_density_morning", "category": "event"},
]
