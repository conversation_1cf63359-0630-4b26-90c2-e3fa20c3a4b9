# 文件名: risk_factors.py
# 类别: 风险控制/预警指标
import pandas as pd
import numpy as np

# -----------------------------------------------------------------------------
# 因子函数命名约定: calculate_risk_描述性名称
# 优化：所有函数都假定输入的 df_symbol_minutes 是单只股票单日的数据，并接收一个包含预计算指标的 daily_metrics 字典。
# -----------------------------------------------------------------------------

def calculate_risk_amplitude_total(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算风险因子: amplitude_total (当日振幅)
    """
    factor_name = "amplitude_total"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'open', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes

    try:
        symbol = group_df['symbol'].iloc[0]
        
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        first_open_of_day = daily_metrics.get('daily_open', np.nan)
        
        amplitude = (daily_high - daily_low) / first_open_of_day if first_open_of_day > 0 else np.nan
        
        result_df = pd.DataFrame([{
            'datetime': group_df['datetime'].iloc[-1], 
            'symbol': symbol,
            factor_name: amplitude
        }])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])
            
    return result_df.dropna(subset=[factor_name, 'datetime', 'symbol'])


def calculate_risk_volatility_am_pm_ratio(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算风险因子: volatility_am_pm_ratio (上午下午波动率比)
    """
    factor_name = "volatility_am_pm_ratio"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes
    
    try:
        symbol = group_df['symbol'].iloc[0]
        group_df['time'] = group_df['datetime'].dt.time

        p = params if params else {}
        morning_start_time = p.get('morning_start_time', pd.Timestamp('09:30:00').time())
        morning_end_time = p.get('morning_end_time', pd.Timestamp('11:30:00').time())
        pm_start_time = p.get('pm_start_time', pd.Timestamp('13:00:00').time())
        pm_end_time = p.get('pm_end_time', pd.Timestamp('15:00:00').time())

        # Calculate AM volatility
        am_df = group_df[(group_df['time'] >= morning_start_time) & (group_df['time'] <= morning_end_time)]
        volatility_am = np.nan
        if len(am_df) >= 2:
            am_df_sorted = am_df.sort_values(by='datetime')
            log_returns_am = np.log(am_df_sorted['close'] / am_df_sorted['close'].shift(1)).dropna()
            if len(log_returns_am) >= 2:
                volatility_am = log_returns_am.std(ddof=0)

        # Calculate PM volatility
        pm_df = group_df[(group_df['time'] >= pm_start_time) & (group_df['time'] <= pm_end_time)]
        volatility_pm = np.nan
        if len(pm_df) >= 2:
            pm_df_sorted = pm_df.sort_values(by='datetime')
            log_returns_pm = np.log(pm_df_sorted['close'] / pm_df_sorted['close'].shift(1)).dropna()
            if len(log_returns_pm) >= 2:
                volatility_pm = log_returns_pm.std(ddof=0)
        
        ratio = np.nan
        if pd.notna(volatility_am) and pd.notna(volatility_pm) and volatility_pm > 0:
            ratio = volatility_am / volatility_pm
        
        result_df = pd.DataFrame([{
            'datetime': group_df['datetime'].iloc[-1], 
            'symbol': symbol, 
            factor_name: ratio
        }])

    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])
            
    return result_df.dropna(subset=[factor_name, 'datetime', 'symbol'])

def calculate_risk_pullback_amplitude(df_symbol_minutes: pd.DataFrame, params: dict = None, daily_metrics: dict = None) -> pd.DataFrame:
    """
    计算风险因子: pullback_amplitude (从高点回落幅度占比)
    """
    factor_name = "pullback_amplitude"
    if df_symbol_minutes.empty or not all(c in df_symbol_minutes.columns for c in ['high', 'low', 'close', 'datetime', 'symbol']):
        return pd.DataFrame(columns=['datetime', 'symbol', factor_name])

    group_df = df_symbol_minutes
    
    try:
        symbol = group_df['symbol'].iloc[0]
        
        daily_high = daily_metrics.get('daily_high', np.nan)
        daily_low = daily_metrics.get('daily_low', np.nan)
        daily_final_close = daily_metrics.get('daily_close', np.nan)
        
        total_range = daily_high - daily_low
        pullback_amp_val = np.nan
        if pd.notna(total_range):
            if total_range > 0:
                pullback_amp_val = (daily_high - daily_final_close) / total_range
            elif total_range == 0 and daily_high == daily_final_close:
                pullback_amp_val = 0.0
        
        result_df = pd.DataFrame([{
            'datetime': group_df['datetime'].iloc[-1], 
            'symbol': symbol, 
            factor_name: pullback_amp_val
        }])
    except Exception as e:
        symbol = df_symbol_minutes['symbol'].iloc[0] if not df_symbol_minutes.empty else None
        date = df_symbol_minutes['datetime'].iloc[0].date() if not df_symbol_minutes.empty else pd.NaT
        result_df = pd.DataFrame([{'datetime': pd.Timestamp(date), 'symbol': symbol, factor_name: np.nan}])
            
    return result_df.dropna(subset=[factor_name, 'datetime', 'symbol'])

FACTOR_CALCULATORS = [
    {"function": calculate_risk_amplitude_total, "name": "amplitude_total", "category": "risk"},
    {"function": calculate_risk_volatility_am_pm_ratio, "name": "volatility_am_pm_ratio", "category": "risk"},
    {"function": calculate_risk_pullback_amplitude, "name": "pullback_amplitude", "category": "risk"},
]
