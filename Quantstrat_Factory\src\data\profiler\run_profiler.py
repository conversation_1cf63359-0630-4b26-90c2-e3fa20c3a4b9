import argparse
import configparser
from pathlib import Path
import sys
from typing import List, Dict, Any, Optional
import multiprocessing
from functools import partial
import time
from tqdm import tqdm
import datetime # 新增导入

# 确保 Quantstrat_Factory 目录在 sys.path 中
# run_profiler.py 位于 Quantstrat_Factory/src/data/profiler
# feature_store_client.py 位于 Quantstrat_Factory/src/data/auditor
# 所以需要将相关路径添加到 sys.path
QUANTSTRAT_FACTORY_ROOT = Path(__file__).resolve().parents[3] # 指向 Quantstrat_Factory
sys.path.append(str(QUANTSTRAT_FACTORY_ROOT))

# 添加 auditor 目录到路径
auditor_path = QUANTSTRAT_FACTORY_ROOT / 'src' / 'data' / 'auditor'
sys.path.append(str(auditor_path))

from feature_store_client import FeatureStoreClient # 新增导入

PROFILERS_DIR = Path(__file__).resolve().parent / "profilers"
sys.path.append(str(PROFILERS_DIR))


def get_config():
    """Reads the main configuration file."""
    # 尝试读取 YAML 配置文件
    try:
        import yaml
        config_path = QUANTSTRAT_FACTORY_ROOT / 'config' / 'app.yaml'
        if config_path.exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
    except ImportError:
        pass

    # 回退到 INI 配置文件
    config_path = QUANTSTRAT_FACTORY_ROOT / 'config.ini'
    if config_path.exists():
        config = configparser.ConfigParser()
        config.read(config_path, encoding='utf-8')
        return config

    # 如果都没有，返回默认配置
    return {
        'data': {
            'cleaned': {
                'minute': 'D:/PY/Data/cleaned/minute'
            },
            'features': {
                'root': 'D:/PY/Data/features'
            }
        }
    }

DEFAULT_AGGREGATION_RULES: List[Dict[str, Any]] = [
    {'feature_name': 'vol_pct_morning', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'return_open_to_10am', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'vol_pct_open30m', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'vol_pct_mid', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'vol_pct_close30m', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'vol_center_of_gravity', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'vol_std_pct', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'return_last_hour', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'return_intraday_skew', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'volatility_am', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'volatility_pm', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'amplitude_total', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'upper_tail_ratio', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'lower_tail_ratio', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'price_volume_corr', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'open_to_vwap_pct_diff', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'volume_cluster_score', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'intraday_range_position', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'price_trend_consistency', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'volume_trend_consistency', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'close_in_upper_quantile', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'pullback_amplitude', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'high_time_density', 'category': 'alpha', 'methods': ['last']},
    {'feature_name': 'tail_15m_spike', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'afternoon_volume_jump', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'reverse_tail_tag', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'is_tail_spike_rise', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'is_midday_vshape', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'is_volatility_squeeze', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'is_bull_stretch', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'high_time_density_morning', 'category': 'event', 'methods': ['last']},
    {'feature_name': 'volatility_am_pm_ratio', 'category': 'risk', 'methods': ['last']},
    # 注意: risk_amplitude_total 和 risk_pullback_amplitude 如果与alpha版本同名且已在上面列出，
    # MinLevelProfiler的输出将只有一个同名列，这里的category将决定聚合后存储的位置。
    # 如果希望两者都作为独立特征聚合，MinLevelProfiler输出时需保证列名唯一。
    # 此处假设 'amplitude_total' 和 'pullback_amplitude' 已被alpha类别包含。
]

def load_symbols_from_file(file_path: Path) -> Optional[List[str]]:
    """从文件加载股票列表，每行一个股票代码。"""
    if not file_path.exists():
        print(f"警告: 股票列表文件未找到: {file_path}")
        return None
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            symbols = [line.strip() for line in f if line.strip()]
        if not symbols:
            print(f"警告: 股票列表文件 {file_path} 为空。")
            return None
        print(f"从 {file_path} 加载了 {len(symbols)} 个股票代码。")
        return symbols
    except Exception as e:
        print(f"读取股票列表文件 {file_path} 失败: {e}")
        return None


def process_symbol_features(symbol: str, config: configparser.ConfigParser, start_date: str, end_date: str, feature_sets: List[str], verbose: bool) -> str:
    """
    单个股票的特征计算工作函数，用于并行处理。
    """
    try:
        from profilers.min_level_profiler import MinLevelProfiler
        
        # 注意：为每个进程重新创建profiler实例
        profiler = MinLevelProfiler(
            config=config, 
            start_date=start_date, 
            end_date=end_date,
            symbol_to_process=symbol,  # 传递单个股票代码
            verbose=verbose  # 传递verbose标志
        )
        profiler.compute_and_save_features(feature_sets)
        return f"SUCCESS: {symbol}"
    except Exception as e:
        # 在子进程中打印错误，并返回失败信息
        print(f"处理股票 {symbol} 时发生错误: {e}")
        return f"FAILED: {symbol}"

def main():
    parser = argparse.ArgumentParser(description="量化策略工厂 - 特征画像器主入口")
    parser.add_argument('--level', type=str, required=True, choices=['min', 'day'], help="计算级别")
    parser.add_argument('--feature-sets', nargs='+', required=True, help="特征集列表")
    parser.add_argument('--start-date', type=str, help="开始日期 (YYYY-MM-DD)")
    parser.add_argument('--end-date', type=str, help="结束日期 (YYYY-MM-DD)")
    parser.add_argument('--symbols', nargs='+', help="可选的股票代码列表，用于特定任务")
    parser.add_argument('--symbol-file', type=str, help="从文件加载股票列表，替代命令行输入")
    parser.add_argument('--processes', type=int, default=multiprocessing.cpu_count(), help="使用的进程数")
    parser.add_argument('--verbose', action='store_true', help="启用详细日志输出。")

    args = parser.parse_args()
    config = get_config()

    print(f"--- 启动特征画像器 ---")
    print(f"级别: {args.level}, 使用 {args.processes} 个进程")
    print(f"特征集: {args.feature_sets}")
    if args.start_date: print(f"开始日期: {args.start_date}")
    if args.end_date: print(f"结束日期: {args.end_date}")

    symbols_to_process = args.symbols
    if args.symbol_file:
        print(f"从文件 {args.symbol_file} 加载股票列表...")
        symbols_to_process = load_symbols_from_file(Path(args.symbol_file))
    
    if not symbols_to_process and args.level == 'min':
        # 如果未提供股票列表，则从数据目录中自动发现
        cleaned_data_path = Path(config['Paths']['cleaned_data_path']) / 'min'
        if cleaned_data_path.exists():
            print("未提供股票列表，将从数据目录中自动扫描...")
            symbols_to_process = [p.name for p in cleaned_data_path.iterdir() if p.is_dir()]
    
    if not symbols_to_process:
        print("错误: 没有要处理的股票。请使用 --symbols 或 --symbol-file 提供股票列表。")
        return

    print(f"总共需要处理 {len(symbols_to_process)} 个股票。")
    if args.symbols: print(f"指定股票: {args.symbols}")


    if args.level == 'min':
        start_time = time.time()
        
        # 使用 functools.partial 包装工作函数，以固定其他参数
        worker_func = partial(process_symbol_features, 
                              config=config, 
                              start_date=args.start_date, 
                              end_date=args.end_date, 
                              feature_sets=args.feature_sets,
                              verbose=args.verbose)

        print("\n开始并行计算特征...")
        with multiprocessing.Pool(processes=args.processes) as pool:
            results = list(tqdm(pool.imap_unordered(worker_func, symbols_to_process), total=len(symbols_to_process), desc="处理进度"))
        
        end_time = time.time()
        
        success_count = sum(1 for r in results if r.startswith("SUCCESS"))
        failed_count = len(results) - success_count
        
        print("\n--- 分钟级特征计算完成 ---")
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        print(f"成功处理: {success_count} 个股票")
        print(f"处理失败: {failed_count} 个股票")

    elif args.level == 'day':
        try:
            from profilers.day_level_profiler import DayLevelProfiler
            
            # 创建一个可修改的 feature_sets 列表
            current_feature_sets = list(args.feature_sets)

            # 处理 'all' 参数，用于生成所有日级别特征
            if 'all' in current_feature_sets:
                print("检测到 'all' 参数，将计算所有可用的日级别特征集: 'daily_basics', 'technical', 'day_from_min_aggregation'")
                current_feature_sets = ['daily_basics', 'technical', 'day_from_min_aggregation']
            
            # 确保 'daily_basics' 总是被计算，因为它提供了基础数据
            if 'daily_basics' not in current_feature_sets:
                current_feature_sets.append('daily_basics')
                print("自动添加 'daily_basics' 到特征集，以确保基础数据可用。")

            # --- 增量计算日期确定逻辑 ---
            actual_start_date = args.start_date
            actual_end_date = args.end_date
            
            # 提前初始化 FeatureStoreClient，确保在任何情况下都可用
            # 从配置中获取特征存储路径
            if isinstance(config, dict):
                data_config = config.get('data', {})
                feature_store_path = data_config.get('features', {}).get('root', 'D:/PY/Data/features')
            else:
                feature_store_path = config.get('Paths', 'feature_store_path', fallback='D:/PY/Data/features')
            fs_client = FeatureStoreClient(base_path=feature_store_path)
            
            if not actual_start_date: # 如果没有指定开始日期，则尝试自动确定增量日期
                # 找到所有待计算特征的最后更新日期中的最早日期
                min_last_update_date = None
                
                # 遍历所有特征集，获取其最后更新日期
                # 这里需要一个映射来知道每个 feature_set 包含哪些具体的 feature_name 和 category
                # 简化处理：假设所有日级别特征都应该更新到最新
                # 我们可以从 DEFAULT_AGGREGATION_RULES 和 _calculate_ma/_calculate_rsi 中提取特征名和类别
                
                features_to_check = []
                if 'daily_basics' in current_feature_sets:
                    features_to_check.append({'name': 'daily_basics', 'category': 'base'})
                if 'technical' in current_feature_sets:
                    features_to_check.extend([
                        {'name': 'MA_5', 'category': 'technical'},
                        {'name': 'MA_10', 'category': 'technical'},
                        {'name': 'MA_20', 'category': 'technical'},
                        {'name': 'RSI_14', 'category': 'technical'},
                    ])
                if 'day_from_min_aggregation' in current_feature_sets and DEFAULT_AGGREGATION_RULES:
                    for rule in DEFAULT_AGGREGATION_RULES:
                        for method in rule['methods']:
                            features_to_check.append({
                                'name': f"{rule['feature_name']}_{method}_day",
                                'category': f"agg_{rule.get('category', 'unknown_min_cat')}"
                            })

                for feature_info in features_to_check:
                    last_update = fs_client.get_last_update_date(feature_info['name'], feature_info['category'])
                    if last_update:
                        if min_last_update_date is None or last_update < min_last_update_date:
                            min_last_update_date = last_update
                
                if min_last_update_date:
                    # 从最后更新日期的下一天开始计算
                    actual_start_date = (min_last_update_date + datetime.timedelta(days=1)).strftime('%Y-%m-%d')
                    print(f"自动确定增量计算开始日期为: {actual_start_date} (基于最旧的特征更新日期 {min_last_update_date})")
                else:
                    print("未找到任何特征的最后更新日期，将从头开始计算或使用命令行指定的日期。")
            
            if not actual_end_date:
                actual_end_date = datetime.date.today().strftime('%Y-%m-%d')
                print(f"自动确定增量计算结束日期为: {actual_end_date} (今天)")

            # 检查日期范围是否有效
            if actual_start_date and actual_end_date:
                start_dt_obj = datetime.datetime.strptime(actual_start_date, '%Y-%m-%d').date()
                end_dt_obj = datetime.datetime.strptime(actual_end_date, '%Y-%m-%d').date()
                if start_dt_obj > end_dt_obj:
                    print(f"所有特征已是最新，无需计算。开始日期 ({actual_start_date}) 晚于结束日期 ({actual_end_date})。")
                    return # 退出程序

            # 优先使用命令行传入的symbols，如果没有，则使用从文件加载的symbols_to_process
            # 如果是聚合任务，且 symbols_to_process 尚未被填充 (即没有通过 --symbols 或 --symbol-file 提供)，
            # 且不计算会加载all_day_data的任务，则尝试从 stock_list_processed.txt 加载。
            if 'day_from_min_aggregation' in current_feature_sets and \
               not symbols_to_process and \
                not any(fs in ['technical', 'daily_basics'] for fs in current_feature_sets):
                print("聚合任务需要股票列表。尝试从 stock_list_processed.txt 加载...")
                # 使用 MinLevelProfiler 生成的 stock_list_processed.txt
                stock_list_file = QUANTSTRAT_FACTORY_ROOT / '03_feature_store' / 'stock_list_processed.txt'
                symbols_from_file = load_symbols_from_file(stock_list_file)
                if symbols_from_file:
                    symbols_to_process = symbols_from_file # 更新 symbols_to_process
                else:
                    print("警告: 未能从 stock_list.txt 加载股票列表，聚合任务可能无法正确执行（除非日线数据已加载）。")

            profiler = DayLevelProfiler(
                config=config, 
                start_date=actual_start_date, # 使用实际确定的开始日期
                end_date=actual_end_date,     # 使用实际确定的结束日期
                symbols_to_process=symbols_to_process, # 传递正确的股票列表
                verbose=args.verbose, # 传递 verbose 参数
                feature_store_client=fs_client # 传递 FeatureStoreClient 实例
            )
            
            rules_for_aggregation = None
            if 'day_from_min_aggregation' in current_feature_sets:
                rules_for_aggregation = DEFAULT_AGGREGATION_RULES
                print(f"检测到 'day_from_min_aggregation'，将使用 {len(rules_for_aggregation)} 条默认聚合规则。")

            profiler.compute_and_save_features(
                feature_sets=current_feature_sets, # 使用修改后的 feature_sets
                aggregation_rules=rules_for_aggregation
            )
        except ImportError:
            print("错误: 无法导入 DayLevelProfiler。")
        except Exception as e:
            print(f"运行日级特征计算时发生错误: {e}")
            
    else:
        print(f"错误: 不支持的特征级别 '{args.level}'。")

    print("--- 特征画像器运行结束 ---")

if __name__ == "__main__":
    main()
