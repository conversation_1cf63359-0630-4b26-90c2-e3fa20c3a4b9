"""
数据源模块。

提供统一的数据源接口，支持多种数据提供商。
"""

from .base import DataSourceBase, DataSourceConfig
from .wind_source import WindDataSource
from .tonghuashun_source import TonghuashunDataSource
from .local_source import LocalDataSource
from .data_manager import DataSourceManager

__all__ = [
    "DataSourceBase",
    "DataSourceConfig", 
    "WindDataSource",
    "TonghuashunDataSource",
    "LocalDataSource",
    "DataSourceManager"
]
