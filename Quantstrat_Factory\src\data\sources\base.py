"""
数据源基类。

定义所有数据源的统一接口和配置。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
import pandas as pd


@dataclass
class DataSourceConfig:
    """数据源配置。"""
    
    name: str
    enabled: bool = True
    priority: int = 1  # 优先级，数字越小优先级越高
    timeout: int = 30  # 超时时间（秒）
    retry_count: int = 3  # 重试次数
    cache_enabled: bool = True  # 是否启用缓存
    rate_limit: Optional[int] = None  # 速率限制（请求/分钟）
    config: Dict[str, Any] = None  # 特定配置参数
    
    def __post_init__(self):
        if self.config is None:
            self.config = {}


class DataSourceBase(ABC):
    """数据源基类。"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化数据源。
        
        Args:
            config: 数据源配置
        """
        self.config = config
        self.name = config.name
        self.enabled = config.enabled
        self._connected = False
        
    @abstractmethod
    def connect(self) -> bool:
        """
        连接数据源。
        
        Returns:
            连接是否成功
        """
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开数据源连接。"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        检查连接状态。
        
        Returns:
            是否已连接
        """
        pass
    
    @abstractmethod
    def get_stock_list(self, market: str = "all") -> List[str]:
        """
        获取股票列表。
        
        Args:
            market: 市场代码（如 "sh", "sz", "all"）
            
        Returns:
            股票代码列表
        """
        pass
    
    @abstractmethod
    def get_daily_data(
        self, 
        symbols: Union[str, List[str]], 
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取日线数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表，默认为 ["open", "high", "low", "close", "volume"]
            
        Returns:
            日线数据DataFrame
        """
        pass
    
    @abstractmethod
    def get_minute_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1min",
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取分钟数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率（如 "1min", "5min", "15min"）
            fields: 字段列表
            
        Returns:
            分钟数据DataFrame
        """
        pass
    
    @abstractmethod
    def get_fundamental_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取基本面数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            
        Returns:
            基本面数据DataFrame
        """
        pass
    
    def get_realtime_data(self, symbols: Union[str, List[str]]) -> pd.DataFrame:
        """
        获取实时数据（可选实现）。
        
        Args:
            symbols: 股票代码或代码列表
            
        Returns:
            实时数据DataFrame
        """
        raise NotImplementedError(f"{self.name} 不支持实时数据获取")
    
    def validate_symbols(self, symbols: Union[str, List[str]]) -> List[str]:
        """
        验证股票代码格式。
        
        Args:
            symbols: 股票代码或代码列表
            
        Returns:
            标准化的股票代码列表
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        # 基本验证逻辑，子类可以重写
        validated = []
        for symbol in symbols:
            symbol = symbol.strip().upper()
            if len(symbol) >= 6:  # 基本长度检查
                validated.append(symbol)
        
        return validated
    
    def normalize_date(self, date_input: Union[str, date, datetime]) -> str:
        """
        标准化日期格式。
        
        Args:
            date_input: 日期输入
            
        Returns:
            标准化的日期字符串 (YYYY-MM-DD)
        """
        if isinstance(date_input, str):
            # 尝试解析字符串日期
            try:
                if len(date_input) == 8:  # YYYYMMDD
                    return f"{date_input[:4]}-{date_input[4:6]}-{date_input[6:8]}"
                elif len(date_input) == 10:  # YYYY-MM-DD
                    return date_input
                else:
                    raise ValueError(f"不支持的日期格式: {date_input}")
            except Exception as e:
                raise ValueError(f"日期格式错误: {date_input}, {e}")
        elif isinstance(date_input, datetime):
            return date_input.strftime("%Y-%m-%d")
        elif isinstance(date_input, date):
            return date_input.strftime("%Y-%m-%d")
        else:
            raise ValueError(f"不支持的日期类型: {type(date_input)}")
    
    def standardize_dataframe(self, df: pd.DataFrame, data_type: str = "daily") -> pd.DataFrame:
        """
        标准化DataFrame格式。
        
        Args:
            df: 原始数据DataFrame
            data_type: 数据类型 ("daily", "minute", "fundamental")
            
        Returns:
            标准化的DataFrame
        """
        if df.empty:
            return df
        
        # 标准化列名
        column_mapping = {
            # 价格数据
            "Open": "open", "OPEN": "open", "开盘价": "open",
            "High": "high", "HIGH": "high", "最高价": "high",
            "Low": "low", "LOW": "low", "最低价": "low", 
            "Close": "close", "CLOSE": "close", "收盘价": "close",
            "Volume": "volume", "VOLUME": "volume", "成交量": "volume",
            "Amount": "amount", "AMOUNT": "amount", "成交额": "amount",
            
            # 时间字段
            "Date": "date", "DATE": "date", "日期": "date",
            "Time": "time", "TIME": "time", "时间": "time",
            "DateTime": "datetime", "DATETIME": "datetime",
            
            # 股票代码
            "Symbol": "symbol", "SYMBOL": "symbol", "代码": "symbol",
            "Code": "symbol", "CODE": "symbol", "股票代码": "symbol",
        }
        
        # 重命名列
        df = df.rename(columns=column_mapping)
        
        # 确保必要的列存在
        if data_type in ["daily", "minute"]:
            required_cols = ["open", "high", "low", "close", "volume"]
            for col in required_cols:
                if col not in df.columns:
                    df[col] = 0.0
        
        # 数据类型转换
        numeric_cols = ["open", "high", "low", "close", "volume", "amount"]
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # 处理时间索引
        if "datetime" in df.columns:
            df["datetime"] = pd.to_datetime(df["datetime"])
        elif "date" in df.columns:
            df["date"] = pd.to_datetime(df["date"])
        
        return df
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name={self.name}, enabled={self.enabled})"
    
    def __repr__(self) -> str:
        return self.__str__()
