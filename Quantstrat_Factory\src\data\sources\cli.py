"""
数据源管理命令行工具。

提供数据源测试、数据获取等命令行功能。
"""

import argparse
import sys
import yaml
from pathlib import Path
import pandas as pd
import logging

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(project_root))

from src.data.sources import DataSourceManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_config():
    """加载配置文件。"""
    config_path = project_root / "config" / "app.yaml"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
            return config.get("data_sources", {})
    else:
        logger.warning(f"配置文件不存在: {config_path}")
        return {}


def test_connections(args):
    """测试数据源连接。"""
    print("🔗 测试数据源连接...")
    
    config = load_config()
    manager = DataSourceManager(config)
    
    # 连接所有数据源
    results = manager.connect_all()
    
    print("\n📊 连接结果:")
    for source, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {source}: {status}")
    
    # 显示可用数据源
    available = manager.get_available_sources()
    print(f"\n🎯 可用数据源: {available}")
    
    manager.disconnect_all()


def list_stocks(args):
    """列出股票代码。"""
    print(f"📋 获取股票列表 (市场: {args.market})...")
    
    config = load_config()
    
    with DataSourceManager(config) as manager:
        stocks = manager.get_stock_list(market=args.market, source=args.source)
        
        if stocks:
            print(f"\n📈 找到 {len(stocks)} 只股票:")
            
            # 分页显示
            page_size = args.limit or 20
            for i in range(0, min(len(stocks), page_size)):
                print(f"  {i+1:3d}. {stocks[i]}")
            
            if len(stocks) > page_size:
                print(f"  ... 还有 {len(stocks) - page_size} 只股票")
            
            # 保存到文件
            if args.output:
                output_path = Path(args.output)
                pd.DataFrame({"symbol": stocks}).to_csv(output_path, index=False)
                print(f"\n💾 股票列表已保存到: {output_path}")
        else:
            print("❌ 未找到股票数据")


def get_daily_data(args):
    """获取日线数据。"""
    print(f"📊 获取日线数据...")
    print(f"  股票: {args.symbols}")
    print(f"  日期: {args.start_date} 到 {args.end_date}")
    
    config = load_config()
    symbols = args.symbols.split(",")
    
    with DataSourceManager(config) as manager:
        df = manager.get_daily_data(
            symbols=symbols,
            start_date=args.start_date,
            end_date=args.end_date,
            source=args.source
        )
        
        if not df.empty:
            print(f"\n📈 获取到 {len(df)} 条数据:")
            print(df.head(10))
            
            if len(df) > 10:
                print(f"  ... 还有 {len(df) - 10} 条数据")
            
            # 保存到文件
            if args.output:
                output_path = Path(args.output)
                df.to_csv(output_path, index=False)
                print(f"\n💾 数据已保存到: {output_path}")
        else:
            print("❌ 未找到数据")


def get_minute_data(args):
    """获取分钟数据。"""
    print(f"📊 获取分钟数据...")
    print(f"  股票: {args.symbols}")
    print(f"  日期: {args.start_date} 到 {args.end_date}")
    print(f"  频率: {args.frequency}")
    
    config = load_config()
    symbols = args.symbols.split(",")
    
    with DataSourceManager(config) as manager:
        df = manager.get_minute_data(
            symbols=symbols,
            start_date=args.start_date,
            end_date=args.end_date,
            frequency=args.frequency,
            source=args.source
        )
        
        if not df.empty:
            print(f"\n📈 获取到 {len(df)} 条数据:")
            print(df.head(10))
            
            if len(df) > 10:
                print(f"  ... 还有 {len(df) - 10} 条数据")
            
            # 保存到文件
            if args.output:
                output_path = Path(args.output)
                df.to_csv(output_path, index=False)
                print(f"\n💾 数据已保存到: {output_path}")
        else:
            print("❌ 未找到数据")


def show_config(args):
    """显示配置信息。"""
    print("⚙️ 数据源配置:")
    
    config = load_config()
    
    for source_name, source_config in config.items():
        print(f"\n📡 {source_name}:")
        for key, value in source_config.items():
            print(f"  {key}: {value}")


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description="数据源管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 测试连接命令
    test_parser = subparsers.add_parser("test", help="测试数据源连接")
    
    # 列出股票命令
    list_parser = subparsers.add_parser("list", help="列出股票代码")
    list_parser.add_argument("--market", default="all", choices=["all", "sh", "sz"], help="市场")
    list_parser.add_argument("--source", help="指定数据源")
    list_parser.add_argument("--limit", type=int, help="显示数量限制")
    list_parser.add_argument("--output", help="输出文件路径")
    
    # 获取日线数据命令
    daily_parser = subparsers.add_parser("daily", help="获取日线数据")
    daily_parser.add_argument("symbols", help="股票代码，多个用逗号分隔")
    daily_parser.add_argument("start_date", help="开始日期 (YYYY-MM-DD)")
    daily_parser.add_argument("end_date", help="结束日期 (YYYY-MM-DD)")
    daily_parser.add_argument("--source", help="指定数据源")
    daily_parser.add_argument("--output", help="输出文件路径")
    
    # 获取分钟数据命令
    minute_parser = subparsers.add_parser("minute", help="获取分钟数据")
    minute_parser.add_argument("symbols", help="股票代码，多个用逗号分隔")
    minute_parser.add_argument("start_date", help="开始日期 (YYYY-MM-DD)")
    minute_parser.add_argument("end_date", help="结束日期 (YYYY-MM-DD)")
    minute_parser.add_argument("--frequency", default="1min", help="频率 (1min, 5min, 15min)")
    minute_parser.add_argument("--source", help="指定数据源")
    minute_parser.add_argument("--output", help="输出文件路径")
    
    # 显示配置命令
    config_parser = subparsers.add_parser("config", help="显示配置信息")
    
    args = parser.parse_args()
    
    if args.command == "test":
        test_connections(args)
    elif args.command == "list":
        list_stocks(args)
    elif args.command == "daily":
        get_daily_data(args)
    elif args.command == "minute":
        get_minute_data(args)
    elif args.command == "config":
        show_config(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
