"""
数据源管理器。

统一管理多个数据源，提供数据获取的统一接口。
"""

import pandas as pd
from typing import List, Optional, Union, Dict, Any
from datetime import datetime, date
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from .base import DataSourceBase, DataSourceConfig
from .wind_source import WindDataSource
from .tonghuashun_source import TonghuashunDataSource
from .local_source import LocalDataSource

logger = logging.getLogger(__name__)


class DataSourceManager:
    """数据源管理器。"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据源管理器。
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.data_sources: Dict[str, DataSourceBase] = {}
        self.connected_sources: List[str] = []
        self.cache_enabled = self.config.get("cache_enabled", True)
        self.cache: Dict[str, Any] = {}
        self.cache_ttl = self.config.get("cache_ttl", 300)  # 5分钟缓存
        
        # 初始化数据源
        self._initialize_data_sources()
    
    def _initialize_data_sources(self):
        """初始化所有配置的数据源。"""
        try:
            # 本地数据源（默认启用）
            local_config = DataSourceConfig(
                name="local",
                enabled=True,
                priority=1,
                config=self.config.get("local", {})
            )
            self.data_sources["local"] = LocalDataSource(local_config)
            
            # Wind数据源
            wind_config_data = self.config.get("wind", {})
            if wind_config_data.get("enabled", False):
                wind_config = DataSourceConfig(
                    name="wind",
                    enabled=True,
                    priority=2,
                    config=wind_config_data
                )
                self.data_sources["wind"] = WindDataSource(wind_config)
            
            # 同花顺数据源
            ths_config_data = self.config.get("tonghuashun", {})
            if ths_config_data.get("enabled", False):
                ths_config = DataSourceConfig(
                    name="tonghuashun",
                    enabled=True,
                    priority=3,
                    config=ths_config_data
                )
                self.data_sources["tonghuashun"] = TonghuashunDataSource(ths_config)
            
            logger.info(f"初始化了 {len(self.data_sources)} 个数据源: {list(self.data_sources.keys())}")
            
        except Exception as e:
            logger.error(f"初始化数据源失败: {e}")
    
    def connect_all(self) -> Dict[str, bool]:
        """
        连接所有数据源。
        
        Returns:
            各数据源的连接状态
        """
        connection_results = {}
        
        for name, source in self.data_sources.items():
            try:
                if source.enabled:
                    success = source.connect()
                    connection_results[name] = success
                    if success:
                        self.connected_sources.append(name)
                        logger.info(f"数据源 {name} 连接成功")
                    else:
                        logger.warning(f"数据源 {name} 连接失败")
                else:
                    connection_results[name] = False
                    logger.info(f"数据源 {name} 已禁用")
            except Exception as e:
                connection_results[name] = False
                logger.error(f"连接数据源 {name} 异常: {e}")
        
        logger.info(f"已连接的数据源: {self.connected_sources}")
        return connection_results
    
    def disconnect_all(self):
        """断开所有数据源连接。"""
        for name, source in self.data_sources.items():
            try:
                source.disconnect()
                logger.info(f"数据源 {name} 已断开")
            except Exception as e:
                logger.error(f"断开数据源 {name} 异常: {e}")
        
        self.connected_sources.clear()
    
    def get_available_sources(self) -> List[str]:
        """
        获取可用的数据源列表。
        
        Returns:
            可用数据源名称列表
        """
        return [name for name in self.connected_sources 
                if self.data_sources[name].is_connected()]
    
    def get_stock_list(self, market: str = "all", source: str = None) -> List[str]:
        """
        获取股票列表。
        
        Args:
            market: 市场代码
            source: 指定数据源，None表示使用优先级最高的可用数据源
            
        Returns:
            股票代码列表
        """
        try:
            # 生成缓存键
            cache_key = f"stock_list_{market}_{source}"
            
            # 检查缓存
            if self.cache_enabled and cache_key in self.cache:
                cache_data = self.cache[cache_key]
                if time.time() - cache_data["timestamp"] < self.cache_ttl:
                    logger.debug(f"从缓存获取股票列表: {cache_key}")
                    return cache_data["data"]
            
            # 选择数据源
            if source:
                if source not in self.connected_sources:
                    logger.error(f"指定的数据源 {source} 不可用")
                    return []
                data_source = self.data_sources[source]
            else:
                # 按优先级选择第一个可用数据源
                available_sources = self.get_available_sources()
                if not available_sources:
                    logger.error("没有可用的数据源")
                    return []
                
                # 按优先级排序
                sorted_sources = sorted(available_sources, 
                                      key=lambda x: self.data_sources[x].config.priority)
                data_source = self.data_sources[sorted_sources[0]]
            
            # 获取数据
            stock_list = data_source.get_stock_list(market)
            
            # 缓存结果
            if self.cache_enabled and stock_list:
                self.cache[cache_key] = {
                    "data": stock_list,
                    "timestamp": time.time()
                }
            
            logger.info(f"从数据源 {data_source.name} 获取到 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表异常: {e}")
            return []
    
    def get_daily_data(
        self, 
        symbols: Union[str, List[str]], 
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None,
        source: str = None,
        fallback: bool = True
    ) -> pd.DataFrame:
        """
        获取日线数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            source: 指定数据源
            fallback: 是否在主数据源失败时尝试其他数据源
            
        Returns:
            日线数据DataFrame
        """
        try:
            # 标准化输入
            if isinstance(symbols, str):
                symbols = [symbols]
            
            # 生成缓存键
            cache_key = f"daily_{'-'.join(symbols)}_{start_date}_{end_date}_{source}"
            
            # 检查缓存
            if self.cache_enabled and cache_key in self.cache:
                cache_data = self.cache[cache_key]
                if time.time() - cache_data["timestamp"] < self.cache_ttl:
                    logger.debug(f"从缓存获取日线数据: {cache_key}")
                    return cache_data["data"]
            
            # 选择数据源
            sources_to_try = []
            if source:
                if source in self.connected_sources:
                    sources_to_try.append(source)
                else:
                    logger.warning(f"指定的数据源 {source} 不可用")
            
            if fallback or not sources_to_try:
                # 按优先级添加所有可用数据源
                available_sources = self.get_available_sources()
                sorted_sources = sorted(available_sources, 
                                      key=lambda x: self.data_sources[x].config.priority)
                for src in sorted_sources:
                    if src not in sources_to_try:
                        sources_to_try.append(src)
            
            if not sources_to_try:
                logger.error("没有可用的数据源")
                return pd.DataFrame()
            
            # 尝试从数据源获取数据
            for src_name in sources_to_try:
                try:
                    data_source = self.data_sources[src_name]
                    logger.info(f"尝试从数据源 {src_name} 获取日线数据")
                    
                    df = data_source.get_daily_data(symbols, start_date, end_date, fields)
                    
                    if not df.empty:
                        # 缓存结果
                        if self.cache_enabled:
                            self.cache[cache_key] = {
                                "data": df,
                                "timestamp": time.time()
                            }
                        
                        logger.info(f"从数据源 {src_name} 成功获取 {len(df)} 条日线数据")
                        return df
                    else:
                        logger.warning(f"数据源 {src_name} 返回空数据")
                        
                except Exception as e:
                    logger.error(f"从数据源 {src_name} 获取日线数据失败: {e}")
                    continue
            
            logger.error("所有数据源都无法获取日线数据")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取日线数据异常: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1min",
        fields: Optional[List[str]] = None,
        source: str = None,
        fallback: bool = True
    ) -> pd.DataFrame:
        """
        获取分钟数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率
            fields: 字段列表
            source: 指定数据源
            fallback: 是否在主数据源失败时尝试其他数据源
            
        Returns:
            分钟数据DataFrame
        """
        try:
            # 标准化输入
            if isinstance(symbols, str):
                symbols = [symbols]
            
            # 生成缓存键
            cache_key = f"minute_{'-'.join(symbols)}_{start_date}_{end_date}_{frequency}_{source}"
            
            # 检查缓存
            if self.cache_enabled and cache_key in self.cache:
                cache_data = self.cache[cache_key]
                if time.time() - cache_data["timestamp"] < self.cache_ttl:
                    logger.debug(f"从缓存获取分钟数据: {cache_key}")
                    return cache_data["data"]
            
            # 选择数据源（逻辑与日线数据类似）
            sources_to_try = []
            if source:
                if source in self.connected_sources:
                    sources_to_try.append(source)
                else:
                    logger.warning(f"指定的数据源 {source} 不可用")
            
            if fallback or not sources_to_try:
                available_sources = self.get_available_sources()
                sorted_sources = sorted(available_sources, 
                                      key=lambda x: self.data_sources[x].config.priority)
                for src in sorted_sources:
                    if src not in sources_to_try:
                        sources_to_try.append(src)
            
            if not sources_to_try:
                logger.error("没有可用的数据源")
                return pd.DataFrame()
            
            # 尝试从数据源获取数据
            for src_name in sources_to_try:
                try:
                    data_source = self.data_sources[src_name]
                    logger.info(f"尝试从数据源 {src_name} 获取分钟数据")
                    
                    df = data_source.get_minute_data(symbols, start_date, end_date, frequency, fields)
                    
                    if not df.empty:
                        # 缓存结果
                        if self.cache_enabled:
                            self.cache[cache_key] = {
                                "data": df,
                                "timestamp": time.time()
                            }
                        
                        logger.info(f"从数据源 {src_name} 成功获取 {len(df)} 条分钟数据")
                        return df
                    else:
                        logger.warning(f"数据源 {src_name} 返回空数据")
                        
                except Exception as e:
                    logger.error(f"从数据源 {src_name} 获取分钟数据失败: {e}")
                    continue
            
            logger.error("所有数据源都无法获取分钟数据")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取分钟数据异常: {e}")
            return pd.DataFrame()
    
    def get_fundamental_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None,
        source: str = None,
        fallback: bool = True
    ) -> pd.DataFrame:
        """
        获取基本面数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            source: 指定数据源
            fallback: 是否在主数据源失败时尝试其他数据源
            
        Returns:
            基本面数据DataFrame
        """
        try:
            # 基本面数据主要来自Wind等专业数据源
            sources_to_try = []
            
            if source:
                if source in self.connected_sources:
                    sources_to_try.append(source)
            else:
                # 优先使用Wind等专业数据源
                preferred_sources = ["wind", "tonghuashun"]
                for src in preferred_sources:
                    if src in self.connected_sources:
                        sources_to_try.append(src)
            
            if not sources_to_try:
                logger.error("没有支持基本面数据的数据源")
                return pd.DataFrame()
            
            # 尝试获取数据
            for src_name in sources_to_try:
                try:
                    data_source = self.data_sources[src_name]
                    logger.info(f"尝试从数据源 {src_name} 获取基本面数据")
                    
                    df = data_source.get_fundamental_data(symbols, start_date, end_date, fields)
                    
                    if not df.empty:
                        logger.info(f"从数据源 {src_name} 成功获取 {len(df)} 条基本面数据")
                        return df
                    else:
                        logger.warning(f"数据源 {src_name} 返回空数据")
                        
                except Exception as e:
                    logger.error(f"从数据源 {src_name} 获取基本面数据失败: {e}")
                    continue
            
            logger.error("所有数据源都无法获取基本面数据")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取基本面数据异常: {e}")
            return pd.DataFrame()
    
    def clear_cache(self):
        """清空缓存。"""
        self.cache.clear()
        logger.info("数据缓存已清空")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        获取缓存信息。
        
        Returns:
            缓存信息字典
        """
        return {
            "cache_enabled": self.cache_enabled,
            "cache_size": len(self.cache),
            "cache_keys": list(self.cache.keys())
        }
    
    def __enter__(self):
        """上下文管理器入口。"""
        self.connect_all()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口。"""
        self.disconnect_all()
