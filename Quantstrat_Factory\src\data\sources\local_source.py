"""
本地数据源接口。

提供本地文件数据的读取功能。
"""

import pandas as pd
import zipfile
from pathlib import Path
from typing import List, Optional, Union
from datetime import datetime, date
import logging

from .base import DataSourceBase, DataSourceConfig

logger = logging.getLogger(__name__)


class LocalDataSource(DataSourceBase):
    """本地数据源。"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化本地数据源。
        
        Args:
            config: 数据源配置
        """
        super().__init__(config)
        self.data_paths = config.config.get("data_paths", {})
        self.raw_data_path = Path(self.data_paths.get("raw_minute", "d:/py/data/1min"))
        self.cleaned_data_path = Path(self.data_paths.get("cleaned_minute", "d:/py/data/min"))
        self.daily_data_path = Path(self.data_paths.get("daily", "d:/py/data/day"))
        
    def connect(self) -> bool:
        """
        连接本地数据源（检查路径是否存在）。
        
        Returns:
            连接是否成功
        """
        try:
            # 检查数据路径是否存在
            paths_exist = []
            
            if self.raw_data_path.exists():
                paths_exist.append("raw_minute")
                logger.info(f"原始分钟数据路径存在: {self.raw_data_path}")
            
            if self.cleaned_data_path.exists():
                paths_exist.append("cleaned_minute")
                logger.info(f"清洗分钟数据路径存在: {self.cleaned_data_path}")
            
            if self.daily_data_path.exists():
                paths_exist.append("daily")
                logger.info(f"日线数据路径存在: {self.daily_data_path}")
            
            if paths_exist:
                self._connected = True
                logger.info(f"本地数据源连接成功，可用路径: {paths_exist}")
                return True
            else:
                logger.error("本地数据源连接失败：没有找到有效的数据路径")
                return False
                
        except Exception as e:
            logger.error(f"本地数据源连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开本地数据源连接。"""
        self._connected = False
        logger.info("本地数据源已断开")
    
    def is_connected(self) -> bool:
        """
        检查连接状态。
        
        Returns:
            是否已连接
        """
        return self._connected
    
    def get_stock_list(self, market: str = "all") -> List[str]:
        """
        获取股票列表（从本地数据文件中提取）。
        
        Args:
            market: 市场代码（"sh", "sz", "all"）
            
        Returns:
            股票代码列表
        """
        if not self.is_connected():
            logger.error("本地数据源未连接")
            return []
        
        try:
            stock_codes = set()
            
            # 从清洗后的分钟数据目录获取股票列表
            if self.cleaned_data_path.exists():
                for stock_dir in self.cleaned_data_path.iterdir():
                    if stock_dir.is_dir():
                        stock_code = stock_dir.name
                        
                        # 根据市场过滤
                        if market.lower() == "sh" and not stock_code.startswith(("sh", "600", "601", "603", "605", "688")):
                            continue
                        elif market.lower() == "sz" and not stock_code.startswith(("sz", "000", "001", "002", "003", "300")):
                            continue
                        
                        stock_codes.add(stock_code)
            
            # 从原始数据中获取股票列表（如果清洗数据不存在）
            if not stock_codes and self.raw_data_path.exists():
                # 随机选择一个zip文件来获取股票列表
                zip_files = list(self.raw_data_path.glob("*.zip"))
                if zip_files:
                    with zipfile.ZipFile(zip_files[0], 'r') as zip_ref:
                        for file_name in zip_ref.namelist():
                            if file_name.endswith('.csv'):
                                stock_code = file_name.replace('.csv', '')
                                
                                # 根据市场过滤
                                if market.lower() == "sh" and not stock_code.startswith(("sh", "600", "601", "603", "605", "688")):
                                    continue
                                elif market.lower() == "sz" and not stock_code.startswith(("sz", "000", "001", "002", "003", "300")):
                                    continue
                                
                                stock_codes.add(stock_code)
            
            return sorted(list(stock_codes))
            
        except Exception as e:
            logger.error(f"获取股票列表异常: {e}")
            return []
    
    def get_daily_data(
        self, 
        symbols: Union[str, List[str]], 
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取日线数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            
        Returns:
            日线数据DataFrame
        """
        if not self.is_connected():
            logger.error("本地数据源未连接")
            return pd.DataFrame()
        
        try:
            # 标准化输入
            symbols = self.validate_symbols(symbols)
            start_date = self.normalize_date(start_date)
            end_date = self.normalize_date(end_date)
            
            if not symbols:
                return pd.DataFrame()
            
            # 默认字段
            if fields is None:
                fields = ["open", "high", "low", "close", "volume"]
            
            all_data = []
            
            # 从日线数据目录读取
            if self.daily_data_path.exists():
                for symbol in symbols:
                    symbol_file = self.daily_data_path / f"{symbol}.parquet"
                    if symbol_file.exists():
                        try:
                            df = pd.read_parquet(symbol_file)
                            
                            # 过滤日期范围
                            if "date" in df.columns:
                                df["date"] = pd.to_datetime(df["date"])
                                mask = (df["date"] >= start_date) & (df["date"] <= end_date)
                                df = df[mask]
                            
                            # 添加股票代码列
                            if "symbol" not in df.columns:
                                df["symbol"] = symbol
                            
                            all_data.append(df)
                            
                        except Exception as e:
                            logger.error(f"读取股票 {symbol} 日线数据失败: {e}")
                            continue
            
            if not all_data:
                logger.warning(f"未找到股票 {symbols} 的日线数据")
                return pd.DataFrame()
            
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 标准化DataFrame
            combined_df = self.standardize_dataframe(combined_df, "daily")
            
            return combined_df
            
        except Exception as e:
            logger.error(f"获取日线数据异常: {e}")
            return pd.DataFrame()
    
    def get_minute_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        frequency: str = "1min",
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取分钟数据。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 频率（目前只支持1min）
            fields: 字段列表
            
        Returns:
            分钟数据DataFrame
        """
        if not self.is_connected():
            logger.error("本地数据源未连接")
            return pd.DataFrame()
        
        try:
            # 标准化输入
            symbols = self.validate_symbols(symbols)
            start_date = self.normalize_date(start_date)
            end_date = self.normalize_date(end_date)
            
            if not symbols:
                return pd.DataFrame()
            
            # 默认字段
            if fields is None:
                fields = ["open", "high", "low", "close", "volume"]
            
            all_data = []
            
            # 从清洗后的分钟数据读取
            if self.cleaned_data_path.exists():
                for symbol in symbols:
                    symbol_dir = self.cleaned_data_path / symbol
                    if symbol_dir.exists():
                        # 遍历年份目录
                        for year_dir in symbol_dir.iterdir():
                            if year_dir.is_dir() and year_dir.name.startswith("year="):
                                year = year_dir.name.split("=")[1]
                                
                                # 检查年份是否在日期范围内
                                if start_date[:4] <= year <= end_date[:4]:
                                    parquet_file = year_dir / "cleaned_data.parquet"
                                    if parquet_file.exists():
                                        try:
                                            df = pd.read_parquet(parquet_file)
                                            
                                            # 过滤日期范围
                                            if "datetime" in df.columns:
                                                df["datetime"] = pd.to_datetime(df["datetime"])
                                                mask = (df["datetime"].dt.date >= pd.to_datetime(start_date).date()) & \
                                                       (df["datetime"].dt.date <= pd.to_datetime(end_date).date())
                                                df = df[mask]
                                            
                                            # 添加股票代码列
                                            if "symbol" not in df.columns:
                                                df["symbol"] = symbol
                                            
                                            all_data.append(df)
                                            
                                        except Exception as e:
                                            logger.error(f"读取股票 {symbol} 年份 {year} 分钟数据失败: {e}")
                                            continue
            
            if not all_data:
                logger.warning(f"未找到股票 {symbols} 的分钟数据")
                return pd.DataFrame()
            
            # 合并所有数据
            combined_df = pd.concat(all_data, ignore_index=True)
            
            # 标准化DataFrame
            combined_df = self.standardize_dataframe(combined_df, "minute")
            
            return combined_df
            
        except Exception as e:
            logger.error(f"获取分钟数据异常: {e}")
            return pd.DataFrame()
    
    def get_fundamental_data(
        self,
        symbols: Union[str, List[str]],
        start_date: Union[str, date, datetime],
        end_date: Union[str, date, datetime],
        fields: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """
        获取基本面数据（本地数据源暂不支持）。
        
        Args:
            symbols: 股票代码或代码列表
            start_date: 开始日期
            end_date: 结束日期
            fields: 字段列表
            
        Returns:
            基本面数据DataFrame
        """
        logger.warning("本地数据源暂不支持基本面数据获取")
        return pd.DataFrame()
    
    def validate_symbols(self, symbols: Union[str, List[str]]) -> List[str]:
        """
        验证股票代码格式。
        
        Args:
            symbols: 股票代码或代码列表
            
        Returns:
            标准化的股票代码列表
        """
        if isinstance(symbols, str):
            symbols = [symbols]
        
        validated = []
        for symbol in symbols:
            symbol = symbol.strip().lower()
            
            # 支持多种格式：sh600000, sz000001, 600000, 000001
            if len(symbol) >= 6:
                validated.append(symbol)
        
        return validated
