"""
高级因子库

提供丰富的技术、基本面和情绪因子计算功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable
import logging
from scipy import stats
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
import talib
import warnings

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class AdvancedFactorLibrary:
    """高级因子库"""
    
    def __init__(self):
        """初始化高级因子库"""
        self.factor_registry = {}
        self.factor_metadata = {}
        self._register_all_factors()
    
    def _register_all_factors(self):
        """注册所有因子"""
        # 技术因子
        self._register_technical_factors()
        # 基本面因子
        self._register_fundamental_factors()
        # 情绪因子
        self._register_sentiment_factors()
        # 复合因子
        self._register_composite_factors()
    
    def _register_technical_factors(self):
        """注册技术因子"""
        technical_factors = {
            # 动量因子
            'momentum_1d': {
                'func': self.momentum_1d,
                'description': '1日动量',
                'category': 'momentum',
                'params': {}
            },
            'momentum_5d': {
                'func': self.momentum_5d,
                'description': '5日动量',
                'category': 'momentum',
                'params': {}
            },
            'momentum_20d': {
                'func': self.momentum_20d,
                'description': '20日动量',
                'category': 'momentum',
                'params': {}
            },
            'rsi': {
                'func': self.rsi,
                'description': 'RSI相对强弱指标',
                'category': 'momentum',
                'params': {'period': 14}
            },
            'macd': {
                'func': self.macd,
                'description': 'MACD指标',
                'category': 'momentum',
                'params': {'fast': 12, 'slow': 26, 'signal': 9}
            },
            
            # 波动率因子
            'volatility_5d': {
                'func': self.volatility_5d,
                'description': '5日波动率',
                'category': 'volatility',
                'params': {}
            },
            'volatility_20d': {
                'func': self.volatility_20d,
                'description': '20日波动率',
                'category': 'volatility',
                'params': {}
            },
            'atr': {
                'func': self.atr,
                'description': '平均真实波幅',
                'category': 'volatility',
                'params': {'period': 14}
            },
            
            # 趋势因子
            'ma_ratio_5_20': {
                'func': self.ma_ratio_5_20,
                'description': '5日/20日均线比率',
                'category': 'trend',
                'params': {}
            },
            'ma_ratio_10_50': {
                'func': self.ma_ratio_10_50,
                'description': '10日/50日均线比率',
                'category': 'trend',
                'params': {}
            },
            'bollinger_position': {
                'func': self.bollinger_position,
                'description': '布林带位置',
                'category': 'trend',
                'params': {'period': 20, 'std': 2}
            },
            
            # 成交量因子
            'volume_ratio_5d': {
                'func': self.volume_ratio_5d,
                'description': '5日成交量比率',
                'category': 'volume',
                'params': {}
            },
            'volume_price_trend': {
                'func': self.volume_price_trend,
                'description': '量价趋势',
                'category': 'volume',
                'params': {}
            },
            'money_flow_index': {
                'func': self.money_flow_index,
                'description': '资金流量指标',
                'category': 'volume',
                'params': {'period': 14}
            }
        }
        
        self.factor_registry.update(technical_factors)
    
    def _register_fundamental_factors(self):
        """注册基本面因子"""
        fundamental_factors = {
            'pe_ratio': {
                'func': self.pe_ratio,
                'description': '市盈率',
                'category': 'valuation',
                'params': {}
            },
            'pb_ratio': {
                'func': self.pb_ratio,
                'description': '市净率',
                'category': 'valuation',
                'params': {}
            },
            'ps_ratio': {
                'func': self.ps_ratio,
                'description': '市销率',
                'category': 'valuation',
                'params': {}
            },
            'roe': {
                'func': self.roe,
                'description': '净资产收益率',
                'category': 'profitability',
                'params': {}
            },
            'roa': {
                'func': self.roa,
                'description': '总资产收益率',
                'category': 'profitability',
                'params': {}
            },
            'debt_to_equity': {
                'func': self.debt_to_equity,
                'description': '负债权益比',
                'category': 'leverage',
                'params': {}
            }
        }
        
        self.factor_registry.update(fundamental_factors)
    
    def _register_sentiment_factors(self):
        """注册情绪因子"""
        sentiment_factors = {
            'price_momentum_divergence': {
                'func': self.price_momentum_divergence,
                'description': '价格动量背离',
                'category': 'sentiment',
                'params': {}
            },
            'volume_momentum_divergence': {
                'func': self.volume_momentum_divergence,
                'description': '成交量动量背离',
                'category': 'sentiment',
                'params': {}
            },
            'market_beta': {
                'func': self.market_beta,
                'description': '市场贝塔',
                'category': 'sentiment',
                'params': {'period': 60}
            }
        }
        
        self.factor_registry.update(sentiment_factors)
    
    def _register_composite_factors(self):
        """注册复合因子"""
        composite_factors = {
            'momentum_quality': {
                'func': self.momentum_quality,
                'description': '动量质量因子',
                'category': 'composite',
                'params': {}
            },
            'value_momentum': {
                'func': self.value_momentum,
                'description': '价值动量复合因子',
                'category': 'composite',
                'params': {}
            },
            'technical_strength': {
                'func': self.technical_strength,
                'description': '技术强度因子',
                'category': 'composite',
                'params': {}
            }
        }
        
        self.factor_registry.update(composite_factors)
    
    # 技术因子实现
    def momentum_1d(self, data: pd.DataFrame) -> pd.Series:
        """1日动量"""
        return data['close'].pct_change()
    
    def momentum_5d(self, data: pd.DataFrame) -> pd.Series:
        """5日动量"""
        return data['close'].pct_change(5)
    
    def momentum_20d(self, data: pd.DataFrame) -> pd.Series:
        """20日动量"""
        return data['close'].pct_change(20)
    
    def rsi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """RSI相对强弱指标"""
        try:
            return talib.RSI(data['close'].values, timeperiod=period)
        except:
            # 手动计算RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))
    
    def macd(self, data: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.Series:
        """MACD指标"""
        try:
            macd_line, macd_signal, macd_hist = talib.MACD(
                data['close'].values, 
                fastperiod=fast, 
                slowperiod=slow, 
                signalperiod=signal
            )
            return pd.Series(macd_line, index=data.index)
        except:
            # 手动计算MACD
            ema_fast = data['close'].ewm(span=fast).mean()
            ema_slow = data['close'].ewm(span=slow).mean()
            return ema_fast - ema_slow
    
    def volatility_5d(self, data: pd.DataFrame) -> pd.Series:
        """5日波动率"""
        returns = data['close'].pct_change()
        return returns.rolling(window=5).std() * np.sqrt(252)
    
    def volatility_20d(self, data: pd.DataFrame) -> pd.Series:
        """20日波动率"""
        returns = data['close'].pct_change()
        return returns.rolling(window=20).std() * np.sqrt(252)
    
    def atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """平均真实波幅"""
        try:
            return talib.ATR(data['high'].values, data['low'].values, data['close'].values, timeperiod=period)
        except:
            # 手动计算ATR
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            return true_range.rolling(window=period).mean()
    
    def ma_ratio_5_20(self, data: pd.DataFrame) -> pd.Series:
        """5日/20日均线比率"""
        ma5 = data['close'].rolling(window=5).mean()
        ma20 = data['close'].rolling(window=20).mean()
        return ma5 / ma20 - 1
    
    def ma_ratio_10_50(self, data: pd.DataFrame) -> pd.Series:
        """10日/50日均线比率"""
        ma10 = data['close'].rolling(window=10).mean()
        ma50 = data['close'].rolling(window=50).mean()
        return ma10 / ma50 - 1
    
    def bollinger_position(self, data: pd.DataFrame, period: int = 20, std: float = 2) -> pd.Series:
        """布林带位置"""
        ma = data['close'].rolling(window=period).mean()
        std_dev = data['close'].rolling(window=period).std()
        upper_band = ma + (std_dev * std)
        lower_band = ma - (std_dev * std)
        return (data['close'] - lower_band) / (upper_band - lower_band)
    
    def volume_ratio_5d(self, data: pd.DataFrame) -> pd.Series:
        """5日成交量比率"""
        volume_ma5 = data['volume'].rolling(window=5).mean()
        volume_ma20 = data['volume'].rolling(window=20).mean()
        return volume_ma5 / volume_ma20 - 1
    
    def volume_price_trend(self, data: pd.DataFrame) -> pd.Series:
        """量价趋势"""
        price_change = data['close'].pct_change()
        volume_change = data['volume'].pct_change()
        return price_change * volume_change
    
    def money_flow_index(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """资金流量指标"""
        try:
            return talib.MFI(
                data['high'].values, 
                data['low'].values, 
                data['close'].values, 
                data['volume'].values, 
                timeperiod=period
            )
        except:
            # 手动计算MFI
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']
            
            positive_flow = money_flow.where(typical_price > typical_price.shift(), 0)
            negative_flow = money_flow.where(typical_price < typical_price.shift(), 0)
            
            positive_mf = positive_flow.rolling(window=period).sum()
            negative_mf = negative_flow.rolling(window=period).sum()
            
            mfi = 100 - (100 / (1 + positive_mf / negative_mf))
            return mfi
    
    # 基本面因子实现
    def pe_ratio(self, data: pd.DataFrame) -> pd.Series:
        """市盈率"""
        if 'pe' in data.columns:
            return data['pe']
        elif 'market_cap' in data.columns and 'net_income' in data.columns:
            return data['market_cap'] / data['net_income']
        else:
            return pd.Series(np.nan, index=data.index)
    
    def pb_ratio(self, data: pd.DataFrame) -> pd.Series:
        """市净率"""
        if 'pb' in data.columns:
            return data['pb']
        elif 'market_cap' in data.columns and 'book_value' in data.columns:
            return data['market_cap'] / data['book_value']
        else:
            return pd.Series(np.nan, index=data.index)
    
    def ps_ratio(self, data: pd.DataFrame) -> pd.Series:
        """市销率"""
        if 'ps' in data.columns:
            return data['ps']
        elif 'market_cap' in data.columns and 'revenue' in data.columns:
            return data['market_cap'] / data['revenue']
        else:
            return pd.Series(np.nan, index=data.index)
    
    def roe(self, data: pd.DataFrame) -> pd.Series:
        """净资产收益率"""
        if 'roe' in data.columns:
            return data['roe']
        elif 'net_income' in data.columns and 'equity' in data.columns:
            return data['net_income'] / data['equity']
        else:
            return pd.Series(np.nan, index=data.index)
    
    def roa(self, data: pd.DataFrame) -> pd.Series:
        """总资产收益率"""
        if 'roa' in data.columns:
            return data['roa']
        elif 'net_income' in data.columns and 'total_assets' in data.columns:
            return data['net_income'] / data['total_assets']
        else:
            return pd.Series(np.nan, index=data.index)
    
    def debt_to_equity(self, data: pd.DataFrame) -> pd.Series:
        """负债权益比"""
        if 'debt_to_equity' in data.columns:
            return data['debt_to_equity']
        elif 'total_debt' in data.columns and 'equity' in data.columns:
            return data['total_debt'] / data['equity']
        else:
            return pd.Series(np.nan, index=data.index)
    
    # 情绪因子实现
    def price_momentum_divergence(self, data: pd.DataFrame) -> pd.Series:
        """价格动量背离"""
        price_momentum = self.momentum_20d(data)
        volume_momentum = data['volume'].pct_change(20)
        
        # 计算相关性
        correlation = price_momentum.rolling(window=20).corr(volume_momentum)
        return -correlation  # 负相关表示背离
    
    def volume_momentum_divergence(self, data: pd.DataFrame) -> pd.Series:
        """成交量动量背离"""
        price_change = data['close'].pct_change()
        volume_change = data['volume'].pct_change()
        
        # 计算20日滚动相关性
        correlation = price_change.rolling(window=20).corr(volume_change)
        return -correlation
    
    def market_beta(self, data: pd.DataFrame, period: int = 60) -> pd.Series:
        """市场贝塔"""
        if 'market_return' in data.columns:
            stock_returns = data['close'].pct_change()
            market_returns = data['market_return']
            
            # 计算滚动贝塔
            def rolling_beta(x, y):
                if len(x) < 10:  # 需要足够的数据点
                    return np.nan
                covariance = np.cov(x, y)[0, 1]
                variance = np.var(y)
                return covariance / variance if variance != 0 else np.nan
            
            beta = stock_returns.rolling(window=period).apply(
                lambda x: rolling_beta(x, market_returns.loc[x.index]), 
                raw=False
            )
            return beta
        else:
            return pd.Series(np.nan, index=data.index)
    
    # 复合因子实现
    def momentum_quality(self, data: pd.DataFrame) -> pd.Series:
        """动量质量因子"""
        momentum = self.momentum_20d(data)
        volatility = self.volatility_20d(data)
        
        # 动量质量 = 动量 / 波动率
        return momentum / volatility
    
    def value_momentum(self, data: pd.DataFrame) -> pd.Series:
        """价值动量复合因子"""
        momentum = self.momentum_20d(data)
        pe = self.pe_ratio(data)
        
        # 简单的价值动量组合
        value_score = -1 / pe  # PE越低，价值分数越高
        momentum_score = momentum
        
        # 标准化后组合
        value_norm = (value_score - value_score.rolling(window=252).mean()) / value_score.rolling(window=252).std()
        momentum_norm = (momentum_score - momentum_score.rolling(window=252).mean()) / momentum_score.rolling(window=252).std()
        
        return 0.5 * value_norm + 0.5 * momentum_norm
    
    def technical_strength(self, data: pd.DataFrame) -> pd.Series:
        """技术强度因子"""
        # 组合多个技术指标
        rsi_score = (self.rsi(data) - 50) / 50  # 标准化RSI
        ma_ratio = self.ma_ratio_5_20(data)
        bollinger_pos = self.bollinger_position(data)
        
        # 等权重组合
        return (rsi_score + ma_ratio + bollinger_pos) / 3
    
    def calculate_factor(self, factor_name: str, data: pd.DataFrame, **kwargs) -> pd.Series:
        """
        计算指定因子
        
        Args:
            factor_name: 因子名称
            data: 输入数据
            **kwargs: 因子参数
            
        Returns:
            因子值序列
        """
        if factor_name not in self.factor_registry:
            raise ValueError(f"未知因子: {factor_name}")
        
        factor_info = self.factor_registry[factor_name]
        factor_func = factor_info['func']
        
        # 合并默认参数和用户参数
        params = factor_info['params'].copy()
        params.update(kwargs)
        
        try:
            if params:
                result = factor_func(data, **params)
            else:
                result = factor_func(data)
            
            # 确保返回Series
            if isinstance(result, np.ndarray):
                result = pd.Series(result, index=data.index)
            
            return result
            
        except Exception as e:
            logger.error(f"计算因子 {factor_name} 失败: {e}")
            return pd.Series(np.nan, index=data.index)
    
    def calculate_multiple_factors(
        self, 
        factor_names: List[str], 
        data: pd.DataFrame,
        **kwargs
    ) -> pd.DataFrame:
        """
        批量计算多个因子
        
        Args:
            factor_names: 因子名称列表
            data: 输入数据
            **kwargs: 因子参数
            
        Returns:
            包含所有因子的DataFrame
        """
        results = {}
        
        for factor_name in factor_names:
            try:
                factor_values = self.calculate_factor(factor_name, data, **kwargs)
                results[factor_name] = factor_values
            except Exception as e:
                logger.error(f"计算因子 {factor_name} 失败: {e}")
                results[factor_name] = pd.Series(np.nan, index=data.index)
        
        return pd.DataFrame(results)
    
    def get_factor_list(self, category: Optional[str] = None) -> List[str]:
        """
        获取因子列表
        
        Args:
            category: 因子类别筛选
            
        Returns:
            因子名称列表
        """
        if category is None:
            return list(self.factor_registry.keys())
        else:
            return [
                name for name, info in self.factor_registry.items()
                if info.get('category') == category
            ]
    
    def get_factor_info(self, factor_name: str) -> Dict[str, Any]:
        """
        获取因子信息
        
        Args:
            factor_name: 因子名称
            
        Returns:
            因子信息字典
        """
        if factor_name not in self.factor_registry:
            raise ValueError(f"未知因子: {factor_name}")
        
        return self.factor_registry[factor_name].copy()
    
    def get_categories(self) -> List[str]:
        """获取所有因子类别"""
        categories = set()
        for info in self.factor_registry.values():
            categories.add(info.get('category', 'unknown'))
        return sorted(list(categories))


class FactorEvaluator:
    """因子评估器"""

    def __init__(self):
        """初始化因子评估器"""
        self.evaluation_results = {}

    def evaluate_factor_performance(
        self,
        factor_values: pd.Series,
        returns: pd.Series,
        periods: List[int] = [1, 5, 10, 20]
    ) -> Dict[str, Any]:
        """
        评估因子表现

        Args:
            factor_values: 因子值
            returns: 收益率
            periods: 评估周期

        Returns:
            评估结果
        """
        results = {}

        # 对齐数据
        aligned_data = pd.DataFrame({
            'factor': factor_values,
            'returns': returns
        }).dropna()

        if len(aligned_data) < 50:
            logger.warning("数据量不足，无法进行有效评估")
            return {}

        # IC分析
        results['ic_analysis'] = self._calculate_ic_analysis(aligned_data, periods)

        # 分层回测
        results['quantile_analysis'] = self._calculate_quantile_analysis(aligned_data, periods)

        # 稳定性分析
        results['stability_analysis'] = self._calculate_stability_analysis(aligned_data)

        # 风险调整收益
        results['risk_adjusted_returns'] = self._calculate_risk_adjusted_returns(aligned_data, periods)

        return results

    def _calculate_ic_analysis(self, data: pd.DataFrame, periods: List[int]) -> Dict[str, Any]:
        """计算IC分析"""
        ic_results = {}

        for period in periods:
            # 计算前瞻收益
            forward_returns = data['returns'].shift(-period).rolling(window=period).sum()

            # 计算IC
            ic = data['factor'].corr(forward_returns)

            # 计算滚动IC
            rolling_ic = data['factor'].rolling(window=60).corr(forward_returns.rolling(window=60))

            ic_results[f'period_{period}'] = {
                'ic': ic,
                'ic_mean': rolling_ic.mean(),
                'ic_std': rolling_ic.std(),
                'ic_ir': rolling_ic.mean() / rolling_ic.std() if rolling_ic.std() != 0 else 0,
                'ic_positive_rate': (rolling_ic > 0).mean()
            }

        return ic_results

    def _calculate_quantile_analysis(self, data: pd.DataFrame, periods: List[int]) -> Dict[str, Any]:
        """计算分位数分析"""
        quantile_results = {}

        for period in periods:
            # 计算前瞻收益
            forward_returns = data['returns'].shift(-period).rolling(window=period).sum()

            # 按因子值分组
            data_with_forward = pd.DataFrame({
                'factor': data['factor'],
                'forward_returns': forward_returns
            }).dropna()

            # 分为5个分位数组
            data_with_forward['quantile'] = pd.qcut(
                data_with_forward['factor'],
                q=5,
                labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5']
            )

            # 计算各分位数组的收益
            quantile_returns = data_with_forward.groupby('quantile')['forward_returns'].agg([
                'mean', 'std', 'count'
            ])

            # 计算多空收益
            if 'Q5' in quantile_returns.index and 'Q1' in quantile_returns.index:
                long_short_return = quantile_returns.loc['Q5', 'mean'] - quantile_returns.loc['Q1', 'mean']
            else:
                long_short_return = np.nan

            quantile_results[f'period_{period}'] = {
                'quantile_returns': quantile_returns.to_dict(),
                'long_short_return': long_short_return
            }

        return quantile_results

    def _calculate_stability_analysis(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算稳定性分析"""
        # 时间序列稳定性
        factor_values = data['factor']

        # 计算自相关性
        autocorr_1 = factor_values.autocorr(lag=1)
        autocorr_5 = factor_values.autocorr(lag=5)
        autocorr_20 = factor_values.autocorr(lag=20)

        # 计算因子衰减
        factor_decay = []
        for lag in range(1, 21):
            decay = factor_values.autocorr(lag=lag)
            factor_decay.append(decay)

        # 计算因子分布稳定性
        rolling_mean = factor_values.rolling(window=60).mean()
        rolling_std = factor_values.rolling(window=60).std()

        mean_stability = rolling_mean.std() / rolling_mean.mean() if rolling_mean.mean() != 0 else np.inf
        std_stability = rolling_std.std() / rolling_std.mean() if rolling_std.mean() != 0 else np.inf

        return {
            'autocorr_1': autocorr_1,
            'autocorr_5': autocorr_5,
            'autocorr_20': autocorr_20,
            'factor_decay': factor_decay,
            'mean_stability': mean_stability,
            'std_stability': std_stability
        }

    def _calculate_risk_adjusted_returns(self, data: pd.DataFrame, periods: List[int]) -> Dict[str, Any]:
        """计算风险调整收益"""
        risk_adjusted_results = {}

        for period in periods:
            # 计算前瞻收益
            forward_returns = data['returns'].shift(-period).rolling(window=period).sum()

            # 按因子值排序，构建投资组合
            portfolio_data = pd.DataFrame({
                'factor': data['factor'],
                'returns': forward_returns
            }).dropna()

            # 计算因子加权收益
            portfolio_data['weights'] = portfolio_data['factor'] / portfolio_data['factor'].abs().sum()
            portfolio_returns = (portfolio_data['weights'] * portfolio_data['returns']).sum()

            # 计算夏普比率
            portfolio_return_series = portfolio_data['weights'] * portfolio_data['returns']
            sharpe_ratio = portfolio_return_series.mean() / portfolio_return_series.std() if portfolio_return_series.std() != 0 else 0

            # 计算最大回撤
            cumulative_returns = (1 + portfolio_return_series).cumprod()
            rolling_max = cumulative_returns.expanding().max()
            drawdown = (cumulative_returns - rolling_max) / rolling_max
            max_drawdown = drawdown.min()

            risk_adjusted_results[f'period_{period}'] = {
                'portfolio_return': portfolio_returns,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown
            }

        return risk_adjusted_results
