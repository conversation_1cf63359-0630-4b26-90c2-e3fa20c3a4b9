"""
因子分析工具模块。

提供因子有效性分析、IC分析、因子衰减等功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from scipy import stats
import warnings

logger = logging.getLogger(__name__)


class FactorAnalyzer:
    """因子分析器。"""
    
    def __init__(self):
        """初始化因子分析器。"""
        pass
    
    def calculate_ic(
        self, 
        factor_data: pd.DataFrame, 
        return_data: pd.DataFrame,
        method: str = 'spearman'
    ) -> pd.DataFrame:
        """
        计算因子IC（信息系数）。
        
        Args:
            factor_data: 因子数据，包含date, symbol, factor_value列
            return_data: 收益率数据，包含date, symbol, return列
            method: 相关性计算方法 ('spearman', 'pearson')
            
        Returns:
            IC分析结果
        """
        # 合并因子数据和收益率数据
        merged_data = pd.merge(
            factor_data, 
            return_data, 
            on=['date', 'symbol'], 
            how='inner'
        )
        
        if merged_data.empty:
            logger.warning("合并后的数据为空，无法计算IC")
            return pd.DataFrame()
        
        # 按日期分组计算IC
        ic_results = []
        
        for date in merged_data['date'].unique():
            date_data = merged_data[merged_data['date'] == date]
            
            if len(date_data) < 2:
                continue
            
            # 获取因子列（除了date, symbol之外的数值列）
            factor_cols = [col for col in date_data.columns 
                          if col not in ['date', 'symbol'] and 
                          date_data[col].dtype in ['float64', 'int64'] and
                          'return' not in col.lower()]
            
            return_cols = [col for col in date_data.columns 
                          if 'return' in col.lower() and 
                          date_data[col].dtype in ['float64', 'int64']]
            
            for factor_col in factor_cols:
                for return_col in return_cols:
                    factor_values = date_data[factor_col].dropna()
                    return_values = date_data[return_col].dropna()
                    
                    # 确保有足够的数据点
                    common_idx = factor_values.index.intersection(return_values.index)
                    if len(common_idx) < 2:
                        continue
                    
                    factor_vals = factor_values.loc[common_idx]
                    return_vals = return_values.loc[common_idx]
                    
                    # 计算相关系数
                    if method == 'spearman':
                        ic, p_value = stats.spearmanr(factor_vals, return_vals)
                    else:
                        ic, p_value = stats.pearsonr(factor_vals, return_vals)
                    
                    if not np.isnan(ic):
                        ic_results.append({
                            'date': date,
                            'factor': factor_col,
                            'return_period': return_col,
                            'ic': ic,
                            'p_value': p_value,
                            'abs_ic': abs(ic)
                        })
        
        if not ic_results:
            logger.warning("没有计算出有效的IC值")
            return pd.DataFrame()
        
        ic_df = pd.DataFrame(ic_results)
        
        return ic_df
    
    def analyze_ic_statistics(self, ic_data: pd.DataFrame) -> pd.DataFrame:
        """
        分析IC统计特征。
        
        Args:
            ic_data: IC数据
            
        Returns:
            IC统计分析结果
        """
        if ic_data.empty:
            return pd.DataFrame()
        
        # 按因子分组计算统计量
        stats_results = []
        
        for factor in ic_data['factor'].unique():
            factor_ic = ic_data[ic_data['factor'] == factor]['ic']
            
            if len(factor_ic) == 0:
                continue
            
            stats_result = {
                'factor': factor,
                'ic_mean': factor_ic.mean(),
                'ic_std': factor_ic.std(),
                'ic_ir': factor_ic.mean() / (factor_ic.std() + 1e-8),  # IC信息比率
                'ic_skew': factor_ic.skew(),
                'ic_kurt': factor_ic.kurtosis(),
                'ic_positive_ratio': (factor_ic > 0).mean(),
                'ic_abs_mean': factor_ic.abs().mean(),
                'ic_t_stat': factor_ic.mean() / (factor_ic.std() / np.sqrt(len(factor_ic)) + 1e-8),
                'ic_count': len(factor_ic)
            }
            
            stats_results.append(stats_result)
        
        return pd.DataFrame(stats_results)
    
    def calculate_factor_decay(
        self, 
        factor_data: pd.DataFrame, 
        periods: List[int] = [1, 5, 10, 20]
    ) -> pd.DataFrame:
        """
        计算因子衰减。
        
        Args:
            factor_data: 因子数据
            periods: 衰减周期列表
            
        Returns:
            因子衰减分析结果
        """
        decay_results = []
        
        # 获取因子列
        factor_cols = [col for col in factor_data.columns 
                      if col not in ['date', 'symbol'] and 
                      factor_data[col].dtype in ['float64', 'int64']]
        
        for factor_col in factor_cols:
            factor_decay_row = {'factor': factor_col}
            
            for period in periods:
                # 计算因子自相关性
                correlations = []
                
                for symbol in factor_data['symbol'].unique():
                    symbol_data = factor_data[factor_data['symbol'] == symbol].sort_values('date')
                    factor_series = symbol_data[factor_col].dropna()
                    
                    if len(factor_series) > period:
                        # 计算滞后相关性
                        current_values = factor_series.iloc[period:]
                        lagged_values = factor_series.iloc[:-period]
                        
                        if len(current_values) > 1 and len(lagged_values) > 1:
                            corr = current_values.corr(lagged_values)
                            if not np.isnan(corr):
                                correlations.append(corr)
                
                if correlations:
                    factor_decay_row[f'decay_{period}'] = np.mean(correlations)
                else:
                    factor_decay_row[f'decay_{period}'] = np.nan
            
            decay_results.append(factor_decay_row)
        
        return pd.DataFrame(decay_results)
    
    def analyze_factor_turnover(
        self, 
        factor_data: pd.DataFrame, 
        quantiles: int = 5
    ) -> pd.DataFrame:
        """
        分析因子换手率。
        
        Args:
            factor_data: 因子数据
            quantiles: 分位数数量
            
        Returns:
            因子换手率分析结果
        """
        turnover_results = []
        
        # 获取因子列
        factor_cols = [col for col in factor_data.columns 
                      if col not in ['date', 'symbol'] and 
                      factor_data[col].dtype in ['float64', 'int64']]
        
        for factor_col in factor_cols:
            # 按日期分组，计算分位数
            factor_quantiles = {}
            dates = sorted(factor_data['date'].unique())
            
            for date in dates:
                date_data = factor_data[factor_data['date'] == date]
                factor_values = date_data[factor_col].dropna()
                
                if len(factor_values) > 0:
                    # 计算分位数
                    quantile_labels = pd.qcut(
                        factor_values, 
                        q=quantiles, 
                        labels=False, 
                        duplicates='drop'
                    )
                    
                    # 存储每个股票的分位数
                    date_quantiles = {}
                    for idx, symbol in enumerate(date_data['symbol']):
                        if not pd.isna(factor_values.iloc[idx]):
                            date_quantiles[symbol] = quantile_labels.iloc[idx]
                    
                    factor_quantiles[date] = date_quantiles
            
            # 计算换手率
            turnovers = []
            for i in range(1, len(dates)):
                prev_date = dates[i-1]
                curr_date = dates[i]
                
                if prev_date in factor_quantiles and curr_date in factor_quantiles:
                    prev_quantiles = factor_quantiles[prev_date]
                    curr_quantiles = factor_quantiles[curr_date]
                    
                    # 找到共同的股票
                    common_symbols = set(prev_quantiles.keys()) & set(curr_quantiles.keys())
                    
                    if len(common_symbols) > 0:
                        # 计算换手率（分位数变化的比例）
                        changes = sum(1 for symbol in common_symbols 
                                    if prev_quantiles[symbol] != curr_quantiles[symbol])
                        turnover = changes / len(common_symbols)
                        turnovers.append(turnover)
            
            if turnovers:
                turnover_results.append({
                    'factor': factor_col,
                    'avg_turnover': np.mean(turnovers),
                    'std_turnover': np.std(turnovers),
                    'min_turnover': np.min(turnovers),
                    'max_turnover': np.max(turnovers)
                })
        
        return pd.DataFrame(turnover_results)
    
    def calculate_factor_exposure(
        self, 
        factor_data: pd.DataFrame, 
        market_cap_data: Optional[pd.DataFrame] = None
    ) -> pd.DataFrame:
        """
        计算因子暴露度。
        
        Args:
            factor_data: 因子数据
            market_cap_data: 市值数据（可选）
            
        Returns:
            因子暴露度分析结果
        """
        exposure_results = []
        
        # 获取因子列
        factor_cols = [col for col in factor_data.columns 
                      if col not in ['date', 'symbol'] and 
                      factor_data[col].dtype in ['float64', 'int64']]
        
        for factor_col in factor_cols:
            # 按日期计算暴露度
            exposures = []
            
            for date in factor_data['date'].unique():
                date_data = factor_data[factor_data['date'] == date]
                factor_values = date_data[factor_col].dropna()
                
                if len(factor_values) > 0:
                    # 计算暴露度统计量
                    exposure_stats = {
                        'date': date,
                        'factor': factor_col,
                        'mean': factor_values.mean(),
                        'std': factor_values.std(),
                        'skew': factor_values.skew(),
                        'kurt': factor_values.kurtosis(),
                        'min': factor_values.min(),
                        'max': factor_values.max(),
                        'count': len(factor_values)
                    }
                    
                    # 如果有市值数据，计算市值加权统计量
                    if market_cap_data is not None:
                        date_market_cap = market_cap_data[market_cap_data['date'] == date]
                        merged = pd.merge(
                            date_data[['symbol', factor_col]], 
                            date_market_cap[['symbol', 'market_cap']], 
                            on='symbol', 
                            how='inner'
                        )
                        
                        if not merged.empty and merged['market_cap'].sum() > 0:
                            weights = merged['market_cap'] / merged['market_cap'].sum()
                            weighted_mean = (merged[factor_col] * weights).sum()
                            exposure_stats['weighted_mean'] = weighted_mean
                    
                    exposures.append(exposure_stats)
            
            exposure_results.extend(exposures)
        
        return pd.DataFrame(exposure_results)
    
    def generate_factor_report(
        self, 
        factor_data: pd.DataFrame, 
        return_data: pd.DataFrame,
        output_path: Optional[str] = None
    ) -> Dict:
        """
        生成因子分析报告。
        
        Args:
            factor_data: 因子数据
            return_data: 收益率数据
            output_path: 输出路径（可选）
            
        Returns:
            因子分析报告字典
        """
        report = {}
        
        try:
            # 1. IC分析
            ic_data = self.calculate_ic(factor_data, return_data)
            ic_stats = self.analyze_ic_statistics(ic_data)
            report['ic_analysis'] = {
                'ic_data': ic_data,
                'ic_statistics': ic_stats
            }
            
            # 2. 因子衰减分析
            decay_analysis = self.calculate_factor_decay(factor_data)
            report['decay_analysis'] = decay_analysis
            
            # 3. 因子换手率分析
            turnover_analysis = self.analyze_factor_turnover(factor_data)
            report['turnover_analysis'] = turnover_analysis
            
            # 4. 因子暴露度分析
            exposure_analysis = self.calculate_factor_exposure(factor_data)
            report['exposure_analysis'] = exposure_analysis
            
            # 5. 生成总结
            summary = self._generate_summary(report)
            report['summary'] = summary
            
            # 6. 保存报告（如果指定了路径）
            if output_path:
                self._save_report(report, output_path)
            
            logger.info("因子分析报告生成完成")
            
        except Exception as e:
            logger.error(f"生成因子分析报告失败: {e}")
            report['error'] = str(e)
        
        return report
    
    def _generate_summary(self, report: Dict) -> Dict:
        """生成分析总结。"""
        summary = {}
        
        try:
            # IC分析总结
            if 'ic_analysis' in report and 'ic_statistics' in report['ic_analysis']:
                ic_stats = report['ic_analysis']['ic_statistics']
                if not ic_stats.empty:
                    summary['best_ic_factor'] = ic_stats.loc[ic_stats['ic_abs_mean'].idxmax(), 'factor']
                    summary['avg_ic_ir'] = ic_stats['ic_ir'].mean()
                    summary['factors_with_positive_ic'] = (ic_stats['ic_mean'] > 0).sum()
            
            # 衰减分析总结
            if 'decay_analysis' in report:
                decay_data = report['decay_analysis']
                if not decay_data.empty:
                    summary['avg_decay_1d'] = decay_data['decay_1'].mean()
                    summary['avg_decay_20d'] = decay_data['decay_20'].mean()
            
            # 换手率分析总结
            if 'turnover_analysis' in report:
                turnover_data = report['turnover_analysis']
                if not turnover_data.empty:
                    summary['avg_turnover'] = turnover_data['avg_turnover'].mean()
                    summary['most_stable_factor'] = turnover_data.loc[
                        turnover_data['avg_turnover'].idxmin(), 'factor'
                    ]
            
        except Exception as e:
            logger.error(f"生成总结失败: {e}")
            summary['error'] = str(e)
        
        return summary
    
    def _save_report(self, report: Dict, output_path: str):
        """保存报告到文件。"""
        try:
            # 这里可以实现保存为Excel、JSON等格式
            # 简单起见，先保存为pickle格式
            import pickle
            with open(output_path, 'wb') as f:
                pickle.dump(report, f)
            logger.info(f"报告已保存到: {output_path}")
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
