"""
因子库主类。

提供统一的因子计算和分析接口。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Any
import logging

logger = logging.getLogger(__name__)

# 延迟导入以避免循环依赖
def _import_sklearn():
    try:
        from sklearn.decomposition import PCA
        from sklearn.preprocessing import StandardScaler
        return PCA, StandardScaler
    except ImportError:
        logger.warning("scikit-learn未安装，某些功能将不可用")
        return None, None


class FactorLibrary:
    """因子库主类。"""

    def __init__(self):
        """初始化因子库。"""
        from .fundamental_factors import FundamentalFactors
        from .technical_factors import TechnicalFactors
        from .sentiment_factors import SentimentFactors

        self.fundamental = FundamentalFactors()
        self.technical = TechnicalFactors()
        self.sentiment = SentimentFactors()

        # 因子类别映射
        self.factor_categories = {
            'fundamental': self.fundamental,
            'technical': self.technical,
            'sentiment': self.sentiment
        }
    
    def get_available_factors(self) -> Dict[str, List[str]]:
        """
        获取可用因子列表。
        
        Returns:
            按类别分组的因子列表
        """
        factors = {}
        
        for category, factor_class in self.factor_categories.items():
            if hasattr(factor_class, 'get_available_factors'):
                factors[category] = factor_class.get_available_factors()
            else:
                # 如果没有get_available_factors方法，通过反射获取
                methods = [method for method in dir(factor_class) 
                          if method.startswith('calculate_') and callable(getattr(factor_class, method))]
                factors[category] = [method.replace('calculate_', '') for method in methods]
        
        return factors
    
    def calculate_factor(
        self, 
        data: pd.DataFrame, 
        factor_name: str, 
        category: str,
        **kwargs
    ) -> pd.Series:
        """
        计算单个因子。
        
        Args:
            data: 输入数据
            factor_name: 因子名称
            category: 因子类别
            **kwargs: 其他参数
            
        Returns:
            因子值序列
        """
        if category not in self.factor_categories:
            raise ValueError(f"未知的因子类别: {category}")
        
        factor_class = self.factor_categories[category]
        method_name = f"calculate_{factor_name}"
        
        if not hasattr(factor_class, method_name):
            raise ValueError(f"因子 {factor_name} 在类别 {category} 中不存在")
        
        method = getattr(factor_class, method_name)
        
        try:
            result = method(data, **kwargs)
            if isinstance(result, pd.Series):
                result.name = factor_name
            return result
        except Exception as e:
            logger.error(f"计算因子 {factor_name} 失败: {e}")
            # 返回空序列
            empty_series = pd.Series(index=data.index, dtype=float, name=factor_name)
            return empty_series
    
    def calculate_multiple_factors(
        self, 
        data: pd.DataFrame, 
        factor_names: List[str], 
        category: str,
        **kwargs
    ) -> pd.DataFrame:
        """
        批量计算多个因子。
        
        Args:
            data: 输入数据
            factor_names: 因子名称列表
            category: 因子类别
            **kwargs: 其他参数
            
        Returns:
            包含所有因子的DataFrame
        """
        results = {}
        
        for factor_name in factor_names:
            try:
                factor_result = self.calculate_factor(data, factor_name, category, **kwargs)
                results[factor_name] = factor_result
            except Exception as e:
                logger.error(f"计算因子 {factor_name} 失败: {e}")
                # 添加空列
                results[factor_name] = pd.Series(index=data.index, dtype=float, name=factor_name)
        
        return pd.DataFrame(results)
    
    def analyze_factor_correlation(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        分析因子相关性。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            相关性矩阵
        """
        # 选择数值列
        numeric_cols = factor_data.select_dtypes(include=[np.number]).columns
        factor_subset = factor_data[numeric_cols]
        
        # 计算相关性矩阵
        correlation_matrix = factor_subset.corr()
        
        return correlation_matrix
    
    def analyze_factor_ic(
        self, 
        factor_data: pd.DataFrame, 
        return_data: pd.DataFrame
    ) -> pd.DataFrame:
        """
        分析因子IC（信息系数）。
        
        Args:
            factor_data: 因子数据
            return_data: 收益率数据
            
        Returns:
            IC分析结果
        """
        ic_results = []
        
        # 选择数值因子列
        factor_cols = factor_data.select_dtypes(include=[np.number]).columns
        return_cols = return_data.select_dtypes(include=[np.number]).columns
        
        for factor_col in factor_cols:
            factor_ic_row = {'factor': factor_col}
            
            for return_col in return_cols:
                # 计算IC（Spearman相关系数）
                ic_series = []
                
                # 按时间分组计算IC
                if 'date' in factor_data.columns:
                    dates = factor_data['date'].unique()
                    for date in dates:
                        date_mask = factor_data['date'] == date
                        if date_mask.sum() > 1:  # 至少需要2个观测值
                            factor_values = factor_data.loc[date_mask, factor_col]
                            return_values = return_data.loc[date_mask, return_col]
                            
                            # 计算Spearman相关系数
                            ic = factor_values.corr(return_values, method='spearman')
                            if not pd.isna(ic):
                                ic_series.append(ic)
                
                if ic_series:
                    ic_array = np.array(ic_series)
                    factor_ic_row[f'{return_col}_ic_mean'] = np.mean(ic_array)
                    factor_ic_row[f'{return_col}_ic_std'] = np.std(ic_array)
                    factor_ic_row[f'{return_col}_ic_ir'] = np.mean(ic_array) / (np.std(ic_array) + 1e-8)
                else:
                    factor_ic_row[f'{return_col}_ic_mean'] = np.nan
                    factor_ic_row[f'{return_col}_ic_std'] = np.nan
                    factor_ic_row[f'{return_col}_ic_ir'] = np.nan
            
            ic_results.append(factor_ic_row)
        
        ic_df = pd.DataFrame(ic_results)
        
        # 简化列名
        if len(return_cols) == 1:
            return_col = return_cols[0]
            ic_df = ic_df.rename(columns={
                f'{return_col}_ic_mean': 'ic_mean',
                f'{return_col}_ic_std': 'ic_std',
                f'{return_col}_ic_ir': 'ic_ir'
            })
        
        return ic_df.set_index('factor') if 'factor' in ic_df.columns else ic_df
    
    def analyze_factor_decay(
        self, 
        factor_data: pd.DataFrame, 
        periods: List[int] = [1, 5, 10, 20]
    ) -> pd.DataFrame:
        """
        分析因子衰减。
        
        Args:
            factor_data: 因子数据
            periods: 衰减周期列表
            
        Returns:
            因子衰减分析结果
        """
        decay_results = {}
        
        # 选择数值因子列
        factor_cols = factor_data.select_dtypes(include=[np.number]).columns
        
        for factor_col in factor_cols:
            factor_decay_row = {}
            
            for period in periods:
                # 计算因子自相关性作为衰减指标
                factor_series = factor_data[factor_col].dropna()
                
                if len(factor_series) > period:
                    # 计算滞后相关性
                    lagged_correlation = factor_series.corr(factor_series.shift(period))
                    factor_decay_row[f'decay_{period}'] = lagged_correlation
                else:
                    factor_decay_row[f'decay_{period}'] = np.nan
            
            decay_results[factor_col] = factor_decay_row
        
        decay_df = pd.DataFrame(decay_results).T
        
        return decay_df
    
    def orthogonalize_factors(
        self, 
        factor_data: pd.DataFrame, 
        method: str = 'gram_schmidt',
        **kwargs
    ) -> pd.DataFrame:
        """
        因子正交化。
        
        Args:
            factor_data: 因子数据
            method: 正交化方法 ('gram_schmidt', 'pca')
            **kwargs: 其他参数
            
        Returns:
            正交化后的因子数据
        """
        # 移除非数值列
        numeric_data = factor_data.select_dtypes(include=[np.number])
        
        # 移除包含NaN的行
        clean_data = numeric_data.dropna()
        
        if clean_data.empty:
            logger.warning("没有有效的数值数据进行正交化")
            return factor_data
        
        if method == 'gram_schmidt':
            return self._gram_schmidt_orthogonalization(clean_data)
        elif method == 'pca':
            n_components = kwargs.get('n_components', clean_data.shape[1])
            return self._pca_orthogonalization(clean_data, n_components)
        else:
            raise ValueError(f"未知的正交化方法: {method}")
    
    def _gram_schmidt_orthogonalization(self, data: pd.DataFrame) -> pd.DataFrame:
        """Gram-Schmidt正交化。"""
        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        
        # Gram-Schmidt过程
        orthogonal_vectors = []
        
        for i in range(scaled_data.shape[1]):
            vector = scaled_data[:, i].copy()
            
            # 减去在之前向量上的投影
            for j in range(len(orthogonal_vectors)):
                projection = np.dot(vector, orthogonal_vectors[j]) * orthogonal_vectors[j]
                vector = vector - projection
            
            # 归一化
            norm = np.linalg.norm(vector)
            if norm > 1e-10:  # 避免除零
                vector = vector / norm
                orthogonal_vectors.append(vector)
        
        # 转换回DataFrame
        orthogonal_data = np.column_stack(orthogonal_vectors)
        result_df = pd.DataFrame(
            orthogonal_data, 
            index=data.index, 
            columns=[f'orth_factor_{i+1}' for i in range(len(orthogonal_vectors))]
        )
        
        return result_df
    
    def _pca_orthogonalization(self, data: pd.DataFrame, n_components: int) -> pd.DataFrame:
        """PCA正交化。"""
        # 标准化数据
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(data)
        
        # PCA变换
        pca = PCA(n_components=n_components)
        pca_data = pca.fit_transform(scaled_data)
        
        # 转换回DataFrame
        result_df = pd.DataFrame(
            pca_data,
            index=data.index,
            columns=[f'pc_{i+1}' for i in range(n_components)]
        )
        
        return result_df
