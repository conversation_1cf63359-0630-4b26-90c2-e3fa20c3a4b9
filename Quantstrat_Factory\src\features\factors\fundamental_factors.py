"""
基本面因子模块。

提供基于财务数据的因子计算功能。
"""

import pandas as pd
import numpy as np
from typing import List
import logging

logger = logging.getLogger(__name__)


class FundamentalFactors:
    """基本面因子计算器。"""
    
    def __init__(self):
        """初始化基本面因子计算器。"""
        pass
    
    def get_available_factors(self) -> List[str]:
        """
        获取可用的基本面因子列表。
        
        Returns:
            因子名称列表
        """
        return [
            'pe_ratio',
            'pb_ratio', 
            'roe',
            'debt_to_equity',
            'current_ratio',
            'quick_ratio',
            'gross_margin',
            'operating_margin',
            'net_margin',
            'asset_turnover',
            'inventory_turnover',
            'receivables_turnover'
        ]
    
    def calculate_pe_ratio(self, data: pd.DataFrame) -> pd.Series:
        """
        计算市盈率 (PE Ratio)。
        
        Args:
            data: 包含market_cap和net_income的数据
            
        Returns:
            PE比率序列
        """
        if 'market_cap' not in data.columns or 'net_income' not in data.columns:
            logger.warning("缺少计算PE比率所需的列: market_cap, net_income")
            return pd.Series(index=data.index, dtype=float, name='pe_ratio')
        
        # PE = 市值 / 净利润
        pe_ratio = data['market_cap'] / data['net_income']
        
        # 过滤异常值（负PE或过大PE）
        pe_ratio = pe_ratio.where((pe_ratio > 0) & (pe_ratio < 1000))
        
        return pe_ratio.rename('pe_ratio')
    
    def calculate_pb_ratio(self, data: pd.DataFrame) -> pd.Series:
        """
        计算市净率 (PB Ratio)。
        
        Args:
            data: 包含market_cap和book_value的数据
            
        Returns:
            PB比率序列
        """
        if 'market_cap' not in data.columns or 'book_value' not in data.columns:
            logger.warning("缺少计算PB比率所需的列: market_cap, book_value")
            return pd.Series(index=data.index, dtype=float, name='pb_ratio')
        
        # PB = 市值 / 账面价值
        pb_ratio = data['market_cap'] / data['book_value']
        
        # 过滤异常值
        pb_ratio = pb_ratio.where((pb_ratio > 0) & (pb_ratio < 100))
        
        return pb_ratio.rename('pb_ratio')
    
    def calculate_roe(self, data: pd.DataFrame) -> pd.Series:
        """
        计算净资产收益率 (ROE)。
        
        Args:
            data: 包含net_income和total_equity的数据
            
        Returns:
            ROE序列
        """
        if 'net_income' not in data.columns or 'total_equity' not in data.columns:
            logger.warning("缺少计算ROE所需的列: net_income, total_equity")
            return pd.Series(index=data.index, dtype=float, name='roe')
        
        # ROE = 净利润 / 股东权益
        roe = data['net_income'] / data['total_equity']
        
        # 过滤异常值
        roe = roe.where((roe >= -1) & (roe <= 5))
        
        return roe.rename('roe')
    
    def calculate_debt_to_equity(self, data: pd.DataFrame) -> pd.Series:
        """
        计算资产负债率 (Debt-to-Equity Ratio)。
        
        Args:
            data: 包含total_debt和total_equity的数据
            
        Returns:
            资产负债率序列
        """
        if 'total_debt' not in data.columns or 'total_equity' not in data.columns:
            logger.warning("缺少计算资产负债率所需的列: total_debt, total_equity")
            return pd.Series(index=data.index, dtype=float, name='debt_to_equity')
        
        # D/E = 总负债 / 股东权益
        debt_to_equity = data['total_debt'] / data['total_equity']
        
        # 过滤异常值
        debt_to_equity = debt_to_equity.where((debt_to_equity >= 0) & (debt_to_equity <= 10))
        
        return debt_to_equity.rename('debt_to_equity')
    
    def calculate_current_ratio(self, data: pd.DataFrame) -> pd.Series:
        """
        计算流动比率 (Current Ratio)。
        
        Args:
            data: 包含current_assets和current_liabilities的数据
            
        Returns:
            流动比率序列
        """
        if 'current_assets' not in data.columns or 'current_liabilities' not in data.columns:
            logger.warning("缺少计算流动比率所需的列: current_assets, current_liabilities")
            return pd.Series(index=data.index, dtype=float, name='current_ratio')
        
        # 流动比率 = 流动资产 / 流动负债
        current_ratio = data['current_assets'] / data['current_liabilities']
        
        # 过滤异常值
        current_ratio = current_ratio.where((current_ratio > 0) & (current_ratio < 20))
        
        return current_ratio.rename('current_ratio')
    
    def calculate_quick_ratio(self, data: pd.DataFrame) -> pd.Series:
        """
        计算速动比率 (Quick Ratio)。
        
        Args:
            data: 包含current_assets、inventory和current_liabilities的数据
            
        Returns:
            速动比率序列
        """
        required_cols = ['current_assets', 'inventory', 'current_liabilities']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算速动比率所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name='quick_ratio')
        
        # 速动比率 = (流动资产 - 存货) / 流动负债
        quick_assets = data['current_assets'] - data['inventory']
        quick_ratio = quick_assets / data['current_liabilities']
        
        # 过滤异常值
        quick_ratio = quick_ratio.where((quick_ratio >= 0) & (quick_ratio < 20))
        
        return quick_ratio.rename('quick_ratio')
    
    def calculate_gross_margin(self, data: pd.DataFrame) -> pd.Series:
        """
        计算毛利率 (Gross Margin)。
        
        Args:
            data: 包含revenue和cost_of_goods_sold的数据
            
        Returns:
            毛利率序列
        """
        if 'revenue' not in data.columns or 'cost_of_goods_sold' not in data.columns:
            logger.warning("缺少计算毛利率所需的列: revenue, cost_of_goods_sold")
            return pd.Series(index=data.index, dtype=float, name='gross_margin')
        
        # 毛利率 = (营收 - 销售成本) / 营收
        gross_profit = data['revenue'] - data['cost_of_goods_sold']
        gross_margin = gross_profit / data['revenue']
        
        # 过滤异常值
        gross_margin = gross_margin.where((gross_margin >= -1) & (gross_margin <= 1))
        
        return gross_margin.rename('gross_margin')
    
    def calculate_operating_margin(self, data: pd.DataFrame) -> pd.Series:
        """
        计算营业利润率 (Operating Margin)。
        
        Args:
            data: 包含operating_income和revenue的数据
            
        Returns:
            营业利润率序列
        """
        if 'operating_income' not in data.columns or 'revenue' not in data.columns:
            logger.warning("缺少计算营业利润率所需的列: operating_income, revenue")
            return pd.Series(index=data.index, dtype=float, name='operating_margin')
        
        # 营业利润率 = 营业利润 / 营收
        operating_margin = data['operating_income'] / data['revenue']
        
        # 过滤异常值
        operating_margin = operating_margin.where((operating_margin >= -1) & (operating_margin <= 1))
        
        return operating_margin.rename('operating_margin')
    
    def calculate_net_margin(self, data: pd.DataFrame) -> pd.Series:
        """
        计算净利润率 (Net Margin)。
        
        Args:
            data: 包含net_income和revenue的数据
            
        Returns:
            净利润率序列
        """
        if 'net_income' not in data.columns or 'revenue' not in data.columns:
            logger.warning("缺少计算净利润率所需的列: net_income, revenue")
            return pd.Series(index=data.index, dtype=float, name='net_margin')
        
        # 净利润率 = 净利润 / 营收
        net_margin = data['net_income'] / data['revenue']
        
        # 过滤异常值
        net_margin = net_margin.where((net_margin >= -1) & (net_margin <= 1))
        
        return net_margin.rename('net_margin')
    
    def calculate_asset_turnover(self, data: pd.DataFrame) -> pd.Series:
        """
        计算资产周转率 (Asset Turnover)。
        
        Args:
            data: 包含revenue和total_assets的数据
            
        Returns:
            资产周转率序列
        """
        if 'revenue' not in data.columns or 'total_assets' not in data.columns:
            logger.warning("缺少计算资产周转率所需的列: revenue, total_assets")
            return pd.Series(index=data.index, dtype=float, name='asset_turnover')
        
        # 资产周转率 = 营收 / 总资产
        asset_turnover = data['revenue'] / data['total_assets']
        
        # 过滤异常值
        asset_turnover = asset_turnover.where((asset_turnover >= 0) & (asset_turnover <= 10))
        
        return asset_turnover.rename('asset_turnover')
    
    def calculate_inventory_turnover(self, data: pd.DataFrame) -> pd.Series:
        """
        计算存货周转率 (Inventory Turnover)。
        
        Args:
            data: 包含cost_of_goods_sold和inventory的数据
            
        Returns:
            存货周转率序列
        """
        if 'cost_of_goods_sold' not in data.columns or 'inventory' not in data.columns:
            logger.warning("缺少计算存货周转率所需的列: cost_of_goods_sold, inventory")
            return pd.Series(index=data.index, dtype=float, name='inventory_turnover')
        
        # 存货周转率 = 销售成本 / 存货
        inventory_turnover = data['cost_of_goods_sold'] / data['inventory']
        
        # 过滤异常值
        inventory_turnover = inventory_turnover.where((inventory_turnover >= 0) & (inventory_turnover <= 100))
        
        return inventory_turnover.rename('inventory_turnover')
    
    def calculate_receivables_turnover(self, data: pd.DataFrame) -> pd.Series:
        """
        计算应收账款周转率 (Receivables Turnover)。
        
        Args:
            data: 包含revenue和accounts_receivable的数据
            
        Returns:
            应收账款周转率序列
        """
        if 'revenue' not in data.columns or 'accounts_receivable' not in data.columns:
            logger.warning("缺少计算应收账款周转率所需的列: revenue, accounts_receivable")
            return pd.Series(index=data.index, dtype=float, name='receivables_turnover')
        
        # 应收账款周转率 = 营收 / 应收账款
        receivables_turnover = data['revenue'] / data['accounts_receivable']
        
        # 过滤异常值
        receivables_turnover = receivables_turnover.where((receivables_turnover >= 0) & (receivables_turnover <= 100))
        
        return receivables_turnover.rename('receivables_turnover')
