#!/usr/bin/env python3
"""
因子分析运行脚本。

提供命令行接口来运行因子计算和分析。
"""

import argparse
import sys
import logging
from pathlib import Path
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(project_root))

from src.features.factors.factor_library import FactorLibrary
from src.features.factors.factor_analyzer import FactorAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def load_sample_data(data_path: str, symbols: list = None, start_date: str = None, end_date: str = None):
    """
    加载样本数据。
    
    Args:
        data_path: 数据路径
        symbols: 股票代码列表
        start_date: 开始日期
        end_date: 结束日期
        
    Returns:
        价格数据DataFrame
    """
    try:
        # 这里应该从实际的数据源加载数据
        # 为了演示，我们生成一些样本数据
        import numpy as np
        
        if not symbols:
            symbols = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH']
        
        if not start_date:
            start_date = '2023-01-01'
        if not end_date:
            end_date = '2023-12-31'
        
        dates = pd.date_range(start_date, end_date, freq='D')
        
        data_list = []
        np.random.seed(42)
        
        for symbol in symbols:
            # 生成价格数据
            base_price = np.random.uniform(10, 100)
            returns = np.random.normal(0, 0.02, len(dates))
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            symbol_data = pd.DataFrame({
                'date': dates,
                'symbol': symbol,
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, len(dates))
            })
            
            # 生成OHLV数据
            symbol_data['open'] = symbol_data['close'].shift(1).fillna(symbol_data['close'].iloc[0])
            symbol_data['high'] = symbol_data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, len(dates)))
            symbol_data['low'] = symbol_data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, len(dates)))
            
            data_list.append(symbol_data)
        
        combined_data = pd.concat(data_list, ignore_index=True)
        logger.info(f"加载了 {len(symbols)} 只股票，{len(dates)} 个交易日的数据")
        
        return combined_data
        
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return pd.DataFrame()


def calculate_factors(data: pd.DataFrame, factor_types: list = None):
    """
    计算因子。
    
    Args:
        data: 价格数据
        factor_types: 因子类型列表
        
    Returns:
        因子数据DataFrame
    """
    if factor_types is None:
        factor_types = ['technical']
    
    factor_lib = FactorLibrary()
    all_factors = []
    
    for symbol in data['symbol'].unique():
        symbol_data = data[data['symbol'] == symbol].sort_values('date').reset_index(drop=True)
        
        if len(symbol_data) < 20:  # 需要足够的数据点
            continue
        
        symbol_factors = {'date': symbol_data['date'], 'symbol': symbol}
        
        for factor_type in factor_types:
            try:
                if factor_type == 'technical':
                    # 计算技术因子
                    momentum = factor_lib.calculate_factor(symbol_data, 'momentum', 'technical', period=20)
                    volatility = factor_lib.calculate_factor(symbol_data, 'volatility', 'technical', period=20)
                    rsi = factor_lib.calculate_factor(symbol_data, 'rsi_14', 'technical')
                    
                    symbol_factors['momentum_20'] = momentum
                    symbol_factors['volatility_20'] = volatility
                    symbol_factors['rsi_14'] = rsi
                
                elif factor_type == 'sentiment':
                    # 计算情绪因子
                    money_flow = factor_lib.calculate_factor(symbol_data, 'money_flow', 'sentiment', period=20)
                    vwap_factor = factor_lib.calculate_factor(symbol_data, 'vwap_factor', 'sentiment', period=20)
                    
                    symbol_factors['money_flow_20'] = money_flow
                    symbol_factors['vwap_factor_20'] = vwap_factor
                
            except Exception as e:
                logger.warning(f"计算 {symbol} 的 {factor_type} 因子失败: {e}")
        
        # 计算未来收益率
        symbol_factors['return_1d'] = symbol_data['close'].pct_change().shift(-1)
        symbol_factors['return_5d'] = symbol_data['close'].pct_change(5).shift(-5)
        symbol_factors['return_20d'] = symbol_data['close'].pct_change(20).shift(-20)
        
        factor_df = pd.DataFrame(symbol_factors)
        all_factors.append(factor_df)
    
    if all_factors:
        combined_factors = pd.concat(all_factors, ignore_index=True)
        logger.info(f"计算了 {len(combined_factors)} 条因子记录")
        return combined_factors
    else:
        logger.warning("没有计算出任何因子")
        return pd.DataFrame()


def run_factor_analysis(factor_data: pd.DataFrame, output_dir: str = None):
    """
    运行因子分析。
    
    Args:
        factor_data: 因子数据
        output_dir: 输出目录
        
    Returns:
        分析结果
    """
    analyzer = FactorAnalyzer()
    
    # 分离因子数据和收益率数据
    factor_cols = [col for col in factor_data.columns 
                  if col not in ['date', 'symbol'] and 'return' not in col]
    return_cols = [col for col in factor_data.columns if 'return' in col]
    
    factor_subset = factor_data[['date', 'symbol'] + factor_cols].dropna()
    return_subset = factor_data[['date', 'symbol'] + return_cols].dropna()
    
    if factor_subset.empty or return_subset.empty:
        logger.error("因子数据或收益率数据为空")
        return None
    
    # 生成分析报告
    report = analyzer.generate_factor_report(
        factor_subset, 
        return_subset,
        output_path=f"{output_dir}/factor_analysis_report.pkl" if output_dir else None
    )
    
    return report


def print_analysis_summary(report: dict):
    """打印分析总结。"""
    if 'error' in report:
        print(f"分析失败: {report['error']}")
        return
    
    print("\n" + "="*50)
    print("因子分析报告总结")
    print("="*50)
    
    # IC分析总结
    if 'ic_analysis' in report and 'ic_statistics' in report['ic_analysis']:
        ic_stats = report['ic_analysis']['ic_statistics']
        if not ic_stats.empty:
            print(f"\nIC分析:")
            print(f"  因子数量: {len(ic_stats)}")
            print(f"  平均IC: {ic_stats['ic_mean'].mean():.4f}")
            print(f"  平均IC_IR: {ic_stats['ic_ir'].mean():.4f}")
            print(f"  最佳因子: {ic_stats.loc[ic_stats['ic_abs_mean'].idxmax(), 'factor']}")
            
            print(f"\n各因子IC统计:")
            for _, row in ic_stats.iterrows():
                print(f"  {row['factor']}: IC={row['ic_mean']:.4f}, IR={row['ic_ir']:.4f}")
    
    # 衰减分析总结
    if 'decay_analysis' in report:
        decay_data = report['decay_analysis']
        if not decay_data.empty:
            print(f"\n因子衰减分析:")
            for _, row in decay_data.iterrows():
                print(f"  {row['factor']}: 1日={row['decay_1']:.4f}, 20日={row['decay_20']:.4f}")
    
    # 换手率分析总结
    if 'turnover_analysis' in report:
        turnover_data = report['turnover_analysis']
        if not turnover_data.empty:
            print(f"\n因子换手率分析:")
            for _, row in turnover_data.iterrows():
                print(f"  {row['factor']}: 平均换手率={row['avg_turnover']:.4f}")
    
    print("\n" + "="*50)


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description='因子分析工具')
    
    parser.add_argument('--data-path', type=str, default='data/',
                       help='数据路径')
    parser.add_argument('--symbols', type=str, nargs='+',
                       help='股票代码列表')
    parser.add_argument('--start-date', type=str, default='2023-01-01',
                       help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2023-12-31',
                       help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--factor-types', type=str, nargs='+', 
                       default=['technical'], choices=['technical', 'sentiment', 'fundamental'],
                       help='因子类型')
    parser.add_argument('--output-dir', type=str, default='output/',
                       help='输出目录')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 1. 加载数据
        logger.info("开始加载数据...")
        data = load_sample_data(
            args.data_path, 
            args.symbols, 
            args.start_date, 
            args.end_date
        )
        
        if data.empty:
            logger.error("数据加载失败")
            return
        
        # 2. 计算因子
        logger.info("开始计算因子...")
        factor_data = calculate_factors(data, args.factor_types)
        
        if factor_data.empty:
            logger.error("因子计算失败")
            return
        
        # 3. 运行分析
        logger.info("开始因子分析...")
        report = run_factor_analysis(factor_data, str(output_dir))
        
        if report is None:
            logger.error("因子分析失败")
            return
        
        # 4. 打印结果
        print_analysis_summary(report)
        
        logger.info("因子分析完成")
        
    except Exception as e:
        logger.error(f"运行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
