"""
情绪面因子模块。

提供基于市场情绪和资金流向的因子计算功能。
"""

import pandas as pd
import numpy as np
from typing import List
import logging

logger = logging.getLogger(__name__)


class SentimentFactors:
    """情绪面因子计算器。"""
    
    def __init__(self):
        """初始化情绪面因子计算器。"""
        pass
    
    def get_available_factors(self) -> List[str]:
        """
        获取可用的情绪面因子列表。
        
        Returns:
            因子名称列表
        """
        return [
            'money_flow',
            'turnover_rate',
            'vwap_factor',
            'price_volume_correlation',
            'volume_momentum',
            'buying_pressure',
            'selling_pressure'
        ]
    
    def calculate_money_flow(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算资金流向因子。
        
        Args:
            data: 包含high, low, close, volume的数据
            period: 计算周期
            
        Returns:
            资金流向因子序列
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算资金流向因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'money_flow_{period}')
        
        # 计算典型价格
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        
        # 计算资金流量
        money_flow = typical_price * data['volume']
        
        # 判断资金流向（正负）
        price_change = typical_price.diff()
        positive_flow = money_flow.where(price_change > 0, 0)
        negative_flow = money_flow.where(price_change < 0, 0)
        
        # 计算资金流向指标
        positive_sum = positive_flow.rolling(window=period).sum()
        negative_sum = negative_flow.rolling(window=period).sum()
        
        money_flow_index = 100 - (100 / (1 + positive_sum / (negative_sum + 1e-8)))
        
        return money_flow_index.rename(f'money_flow_{period}')
    
    def calculate_turnover_rate(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算换手率因子。
        
        Args:
            data: 包含volume和shares_outstanding的数据
            period: 计算周期
            
        Returns:
            换手率因子序列
        """
        if 'volume' not in data.columns or 'shares_outstanding' not in data.columns:
            logger.warning("缺少计算换手率因子所需的列: volume, shares_outstanding")
            return pd.Series(index=data.index, dtype=float, name=f'turnover_rate_{period}')
        
        # 换手率 = 成交量 / 流通股本
        daily_turnover = data['volume'] / data['shares_outstanding']
        
        # 计算平均换手率
        avg_turnover = daily_turnover.rolling(window=period).mean()
        
        return avg_turnover.rename(f'turnover_rate_{period}')
    
    def calculate_vwap_factor(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算VWAP因子。
        
        Args:
            data: 包含high, low, close, volume的数据
            period: 计算周期
            
        Returns:
            VWAP因子序列
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算VWAP因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'vwap_factor_{period}')
        
        # 计算典型价格
        typical_price = (data['high'] + data['low'] + data['close']) / 3
        
        # 计算VWAP
        price_volume = typical_price * data['volume']
        vwap = price_volume.rolling(window=period).sum() / data['volume'].rolling(window=period).sum()
        
        # VWAP因子 = 当前价格 / VWAP - 1
        vwap_factor = data['close'] / vwap - 1
        
        return vwap_factor.rename(f'vwap_factor_{period}')
    
    def calculate_price_volume_correlation(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算价量相关性因子。
        
        Args:
            data: 包含close, volume的数据
            period: 计算周期
            
        Returns:
            价量相关性因子序列
        """
        required_cols = ['close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算价量相关性因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'price_volume_correlation_{period}')
        
        # 计算价格和成交量的收益率
        price_returns = data['close'].pct_change()
        volume_returns = data['volume'].pct_change()
        
        # 计算滚动相关系数
        correlation = price_returns.rolling(window=period).corr(volume_returns)
        
        return correlation.rename(f'price_volume_correlation_{period}')
    
    def calculate_volume_momentum(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算成交量动量因子。
        
        Args:
            data: 包含volume的数据
            period: 计算周期
            
        Returns:
            成交量动量因子序列
        """
        if 'volume' not in data.columns:
            logger.warning("缺少计算成交量动量因子所需的列: volume")
            return pd.Series(index=data.index, dtype=float, name=f'volume_momentum_{period}')
        
        # 成交量动量 = 当前成交量 / N期前成交量 - 1
        volume_momentum = data['volume'] / data['volume'].shift(period) - 1
        
        return volume_momentum.rename(f'volume_momentum_{period}')
    
    def calculate_buying_pressure(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算买盘压力因子。
        
        Args:
            data: 包含high, low, close, volume的数据
            period: 计算周期
            
        Returns:
            买盘压力因子序列
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算买盘压力因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'buying_pressure_{period}')
        
        # 计算买盘压力指标
        # 假设收盘价越接近最高价，买盘压力越大
        buying_pressure = (data['close'] - data['low']) / (data['high'] - data['low'])
        buying_pressure = buying_pressure.fillna(0.5)  # 当high=low时，设为中性值
        
        # 加权成交量
        weighted_buying_pressure = buying_pressure * data['volume']
        
        # 计算滚动平均
        avg_buying_pressure = weighted_buying_pressure.rolling(window=period).mean()
        
        return avg_buying_pressure.rename(f'buying_pressure_{period}')
    
    def calculate_selling_pressure(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算卖盘压力因子。
        
        Args:
            data: 包含high, low, close, volume的数据
            period: 计算周期
            
        Returns:
            卖盘压力因子序列
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算卖盘压力因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'selling_pressure_{period}')
        
        # 计算卖盘压力指标
        # 假设收盘价越接近最低价，卖盘压力越大
        selling_pressure = (data['high'] - data['close']) / (data['high'] - data['low'])
        selling_pressure = selling_pressure.fillna(0.5)  # 当high=low时，设为中性值
        
        # 加权成交量
        weighted_selling_pressure = selling_pressure * data['volume']
        
        # 计算滚动平均
        avg_selling_pressure = weighted_selling_pressure.rolling(window=period).mean()
        
        return avg_selling_pressure.rename(f'selling_pressure_{period}')
    
    def calculate_accumulation_distribution(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算累积/派发线因子。
        
        Args:
            data: 包含high, low, close, volume的数据
            period: 计算周期
            
        Returns:
            累积/派发线因子序列
        """
        required_cols = ['high', 'low', 'close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算累积/派发线因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'accumulation_distribution_{period}')
        
        # 计算资金流量乘数
        clv = ((data['close'] - data['low']) - (data['high'] - data['close'])) / (data['high'] - data['low'])
        clv = clv.fillna(0)  # 当high=low时，设为0
        
        # 计算累积/派发线
        ad_line = (clv * data['volume']).cumsum()
        
        # 计算变化率作为因子
        ad_factor = ad_line / ad_line.shift(period) - 1
        
        return ad_factor.rename(f'accumulation_distribution_{period}')
    
    def calculate_on_balance_volume(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算能量潮因子。
        
        Args:
            data: 包含close, volume的数据
            period: 计算周期
            
        Returns:
            能量潮因子序列
        """
        required_cols = ['close', 'volume']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算能量潮因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'on_balance_volume_{period}')
        
        # 计算价格变化方向
        price_change = data['close'].diff()
        
        # 计算OBV
        obv_change = data['volume'].where(price_change > 0, 
                                         -data['volume'].where(price_change < 0, 0))
        obv = obv_change.cumsum()
        
        # 计算OBV变化率作为因子
        obv_factor = obv / obv.shift(period) - 1
        
        return obv_factor.rename(f'on_balance_volume_{period}')
    
    def calculate_volume_rate_of_change(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算成交量变化率因子。
        
        Args:
            data: 包含volume的数据
            period: 计算周期
            
        Returns:
            成交量变化率因子序列
        """
        if 'volume' not in data.columns:
            logger.warning("缺少计算成交量变化率因子所需的列: volume")
            return pd.Series(index=data.index, dtype=float, name=f'volume_rate_of_change_{period}')
        
        # 成交量变化率 = (当前成交量 - N期前成交量) / N期前成交量
        volume_roc = (data['volume'] - data['volume'].shift(period)) / data['volume'].shift(period)
        
        return volume_roc.rename(f'volume_rate_of_change_{period}')
