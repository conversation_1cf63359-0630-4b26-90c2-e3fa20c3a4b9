"""
技术面因子模块。

提供基于价格和成交量数据的因子计算功能。
"""

import pandas as pd
import numpy as np
from typing import List
import logging

logger = logging.getLogger(__name__)


class TechnicalFactors:
    """技术面因子计算器。"""

    def __init__(self):
        """初始化技术面因子计算器。"""
        pass

    def get_available_factors(self) -> List[str]:
        """
        获取可用的技术面因子列表。

        Returns:
            因子名称列表
        """
        return [
            'momentum',
            'reversal',
            'volatility',
            'volume_factor',
            'price_factor',
            'rsi_14',
            'sma_20',
            'volatility_20'
        ]

    def calculate_momentum(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算动量因子。

        Args:
            data: 包含close价格的数据
            period: 计算周期

        Returns:
            动量因子序列
        """
        if 'close' not in data.columns:
            logger.warning("缺少计算动量因子所需的列: close")
            return pd.Series(index=data.index, dtype=float, name=f'momentum_{period}')

        # 动量 = 当前价格 / N期前价格 - 1
        momentum = data['close'] / data['close'].shift(period) - 1

        return momentum.rename(f'momentum_{period}')

    def calculate_reversal(self, data: pd.DataFrame, period: int = 5) -> pd.Series:
        """
        计算反转因子。

        Args:
            data: 包含close价格的数据
            period: 计算周期

        Returns:
            反转因子序列
        """
        if 'close' not in data.columns:
            logger.warning("缺少计算反转因子所需的列: close")
            return pd.Series(index=data.index, dtype=float, name=f'reversal_{period}')

        # 反转因子 = -短期收益率（负号表示反转）
        short_return = data['close'] / data['close'].shift(period) - 1
        reversal = -short_return

        return reversal.rename(f'reversal_{period}')

    def calculate_volatility(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算波动率因子。

        Args:
            data: 包含close价格的数据
            period: 计算周期

        Returns:
            波动率因子序列
        """
        if 'close' not in data.columns:
            logger.warning("缺少计算波动率因子所需的列: close")
            return pd.Series(index=data.index, dtype=float, name=f'volatility_{period}')

        # 计算日收益率
        returns = data['close'].pct_change()

        # 波动率 = 收益率的滚动标准差
        volatility = returns.rolling(window=period).std()

        return volatility.rename(f'volatility_{period}')

    def calculate_volume_factor(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算成交量因子。

        Args:
            data: 包含volume的数据
            period: 计算周期

        Returns:
            成交量因子序列
        """
        if 'volume' not in data.columns:
            logger.warning("缺少计算成交量因子所需的列: volume")
            return pd.Series(index=data.index, dtype=float, name=f'volume_factor_{period}')

        # 成交量因子 = 当前成交量 / 平均成交量
        avg_volume = data['volume'].rolling(window=period).mean()
        volume_factor = data['volume'] / avg_volume

        return volume_factor.rename(f'volume_factor_{period}')

    def calculate_price_factor(self, data: pd.DataFrame, period: int = 20) -> pd.Series:
        """
        计算价格因子。

        Args:
            data: 包含high, low, close价格的数据
            period: 计算周期

        Returns:
            价格因子序列
        """
        required_cols = ['high', 'low', 'close']
        if not all(col in data.columns for col in required_cols):
            logger.warning(f"缺少计算价格因子所需的列: {required_cols}")
            return pd.Series(index=data.index, dtype=float, name=f'price_factor_{period}')

        # 价格因子 = (收盘价 - 最低价) / (最高价 - 最低价)
        # 表示收盘价在当日价格区间中的位置
        price_position = (data['close'] - data['low']) / (data['high'] - data['low'])

        # 计算滚动平均
        price_factor = price_position.rolling(window=period).mean()

        return price_factor.rename(f'price_factor_{period}')

    def calculate_rsi_14(self, data: pd.DataFrame) -> pd.Series:
        """
        计算RSI指标作为因子。

        Args:
            data: 包含close价格的数据

        Returns:
            RSI因子序列
        """
        if 'close' not in data.columns:
            logger.warning("缺少计算RSI因子所需的列: close")
            return pd.Series(index=data.index, dtype=float, name='rsi_14')

        # 计算价格变化
        delta = data['close'].diff()

        # 分离上涨和下跌
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()

        # 计算RSI
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        return rsi.rename('rsi_14')

    def calculate_sma_20(self, data: pd.DataFrame) -> pd.Series:
        """
        计算20日简单移动平均作为因子。

        Args:
            data: 包含close价格的数据

        Returns:
            SMA因子序列
        """
        if 'close' not in data.columns:
            logger.warning("缺少计算SMA因子所需的列: close")
            return pd.Series(index=data.index, dtype=float, name='sma_20')

        # 计算20日简单移动平均
        sma = data['close'].rolling(window=20).mean()

        return sma.rename('sma_20')

    def calculate_volatility_20(self, data: pd.DataFrame) -> pd.Series:
        """
        计算20日波动率作为因子。

        Args:
            data: 包含close价格的数据

        Returns:
            波动率因子序列
        """
        return self.calculate_volatility(data, period=20)