"""
动量指标模块。

提供各种动量分析指标的计算功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class MomentumIndicators:
    """动量指标计算器。"""
    
    def __init__(self):
        """初始化动量指标计算器。"""
        pass
    
    def calculate_momentum(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        计算动量指标 (Momentum)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            动量序列
        """
        return data.diff(period)
    
    def calculate_roc(self, data: pd.Series, period: int = 10) -> pd.Series:
        """
        计算变化率 (Rate of Change)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            ROC序列
        """
        return ((data / data.shift(period)) - 1) * 100
    
    def calculate_stochastic(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        k_period: int = 14,
        d_period: int = 3,
        smooth_k: int = 3
    ) -> Dict[str, pd.Series]:
        """
        计算随机指标 (Stochastic Oscillator)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            k_period: K值周期
            d_period: D值周期
            smooth_k: K值平滑周期
            
        Returns:
            包含%K和%D的字典
        """
        # 计算原始%K
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        raw_k = (close - lowest_low) / (highest_high - lowest_low) * 100
        
        # 平滑%K
        k_percent = raw_k.rolling(window=smooth_k).mean()
        
        # 计算%D
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return {
            'k_percent': k_percent,
            'd_percent': d_percent
        }
    
    def calculate_ultimate_oscillator(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series,
        period1: int = 7,
        period2: int = 14,
        period3: int = 28
    ) -> pd.Series:
        """
        计算终极振荡器 (Ultimate Oscillator)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period1: 短期周期
            period2: 中期周期
            period3: 长期周期
            
        Returns:
            UO序列
        """
        prev_close = close.shift(1)
        
        # 计算买压 (Buying Pressure)
        bp = close - pd.concat([low, prev_close], axis=1).min(axis=1)
        
        # 计算真实波幅 (True Range)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算各周期的平均值
        avg1 = bp.rolling(window=period1).sum() / true_range.rolling(window=period1).sum()
        avg2 = bp.rolling(window=period2).sum() / true_range.rolling(window=period2).sum()
        avg3 = bp.rolling(window=period3).sum() / true_range.rolling(window=period3).sum()
        
        # 计算终极振荡器
        uo = ((4 * avg1) + (2 * avg2) + avg3) / 7 * 100
        
        return uo
    
    def calculate_awesome_oscillator(
        self, 
        high: pd.Series, 
        low: pd.Series,
        fast_period: int = 5,
        slow_period: int = 34
    ) -> pd.Series:
        """
        计算动量震荡器 (Awesome Oscillator)。
        
        Args:
            high: 最高价
            low: 最低价
            fast_period: 快速周期
            slow_period: 慢速周期
            
        Returns:
            AO序列
        """
        median_price = (high + low) / 2
        
        fast_sma = median_price.rolling(window=fast_period).mean()
        slow_sma = median_price.rolling(window=slow_period).mean()
        
        ao = fast_sma - slow_sma
        
        return ao
    
    def calculate_trix(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        计算TRIX指标 (Triple Exponential Average)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            TRIX序列
        """
        # 三重指数平滑
        ema1 = data.ewm(span=period).mean()
        ema2 = ema1.ewm(span=period).mean()
        ema3 = ema2.ewm(span=period).mean()
        
        # 计算TRIX
        trix = ema3.pct_change() * 10000
        
        return trix
    
    def calculate_ppo(
        self, 
        data: pd.Series, 
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9
    ) -> Dict[str, pd.Series]:
        """
        计算价格震荡器 (Percentage Price Oscillator)。
        
        Args:
            data: 价格数据
            fast_period: 快速周期
            slow_period: 慢速周期
            signal_period: 信号线周期
            
        Returns:
            包含PPO、信号线和柱状图的字典
        """
        fast_ema = data.ewm(span=fast_period).mean()
        slow_ema = data.ewm(span=slow_period).mean()
        
        ppo = ((fast_ema - slow_ema) / slow_ema) * 100
        signal = ppo.ewm(span=signal_period).mean()
        histogram = ppo - signal
        
        return {
            'ppo': ppo,
            'signal': signal,
            'histogram': histogram
        }
    
    def calculate_dmi(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> Dict[str, pd.Series]:
        """
        计算方向性移动指标 (Directional Movement Index)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            包含+DI、-DI、DX的字典
        """
        # 计算真实波幅
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算方向性移动
        dm_plus = high.diff()
        dm_minus = -low.diff()
        
        dm_plus[dm_plus < 0] = 0
        dm_minus[dm_minus < 0] = 0
        dm_plus[(dm_plus - dm_minus) <= 0] = 0
        dm_minus[(dm_minus - dm_plus) <= 0] = 0
        
        # 计算平滑的TR和DM
        atr = true_range.ewm(span=period).mean()
        adm_plus = dm_plus.ewm(span=period).mean()
        adm_minus = dm_minus.ewm(span=period).mean()
        
        # 计算DI
        di_plus = (adm_plus / atr) * 100
        di_minus = (adm_minus / atr) * 100
        
        # 计算DX
        dx = abs(di_plus - di_minus) / (di_plus + di_minus) * 100
        
        return {
            'di_plus': di_plus,
            'di_minus': di_minus,
            'dx': dx
        }
    
    def calculate_mfi(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        volume: pd.Series,
        period: int = 14
    ) -> pd.Series:
        """
        计算资金流量指标 (Money Flow Index)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            volume: 成交量
            period: 周期
            
        Returns:
            MFI序列
        """
        # 计算典型价格
        typical_price = (high + low + close) / 3
        
        # 计算资金流量
        money_flow = typical_price * volume
        
        # 计算正负资金流量
        price_change = typical_price.diff()
        positive_flow = money_flow.where(price_change > 0, 0)
        negative_flow = money_flow.where(price_change < 0, 0)
        
        # 计算资金流量比率
        positive_mf = positive_flow.rolling(window=period).sum()
        negative_mf = negative_flow.rolling(window=period).sum()
        
        mfr = positive_mf / negative_mf
        mfi = 100 - (100 / (1 + mfr))
        
        return mfi
