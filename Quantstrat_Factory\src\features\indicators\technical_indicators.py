"""
技术指标计算器。

提供统一的技术指标计算接口。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Union
import logging

logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """技术指标计算器。"""
    
    def __init__(self):
        """初始化技术指标计算器。"""
        pass
    
    def calculate_sma(self, data: pd.Series, period: int = 20) -> pd.Series:
        """
        计算简单移动平均线 (Simple Moving Average)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            SMA序列
        """
        return data.rolling(window=period).mean()
    
    def calculate_ema(self, data: pd.Series, period: int = 20) -> pd.Series:
        """
        计算指数移动平均线 (Exponential Moving Average)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            EMA序列
        """
        return data.ewm(span=period).mean()
    
    def calculate_rsi(self, data: pd.Series, period: int = 14) -> pd.Series:
        """
        计算相对强弱指数 (Relative Strength Index)。
        
        Args:
            data: 价格数据
            period: 周期
            
        Returns:
            RSI序列
        """
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(
        self, 
        data: pd.Series, 
        fast: int = 12, 
        slow: int = 26, 
        signal: int = 9
    ) -> Dict[str, pd.Series]:
        """
        计算MACD指标 (Moving Average Convergence Divergence)。
        
        Args:
            data: 价格数据
            fast: 快线周期
            slow: 慢线周期
            signal: 信号线周期
            
        Returns:
            包含MACD、信号线和柱状图的字典
        """
        ema_fast = self.calculate_ema(data, fast)
        ema_slow = self.calculate_ema(data, slow)
        macd = ema_fast - ema_slow
        macd_signal = self.calculate_ema(macd, signal)
        macd_histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'signal': macd_signal,
            'histogram': macd_histogram
        }
    
    def calculate_kdj(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 9,
        k_period: int = 3,
        d_period: int = 3
    ) -> Dict[str, pd.Series]:
        """
        计算KDJ指标 (Stochastic Oscillator)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: RSV周期
            k_period: K值平滑周期
            d_period: D值平滑周期
            
        Returns:
            包含K、D、J值的字典
        """
        # 计算RSV
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        
        # 计算K、D、J
        k = rsv.ewm(alpha=1/k_period).mean()
        d = k.ewm(alpha=1/d_period).mean()
        j = 3 * k - 2 * d
        
        return {
            'k': k,
            'd': d,
            'j': j
        }
    
    def calculate_bollinger_bands(
        self, 
        data: pd.Series, 
        period: int = 20, 
        std_dev: float = 2.0
    ) -> Dict[str, pd.Series]:
        """
        计算布林带 (Bollinger Bands)。
        
        Args:
            data: 价格数据
            period: 周期
            std_dev: 标准差倍数
            
        Returns:
            包含上轨、中轨、下轨的字典
        """
        sma = self.calculate_sma(data, period)
        std = data.rolling(window=period).std()
        
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return {
            'upper': upper,
            'middle': sma,
            'lower': lower
        }
    
    def calculate_atr(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> pd.Series:
        """
        计算平均真实波幅 (Average True Range)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            ATR序列
        """
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr
    
    def calculate_williams_r(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> pd.Series:
        """
        计算威廉指标 (Williams %R)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            Williams %R序列
        """
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        
        wr = (highest_high - close) / (highest_high - lowest_low) * -100
        
        return wr
    
    def calculate_cci(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 20
    ) -> pd.Series:
        """
        计算商品通道指数 (Commodity Channel Index)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            CCI序列
        """
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(
            lambda x: np.mean(np.abs(x - x.mean()))
        )
        
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        
        return cci
    
    def calculate_obv(self, close: pd.Series, volume: pd.Series) -> pd.Series:
        """
        计算能量潮 (On-Balance Volume)。
        
        Args:
            close: 收盘价
            volume: 成交量
            
        Returns:
            OBV序列
        """
        price_change = close.diff()
        obv = pd.Series(index=close.index, dtype=float)
        obv.iloc[0] = volume.iloc[0]
        
        for i in range(1, len(close)):
            if price_change.iloc[i] > 0:
                obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
            elif price_change.iloc[i] < 0:
                obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
            else:
                obv.iloc[i] = obv.iloc[i-1]
        
        return obv
    
    def calculate_multiple_indicators(
        self, 
        df: pd.DataFrame, 
        indicators: List[str] = None
    ) -> pd.DataFrame:
        """
        批量计算多个技术指标。
        
        Args:
            df: 包含OHLCV数据的DataFrame
            indicators: 指标列表
            
        Returns:
            包含所有指标的DataFrame
        """
        if indicators is None:
            indicators = ['sma_20', 'ema_12', 'rsi_14', 'macd', 'kdj', 'bollinger']
        
        result_df = df.copy()
        
        # 确保必要的列存在
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        for col in required_cols:
            if col not in df.columns:
                logger.warning(f"缺少必要列: {col}")
                return result_df
        
        for indicator in indicators:
            try:
                if indicator.startswith('sma_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_sma(df['close'], period)
                
                elif indicator.startswith('ema_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_ema(df['close'], period)
                
                elif indicator.startswith('rsi_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_rsi(df['close'], period)
                
                elif indicator == 'macd':
                    macd_data = self.calculate_macd(df['close'])
                    result_df['macd'] = macd_data['macd']
                    result_df['macd_signal'] = macd_data['signal']
                    result_df['macd_histogram'] = macd_data['histogram']
                
                elif indicator == 'kdj':
                    kdj_data = self.calculate_kdj(df['high'], df['low'], df['close'])
                    result_df['kdj_k'] = kdj_data['k']
                    result_df['kdj_d'] = kdj_data['d']
                    result_df['kdj_j'] = kdj_data['j']
                
                elif indicator == 'bollinger':
                    bb_data = self.calculate_bollinger_bands(df['close'])
                    result_df['bb_upper'] = bb_data['upper']
                    result_df['bb_middle'] = bb_data['middle']
                    result_df['bb_lower'] = bb_data['lower']
                
                elif indicator.startswith('atr_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_atr(df['high'], df['low'], df['close'], period)
                
                elif indicator.startswith('williams_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_williams_r(df['high'], df['low'], df['close'], period)
                
                elif indicator.startswith('cci_'):
                    period = int(indicator.split('_')[1])
                    result_df[indicator] = self.calculate_cci(df['high'], df['low'], df['close'], period)
                
                elif indicator == 'obv':
                    result_df[indicator] = self.calculate_obv(df['close'], df['volume'])
                
            except Exception as e:
                logger.error(f"计算指标 {indicator} 失败: {e}")
                continue
        
        return result_df
