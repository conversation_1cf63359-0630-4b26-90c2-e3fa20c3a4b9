"""
趋势指标模块。

提供各种趋势分析指标的计算功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class TrendIndicators:
    """趋势指标计算器。"""
    
    def __init__(self):
        """初始化趋势指标计算器。"""
        pass
    
    def calculate_adx(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 14
    ) -> Dict[str, pd.Series]:
        """
        计算平均趋向指数 (Average Directional Index)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: 周期
            
        Returns:
            包含ADX、+DI、-DI的字典
        """
        # 计算真实波幅
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        
        # 计算方向性移动
        dm_plus = high.diff()
        dm_minus = -low.diff()
        
        dm_plus[dm_plus < 0] = 0
        dm_minus[dm_minus < 0] = 0
        dm_plus[(dm_plus - dm_minus) <= 0] = 0
        dm_minus[(dm_minus - dm_plus) <= 0] = 0
        
        # 计算平滑的TR和DM
        atr = true_range.rolling(window=period).mean()
        adm_plus = dm_plus.rolling(window=period).mean()
        adm_minus = dm_minus.rolling(window=period).mean()
        
        # 计算DI
        di_plus = (adm_plus / atr) * 100
        di_minus = (adm_minus / atr) * 100
        
        # 计算DX和ADX
        dx = abs(di_plus - di_minus) / (di_plus + di_minus) * 100
        adx = dx.rolling(window=period).mean()
        
        return {
            'adx': adx,
            'di_plus': di_plus,
            'di_minus': di_minus
        }
    
    def calculate_aroon(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        period: int = 25
    ) -> Dict[str, pd.Series]:
        """
        计算阿隆指标 (Aroon Indicator)。
        
        Args:
            high: 最高价
            low: 最低价
            period: 周期
            
        Returns:
            包含Aroon Up、Aroon Down、Aroon Oscillator的字典
        """
        aroon_up = pd.Series(index=high.index, dtype=float)
        aroon_down = pd.Series(index=low.index, dtype=float)
        
        for i in range(period, len(high)):
            high_period = high.iloc[i-period+1:i+1]
            low_period = low.iloc[i-period+1:i+1]
            
            high_idx = high_period.idxmax()
            low_idx = low_period.idxmin()
            
            periods_since_high = i - high_period.index.get_loc(high_idx)
            periods_since_low = i - low_period.index.get_loc(low_idx)
            
            aroon_up.iloc[i] = ((period - periods_since_high) / period) * 100
            aroon_down.iloc[i] = ((period - periods_since_low) / period) * 100
        
        aroon_oscillator = aroon_up - aroon_down
        
        return {
            'aroon_up': aroon_up,
            'aroon_down': aroon_down,
            'aroon_oscillator': aroon_oscillator
        }
    
    def calculate_parabolic_sar(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series,
        af_start: float = 0.02,
        af_increment: float = 0.02,
        af_max: float = 0.2
    ) -> pd.Series:
        """
        计算抛物线SAR (Parabolic Stop and Reverse)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            af_start: 初始加速因子
            af_increment: 加速因子增量
            af_max: 最大加速因子
            
        Returns:
            SAR序列
        """
        sar = pd.Series(index=close.index, dtype=float)
        trend = pd.Series(index=close.index, dtype=int)  # 1为上升趋势，-1为下降趋势
        af = pd.Series(index=close.index, dtype=float)
        ep = pd.Series(index=close.index, dtype=float)  # 极值点
        
        # 初始化
        sar.iloc[0] = low.iloc[0]
        trend.iloc[0] = 1
        af.iloc[0] = af_start
        ep.iloc[0] = high.iloc[0]
        
        for i in range(1, len(close)):
            if trend.iloc[i-1] == 1:  # 上升趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                
                # 检查是否需要反转
                if low.iloc[i] <= sar.iloc[i]:
                    trend.iloc[i] = -1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = af_start
                    ep.iloc[i] = low.iloc[i]
                else:
                    trend.iloc[i] = 1
                    if high.iloc[i] > ep.iloc[i-1]:
                        ep.iloc[i] = high.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
            
            else:  # 下降趋势
                sar.iloc[i] = sar.iloc[i-1] + af.iloc[i-1] * (ep.iloc[i-1] - sar.iloc[i-1])
                
                # 检查是否需要反转
                if high.iloc[i] >= sar.iloc[i]:
                    trend.iloc[i] = 1
                    sar.iloc[i] = ep.iloc[i-1]
                    af.iloc[i] = af_start
                    ep.iloc[i] = high.iloc[i]
                else:
                    trend.iloc[i] = -1
                    if low.iloc[i] < ep.iloc[i-1]:
                        ep.iloc[i] = low.iloc[i]
                        af.iloc[i] = min(af.iloc[i-1] + af_increment, af_max)
                    else:
                        ep.iloc[i] = ep.iloc[i-1]
                        af.iloc[i] = af.iloc[i-1]
        
        return sar
    
    def calculate_ichimoku(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series,
        tenkan_period: int = 9,
        kijun_period: int = 26,
        senkou_b_period: int = 52,
        displacement: int = 26
    ) -> Dict[str, pd.Series]:
        """
        计算一目均衡表 (Ichimoku Cloud)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            tenkan_period: 转换线周期
            kijun_period: 基准线周期
            senkou_b_period: 先行带B周期
            displacement: 位移
            
        Returns:
            包含各条线的字典
        """
        # 转换线 (Tenkan-sen)
        tenkan_high = high.rolling(window=tenkan_period).max()
        tenkan_low = low.rolling(window=tenkan_period).min()
        tenkan_sen = (tenkan_high + tenkan_low) / 2
        
        # 基准线 (Kijun-sen)
        kijun_high = high.rolling(window=kijun_period).max()
        kijun_low = low.rolling(window=kijun_period).min()
        kijun_sen = (kijun_high + kijun_low) / 2
        
        # 先行带A (Senkou Span A)
        senkou_a = ((tenkan_sen + kijun_sen) / 2).shift(displacement)
        
        # 先行带B (Senkou Span B)
        senkou_b_high = high.rolling(window=senkou_b_period).max()
        senkou_b_low = low.rolling(window=senkou_b_period).min()
        senkou_b = ((senkou_b_high + senkou_b_low) / 2).shift(displacement)
        
        # 滞后线 (Chikou Span)
        chikou_span = close.shift(-displacement)
        
        return {
            'tenkan_sen': tenkan_sen,
            'kijun_sen': kijun_sen,
            'senkou_a': senkou_a,
            'senkou_b': senkou_b,
            'chikou_span': chikou_span
        }
    
    def calculate_supertrend(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series,
        period: int = 10,
        multiplier: float = 3.0
    ) -> Dict[str, pd.Series]:
        """
        计算超级趋势 (SuperTrend)。
        
        Args:
            high: 最高价
            low: 最低价
            close: 收盘价
            period: ATR周期
            multiplier: ATR倍数
            
        Returns:
            包含SuperTrend和趋势方向的字典
        """
        # 计算ATR
        prev_close = close.shift(1)
        tr1 = high - low
        tr2 = abs(high - prev_close)
        tr3 = abs(low - prev_close)
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        # 计算基本上下轨
        hl2 = (high + low) / 2
        upper_band = hl2 + (multiplier * atr)
        lower_band = hl2 - (multiplier * atr)
        
        # 计算最终上下轨
        final_upper = pd.Series(index=close.index, dtype=float)
        final_lower = pd.Series(index=close.index, dtype=float)
        supertrend = pd.Series(index=close.index, dtype=float)
        trend = pd.Series(index=close.index, dtype=int)
        
        for i in range(len(close)):
            if i == 0:
                final_upper.iloc[i] = upper_band.iloc[i]
                final_lower.iloc[i] = lower_band.iloc[i]
                supertrend.iloc[i] = upper_band.iloc[i]
                trend.iloc[i] = -1
            else:
                # 最终上轨
                if upper_band.iloc[i] < final_upper.iloc[i-1] or close.iloc[i-1] > final_upper.iloc[i-1]:
                    final_upper.iloc[i] = upper_band.iloc[i]
                else:
                    final_upper.iloc[i] = final_upper.iloc[i-1]
                
                # 最终下轨
                if lower_band.iloc[i] > final_lower.iloc[i-1] or close.iloc[i-1] < final_lower.iloc[i-1]:
                    final_lower.iloc[i] = lower_band.iloc[i]
                else:
                    final_lower.iloc[i] = final_lower.iloc[i-1]
                
                # SuperTrend和趋势
                if supertrend.iloc[i-1] == final_upper.iloc[i-1] and close.iloc[i] <= final_upper.iloc[i]:
                    supertrend.iloc[i] = final_upper.iloc[i]
                    trend.iloc[i] = -1
                elif supertrend.iloc[i-1] == final_upper.iloc[i-1] and close.iloc[i] > final_upper.iloc[i]:
                    supertrend.iloc[i] = final_lower.iloc[i]
                    trend.iloc[i] = 1
                elif supertrend.iloc[i-1] == final_lower.iloc[i-1] and close.iloc[i] >= final_lower.iloc[i]:
                    supertrend.iloc[i] = final_lower.iloc[i]
                    trend.iloc[i] = 1
                elif supertrend.iloc[i-1] == final_lower.iloc[i-1] and close.iloc[i] < final_lower.iloc[i]:
                    supertrend.iloc[i] = final_upper.iloc[i]
                    trend.iloc[i] = -1
                else:
                    supertrend.iloc[i] = supertrend.iloc[i-1]
                    trend.iloc[i] = trend.iloc[i-1]
        
        return {
            'supertrend': supertrend,
            'trend': trend,
            'upper_band': final_upper,
            'lower_band': final_lower
        }
