"""
告警系统模块。

提供系统监控告警功能，包括规则定义、条件检查和通知发送。
"""

import time
import threading
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import json
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart


logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """告警严重程度。"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """告警状态。"""
    ACTIVE = "active"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"


@dataclass
class AlertRule:
    """告警规则。"""
    name: str
    description: str
    metric_name: str
    condition: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    threshold: float
    severity: AlertSeverity
    duration: int = 300  # 持续时间（秒）
    cooldown: int = 3600  # 冷却时间（秒）
    enabled: bool = True
    tags: Dict[str, str] = field(default_factory=dict)
    
    def evaluate(self, value: float) -> bool:
        """评估告警条件。"""
        if self.condition == 'gt':
            return value > self.threshold
        elif self.condition == 'lt':
            return value < self.threshold
        elif self.condition == 'eq':
            return value == self.threshold
        elif self.condition == 'gte':
            return value >= self.threshold
        elif self.condition == 'lte':
            return value <= self.threshold
        else:
            return False


@dataclass
class Alert:
    """告警实例。"""
    rule_name: str
    metric_name: str
    current_value: float
    threshold: float
    severity: AlertSeverity
    message: str
    timestamp: datetime
    status: AlertStatus = AlertStatus.ACTIVE
    resolved_at: Optional[datetime] = None
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return {
            'rule_name': self.rule_name,
            'metric_name': self.metric_name,
            'current_value': self.current_value,
            'threshold': self.threshold,
            'severity': self.severity.value,
            'message': self.message,
            'timestamp': self.timestamp.isoformat(),
            'status': self.status.value,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'tags': self.tags
        }


class NotificationChannel:
    """通知渠道基类。"""
    
    def send(self, alert: Alert) -> bool:
        """发送告警通知。"""
        raise NotImplementedError


class EmailNotificationChannel(NotificationChannel):
    """邮件通知渠道。"""
    
    def __init__(self, 
                 smtp_server: str,
                 smtp_port: int,
                 username: str,
                 password: str,
                 from_email: str,
                 to_emails: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
    
    def send(self, alert: Alert) -> bool:
        """发送邮件告警。"""
        try:
            # 创建邮件内容
            msg = MimeMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.rule_name}"
            
            # 邮件正文
            body = f"""
告警详情:
- 规则名称: {alert.rule_name}
- 指标名称: {alert.metric_name}
- 当前值: {alert.current_value}
- 阈值: {alert.threshold}
- 严重程度: {alert.severity.value}
- 时间: {alert.timestamp}
- 消息: {alert.message}

标签: {alert.tags}
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"邮件告警发送成功: {alert.rule_name}")
            return True
            
        except Exception as e:
            logger.error(f"邮件告警发送失败: {e}")
            return False


class WebhookNotificationChannel(NotificationChannel):
    """Webhook通知渠道。"""
    
    def __init__(self, webhook_url: str, headers: Optional[Dict[str, str]] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {}
    
    def send(self, alert: Alert) -> bool:
        """发送Webhook告警。"""
        try:
            import requests
            
            payload = {
                'alert': alert.to_dict(),
                'timestamp': datetime.now().isoformat()
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                logger.info(f"Webhook告警发送成功: {alert.rule_name}")
                return True
            else:
                logger.error(f"Webhook告警发送失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Webhook告警发送失败: {e}")
            return False


class SlackNotificationChannel(NotificationChannel):
    """Slack通知渠道。"""
    
    def __init__(self, webhook_url: str, channel: str = "#alerts"):
        self.webhook_url = webhook_url
        self.channel = channel
    
    def send(self, alert: Alert) -> bool:
        """发送Slack告警。"""
        try:
            import requests
            
            # 根据严重程度选择颜色
            color_map = {
                AlertSeverity.INFO: "#36a64f",      # 绿色
                AlertSeverity.WARNING: "#ff9500",   # 橙色
                AlertSeverity.ERROR: "#ff0000",     # 红色
                AlertSeverity.CRITICAL: "#8b0000"   # 深红色
            }
            
            payload = {
                "channel": self.channel,
                "username": "Quantstrat Alert Bot",
                "icon_emoji": ":warning:",
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "#ff0000"),
                        "title": f"{alert.severity.value.upper()}: {alert.rule_name}",
                        "text": alert.message,
                        "fields": [
                            {
                                "title": "指标",
                                "value": alert.metric_name,
                                "short": True
                            },
                            {
                                "title": "当前值",
                                "value": str(alert.current_value),
                                "short": True
                            },
                            {
                                "title": "阈值",
                                "value": str(alert.threshold),
                                "short": True
                            },
                            {
                                "title": "时间",
                                "value": alert.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                                "short": True
                            }
                        ],
                        "footer": "Quantstrat Factory",
                        "ts": int(alert.timestamp.timestamp())
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"Slack告警发送成功: {alert.rule_name}")
                return True
            else:
                logger.error(f"Slack告警发送失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Slack告警发送失败: {e}")
            return False


class AlertManager:
    """告警管理器。"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.notification_channels: List[NotificationChannel] = []
        self.rule_states: Dict[str, Dict] = {}  # 规则状态跟踪
        self.is_running = False
        self.check_thread = None
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则。"""
        self.rules[rule.name] = rule
        self.rule_states[rule.name] = {
            'triggered_at': None,
            'last_alert_at': None,
            'consecutive_violations': 0
        }
        logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除告警规则。"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            del self.rule_states[rule_name]
            logger.info(f"移除告警规则: {rule_name}")
    
    def add_notification_channel(self, channel: NotificationChannel):
        """添加通知渠道。"""
        self.notification_channels.append(channel)
        logger.info(f"添加通知渠道: {type(channel).__name__}")
    
    def check_metric(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """检查指标是否触发告警。"""
        current_time = datetime.now()
        
        for rule_name, rule in self.rules.items():
            if not rule.enabled or rule.metric_name != metric_name:
                continue
            
            # 检查标签匹配
            if rule.tags and tags:
                if not all(tags.get(k) == v for k, v in rule.tags.items()):
                    continue
            
            rule_state = self.rule_states[rule_name]
            
            # 评估告警条件
            if rule.evaluate(value):
                # 条件满足
                if rule_state['triggered_at'] is None:
                    # 首次触发
                    rule_state['triggered_at'] = current_time
                    rule_state['consecutive_violations'] = 1
                else:
                    rule_state['consecutive_violations'] += 1
                
                # 检查是否达到持续时间要求
                if (current_time - rule_state['triggered_at']).total_seconds() >= rule.duration:
                    self._trigger_alert(rule, value, current_time, tags or {})
            else:
                # 条件不满足，重置状态
                if rule_state['triggered_at'] is not None:
                    rule_state['triggered_at'] = None
                    rule_state['consecutive_violations'] = 0
                    
                    # 如果有活跃告警，标记为已解决
                    if rule_name in self.active_alerts:
                        self._resolve_alert(rule_name, current_time)
    
    def _trigger_alert(self, rule: AlertRule, value: float, timestamp: datetime, tags: Dict[str, str]):
        """触发告警。"""
        rule_state = self.rule_states[rule.name]
        
        # 检查冷却时间
        if (rule_state['last_alert_at'] and 
            (timestamp - rule_state['last_alert_at']).total_seconds() < rule.cooldown):
            return
        
        # 创建告警
        alert = Alert(
            rule_name=rule.name,
            metric_name=rule.metric_name,
            current_value=value,
            threshold=rule.threshold,
            severity=rule.severity,
            message=f"{rule.description} - 当前值: {value}, 阈值: {rule.threshold}",
            timestamp=timestamp,
            tags=tags
        )
        
        # 记录告警
        self.active_alerts[rule.name] = alert
        self.alert_history.append(alert)
        rule_state['last_alert_at'] = timestamp
        
        # 发送通知
        self._send_notifications(alert)
        
        logger.warning(f"触发告警: {rule.name} - {alert.message}")
    
    def _resolve_alert(self, rule_name: str, timestamp: datetime):
        """解决告警。"""
        if rule_name in self.active_alerts:
            alert = self.active_alerts[rule_name]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = timestamp
            
            del self.active_alerts[rule_name]
            
            logger.info(f"告警已解决: {rule_name}")
    
    def _send_notifications(self, alert: Alert):
        """发送告警通知。"""
        for channel in self.notification_channels:
            try:
                channel.send(alert)
            except Exception as e:
                logger.error(f"发送告警通知失败: {e}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警。"""
        return list(self.active_alerts.values())
    
    def get_alert_history(self, hours: int = 24) -> List[Alert]:
        """获取告警历史。"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [alert for alert in self.alert_history if alert.timestamp >= cutoff_time]
    
    def get_alert_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取告警统计。"""
        recent_alerts = self.get_alert_history(hours)
        
        severity_counts = {}
        for severity in AlertSeverity:
            severity_counts[severity.value] = len([
                alert for alert in recent_alerts if alert.severity == severity
            ])
        
        return {
            'active_alerts_count': len(self.active_alerts),
            'total_alerts_last_24h': len(recent_alerts),
            'severity_breakdown': severity_counts,
            'most_frequent_rules': self._get_most_frequent_rules(recent_alerts)
        }
    
    def _get_most_frequent_rules(self, alerts: List[Alert]) -> List[Dict[str, Any]]:
        """获取最频繁的告警规则。"""
        rule_counts = {}
        for alert in alerts:
            rule_counts[alert.rule_name] = rule_counts.get(alert.rule_name, 0) + 1
        
        return [
            {'rule_name': rule, 'count': count}
            for rule, count in sorted(rule_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        ]


# 全局告警管理器实例
alert_manager = AlertManager()


# 预定义的告警规则
def setup_default_alert_rules():
    """设置默认告警规则。"""
    # CPU使用率告警
    alert_manager.add_rule(AlertRule(
        name="high_cpu_usage",
        description="CPU使用率过高",
        metric_name="system_cpu_percent",
        condition="gt",
        threshold=80.0,
        severity=AlertSeverity.WARNING,
        duration=300,
        cooldown=1800
    ))
    
    # 内存使用率告警
    alert_manager.add_rule(AlertRule(
        name="high_memory_usage",
        description="内存使用率过高",
        metric_name="system_memory_percent",
        condition="gt",
        threshold=85.0,
        severity=AlertSeverity.ERROR,
        duration=180,
        cooldown=1800
    ))
    
    # 磁盘使用率告警
    alert_manager.add_rule(AlertRule(
        name="high_disk_usage",
        description="磁盘使用率过高",
        metric_name="system_disk_usage_percent",
        condition="gt",
        threshold=90.0,
        severity=AlertSeverity.CRITICAL,
        duration=60,
        cooldown=3600
    ))


# 示例使用
if __name__ == "__main__":
    # 设置默认告警规则
    setup_default_alert_rules()
    
    # 添加Slack通知渠道（需要实际的Webhook URL）
    # slack_channel = SlackNotificationChannel("https://hooks.slack.com/services/...")
    # alert_manager.add_notification_channel(slack_channel)
    
    # 模拟指标检查
    alert_manager.check_metric("system_cpu_percent", 85.0)
    alert_manager.check_metric("system_memory_percent", 90.0)
    
    # 获取告警统计
    stats = alert_manager.get_alert_statistics()
    print("告警统计:", json.dumps(stats, indent=2, ensure_ascii=False))
    
    print("告警系统示例完成")
