"""
日志系统模块。

提供统一的日志配置和管理功能。
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime
# import structlog  # 可选依赖


class JSONFormatter(logging.Formatter):
    """JSON格式化器。"""
    
    def format(self, record):
        """格式化日志记录为JSON。"""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class ContextFilter(logging.Filter):
    """上下文过滤器，添加额外的上下文信息。"""
    
    def filter(self, record):
        """添加上下文信息到日志记录。"""
        # 添加进程和线程信息
        record.process_name = record.processName
        record.thread_name = record.threadName
        
        # 添加环境信息
        record.environment = os.getenv('QUANTSTRAT_ENV', 'development')
        
        return True


class QuantstratLogger:
    """量化策略工厂日志器。"""

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(name)

    def _format_message(self, message: str, **kwargs) -> str:
        """格式化消息。"""
        if kwargs:
            extra_info = " ".join([f"{k}={v}" for k, v in kwargs.items()])
            return f"{message} [{extra_info}]"
        return message

    def info(self, message: str, **kwargs):
        """记录信息日志。"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.info(formatted_message)

    def warning(self, message: str, **kwargs):
        """记录警告日志。"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.warning(formatted_message)

    def error(self, message: str, **kwargs):
        """记录错误日志。"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.error(formatted_message)

    def debug(self, message: str, **kwargs):
        """记录调试日志。"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.debug(formatted_message)

    def critical(self, message: str, **kwargs):
        """记录严重错误日志。"""
        formatted_message = self._format_message(message, **kwargs)
        self.logger.critical(formatted_message)


def setup_logging(
    level: str = "INFO",
    log_file: Optional[str] = None,
    log_format: str = "json",
    max_file_size: int = 100 * 1024 * 1024,  # 100MB
    backup_count: int = 5
) -> None:
    """
    设置日志系统。
    
    Args:
        level: 日志级别
        log_file: 日志文件路径
        log_format: 日志格式 ('json' 或 'text')
        max_file_size: 最大文件大小（字节）
        backup_count: 备份文件数量
    """
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置日志级别
    log_level = getattr(logging, level.upper())
    root_logger.setLevel(log_level)
    
    # 创建格式化器
    if log_format == "json":
        formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    console_handler.addFilter(ContextFilter())
    root_logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        file_handler.addFilter(ContextFilter())
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    
    logging.info("日志系统初始化完成")


def get_logger(name: str) -> QuantstratLogger:
    """
    获取日志器实例。
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    return QuantstratLogger(name)


class LogAnalyzer:
    """日志分析器。"""
    
    def __init__(self, log_file: str):
        self.log_file = log_file
    
    def analyze_errors(self, hours: int = 24) -> Dict[str, Any]:
        """分析错误日志。"""
        error_count = 0
        warning_count = 0
        error_types = {}
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        
                        # 检查时间范围
                        log_time = datetime.fromisoformat(log_entry['timestamp'])
                        if (datetime.now() - log_time).total_seconds() > hours * 3600:
                            continue
                        
                        level = log_entry.get('level', '')
                        if level == 'ERROR':
                            error_count += 1
                            module = log_entry.get('module', 'unknown')
                            error_types[module] = error_types.get(module, 0) + 1
                        elif level == 'WARNING':
                            warning_count += 1
                    
                    except (json.JSONDecodeError, KeyError):
                        continue
        
        except FileNotFoundError:
            return {"error": "日志文件不存在"}
        
        return {
            'error_count': error_count,
            'warning_count': warning_count,
            'error_types': error_types,
            'analysis_period_hours': hours
        }
    
    def get_performance_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能指标。"""
        execution_times = []
        memory_usages = []
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line in f:
                    try:
                        log_entry = json.loads(line.strip())
                        
                        # 检查时间范围
                        log_time = datetime.fromisoformat(log_entry['timestamp'])
                        if (datetime.now() - log_time).total_seconds() > hours * 3600:
                            continue
                        
                        # 提取性能数据
                        if 'execution_time' in log_entry:
                            execution_times.append(log_entry['execution_time'])
                        
                        if 'memory_usage' in log_entry:
                            memory_usages.append(log_entry['memory_usage'])
                    
                    except (json.JSONDecodeError, KeyError):
                        continue
        
        except FileNotFoundError:
            return {"error": "日志文件不存在"}
        
        import statistics
        
        metrics = {}
        
        if execution_times:
            metrics['execution_time'] = {
                'mean': statistics.mean(execution_times),
                'median': statistics.median(execution_times),
                'max': max(execution_times),
                'min': min(execution_times)
            }
        
        if memory_usages:
            metrics['memory_usage'] = {
                'mean': statistics.mean(memory_usages),
                'median': statistics.median(memory_usages),
                'max': max(memory_usages),
                'min': min(memory_usages)
            }
        
        return metrics


# 示例使用
if __name__ == "__main__":
    # 设置日志系统
    setup_logging(
        level="INFO",
        log_file="logs/quantstrat.log",
        log_format="json"
    )
    
    # 获取日志器
    logger = get_logger("test_module")
    
    # 记录不同类型的日志
    logger.info("系统启动", component="main", version="1.0.0")
    logger.warning("配置文件缺失", config_file="config.yaml")
    logger.error("数据库连接失败", database="postgresql", error_code=1001)
    
    # 性能日志
    logger.info("函数执行完成", 
               function="calculate_factors",
               execution_time=2.5,
               memory_usage=150.2)
    
    print("日志示例完成")
