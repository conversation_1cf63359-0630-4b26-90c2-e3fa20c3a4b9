"""
指标收集和监控模块。

提供系统性能指标收集、存储和分析功能。
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json
from pathlib import Path
import queue
import logging


logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """指标数据点。"""
    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return {
            'name': self.name,
            'value': self.value,
            'timestamp': self.timestamp.isoformat(),
            'tags': self.tags
        }


@dataclass
class SystemMetrics:
    """系统指标。"""
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return {
            'cpu_percent': self.cpu_percent,
            'memory_percent': self.memory_percent,
            'memory_used_mb': self.memory_used_mb,
            'memory_available_mb': self.memory_available_mb,
            'disk_usage_percent': self.disk_usage_percent,
            'disk_free_gb': self.disk_free_gb,
            'network_bytes_sent': self.network_bytes_sent,
            'network_bytes_recv': self.network_bytes_recv,
            'timestamp': self.timestamp.isoformat()
        }


class MetricsCollector:
    """指标收集器。"""
    
    def __init__(self, 
                 collection_interval: int = 60,
                 storage_path: Optional[str] = None):
        """
        初始化指标收集器。
        
        Args:
            collection_interval: 收集间隔（秒）
            storage_path: 存储路径
        """
        self.collection_interval = collection_interval
        self.storage_path = storage_path
        self.metrics_queue = queue.Queue()
        self.custom_metrics = {}
        self.is_running = False
        self.collection_thread = None
        self.storage_thread = None
        
        # 网络统计基线
        self._network_baseline = None
    
    def start(self):
        """启动指标收集。"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动收集线程
        self.collection_thread = threading.Thread(
            target=self._collection_loop,
            daemon=True
        )
        self.collection_thread.start()
        
        # 启动存储线程
        if self.storage_path:
            self.storage_thread = threading.Thread(
                target=self._storage_loop,
                daemon=True
            )
            self.storage_thread.start()
        
        logger.info("指标收集器已启动")
    
    def stop(self):
        """停止指标收集。"""
        self.is_running = False
        
        if self.collection_thread:
            self.collection_thread.join(timeout=5)
        
        if self.storage_thread:
            self.storage_thread.join(timeout=5)
        
        logger.info("指标收集器已停止")
    
    def collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标。"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_used_mb = memory.used / 1024 / 1024
        memory_available_mb = memory.available / 1024 / 1024
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        disk_usage_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / 1024 / 1024 / 1024
        
        # 网络使用情况
        network = psutil.net_io_counters()
        if self._network_baseline is None:
            self._network_baseline = network
        
        network_bytes_sent = network.bytes_sent - self._network_baseline.bytes_sent
        network_bytes_recv = network.bytes_recv - self._network_baseline.bytes_recv
        
        return SystemMetrics(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            memory_used_mb=memory_used_mb,
            memory_available_mb=memory_available_mb,
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=network_bytes_sent,
            network_bytes_recv=network_bytes_recv,
            timestamp=datetime.now()
        )
    
    def record_metric(self, 
                     name: str, 
                     value: float, 
                     tags: Optional[Dict[str, str]] = None):
        """记录自定义指标。"""
        metric = MetricPoint(
            name=name,
            value=value,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        self.metrics_queue.put(metric)
    
    def record_execution_time(self, name: str):
        """执行时间记录装饰器。"""
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    execution_time = time.time() - start_time
                    self.record_metric(
                        f"{name}_execution_time",
                        execution_time,
                        {"function": func.__name__}
                    )
            return wrapper
        return decorator
    
    def record_counter(self, name: str, tags: Optional[Dict[str, str]] = None):
        """记录计数器指标。"""
        current_count = self.custom_metrics.get(name, 0)
        self.custom_metrics[name] = current_count + 1
        
        self.record_metric(name, self.custom_metrics[name], tags)
    
    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录仪表盘指标。"""
        self.custom_metrics[name] = value
        self.record_metric(name, value, tags)
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要。"""
        # 这里应该从存储中读取历史数据
        # 简化实现，返回当前状态
        current_system_metrics = self.collect_system_metrics()
        
        return {
            'system_metrics': current_system_metrics.to_dict(),
            'custom_metrics': self.custom_metrics.copy(),
            'collection_interval': self.collection_interval,
            'queue_size': self.metrics_queue.qsize()
        }
    
    def _collection_loop(self):
        """指标收集循环。"""
        while self.is_running:
            try:
                # 收集系统指标
                system_metrics = self.collect_system_metrics()
                
                # 将系统指标转换为MetricPoint并加入队列
                for key, value in system_metrics.to_dict().items():
                    if key != 'timestamp' and isinstance(value, (int, float)):
                        metric = MetricPoint(
                            name=f"system_{key}",
                            value=float(value),
                            timestamp=system_metrics.timestamp,
                            tags={"type": "system"}
                        )
                        self.metrics_queue.put(metric)
                
                time.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"指标收集失败: {e}")
                time.sleep(self.collection_interval)
    
    def _storage_loop(self):
        """指标存储循环。"""
        if not self.storage_path:
            return
        
        storage_file = Path(self.storage_path)
        storage_file.parent.mkdir(parents=True, exist_ok=True)
        
        while self.is_running:
            try:
                # 批量处理指标
                metrics_batch = []
                
                # 收集一批指标（最多等待10秒）
                end_time = time.time() + 10
                while time.time() < end_time and len(metrics_batch) < 100:
                    try:
                        metric = self.metrics_queue.get(timeout=1)
                        metrics_batch.append(metric.to_dict())
                    except queue.Empty:
                        break
                
                # 写入文件
                if metrics_batch:
                    with open(storage_file, 'a', encoding='utf-8') as f:
                        for metric in metrics_batch:
                            f.write(json.dumps(metric, ensure_ascii=False) + '\n')
                
            except Exception as e:
                logger.error(f"指标存储失败: {e}")
                time.sleep(5)


class PerformanceTracker:
    """性能追踪器。"""
    
    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector
        self.active_timers = {}
    
    def start_timer(self, name: str, tags: Optional[Dict[str, str]] = None):
        """开始计时。"""
        self.active_timers[name] = {
            'start_time': time.time(),
            'tags': tags or {}
        }
    
    def end_timer(self, name: str) -> float:
        """结束计时并记录指标。"""
        if name not in self.active_timers:
            logger.warning(f"计时器 {name} 未启动")
            return 0.0
        
        timer_info = self.active_timers.pop(name)
        duration = time.time() - timer_info['start_time']
        
        self.metrics_collector.record_metric(
            f"{name}_duration",
            duration,
            timer_info['tags']
        )
        
        return duration
    
    def track_function_performance(self, name: str, tags: Optional[Dict[str, str]] = None):
        """函数性能追踪装饰器。"""
        def decorator(func: Callable):
            def wrapper(*args, **kwargs):
                timer_name = f"{name}_{func.__name__}"
                self.start_timer(timer_name, tags)
                
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    self.end_timer(timer_name)
            
            return wrapper
        return decorator


# 全局指标收集器实例
metrics_collector = MetricsCollector(
    collection_interval=60,
    storage_path="logs/metrics.jsonl"
)

# 全局性能追踪器实例
performance_tracker = PerformanceTracker(metrics_collector)


def track_performance(name: str, tags: Optional[Dict[str, str]] = None):
    """性能追踪装饰器。"""
    return performance_tracker.track_function_performance(name, tags)


def record_metric(name: str, value: float, tags: Optional[Dict[str, str]] = None):
    """记录指标的便捷函数。"""
    metrics_collector.record_metric(name, value, tags)


# 示例使用
if __name__ == "__main__":
    # 启动指标收集
    metrics_collector.start()
    
    # 记录一些自定义指标
    metrics_collector.record_counter("api_requests", {"endpoint": "/health"})
    metrics_collector.record_gauge("active_users", 150.0)
    
    # 使用性能追踪装饰器
    @track_performance("data_processing")
    def process_data():
        time.sleep(0.1)  # 模拟处理时间
        return "processed"
    
    # 执行函数
    result = process_data()
    
    # 获取指标摘要
    summary = metrics_collector.get_metrics_summary()
    print("指标摘要:", json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 停止收集
    time.sleep(2)
    metrics_collector.stop()
    
    print("指标收集示例完成")
