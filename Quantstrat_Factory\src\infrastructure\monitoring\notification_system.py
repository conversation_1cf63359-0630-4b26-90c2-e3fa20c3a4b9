"""
通知系统模块。

提供多渠道通知功能，包括邮件、短信、Slack等。
"""

import logging
import smtplib
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from abc import ABC, abstractmethod


logger = logging.getLogger(__name__)


@dataclass
class Notification:
    """通知消息。"""
    title: str
    message: str
    level: str = "info"  # info, warning, error, critical
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


class NotificationChannel(ABC):
    """通知渠道基类。"""
    
    @abstractmethod
    def send(self, notification: Notification) -> bool:
        """发送通知。"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查渠道是否可用。"""
        pass


class EmailChannel(NotificationChannel):
    """邮件通知渠道。"""
    
    def __init__(self, 
                 smtp_server: str,
                 smtp_port: int,
                 username: str,
                 password: str,
                 from_email: str,
                 to_emails: List[str]):
        """
        初始化邮件渠道。
        
        Args:
            smtp_server: SMTP服务器
            smtp_port: SMTP端口
            username: 用户名
            password: 密码
            from_email: 发件人邮箱
            to_emails: 收件人邮箱列表
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
    
    def send(self, notification: Notification) -> bool:
        """发送邮件通知。"""
        try:
            # 创建邮件
            msg = MimeMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = f"[{notification.level.upper()}] {notification.title}"
            
            # 邮件正文
            body = f"""
时间: {notification.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
级别: {notification.level.upper()}
标题: {notification.title}

消息:
{notification.message}

元数据:
{notification.metadata}
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"邮件通知发送成功: {notification.title}")
            return True
            
        except Exception as e:
            logger.error(f"邮件通知发送失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """检查邮件渠道是否可用。"""
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
            return True
        except Exception:
            return False


class SlackChannel(NotificationChannel):
    """Slack通知渠道。"""
    
    def __init__(self, webhook_url: str, channel: str = "#alerts"):
        """
        初始化Slack渠道。
        
        Args:
            webhook_url: Webhook URL
            channel: 频道名称
        """
        self.webhook_url = webhook_url
        self.channel = channel
    
    def send(self, notification: Notification) -> bool:
        """发送Slack通知。"""
        try:
            import requests
            
            # 根据级别选择颜色
            color_map = {
                "info": "#36a64f",      # 绿色
                "warning": "#ff9500",   # 橙色
                "error": "#ff0000",     # 红色
                "critical": "#8b0000"   # 深红色
            }
            
            payload = {
                "channel": self.channel,
                "username": "Quantstrat Bot",
                "icon_emoji": ":robot_face:",
                "attachments": [
                    {
                        "color": color_map.get(notification.level, "#ff0000"),
                        "title": notification.title,
                        "text": notification.message,
                        "fields": [
                            {
                                "title": "级别",
                                "value": notification.level.upper(),
                                "short": True
                            },
                            {
                                "title": "时间",
                                "value": notification.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                                "short": True
                            }
                        ],
                        "footer": "Quantstrat Factory",
                        "ts": int(notification.timestamp.timestamp())
                    }
                ]
            }
            
            response = requests.post(self.webhook_url, json=payload, timeout=10)
            
            if response.status_code == 200:
                logger.info(f"Slack通知发送成功: {notification.title}")
                return True
            else:
                logger.error(f"Slack通知发送失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Slack通知发送失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """检查Slack渠道是否可用。"""
        try:
            import requests
            # 发送测试消息
            test_payload = {"text": "连接测试"}
            response = requests.post(self.webhook_url, json=test_payload, timeout=5)
            return response.status_code == 200
        except Exception:
            return False


class ConsoleChannel(NotificationChannel):
    """控制台通知渠道。"""
    
    def send(self, notification: Notification) -> bool:
        """发送控制台通知。"""
        try:
            level_symbols = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "❌",
                "critical": "🚨"
            }
            
            symbol = level_symbols.get(notification.level, "📢")
            timestamp = notification.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            
            print(f"\n{symbol} [{timestamp}] {notification.level.upper()}: {notification.title}")
            print(f"   {notification.message}")
            
            if notification.metadata:
                print(f"   元数据: {notification.metadata}")
            
            return True
            
        except Exception as e:
            logger.error(f"控制台通知发送失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """控制台渠道总是可用。"""
        return True


class FileChannel(NotificationChannel):
    """文件通知渠道。"""
    
    def __init__(self, file_path: str):
        """
        初始化文件渠道。
        
        Args:
            file_path: 文件路径
        """
        self.file_path = file_path
    
    def send(self, notification: Notification) -> bool:
        """发送文件通知。"""
        try:
            import json
            
            notification_data = {
                "timestamp": notification.timestamp.isoformat(),
                "level": notification.level,
                "title": notification.title,
                "message": notification.message,
                "metadata": notification.metadata
            }
            
            with open(self.file_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(notification_data, ensure_ascii=False) + '\n')
            
            return True
            
        except Exception as e:
            logger.error(f"文件通知发送失败: {e}")
            return False
    
    def is_available(self) -> bool:
        """检查文件渠道是否可用。"""
        try:
            # 尝试写入测试
            with open(self.file_path, 'a', encoding='utf-8') as f:
                pass
            return True
        except Exception:
            return False


class NotificationSystem:
    """通知系统。"""
    
    def __init__(self):
        """初始化通知系统。"""
        self.channels: Dict[str, NotificationChannel] = {}
        self.rules: List[Dict[str, Any]] = []
        self.notification_history: List[Notification] = []
        self.max_history_size = 1000
    
    def add_channel(self, name: str, channel: NotificationChannel):
        """
        添加通知渠道。
        
        Args:
            name: 渠道名称
            channel: 通知渠道实例
        """
        self.channels[name] = channel
        logger.info(f"添加通知渠道: {name}")
    
    def remove_channel(self, name: str):
        """移除通知渠道。"""
        if name in self.channels:
            del self.channels[name]
            logger.info(f"移除通知渠道: {name}")
    
    def add_rule(self, 
                 name: str,
                 level_filter: Optional[List[str]] = None,
                 title_pattern: Optional[str] = None,
                 channels: Optional[List[str]] = None):
        """
        添加通知规则。
        
        Args:
            name: 规则名称
            level_filter: 级别过滤器
            title_pattern: 标题模式
            channels: 使用的渠道列表
        """
        rule = {
            'name': name,
            'level_filter': level_filter or [],
            'title_pattern': title_pattern,
            'channels': channels or list(self.channels.keys())
        }
        
        self.rules.append(rule)
        logger.info(f"添加通知规则: {name}")
    
    def send_notification(self, 
                         title: str,
                         message: str,
                         level: str = "info",
                         metadata: Optional[Dict[str, Any]] = None,
                         channels: Optional[List[str]] = None) -> bool:
        """
        发送通知。
        
        Args:
            title: 通知标题
            message: 通知消息
            level: 通知级别
            metadata: 元数据
            channels: 指定渠道列表
            
        Returns:
            是否发送成功
        """
        notification = Notification(
            title=title,
            message=message,
            level=level,
            metadata=metadata or {}
        )
        
        # 记录历史
        self.notification_history.append(notification)
        if len(self.notification_history) > self.max_history_size:
            self.notification_history.pop(0)
        
        # 确定使用的渠道
        target_channels = channels or self._get_channels_for_notification(notification)
        
        success_count = 0
        total_count = len(target_channels)
        
        for channel_name in target_channels:
            if channel_name not in self.channels:
                logger.warning(f"通知渠道不存在: {channel_name}")
                continue
            
            channel = self.channels[channel_name]
            
            if not channel.is_available():
                logger.warning(f"通知渠道不可用: {channel_name}")
                continue
            
            try:
                if channel.send(notification):
                    success_count += 1
            except Exception as e:
                logger.error(f"通知发送失败 {channel_name}: {e}")
        
        success_rate = success_count / total_count if total_count > 0 else 0
        logger.info(f"通知发送完成: {success_count}/{total_count} 成功")
        
        return success_rate > 0.5  # 超过一半的渠道成功即认为成功
    
    def _get_channels_for_notification(self, notification: Notification) -> List[str]:
        """根据规则获取通知渠道。"""
        target_channels = set()
        
        for rule in self.rules:
            # 检查级别过滤
            if rule['level_filter'] and notification.level not in rule['level_filter']:
                continue
            
            # 检查标题模式
            if rule['title_pattern']:
                import re
                if not re.search(rule['title_pattern'], notification.title):
                    continue
            
            # 添加渠道
            target_channels.update(rule['channels'])
        
        # 如果没有匹配的规则，使用所有渠道
        if not target_channels:
            target_channels = set(self.channels.keys())
        
        return list(target_channels)
    
    def get_notification_history(self, 
                               level: Optional[str] = None,
                               hours: int = 24) -> List[Notification]:
        """
        获取通知历史。
        
        Args:
            level: 级别过滤
            hours: 时间范围（小时）
            
        Returns:
            通知历史列表
        """
        from datetime import timedelta
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        filtered_notifications = []
        for notification in self.notification_history:
            if notification.timestamp < cutoff_time:
                continue
            
            if level and notification.level != level:
                continue
            
            filtered_notifications.append(notification)
        
        return filtered_notifications
    
    def get_notification_stats(self, hours: int = 24) -> Dict[str, Any]:
        """获取通知统计。"""
        recent_notifications = self.get_notification_history(hours=hours)
        
        level_counts = {}
        for notification in recent_notifications:
            level = notification.level
            level_counts[level] = level_counts.get(level, 0) + 1
        
        return {
            'total_notifications': len(recent_notifications),
            'level_breakdown': level_counts,
            'active_channels': len(self.channels),
            'available_channels': len([
                name for name, channel in self.channels.items()
                if channel.is_available()
            ])
        }


# 便捷函数
def create_notification_system() -> NotificationSystem:
    """创建默认通知系统。"""
    system = NotificationSystem()
    
    # 添加控制台渠道
    system.add_channel("console", ConsoleChannel())
    
    # 添加文件渠道
    system.add_channel("file", FileChannel("logs/notifications.jsonl"))
    
    # 添加默认规则
    system.add_rule(
        name="critical_alerts",
        level_filter=["critical", "error"],
        channels=["console", "file"]
    )
    
    system.add_rule(
        name="all_notifications",
        channels=["file"]
    )
    
    return system


# 示例使用
if __name__ == "__main__":
    # 创建通知系统
    notification_system = create_notification_system()
    
    # 发送测试通知
    notification_system.send_notification(
        title="系统启动",
        message="Quantstrat Factory系统已成功启动",
        level="info",
        metadata={"version": "1.0.0", "environment": "development"}
    )
    
    notification_system.send_notification(
        title="策略执行警告",
        message="策略执行时间超过预期",
        level="warning",
        metadata={"strategy_id": "strategy_001", "execution_time": 5.2}
    )
    
    notification_system.send_notification(
        title="系统错误",
        message="数据库连接失败",
        level="error",
        metadata={"error_code": "DB_CONNECTION_FAILED"}
    )
    
    # 获取统计信息
    stats = notification_system.get_notification_stats()
    print(f"通知统计: {stats}")
    
    print("✅ 通知系统示例完成")
