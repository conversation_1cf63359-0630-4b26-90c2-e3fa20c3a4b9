"""
代码分析器模块。

提供代码质量分析、复杂度计算和质量指标统计功能。
"""

import ast
import os
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from pathlib import Path
import logging


logger = logging.getLogger(__name__)


@dataclass
class QualityMetrics:
    """代码质量指标。"""
    lines_of_code: int = 0
    lines_of_comments: int = 0
    blank_lines: int = 0
    cyclomatic_complexity: int = 0
    cognitive_complexity: int = 0
    maintainability_index: float = 0.0
    test_coverage: float = 0.0
    duplication_ratio: float = 0.0
    technical_debt_ratio: float = 0.0
    
    @property
    def comment_ratio(self) -> float:
        """注释比例。"""
        total_lines = self.lines_of_code + self.lines_of_comments
        return self.lines_of_comments / total_lines if total_lines > 0 else 0.0
    
    @property
    def code_density(self) -> float:
        """代码密度。"""
        total_lines = self.lines_of_code + self.lines_of_comments + self.blank_lines
        return self.lines_of_code / total_lines if total_lines > 0 else 0.0


class ComplexityAnalyzer(ast.NodeVisitor):
    """复杂度分析器。"""
    
    def __init__(self):
        self.cyclomatic_complexity = 1  # 基础复杂度
        self.cognitive_complexity = 0
        self.nesting_level = 0
        self.function_count = 0
        self.class_count = 0
    
    def visit_FunctionDef(self, node):
        """访问函数定义。"""
        self.function_count += 1
        old_complexity = self.cyclomatic_complexity
        old_cognitive = self.cognitive_complexity
        old_nesting = self.nesting_level
        
        # 重置函数级别的复杂度
        self.cyclomatic_complexity = 1
        self.cognitive_complexity = 0
        self.nesting_level = 0
        
        self.generic_visit(node)
        
        # 记录函数复杂度
        func_cyclomatic = self.cyclomatic_complexity
        func_cognitive = self.cognitive_complexity
        
        # 恢复上级复杂度
        self.cyclomatic_complexity = old_complexity + func_cyclomatic
        self.cognitive_complexity = old_cognitive + func_cognitive
        self.nesting_level = old_nesting
    
    def visit_ClassDef(self, node):
        """访问类定义。"""
        self.class_count += 1
        self.generic_visit(node)
    
    def visit_If(self, node):
        """访问if语句。"""
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.nesting_level
        
        self.nesting_level += 1
        self.generic_visit(node)
        self.nesting_level -= 1
    
    def visit_While(self, node):
        """访问while循环。"""
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.nesting_level
        
        self.nesting_level += 1
        self.generic_visit(node)
        self.nesting_level -= 1
    
    def visit_For(self, node):
        """访问for循环。"""
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.nesting_level
        
        self.nesting_level += 1
        self.generic_visit(node)
        self.nesting_level -= 1
    
    def visit_ExceptHandler(self, node):
        """访问异常处理。"""
        self.cyclomatic_complexity += 1
        self.cognitive_complexity += 1 + self.nesting_level
        
        self.nesting_level += 1
        self.generic_visit(node)
        self.nesting_level -= 1
    
    def visit_With(self, node):
        """访问with语句。"""
        self.cognitive_complexity += 1 + self.nesting_level
        
        self.nesting_level += 1
        self.generic_visit(node)
        self.nesting_level -= 1


class CodeAnalyzer:
    """代码分析器。"""
    
    def __init__(self, project_root: str):
        """
        初始化代码分析器。
        
        Args:
            project_root: 项目根目录
        """
        self.project_root = Path(project_root)
        self.python_files: List[Path] = []
        self.analysis_results: Dict[str, QualityMetrics] = {}
    
    def scan_project(self, exclude_patterns: Optional[List[str]] = None) -> List[Path]:
        """
        扫描项目中的Python文件。
        
        Args:
            exclude_patterns: 排除模式列表
            
        Returns:
            Python文件列表
        """
        exclude_patterns = exclude_patterns or [
            '__pycache__',
            '.git',
            '.pytest_cache',
            'venv',
            'env',
            '.venv'
        ]
        
        python_files = []
        
        for py_file in self.project_root.rglob("*.py"):
            # 检查是否应该排除
            should_exclude = False
            for pattern in exclude_patterns:
                if pattern in str(py_file):
                    should_exclude = True
                    break
            
            if not should_exclude:
                python_files.append(py_file)
        
        self.python_files = python_files
        return python_files
    
    def analyze_file(self, file_path: Path) -> QualityMetrics:
        """
        分析单个文件。
        
        Args:
            file_path: 文件路径
            
        Returns:
            质量指标
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基础行数统计
            lines = content.split('\n')
            lines_of_code = 0
            lines_of_comments = 0
            blank_lines = 0
            
            for line in lines:
                stripped = line.strip()
                if not stripped:
                    blank_lines += 1
                elif stripped.startswith('#'):
                    lines_of_comments += 1
                else:
                    lines_of_code += 1
                    # 检查行内注释
                    if '#' in line:
                        lines_of_comments += 0.5  # 行内注释算半行
            
            # 复杂度分析
            try:
                tree = ast.parse(content)
                complexity_analyzer = ComplexityAnalyzer()
                complexity_analyzer.visit(tree)
                
                cyclomatic_complexity = complexity_analyzer.cyclomatic_complexity
                cognitive_complexity = complexity_analyzer.cognitive_complexity
            except SyntaxError:
                logger.warning(f"语法错误，跳过复杂度分析: {file_path}")
                cyclomatic_complexity = 0
                cognitive_complexity = 0
            
            # 可维护性指数计算（简化版）
            maintainability_index = self._calculate_maintainability_index(
                lines_of_code, cyclomatic_complexity, lines_of_comments
            )
            
            metrics = QualityMetrics(
                lines_of_code=int(lines_of_code),
                lines_of_comments=int(lines_of_comments),
                blank_lines=blank_lines,
                cyclomatic_complexity=cyclomatic_complexity,
                cognitive_complexity=cognitive_complexity,
                maintainability_index=maintainability_index
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"分析文件失败 {file_path}: {e}")
            return QualityMetrics()
    
    def analyze_project(self) -> Dict[str, QualityMetrics]:
        """
        分析整个项目。
        
        Returns:
            文件质量指标字典
        """
        if not self.python_files:
            self.scan_project()
        
        results = {}
        
        for file_path in self.python_files:
            relative_path = str(file_path.relative_to(self.project_root))
            metrics = self.analyze_file(file_path)
            results[relative_path] = metrics
        
        self.analysis_results = results
        return results
    
    def get_project_summary(self) -> QualityMetrics:
        """
        获取项目质量摘要。
        
        Returns:
            项目整体质量指标
        """
        if not self.analysis_results:
            self.analyze_project()
        
        total_metrics = QualityMetrics()
        
        for metrics in self.analysis_results.values():
            total_metrics.lines_of_code += metrics.lines_of_code
            total_metrics.lines_of_comments += metrics.lines_of_comments
            total_metrics.blank_lines += metrics.blank_lines
            total_metrics.cyclomatic_complexity += metrics.cyclomatic_complexity
            total_metrics.cognitive_complexity += metrics.cognitive_complexity
        
        # 计算平均可维护性指数
        if self.analysis_results:
            total_metrics.maintainability_index = sum(
                m.maintainability_index for m in self.analysis_results.values()
            ) / len(self.analysis_results)
        
        return total_metrics
    
    def get_top_complex_files(self, limit: int = 10) -> List[Tuple[str, QualityMetrics]]:
        """
        获取复杂度最高的文件。
        
        Args:
            limit: 返回文件数量限制
            
        Returns:
            复杂度最高的文件列表
        """
        if not self.analysis_results:
            self.analyze_project()
        
        sorted_files = sorted(
            self.analysis_results.items(),
            key=lambda x: x[1].cyclomatic_complexity,
            reverse=True
        )
        
        return sorted_files[:limit]
    
    def get_quality_issues(self) -> Dict[str, List[str]]:
        """
        获取质量问题。
        
        Returns:
            质量问题字典
        """
        if not self.analysis_results:
            self.analyze_project()
        
        issues = {}
        
        for file_path, metrics in self.analysis_results.items():
            file_issues = []
            
            # 检查复杂度
            if metrics.cyclomatic_complexity > 10:
                file_issues.append(f"圈复杂度过高: {metrics.cyclomatic_complexity}")
            
            if metrics.cognitive_complexity > 15:
                file_issues.append(f"认知复杂度过高: {metrics.cognitive_complexity}")
            
            # 检查可维护性
            if metrics.maintainability_index < 20:
                file_issues.append(f"可维护性指数过低: {metrics.maintainability_index:.1f}")
            
            # 检查注释比例
            if metrics.comment_ratio < 0.1:
                file_issues.append(f"注释比例过低: {metrics.comment_ratio:.1%}")
            
            # 检查文件大小
            if metrics.lines_of_code > 500:
                file_issues.append(f"文件过大: {metrics.lines_of_code} 行代码")
            
            if file_issues:
                issues[file_path] = file_issues
        
        return issues
    
    def _calculate_maintainability_index(self, 
                                       lines_of_code: int,
                                       cyclomatic_complexity: int,
                                       lines_of_comments: int) -> float:
        """
        计算可维护性指数。
        
        简化版的可维护性指数计算公式。
        """
        if lines_of_code == 0:
            return 100.0
        
        # 基础分数
        base_score = 100.0
        
        # 复杂度惩罚
        complexity_penalty = cyclomatic_complexity * 2
        
        # 代码长度惩罚
        length_penalty = lines_of_code / 10
        
        # 注释奖励
        comment_bonus = (lines_of_comments / lines_of_code) * 10 if lines_of_code > 0 else 0
        
        maintainability_index = base_score - complexity_penalty - length_penalty + comment_bonus
        
        return max(0.0, min(100.0, maintainability_index))
    
    def generate_report(self, output_path: str):
        """
        生成质量报告。
        
        Args:
            output_path: 输出文件路径
        """
        if not self.analysis_results:
            self.analyze_project()
        
        summary = self.get_project_summary()
        complex_files = self.get_top_complex_files()
        issues = self.get_quality_issues()
        
        report = f"""# 代码质量分析报告

## 项目概览
- 总代码行数: {summary.lines_of_code:,}
- 总注释行数: {summary.lines_of_comments:,}
- 空白行数: {summary.blank_lines:,}
- 注释比例: {summary.comment_ratio:.1%}
- 代码密度: {summary.code_density:.1%}
- 平均可维护性指数: {summary.maintainability_index:.1f}

## 复杂度统计
- 总圈复杂度: {summary.cyclomatic_complexity}
- 总认知复杂度: {summary.cognitive_complexity}
- 平均圈复杂度: {summary.cyclomatic_complexity / len(self.analysis_results):.1f}

## 最复杂的文件 (Top 10)
"""
        
        for i, (file_path, metrics) in enumerate(complex_files, 1):
            report += f"{i}. {file_path} (圈复杂度: {metrics.cyclomatic_complexity})\n"
        
        report += "\n## 质量问题\n"
        
        if issues:
            for file_path, file_issues in issues.items():
                report += f"\n### {file_path}\n"
                for issue in file_issues:
                    report += f"- {issue}\n"
        else:
            report += "未发现质量问题。\n"
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"质量报告已生成: {output_path}")


# 示例使用
if __name__ == "__main__":
    # 分析当前项目
    analyzer = CodeAnalyzer(".")
    analyzer.scan_project()
    
    # 分析项目
    results = analyzer.analyze_project()
    
    # 获取摘要
    summary = analyzer.get_project_summary()
    print(f"项目代码行数: {summary.lines_of_code}")
    print(f"注释比例: {summary.comment_ratio:.1%}")
    print(f"平均可维护性指数: {summary.maintainability_index:.1f}")
    
    # 获取复杂文件
    complex_files = analyzer.get_top_complex_files(5)
    print("\n最复杂的5个文件:")
    for file_path, metrics in complex_files:
        print(f"- {file_path}: 圈复杂度 {metrics.cyclomatic_complexity}")
    
    # 生成报告
    analyzer.generate_report("quality_report.md")
    
    print("✅ 代码质量分析完成")
