"""
质量门禁模块。

提供代码质量检查和质量门禁功能。
"""

from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging
from .code_analyzer import CodeAnalyzer, QualityMetrics


logger = logging.getLogger(__name__)


class QualityRuleType(Enum):
    """质量规则类型。"""
    THRESHOLD = "threshold"  # 阈值检查
    RATIO = "ratio"  # 比例检查
    CUSTOM = "custom"  # 自定义检查


class QualityLevel(Enum):
    """质量等级。"""
    EXCELLENT = "excellent"
    GOOD = "good"
    ACCEPTABLE = "acceptable"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class QualityRule:
    """质量规则。"""
    name: str
    description: str
    rule_type: QualityRuleType
    metric_name: str
    threshold: Optional[float] = None
    operator: str = "le"  # le, ge, lt, gt, eq
    severity: str = "error"  # error, warning, info
    enabled: bool = True
    custom_checker: Optional[Callable] = None
    
    def check(self, metrics: QualityMetrics) -> bool:
        """
        检查规则。
        
        Args:
            metrics: 质量指标
            
        Returns:
            是否通过检查
        """
        if not self.enabled:
            return True
        
        if self.rule_type == QualityRuleType.CUSTOM and self.custom_checker:
            return self.custom_checker(metrics)
        
        # 获取指标值
        metric_value = getattr(metrics, self.metric_name, None)
        if metric_value is None:
            logger.warning(f"指标不存在: {self.metric_name}")
            return True
        
        if self.threshold is None:
            return True
        
        # 执行比较
        if self.operator == "le":
            return metric_value <= self.threshold
        elif self.operator == "ge":
            return metric_value >= self.threshold
        elif self.operator == "lt":
            return metric_value < self.threshold
        elif self.operator == "gt":
            return metric_value > self.threshold
        elif self.operator == "eq":
            return metric_value == self.threshold
        else:
            logger.warning(f"未知操作符: {self.operator}")
            return True


class QualityGate:
    """质量门禁。"""
    
    def __init__(self, name: str = "default"):
        """
        初始化质量门禁。
        
        Args:
            name: 门禁名称
        """
        self.name = name
        self.rules: List[QualityRule] = []
        self.results: Dict[str, Any] = {}
    
    def add_rule(self, rule: QualityRule):
        """添加质量规则。"""
        self.rules.append(rule)
        logger.info(f"添加质量规则: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """移除质量规则。"""
        self.rules = [rule for rule in self.rules if rule.name != rule_name]
        logger.info(f"移除质量规则: {rule_name}")
    
    def check_file(self, file_path: str, metrics: QualityMetrics) -> Dict[str, Any]:
        """
        检查单个文件。
        
        Args:
            file_path: 文件路径
            metrics: 质量指标
            
        Returns:
            检查结果
        """
        results = {
            'file_path': file_path,
            'passed': True,
            'violations': [],
            'warnings': [],
            'info': []
        }
        
        for rule in self.rules:
            if not rule.check(metrics):
                violation = {
                    'rule_name': rule.name,
                    'description': rule.description,
                    'severity': rule.severity,
                    'metric_name': rule.metric_name,
                    'actual_value': getattr(metrics, rule.metric_name, None),
                    'threshold': rule.threshold
                }
                
                if rule.severity == "error":
                    results['violations'].append(violation)
                    results['passed'] = False
                elif rule.severity == "warning":
                    results['warnings'].append(violation)
                else:
                    results['info'].append(violation)
        
        return results
    
    def check_project(self, project_root: str) -> Dict[str, Any]:
        """
        检查整个项目。
        
        Args:
            project_root: 项目根目录
            
        Returns:
            项目检查结果
        """
        analyzer = CodeAnalyzer(project_root)
        analysis_results = analyzer.analyze_project()
        
        project_results = {
            'project_root': project_root,
            'total_files': len(analysis_results),
            'passed_files': 0,
            'failed_files': 0,
            'file_results': {},
            'project_summary': analyzer.get_project_summary(),
            'overall_passed': True
        }
        
        for file_path, metrics in analysis_results.items():
            file_result = self.check_file(file_path, metrics)
            project_results['file_results'][file_path] = file_result
            
            if file_result['passed']:
                project_results['passed_files'] += 1
            else:
                project_results['failed_files'] += 1
                project_results['overall_passed'] = False
        
        # 检查项目级别的规则
        project_metrics = project_results['project_summary']
        project_check = self.check_file("PROJECT_SUMMARY", project_metrics)
        project_results['project_check'] = project_check
        
        if not project_check['passed']:
            project_results['overall_passed'] = False
        
        self.results = project_results
        return project_results
    
    def get_quality_level(self, metrics: QualityMetrics) -> QualityLevel:
        """
        获取质量等级。
        
        Args:
            metrics: 质量指标
            
        Returns:
            质量等级
        """
        score = 0
        
        # 可维护性指数评分
        if metrics.maintainability_index >= 80:
            score += 25
        elif metrics.maintainability_index >= 60:
            score += 20
        elif metrics.maintainability_index >= 40:
            score += 15
        elif metrics.maintainability_index >= 20:
            score += 10
        else:
            score += 0
        
        # 复杂度评分
        if metrics.cyclomatic_complexity <= 5:
            score += 25
        elif metrics.cyclomatic_complexity <= 10:
            score += 20
        elif metrics.cyclomatic_complexity <= 15:
            score += 15
        elif metrics.cyclomatic_complexity <= 20:
            score += 10
        else:
            score += 0
        
        # 注释比例评分
        if metrics.comment_ratio >= 0.2:
            score += 25
        elif metrics.comment_ratio >= 0.15:
            score += 20
        elif metrics.comment_ratio >= 0.1:
            score += 15
        elif metrics.comment_ratio >= 0.05:
            score += 10
        else:
            score += 0
        
        # 代码密度评分
        if 0.6 <= metrics.code_density <= 0.8:
            score += 25
        elif 0.5 <= metrics.code_density <= 0.9:
            score += 20
        elif 0.4 <= metrics.code_density <= 0.95:
            score += 15
        else:
            score += 10
        
        # 根据总分确定等级
        if score >= 90:
            return QualityLevel.EXCELLENT
        elif score >= 75:
            return QualityLevel.GOOD
        elif score >= 60:
            return QualityLevel.ACCEPTABLE
        elif score >= 40:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def generate_report(self, output_path: str):
        """
        生成质量门禁报告。
        
        Args:
            output_path: 输出文件路径
        """
        if not self.results:
            logger.warning("没有检查结果，请先运行check_project")
            return
        
        results = self.results
        
        report = f"""# 质量门禁报告

## 概览
- 项目路径: {results['project_root']}
- 总文件数: {results['total_files']}
- 通过文件数: {results['passed_files']}
- 失败文件数: {results['failed_files']}
- 整体状态: {'✅ 通过' if results['overall_passed'] else '❌ 失败'}

## 项目质量指标
"""
        
        summary = results['project_summary']
        quality_level = self.get_quality_level(summary)
        
        report += f"""
- 代码行数: {summary.lines_of_code:,}
- 注释比例: {summary.comment_ratio:.1%}
- 可维护性指数: {summary.maintainability_index:.1f}
- 圈复杂度: {summary.cyclomatic_complexity}
- 质量等级: {quality_level.value.upper()}

## 规则检查结果
"""
        
        # 项目级别检查
        project_check = results.get('project_check', {})
        if project_check.get('violations'):
            report += "\n### 项目级别违规\n"
            for violation in project_check['violations']:
                report += f"- **{violation['rule_name']}**: {violation['description']}\n"
                report += f"  - 实际值: {violation['actual_value']}\n"
                report += f"  - 阈值: {violation['threshold']}\n"
        
        # 文件级别检查
        failed_files = [
            (path, result) for path, result in results['file_results'].items()
            if not result['passed']
        ]
        
        if failed_files:
            report += f"\n### 失败文件 ({len(failed_files)} 个)\n"
            
            for file_path, file_result in failed_files[:10]:  # 只显示前10个
                report += f"\n#### {file_path}\n"
                
                for violation in file_result['violations']:
                    report += f"- **{violation['rule_name']}**: {violation['description']}\n"
                    report += f"  - 实际值: {violation['actual_value']}\n"
                    report += f"  - 阈值: {violation['threshold']}\n"
        
        # 警告信息
        all_warnings = []
        for file_result in results['file_results'].values():
            all_warnings.extend(file_result.get('warnings', []))
        
        if all_warnings:
            report += f"\n### 警告信息 ({len(all_warnings)} 个)\n"
            
            # 按规则分组显示警告
            warning_groups = {}
            for warning in all_warnings:
                rule_name = warning['rule_name']
                if rule_name not in warning_groups:
                    warning_groups[rule_name] = []
                warning_groups[rule_name].append(warning)
            
            for rule_name, warnings in warning_groups.items():
                report += f"\n#### {rule_name} ({len(warnings)} 个文件)\n"
                for warning in warnings[:5]:  # 只显示前5个
                    report += f"- 实际值: {warning['actual_value']}, 阈值: {warning['threshold']}\n"
        
        # 保存报告
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"质量门禁报告已生成: {output_path}")


def create_default_quality_gate() -> QualityGate:
    """创建默认质量门禁。"""
    gate = QualityGate("default")
    
    # 复杂度规则
    gate.add_rule(QualityRule(
        name="max_cyclomatic_complexity",
        description="圈复杂度不应超过15",
        rule_type=QualityRuleType.THRESHOLD,
        metric_name="cyclomatic_complexity",
        threshold=15,
        operator="le",
        severity="error"
    ))
    
    gate.add_rule(QualityRule(
        name="max_cognitive_complexity",
        description="认知复杂度不应超过20",
        rule_type=QualityRuleType.THRESHOLD,
        metric_name="cognitive_complexity",
        threshold=20,
        operator="le",
        severity="error"
    ))
    
    # 可维护性规则
    gate.add_rule(QualityRule(
        name="min_maintainability_index",
        description="可维护性指数不应低于20",
        rule_type=QualityRuleType.THRESHOLD,
        metric_name="maintainability_index",
        threshold=20,
        operator="ge",
        severity="warning"
    ))
    
    # 注释规则
    gate.add_rule(QualityRule(
        name="min_comment_ratio",
        description="注释比例不应低于5%",
        rule_type=QualityRuleType.THRESHOLD,
        metric_name="comment_ratio",
        threshold=0.05,
        operator="ge",
        severity="warning"
    ))
    
    # 文件大小规则
    gate.add_rule(QualityRule(
        name="max_lines_of_code",
        description="单文件代码行数不应超过500行",
        rule_type=QualityRuleType.THRESHOLD,
        metric_name="lines_of_code",
        threshold=500,
        operator="le",
        severity="warning"
    ))
    
    return gate


# 示例使用
if __name__ == "__main__":
    # 创建质量门禁
    gate = create_default_quality_gate()
    
    # 检查项目
    results = gate.check_project(".")
    
    print(f"项目检查结果: {'通过' if results['overall_passed'] else '失败'}")
    print(f"总文件数: {results['total_files']}")
    print(f"通过文件数: {results['passed_files']}")
    print(f"失败文件数: {results['failed_files']}")
    
    # 生成报告
    gate.generate_report("quality_gate_report.md")
    
    print("✅ 质量门禁检查完成")
