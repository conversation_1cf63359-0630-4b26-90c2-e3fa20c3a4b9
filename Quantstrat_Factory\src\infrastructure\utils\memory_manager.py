"""
内存管理优化模块。

提供内存使用优化、数据流式处理和内存监控功能。
"""

import gc
import psutil
import pandas as pd
import numpy as np
from typing import Iterator, Optional, Dict, Any, List, Callable, Union
import logging
import threading
import time
from pathlib import Path
import pickle
import tempfile
import os
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime


logger = logging.getLogger(__name__)


@dataclass
class MemoryUsage:
    """内存使用情况。"""
    rss_mb: float  # 物理内存
    vms_mb: float  # 虚拟内存
    percent: float  # 内存使用百分比
    available_mb: float  # 可用内存
    timestamp: datetime


class MemoryMonitor:
    """内存监控器。"""
    
    def __init__(self, warning_threshold: float = 80.0, critical_threshold: float = 90.0):
        """
        初始化内存监控器。
        
        Args:
            warning_threshold: 警告阈值（百分比）
            critical_threshold: 严重阈值（百分比）
        """
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.usage_history: List[MemoryUsage] = []
        self.max_history_size = 1000
    
    def get_current_usage(self) -> MemoryUsage:
        """获取当前内存使用情况。"""
        process = psutil.Process()
        memory_info = process.memory_info()
        system_memory = psutil.virtual_memory()
        
        usage = MemoryUsage(
            rss_mb=memory_info.rss / 1024 / 1024,
            vms_mb=memory_info.vms / 1024 / 1024,
            percent=process.memory_percent(),
            available_mb=system_memory.available / 1024 / 1024,
            timestamp=datetime.now()
        )
        
        # 记录历史
        self.usage_history.append(usage)
        if len(self.usage_history) > self.max_history_size:
            self.usage_history.pop(0)
        
        # 检查阈值
        self._check_thresholds(usage)
        
        return usage
    
    def _check_thresholds(self, usage: MemoryUsage):
        """检查内存使用阈值。"""
        if usage.percent > self.critical_threshold:
            logger.critical(f"内存使用严重告警: {usage.percent:.1f}% (阈值: {self.critical_threshold}%)")
        elif usage.percent > self.warning_threshold:
            logger.warning(f"内存使用警告: {usage.percent:.1f}% (阈值: {self.warning_threshold}%)")
    
    def get_peak_usage(self) -> Optional[MemoryUsage]:
        """获取峰值内存使用。"""
        if not self.usage_history:
            return None
        
        return max(self.usage_history, key=lambda x: x.rss_mb)
    
    def get_average_usage(self, minutes: int = 10) -> Optional[float]:
        """获取平均内存使用率。"""
        if not self.usage_history:
            return None
        
        cutoff_time = datetime.now().timestamp() - minutes * 60
        recent_usage = [
            u for u in self.usage_history 
            if u.timestamp.timestamp() > cutoff_time
        ]
        
        if not recent_usage:
            return None
        
        return sum(u.percent for u in recent_usage) / len(recent_usage)


class DataFrameStreamer:
    """DataFrame流式处理器。"""
    
    def __init__(self, chunk_size: int = 10000):
        """
        初始化流式处理器。
        
        Args:
            chunk_size: 数据块大小
        """
        self.chunk_size = chunk_size
        self.memory_monitor = MemoryMonitor()
    
    def stream_from_file(self, file_path: str, **kwargs) -> Iterator[pd.DataFrame]:
        """
        从文件流式读取数据。
        
        Args:
            file_path: 文件路径
            **kwargs: pandas读取参数
            
        Yields:
            数据块
        """
        file_path = Path(file_path)
        
        if file_path.suffix.lower() == '.csv':
            yield from self._stream_csv(file_path, **kwargs)
        elif file_path.suffix.lower() in ['.parquet', '.pq']:
            yield from self._stream_parquet(file_path, **kwargs)
        else:
            raise ValueError(f"不支持的文件格式: {file_path.suffix}")
    
    def _stream_csv(self, file_path: Path, **kwargs) -> Iterator[pd.DataFrame]:
        """流式读取CSV文件。"""
        try:
            for chunk in pd.read_csv(file_path, chunksize=self.chunk_size, **kwargs):
                self.memory_monitor.get_current_usage()
                yield chunk
                
        except Exception as e:
            logger.error(f"流式读取CSV失败: {e}")
            raise
    
    def _stream_parquet(self, file_path: Path, **kwargs) -> Iterator[pd.DataFrame]:
        """流式读取Parquet文件。"""
        try:
            import pyarrow.parquet as pq
            
            parquet_file = pq.ParquetFile(file_path)
            
            for batch in parquet_file.iter_batches(batch_size=self.chunk_size):
                chunk = batch.to_pandas()
                self.memory_monitor.get_current_usage()
                yield chunk
                
        except ImportError:
            logger.warning("pyarrow未安装，使用pandas读取parquet")
            # 回退到pandas
            df = pd.read_parquet(file_path, **kwargs)
            yield from self.stream_dataframe(df)
        except Exception as e:
            logger.error(f"流式读取Parquet失败: {e}")
            raise
    
    def stream_dataframe(self, df: pd.DataFrame) -> Iterator[pd.DataFrame]:
        """
        将DataFrame分块流式处理。
        
        Args:
            df: 输入DataFrame
            
        Yields:
            数据块
        """
        total_rows = len(df)
        
        for start_idx in range(0, total_rows, self.chunk_size):
            end_idx = min(start_idx + self.chunk_size, total_rows)
            chunk = df.iloc[start_idx:end_idx].copy()
            
            self.memory_monitor.get_current_usage()
            yield chunk
    
    def process_stream(self, 
                      data_stream: Iterator[pd.DataFrame],
                      processor_func: Callable[[pd.DataFrame], pd.DataFrame],
                      output_path: Optional[str] = None) -> Iterator[pd.DataFrame]:
        """
        流式处理数据。
        
        Args:
            data_stream: 数据流
            processor_func: 处理函数
            output_path: 输出路径（可选）
            
        Yields:
            处理后的数据块
        """
        output_file = None
        if output_path:
            output_file = open(output_path, 'w', encoding='utf-8')
            # 写入CSV头部（假设第一个块包含所有列）
            first_chunk = True
        
        try:
            for chunk in data_stream:
                # 处理数据块
                processed_chunk = processor_func(chunk)
                
                # 保存到文件
                if output_file:
                    processed_chunk.to_csv(
                        output_file, 
                        index=False, 
                        header=first_chunk,
                        mode='a' if not first_chunk else 'w'
                    )
                    first_chunk = False
                
                yield processed_chunk
                
                # 强制垃圾回收
                del chunk, processed_chunk
                gc.collect()
                
        finally:
            if output_file:
                output_file.close()


class MemoryEfficientDataProcessor:
    """内存高效的数据处理器。"""
    
    def __init__(self, temp_dir: Optional[str] = None):
        """
        初始化处理器。
        
        Args:
            temp_dir: 临时文件目录
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.temp_files: List[str] = []
        self.memory_monitor = MemoryMonitor()
    
    def __del__(self):
        """清理临时文件。"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """清理临时文件。"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"清理临时文件失败 {temp_file}: {e}")
        
        self.temp_files.clear()
    
    def create_temp_file(self, suffix: str = '.pkl') -> str:
        """创建临时文件。"""
        temp_file = tempfile.NamedTemporaryFile(
            dir=self.temp_dir,
            suffix=suffix,
            delete=False
        )
        temp_path = temp_file.name
        temp_file.close()
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def save_to_temp(self, data: Any, compress: bool = True) -> str:
        """保存数据到临时文件。"""
        temp_path = self.create_temp_file()
        
        try:
            with open(temp_path, 'wb') as f:
                if compress:
                    import gzip
                    with gzip.open(f, 'wb') as gz_f:
                        pickle.dump(data, gz_f)
                else:
                    pickle.dump(data, f)
            
            logger.debug(f"数据已保存到临时文件: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"保存临时文件失败: {e}")
            raise
    
    def load_from_temp(self, temp_path: str, compress: bool = True) -> Any:
        """从临时文件加载数据。"""
        try:
            with open(temp_path, 'rb') as f:
                if compress:
                    import gzip
                    with gzip.open(f, 'rb') as gz_f:
                        data = pickle.load(gz_f)
                else:
                    data = pickle.load(f)
            
            return data
            
        except Exception as e:
            logger.error(f"加载临时文件失败: {e}")
            raise
    
    def process_large_dataframe(self, 
                               df: pd.DataFrame,
                               processor_func: Callable[[pd.DataFrame], pd.DataFrame],
                               chunk_size: int = 10000,
                               use_temp_storage: bool = True) -> pd.DataFrame:
        """
        处理大型DataFrame。
        
        Args:
            df: 输入DataFrame
            processor_func: 处理函数
            chunk_size: 数据块大小
            use_temp_storage: 是否使用临时存储
            
        Returns:
            处理后的DataFrame
        """
        if len(df) <= chunk_size:
            # 小数据直接处理
            return processor_func(df)
        
        # 大数据分块处理
        streamer = DataFrameStreamer(chunk_size)
        processed_chunks = []
        temp_files = []
        
        try:
            for i, chunk in enumerate(streamer.stream_dataframe(df)):
                processed_chunk = processor_func(chunk)
                
                if use_temp_storage and len(processed_chunks) > 10:
                    # 保存到临时文件以释放内存
                    temp_path = self.save_to_temp(processed_chunks)
                    temp_files.append(temp_path)
                    processed_chunks.clear()
                    gc.collect()
                
                processed_chunks.append(processed_chunk)
                
                # 监控内存使用
                usage = self.memory_monitor.get_current_usage()
                if usage.percent > 85:  # 内存使用超过85%
                    logger.warning(f"内存使用过高: {usage.percent:.1f}%，强制垃圾回收")
                    gc.collect()
            
            # 合并所有结果
            all_chunks = processed_chunks.copy()
            
            # 加载临时文件中的数据
            for temp_path in temp_files:
                temp_chunks = self.load_from_temp(temp_path)
                all_chunks.extend(temp_chunks)
            
            # 合并DataFrame
            if all_chunks:
                result = pd.concat(all_chunks, ignore_index=True)
            else:
                result = pd.DataFrame()
            
            return result
            
        finally:
            # 清理临时文件
            for temp_path in temp_files:
                try:
                    os.unlink(temp_path)
                except:
                    pass


@contextmanager
def memory_limit_context(limit_mb: float):
    """
    内存限制上下文管理器。
    
    Args:
        limit_mb: 内存限制（MB）
    """
    monitor = MemoryMonitor()
    initial_usage = monitor.get_current_usage()
    
    try:
        yield monitor
    finally:
        final_usage = monitor.get_current_usage()
        memory_increase = final_usage.rss_mb - initial_usage.rss_mb
        
        if memory_increase > limit_mb:
            logger.warning(f"内存使用超出限制: 增加了 {memory_increase:.1f}MB (限制: {limit_mb}MB)")
        
        # 强制垃圾回收
        gc.collect()


def optimize_dataframe_memory(df: pd.DataFrame) -> pd.DataFrame:
    """
    优化DataFrame内存使用。
    
    Args:
        df: 输入DataFrame
        
    Returns:
        优化后的DataFrame
    """
    optimized_df = df.copy()
    
    for col in optimized_df.columns:
        col_type = optimized_df[col].dtype
        
        if col_type == 'object':
            # 尝试转换为category
            if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                optimized_df[col] = optimized_df[col].astype('category')
        
        elif col_type == 'int64':
            # 优化整数类型
            col_min = optimized_df[col].min()
            col_max = optimized_df[col].max()
            
            if col_min >= 0:
                if col_max < 255:
                    optimized_df[col] = optimized_df[col].astype('uint8')
                elif col_max < 65535:
                    optimized_df[col] = optimized_df[col].astype('uint16')
                elif col_max < 4294967295:
                    optimized_df[col] = optimized_df[col].astype('uint32')
            else:
                if col_min > -128 and col_max < 127:
                    optimized_df[col] = optimized_df[col].astype('int8')
                elif col_min > -32768 and col_max < 32767:
                    optimized_df[col] = optimized_df[col].astype('int16')
                elif col_min > -2147483648 and col_max < 2147483647:
                    optimized_df[col] = optimized_df[col].astype('int32')
        
        elif col_type == 'float64':
            # 优化浮点类型
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
    
    return optimized_df


# 示例使用
if __name__ == "__main__":
    print("=== 内存管理优化示例 ===")
    
    # 1. 内存监控
    print("\\n--- 内存监控 ---")
    monitor = MemoryMonitor()
    usage = monitor.get_current_usage()
    print(f"当前内存使用: {usage.rss_mb:.1f}MB ({usage.percent:.1f}%)")
    
    # 2. 创建测试数据
    print("\\n--- 创建大型DataFrame ---")
    np.random.seed(42)
    large_df = pd.DataFrame({
        'datetime': pd.date_range('2020-01-01', periods=100000),
        'symbol': np.random.choice(['A', 'B', 'C', 'D', 'E'], 100000),
        'price': np.random.normal(100, 10, 100000),
        'volume': np.random.randint(1000, 10000, 100000)
    })
    
    print(f"原始数据大小: {large_df.memory_usage(deep=True).sum() / 1024**2:.1f}MB")
    
    # 3. 内存优化
    print("\\n--- 内存优化 ---")
    optimized_df = optimize_dataframe_memory(large_df)
    print(f"优化后数据大小: {optimized_df.memory_usage(deep=True).sum() / 1024**2:.1f}MB")
    
    # 4. 流式处理
    print("\\n--- 流式处理 ---")
    streamer = DataFrameStreamer(chunk_size=10000)
    
    def simple_processor(chunk):
        # 简单的数据处理
        chunk['price_ma'] = chunk['price'].rolling(window=5, min_periods=1).mean()
        return chunk
    
    processed_chunks = []
    for chunk in streamer.stream_dataframe(large_df):
        processed_chunk = simple_processor(chunk)
        processed_chunks.append(processed_chunk)
    
    result_df = pd.concat(processed_chunks, ignore_index=True)
    print(f"流式处理完成，结果大小: {len(result_df)} 行")
    
    # 5. 内存限制上下文
    print("\\n--- 内存限制上下文 ---")
    with memory_limit_context(100.0) as mem_monitor:
        # 执行一些内存密集型操作
        temp_data = pd.DataFrame(np.random.random((50000, 10)))
        temp_result = temp_data.sum()
        del temp_data
    
    print("✅ 内存管理优化示例完成")
