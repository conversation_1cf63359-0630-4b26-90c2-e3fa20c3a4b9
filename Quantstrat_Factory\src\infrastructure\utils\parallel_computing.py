"""
并行计算工具模块。

提供多进程和多线程的并行计算功能，用于加速数据处理和特征计算。
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Callable, Any, Optional, Union
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
import multiprocessing as mp
from functools import partial
import time
import logging
from pathlib import Path
import gc


logger = logging.getLogger(__name__)


class ParallelProcessor:
    """
    并行处理器，支持多进程和多线程并行计算。
    """
    
    def __init__(self, 
                 n_workers: Optional[int] = None,
                 backend: str = 'process',
                 chunk_size: Optional[int] = None):
        """
        初始化并行处理器。
        
        Args:
            n_workers: 工作进程/线程数，默认为CPU核心数
            backend: 并行后端，'process'或'thread'
            chunk_size: 数据块大小，用于分割大数据集
        """
        self.n_workers = n_workers or mp.cpu_count()
        self.backend = backend
        self.chunk_size = chunk_size or 1000
        
        logger.info(f"初始化并行处理器: {self.n_workers}个{backend}, 块大小: {self.chunk_size}")
    
    def parallel_apply(self, 
                      data: pd.DataFrame,
                      func: Callable,
                      group_by: Optional[str] = None,
                      **kwargs) -> pd.DataFrame:
        """
        并行应用函数到数据。
        
        Args:
            data: 输入数据
            func: 要应用的函数
            group_by: 分组列名，如果指定则按组并行处理
            **kwargs: 传递给函数的额外参数
            
        Returns:
            处理后的数据
        """
        if group_by:
            return self._parallel_groupby_apply(data, func, group_by, **kwargs)
        else:
            return self._parallel_chunk_apply(data, func, **kwargs)
    
    def _parallel_groupby_apply(self, 
                               data: pd.DataFrame,
                               func: Callable,
                               group_by: str,
                               **kwargs) -> pd.DataFrame:
        """按组并行处理数据。"""
        # 按组分割数据
        groups = [(name, group) for name, group in data.groupby(group_by)]
        
        logger.info(f"按{group_by}分组，共{len(groups)}个组")
        
        # 创建部分函数
        partial_func = partial(self._apply_func_to_group, func=func, **kwargs)
        
        # 并行处理
        executor_class = ProcessPoolExecutor if self.backend == 'process' else ThreadPoolExecutor
        
        with executor_class(max_workers=self.n_workers) as executor:
            futures = {executor.submit(partial_func, group): name 
                      for name, group in groups}
            
            results = []
            for future in as_completed(futures):
                try:
                    result = future.result()
                    if result is not None and not result.empty:
                        results.append(result)
                except Exception as e:
                    group_name = futures[future]
                    logger.error(f"处理组 {group_name} 时发生错误: {e}")
        
        # 合并结果
        if results:
            return pd.concat(results, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def _parallel_chunk_apply(self, 
                             data: pd.DataFrame,
                             func: Callable,
                             **kwargs) -> pd.DataFrame:
        """按块并行处理数据。"""
        # 分割数据为块
        chunks = [data.iloc[i:i + self.chunk_size] 
                 for i in range(0, len(data), self.chunk_size)]
        
        logger.info(f"数据分割为{len(chunks)}个块")
        
        # 创建部分函数
        partial_func = partial(self._apply_func_to_chunk, func=func, **kwargs)
        
        # 并行处理
        executor_class = ProcessPoolExecutor if self.backend == 'process' else ThreadPoolExecutor
        
        with executor_class(max_workers=self.n_workers) as executor:
            futures = [executor.submit(partial_func, chunk) for chunk in chunks]
            
            results = []
            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    if result is not None and not result.empty:
                        results.append(result)
                    
                    if (i + 1) % 10 == 0:
                        logger.info(f"已完成 {i + 1}/{len(futures)} 个块")
                        
                except Exception as e:
                    logger.error(f"处理块时发生错误: {e}")
        
        # 合并结果
        if results:
            return pd.concat(results, ignore_index=True)
        else:
            return pd.DataFrame()
    
    @staticmethod
    def _apply_func_to_group(group_data: tuple, func: Callable, **kwargs):
        """应用函数到组数据。"""
        name, group = group_data
        try:
            result = func(group, **kwargs)
            # 清理内存
            del group
            gc.collect()
            return result
        except Exception as e:
            logger.error(f"处理组 {name} 时发生错误: {e}")
            return None
    
    @staticmethod
    def _apply_func_to_chunk(chunk: pd.DataFrame, func: Callable, **kwargs):
        """应用函数到数据块。"""
        try:
            result = func(chunk, **kwargs)
            # 清理内存
            del chunk
            gc.collect()
            return result
        except Exception as e:
            logger.error(f"处理数据块时发生错误: {e}")
            return None


class ParallelFactorCalculator:
    """
    并行因子计算器，专门用于因子计算的并行处理。
    """
    
    def __init__(self, n_workers: Optional[int] = None):
        """初始化并行因子计算器。"""
        self.processor = ParallelProcessor(n_workers=n_workers, backend='process')
    
    def calculate_factors_parallel(self,
                                 data: pd.DataFrame,
                                 factor_functions: Dict[str, Callable],
                                 group_by: str = 'symbol') -> pd.DataFrame:
        """
        并行计算多个因子。
        
        Args:
            data: 输入数据
            factor_functions: 因子计算函数字典 {因子名: 计算函数}
            group_by: 分组列名
            
        Returns:
            包含所有因子的数据
        """
        logger.info(f"开始并行计算 {len(factor_functions)} 个因子")
        
        results = []
        
        for factor_name, factor_func in factor_functions.items():
            logger.info(f"计算因子: {factor_name}")
            
            try:
                # 并行计算单个因子
                factor_result = self.processor.parallel_apply(
                    data, 
                    self._calculate_single_factor,
                    group_by=group_by,
                    factor_func=factor_func,
                    factor_name=factor_name
                )
                
                if not factor_result.empty:
                    results.append(factor_result)
                    
            except Exception as e:
                logger.error(f"计算因子 {factor_name} 时发生错误: {e}")
        
        # 合并所有因子结果
        if results:
            # 按datetime和symbol合并
            final_result = results[0]
            for result in results[1:]:
                final_result = pd.merge(
                    final_result, 
                    result, 
                    on=['datetime', 'symbol'], 
                    how='outer'
                )
            return final_result
        else:
            return pd.DataFrame()
    
    @staticmethod
    def _calculate_single_factor(group: pd.DataFrame, 
                               factor_func: Callable,
                               factor_name: str) -> pd.DataFrame:
        """计算单个因子。"""
        try:
            # 确保数据按时间排序
            group_sorted = group.sort_values('datetime')
            
            # 计算因子值
            factor_values = factor_func(group_sorted)
            
            # 创建结果DataFrame
            result = group_sorted[['datetime', 'symbol']].copy()
            result[factor_name] = factor_values
            
            return result
            
        except Exception as e:
            logger.error(f"计算因子 {factor_name} 时发生错误: {e}")
            return pd.DataFrame()


class ParallelICCalculator:
    """
    并行IC计算器，用于大规模IC计算。
    """
    
    def __init__(self, n_workers: Optional[int] = None):
        """初始化并行IC计算器。"""
        self.processor = ParallelProcessor(n_workers=n_workers, backend='process')
    
    def calculate_ic_parallel(self,
                            data: pd.DataFrame,
                            factor_cols: List[str],
                            return_col: str = 'fwd_return_1d') -> Dict[str, pd.Series]:
        """
        并行计算多个因子的IC。
        
        Args:
            data: 输入数据
            factor_cols: 因子列名列表
            return_col: 收益率列名
            
        Returns:
            因子IC字典 {因子名: IC序列}
        """
        logger.info(f"开始并行计算 {len(factor_cols)} 个因子的IC")
        
        # 准备计算任务
        tasks = [(data, factor_col, return_col) for factor_col in factor_cols]
        
        # 并行计算
        executor_class = ProcessPoolExecutor
        
        with executor_class(max_workers=self.processor.n_workers) as executor:
            futures = {
                executor.submit(self._calculate_single_ic, task): factor_cols[i]
                for i, task in enumerate(tasks)
            }
            
            results = {}
            for future in as_completed(futures):
                factor_name = futures[future]
                try:
                    ic_series = future.result()
                    if ic_series is not None:
                        results[factor_name] = ic_series
                        logger.info(f"完成因子 {factor_name} 的IC计算")
                except Exception as e:
                    logger.error(f"计算因子 {factor_name} 的IC时发生错误: {e}")
        
        return results
    
    @staticmethod
    def _calculate_single_ic(task: tuple) -> Optional[pd.Series]:
        """计算单个因子的IC。"""
        data, factor_col, return_col = task
        
        try:
            # 按日期分组计算IC
            def get_daily_ic(group):
                if len(group) < 2:
                    return np.nan
                return group[factor_col].corr(group[return_col])
            
            ic_series = data.groupby('datetime').apply(
                get_daily_ic, include_groups=False
            )
            
            return ic_series
            
        except Exception as e:
            logger.error(f"计算IC时发生错误: {e}")
            return None


def benchmark_parallel_performance():
    """
    并行计算性能基准测试。
    """
    print("=== 并行计算性能基准测试 ===")
    
    # 创建测试数据
    np.random.seed(42)
    n_samples = 50000
    n_symbols = 100
    
    dates = pd.date_range('2023-01-01', periods=n_samples//n_symbols)
    symbols = [f'STOCK_{i:03d}' for i in range(n_symbols)]
    
    data_list = []
    for date in dates:
        for symbol in symbols:
            data_list.append({
                'datetime': date,
                'symbol': symbol,
                'close': 100 + np.random.normal(0, 10),
                'volume': np.random.randint(1000, 10000),
                'fwd_return_1d': np.random.normal(0, 0.02)
            })
    
    test_data = pd.DataFrame(data_list)
    print(f"创建测试数据: {len(test_data)} 行, {test_data['symbol'].nunique()} 只股票")
    
    # 定义测试因子函数
    def momentum_factor(group):
        """动量因子：过去20日收益率。"""
        if len(group) < 20:
            return pd.Series([np.nan] * len(group), index=group.index)
        return group['close'].pct_change(20)
    
    def volatility_factor(group):
        """波动率因子：过去20日收益率标准差。"""
        if len(group) < 20:
            return pd.Series([np.nan] * len(group), index=group.index)
        returns = group['close'].pct_change()
        return returns.rolling(20).std()
    
    # 测试并行因子计算
    factor_functions = {
        'momentum': momentum_factor,
        'volatility': volatility_factor
    }
    
    calculator = ParallelFactorCalculator(n_workers=4)
    
    start_time = time.time()
    factor_results = calculator.calculate_factors_parallel(
        test_data, factor_functions, group_by='symbol'
    )
    parallel_time = time.time() - start_time
    
    print(f"并行因子计算完成: {parallel_time:.2f}秒")
    print(f"结果数据: {len(factor_results)} 行")
    
    # 测试并行IC计算
    if not factor_results.empty:
        ic_calculator = ParallelICCalculator(n_workers=4)
        
        start_time = time.time()
        ic_results = ic_calculator.calculate_ic_parallel(
            factor_results, 
            ['momentum', 'volatility'], 
            'fwd_return_1d'
        )
        ic_time = time.time() - start_time
        
        print(f"并行IC计算完成: {ic_time:.2f}秒")
        print(f"IC结果: {list(ic_results.keys())}")
        
        for factor, ic_series in ic_results.items():
            if not ic_series.empty:
                print(f"  {factor}: IC均值 = {ic_series.mean():.4f}")
    
    print("✅ 并行计算性能测试完成")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行性能基准测试
    benchmark_parallel_performance()
