"""
性能优化工具模块。

提供各种性能优化技术，包括：
- 数据处理优化
- 内存管理优化
- 计算加速
- 缓存机制
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Callable, Union
import time
import gc
import psutil
import functools
import logging
from pathlib import Path
import pickle
import hashlib
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp


logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """性能监控器。"""
    
    def __init__(self):
        self.metrics = {}
        self.start_times = {}
    
    def start_timer(self, name: str):
        """开始计时。"""
        self.start_times[name] = time.time()
    
    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时。"""
        if name not in self.start_times:
            raise ValueError(f"计时器 {name} 未启动")
        
        duration = time.time() - self.start_times[name]
        self.metrics[name] = duration
        del self.start_times[name]
        return duration
    
    def get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况。"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': process.memory_percent()
        }
    
    def get_cpu_usage(self) -> float:
        """获取CPU使用率。"""
        return psutil.cpu_percent(interval=1)
    
    def profile_function(self, func: Callable):
        """函数性能分析装饰器。"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            
            # 记录开始状态
            start_memory = self.get_memory_usage()
            self.start_timer(func_name)
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 记录结束状态
                duration = self.end_timer(func_name)
                end_memory = self.get_memory_usage()
                
                logger.info(f"函数 {func_name} 性能统计:")
                logger.info(f"  执行时间: {duration:.4f}秒")
                logger.info(f"  内存变化: {end_memory['rss_mb'] - start_memory['rss_mb']:.2f}MB")
        
        return wrapper


class DataFrameOptimizer:
    """DataFrame优化器。"""
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame的数据类型。"""
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            if col_type == 'object':
                # 尝试转换为category
                if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                    optimized_df[col] = optimized_df[col].astype('category')
            
            elif col_type == 'int64':
                # 优化整数类型
                col_min = optimized_df[col].min()
                col_max = optimized_df[col].max()
                
                if col_min >= 0:
                    if col_max < 255:
                        optimized_df[col] = optimized_df[col].astype('uint8')
                    elif col_max < 65535:
                        optimized_df[col] = optimized_df[col].astype('uint16')
                    elif col_max < 4294967295:
                        optimized_df[col] = optimized_df[col].astype('uint32')
                else:
                    if col_min > -128 and col_max < 127:
                        optimized_df[col] = optimized_df[col].astype('int8')
                    elif col_min > -32768 and col_max < 32767:
                        optimized_df[col] = optimized_df[col].astype('int16')
                    elif col_min > -2147483648 and col_max < 2147483647:
                        optimized_df[col] = optimized_df[col].astype('int32')
            
            elif col_type == 'float64':
                # 优化浮点类型
                optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
        
        return optimized_df
    
    @staticmethod
    def reduce_memory_usage(df: pd.DataFrame) -> pd.DataFrame:
        """减少DataFrame内存使用。"""
        start_memory = df.memory_usage(deep=True).sum() / 1024**2
        
        optimized_df = DataFrameOptimizer.optimize_dtypes(df)
        
        end_memory = optimized_df.memory_usage(deep=True).sum() / 1024**2
        reduction = (start_memory - end_memory) / start_memory * 100
        
        logger.info(f"内存优化完成: {start_memory:.2f}MB -> {end_memory:.2f}MB "
                   f"(减少 {reduction:.1f}%)")
        
        return optimized_df
    
    @staticmethod
    def chunk_process(df: pd.DataFrame, 
                     func: Callable,
                     chunk_size: int = 10000,
                     **kwargs) -> pd.DataFrame:
        """分块处理大DataFrame。"""
        results = []
        total_chunks = len(df) // chunk_size + (1 if len(df) % chunk_size else 0)
        
        for i, chunk in enumerate(np.array_split(df, total_chunks)):
            if len(chunk) == 0:
                continue
            
            result = func(chunk, **kwargs)
            results.append(result)
            
            if (i + 1) % 10 == 0:
                logger.info(f"已处理 {i + 1}/{total_chunks} 个数据块")
                gc.collect()  # 强制垃圾回收
        
        return pd.concat(results, ignore_index=True)


class CacheManager:
    """缓存管理器。"""
    
    def __init__(self, cache_dir: str = "cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
    
    def _get_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键。"""
        # 创建参数的哈希值
        key_data = f"{func_name}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key: str) -> Path:
        """获取缓存文件路径。"""
        return self.cache_dir / f"{cache_key}.pkl"
    
    def get(self, cache_key: str) -> Any:
        """获取缓存数据。"""
        cache_path = self._get_cache_path(cache_key)
        
        if cache_path.exists():
            try:
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
            except Exception as e:
                logger.warning(f"读取缓存失败: {e}")
                cache_path.unlink(missing_ok=True)
        
        return None
    
    def set(self, cache_key: str, data: Any):
        """设置缓存数据。"""
        cache_path = self._get_cache_path(cache_key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.warning(f"写入缓存失败: {e}")
    
    def cache_function(self, expire_hours: int = 24):
        """函数缓存装饰器。"""
        def decorator(func: Callable):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                cache_key = self._get_cache_key(func.__name__, args, kwargs)
                cache_path = self._get_cache_path(cache_key)
                
                # 检查缓存是否存在且未过期
                if cache_path.exists():
                    cache_age = time.time() - cache_path.stat().st_mtime
                    if cache_age < expire_hours * 3600:
                        cached_result = self.get(cache_key)
                        if cached_result is not None:
                            logger.debug(f"使用缓存结果: {func.__name__}")
                            return cached_result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                self.set(cache_key, result)
                
                return result
            
            return wrapper
        return decorator
    
    def clear_cache(self, pattern: str = "*"):
        """清理缓存。"""
        import glob
        
        cache_files = glob.glob(str(self.cache_dir / f"{pattern}.pkl"))
        for cache_file in cache_files:
            Path(cache_file).unlink(missing_ok=True)
        
        logger.info(f"已清理 {len(cache_files)} 个缓存文件")


class ParallelProcessor:
    """并行处理器。"""
    
    def __init__(self, n_workers: Optional[int] = None, backend: str = 'thread'):
        self.n_workers = n_workers or mp.cpu_count()
        self.backend = backend
    
    def parallel_apply(self, 
                      data: List[Any],
                      func: Callable,
                      **kwargs) -> List[Any]:
        """并行应用函数。"""
        if self.backend == 'thread':
            executor_class = ThreadPoolExecutor
        else:
            executor_class = ProcessPoolExecutor
        
        with executor_class(max_workers=self.n_workers) as executor:
            futures = [executor.submit(func, item, **kwargs) for item in data]
            results = [future.result() for future in futures]
        
        return results
    
    def parallel_dataframe_apply(self,
                                df: pd.DataFrame,
                                func: Callable,
                                axis: int = 0,
                                **kwargs) -> pd.DataFrame:
        """并行处理DataFrame。"""
        if axis == 0:
            # 按行分割
            chunks = np.array_split(df, self.n_workers)
        else:
            # 按列分割
            chunks = [df.iloc[:, i::self.n_workers] for i in range(self.n_workers)]
        
        # 过滤空块
        chunks = [chunk for chunk in chunks if not chunk.empty]
        
        # 并行处理
        results = self.parallel_apply(chunks, func, **kwargs)
        
        # 合并结果
        if axis == 0:
            return pd.concat(results, ignore_index=True)
        else:
            return pd.concat(results, axis=1)


class PerformanceOptimizer:
    """性能优化器主类。"""
    
    def __init__(self):
        self.monitor = PerformanceMonitor()
        self.cache_manager = CacheManager()
        self.parallel_processor = ParallelProcessor()
    
    def optimize_dataframe_operations(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame操作。"""
        # 1. 优化数据类型
        df = DataFrameOptimizer.reduce_memory_usage(df)
        
        # 2. 设置索引（如果有datetime列）
        if 'datetime' in df.columns:
            df = df.set_index('datetime')
        
        # 3. 排序（提升查询性能）
        if df.index.name == 'datetime':
            df = df.sort_index()
        
        return df
    
    def optimize_factor_calculation(self, 
                                  data: pd.DataFrame,
                                  factor_func: Callable,
                                  use_cache: bool = True,
                                  use_parallel: bool = True) -> pd.DataFrame:
        """优化因子计算。"""
        if use_cache:
            # 使用缓存装饰器
            cached_func = self.cache_manager.cache_function(expire_hours=24)(factor_func)
        else:
            cached_func = factor_func
        
        if use_parallel and 'symbol' in data.columns:
            # 按股票并行计算
            symbol_groups = [group for _, group in data.groupby('symbol')]
            results = self.parallel_processor.parallel_apply(symbol_groups, cached_func)
            return pd.concat(results, ignore_index=True)
        else:
            return cached_func(data)
    
    def benchmark_function(self, 
                          func: Callable,
                          *args,
                          n_runs: int = 5,
                          **kwargs) -> Dict[str, float]:
        """函数性能基准测试。"""
        times = []
        memory_usages = []
        
        for i in range(n_runs):
            # 垃圾回收
            gc.collect()
            
            # 记录开始状态
            start_memory = self.monitor.get_memory_usage()['rss_mb']
            start_time = time.time()
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 记录结束状态
            end_time = time.time()
            end_memory = self.monitor.get_memory_usage()['rss_mb']
            
            times.append(end_time - start_time)
            memory_usages.append(end_memory - start_memory)
        
        return {
            'mean_time': np.mean(times),
            'std_time': np.std(times),
            'min_time': np.min(times),
            'max_time': np.max(times),
            'mean_memory_delta': np.mean(memory_usages),
            'max_memory_delta': np.max(memory_usages)
        }


# 全局性能优化器实例
performance_optimizer = PerformanceOptimizer()


def optimize_performance(func: Callable):
    """性能优化装饰器。"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # 应用性能监控
        monitored_func = performance_optimizer.monitor.profile_function(func)
        return monitored_func(*args, **kwargs)
    
    return wrapper


def cache_result(expire_hours: int = 24):
    """结果缓存装饰器。"""
    return performance_optimizer.cache_manager.cache_function(expire_hours)


# 示例使用
if __name__ == "__main__":
    # 创建测试数据
    np.random.seed(42)
    test_data = pd.DataFrame({
        'datetime': pd.date_range('2023-01-01', periods=10000),
        'symbol': np.random.choice(['A', 'B', 'C'], 10000),
        'price': np.random.normal(100, 10, 10000),
        'volume': np.random.randint(1000, 10000, 10000)
    })
    
    print("=== 性能优化示例 ===")
    
    # 1. DataFrame优化
    print(f"原始数据内存使用: {test_data.memory_usage(deep=True).sum() / 1024**2:.2f}MB")
    optimized_data = DataFrameOptimizer.reduce_memory_usage(test_data)
    
    # 2. 缓存示例
    @cache_result(expire_hours=1)
    def expensive_calculation(data):
        time.sleep(0.1)  # 模拟耗时计算
        return data.groupby('symbol')['price'].mean()
    
    # 第一次调用（会缓存）
    start_time = time.time()
    result1 = expensive_calculation(optimized_data)
    first_call_time = time.time() - start_time
    
    # 第二次调用（使用缓存）
    start_time = time.time()
    result2 = expensive_calculation(optimized_data)
    second_call_time = time.time() - start_time
    
    print(f"第一次调用耗时: {first_call_time:.4f}秒")
    print(f"第二次调用耗时: {second_call_time:.4f}秒")
    print(f"缓存加速比: {first_call_time / second_call_time:.1f}x")
    
    print("✅ 性能优化示例完成")
