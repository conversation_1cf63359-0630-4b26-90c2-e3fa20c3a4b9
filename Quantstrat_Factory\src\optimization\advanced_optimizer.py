"""
高级参数优化器

实现更高级的参数优化算法和自动化调参功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable, Union
import logging
from dataclasses import dataclass
from enum import Enum
import warnings
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import multiprocessing as mp
from scipy.optimize import minimize, differential_evolution, basinhopping
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
from sklearn.model_selection import ParameterGrid
import optuna
import time

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class OptimizationMethod(Enum):
    """优化方法"""
    GRID_SEARCH = "grid_search"
    RANDOM_SEARCH = "random_search"
    BAYESIAN = "bayesian"
    GENETIC = "genetic"
    PARTICLE_SWARM = "particle_swarm"
    SIMULATED_ANNEALING = "simulated_annealing"
    OPTUNA = "optuna"


@dataclass
class ParameterSpace:
    """参数空间定义"""
    name: str
    param_type: str  # 'int', 'float', 'categorical'
    low: Optional[Union[int, float]] = None
    high: Optional[Union[int, float]] = None
    choices: Optional[List[Any]] = None
    step: Optional[Union[int, float]] = None


@dataclass
class OptimizationResult:
    """优化结果"""
    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]]
    total_evaluations: int
    optimization_time: float
    convergence_info: Dict[str, Any]


class AdvancedParameterOptimizer:
    """高级参数优化器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化高级参数优化器
        
        Args:
            max_workers: 最大工作进程数
        """
        self.max_workers = max_workers or mp.cpu_count()
        self.optimization_history = []
        self.best_results = {}
        
        logger.info(f"高级参数优化器初始化完成，最大工作进程数: {self.max_workers}")
    
    def optimize_parameters(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        method: OptimizationMethod = OptimizationMethod.BAYESIAN,
        n_trials: int = 100,
        timeout: Optional[int] = None,
        **kwargs
    ) -> OptimizationResult:
        """
        优化参数
        
        Args:
            objective_function: 目标函数
            parameter_space: 参数空间
            method: 优化方法
            n_trials: 试验次数
            timeout: 超时时间（秒）
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        logger.info(f"开始参数优化，方法: {method.value}, 试验次数: {n_trials}")
        
        if method == OptimizationMethod.GRID_SEARCH:
            result = self._grid_search_optimization(
                objective_function, parameter_space, **kwargs
            )
        elif method == OptimizationMethod.RANDOM_SEARCH:
            result = self._random_search_optimization(
                objective_function, parameter_space, n_trials, **kwargs
            )
        elif method == OptimizationMethod.BAYESIAN:
            result = self._bayesian_optimization(
                objective_function, parameter_space, n_trials, **kwargs
            )
        elif method == OptimizationMethod.GENETIC:
            result = self._genetic_optimization(
                objective_function, parameter_space, n_trials, **kwargs
            )
        elif method == OptimizationMethod.OPTUNA:
            result = self._optuna_optimization(
                objective_function, parameter_space, n_trials, timeout, **kwargs
            )
        else:
            raise ValueError(f"不支持的优化方法: {method}")
        
        optimization_time = time.time() - start_time
        result.optimization_time = optimization_time
        
        # 保存结果
        self.optimization_history.append(result)
        self.best_results[method.value] = result
        
        logger.info(f"参数优化完成，最佳分数: {result.best_score:.6f}, "
                   f"用时: {optimization_time:.2f}秒")
        
        return result
    
    def _grid_search_optimization(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        **kwargs
    ) -> OptimizationResult:
        """网格搜索优化"""
        # 构建参数网格
        param_grid = {}
        for param in parameter_space:
            if param.param_type == 'categorical':
                param_grid[param.name] = param.choices
            elif param.param_type in ['int', 'float']:
                if param.step:
                    param_grid[param.name] = np.arange(
                        param.low, param.high + param.step, param.step
                    )
                else:
                    # 默认分10个点
                    param_grid[param.name] = np.linspace(param.low, param.high, 10)
                    if param.param_type == 'int':
                        param_grid[param.name] = param_grid[param.name].astype(int)
        
        # 生成所有参数组合
        grid = ParameterGrid(param_grid)
        
        # 并行评估
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            for params in grid:
                future = executor.submit(self._safe_objective_call, objective_function, params)
                futures.append((params, future))
            
            results = []
            for params, future in futures:
                try:
                    score = future.result()
                    results.append({'params': params, 'score': score})
                except Exception as e:
                    logger.error(f"参数评估失败 {params}: {e}")
                    results.append({'params': params, 'score': float('-inf')})
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score'])
        
        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['score'],
            optimization_history=results,
            total_evaluations=len(results),
            optimization_time=0.0,  # 将在外部设置
            convergence_info={'method': 'grid_search', 'grid_size': len(results)}
        )
    
    def _random_search_optimization(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        n_trials: int,
        **kwargs
    ) -> OptimizationResult:
        """随机搜索优化"""
        results = []
        best_score = float('-inf')
        best_params = None
        
        for trial in range(n_trials):
            # 随机采样参数
            params = {}
            for param in parameter_space:
                if param.param_type == 'categorical':
                    params[param.name] = np.random.choice(param.choices)
                elif param.param_type == 'int':
                    params[param.name] = np.random.randint(param.low, param.high + 1)
                elif param.param_type == 'float':
                    params[param.name] = np.random.uniform(param.low, param.high)
            
            # 评估参数
            try:
                score = self._safe_objective_call(objective_function, params)
                results.append({'params': params, 'score': score, 'trial': trial})
                
                if score > best_score:
                    best_score = score
                    best_params = params.copy()
                    
            except Exception as e:
                logger.error(f"参数评估失败 {params}: {e}")
                results.append({'params': params, 'score': float('-inf'), 'trial': trial})
        
        return OptimizationResult(
            best_params=best_params,
            best_score=best_score,
            optimization_history=results,
            total_evaluations=n_trials,
            optimization_time=0.0,
            convergence_info={'method': 'random_search'}
        )
    
    def _bayesian_optimization(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        n_trials: int,
        **kwargs
    ) -> OptimizationResult:
        """贝叶斯优化"""
        # 初始随机采样
        n_initial = min(10, n_trials // 4)
        
        # 收集数值参数
        numeric_params = [p for p in parameter_space if p.param_type in ['int', 'float']]
        categorical_params = [p for p in parameter_space if p.param_type == 'categorical']
        
        if not numeric_params:
            # 如果没有数值参数，回退到随机搜索
            return self._random_search_optimization(objective_function, parameter_space, n_trials)
        
        # 初始化
        X_samples = []
        y_samples = []
        results = []
        
        # 初始随机采样
        for _ in range(n_initial):
            params = self._sample_random_params(parameter_space)
            
            try:
                score = self._safe_objective_call(objective_function, params)
                
                # 转换为数值向量（仅数值参数）
                x_vector = [params[p.name] for p in numeric_params]
                X_samples.append(x_vector)
                y_samples.append(score)
                
                results.append({'params': params, 'score': score})
                
            except Exception as e:
                logger.error(f"初始采样失败 {params}: {e}")
        
        if not X_samples:
            return self._random_search_optimization(objective_function, parameter_space, n_trials)
        
        # 贝叶斯优化主循环
        X_samples = np.array(X_samples)
        y_samples = np.array(y_samples)
        
        # 高斯过程回归
        kernel = Matern(length_scale=1.0, nu=2.5)
        gp = GaussianProcessRegressor(kernel=kernel, alpha=1e-6, normalize_y=True)
        
        for trial in range(n_initial, n_trials):
            # 训练高斯过程
            gp.fit(X_samples, y_samples)
            
            # 采集函数优化（期望改进）
            best_params = self._optimize_acquisition_function(
                gp, numeric_params, categorical_params, y_samples.max()
            )
            
            try:
                score = self._safe_objective_call(objective_function, best_params)
                
                # 更新样本
                x_vector = [best_params[p.name] for p in numeric_params]
                X_samples = np.vstack([X_samples, x_vector])
                y_samples = np.append(y_samples, score)
                
                results.append({'params': best_params, 'score': score, 'trial': trial})
                
            except Exception as e:
                logger.error(f"贝叶斯优化评估失败 {best_params}: {e}")
        
        # 找到最佳结果
        best_result = max(results, key=lambda x: x['score'])
        
        return OptimizationResult(
            best_params=best_result['params'],
            best_score=best_result['score'],
            optimization_history=results,
            total_evaluations=len(results),
            optimization_time=0.0,
            convergence_info={'method': 'bayesian', 'n_initial': n_initial}
        )
    
    def _genetic_optimization(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        n_trials: int,
        **kwargs
    ) -> OptimizationResult:
        """遗传算法优化"""
        # 构建边界
        bounds = []
        param_names = []
        
        for param in parameter_space:
            if param.param_type in ['int', 'float']:
                bounds.append((param.low, param.high))
                param_names.append(param.name)
            else:
                # 分类参数转换为数值
                bounds.append((0, len(param.choices) - 1))
                param_names.append(param.name)
        
        def objective_wrapper(x):
            params = {}
            for i, param in enumerate(parameter_space):
                if param.param_type == 'int':
                    params[param.name] = int(round(x[i]))
                elif param.param_type == 'float':
                    params[param.name] = x[i]
                elif param.param_type == 'categorical':
                    idx = int(round(x[i]))
                    idx = max(0, min(idx, len(param.choices) - 1))
                    params[param.name] = param.choices[idx]
            
            try:
                return -self._safe_objective_call(objective_function, params)  # 最小化
            except:
                return float('inf')
        
        # 运行差分进化算法
        result = differential_evolution(
            objective_wrapper,
            bounds,
            maxiter=n_trials // 20,
            popsize=min(15, n_trials // 10),
            seed=42
        )
        
        # 转换最佳参数
        best_params = {}
        for i, param in enumerate(parameter_space):
            if param.param_type == 'int':
                best_params[param.name] = int(round(result.x[i]))
            elif param.param_type == 'float':
                best_params[param.name] = result.x[i]
            elif param.param_type == 'categorical':
                idx = int(round(result.x[i]))
                idx = max(0, min(idx, len(param.choices) - 1))
                best_params[param.name] = param.choices[idx]
        
        return OptimizationResult(
            best_params=best_params,
            best_score=-result.fun,
            optimization_history=[],
            total_evaluations=result.nfev,
            optimization_time=0.0,
            convergence_info={'method': 'genetic', 'success': result.success}
        )
    
    def _optuna_optimization(
        self,
        objective_function: Callable,
        parameter_space: List[ParameterSpace],
        n_trials: int,
        timeout: Optional[int] = None,
        **kwargs
    ) -> OptimizationResult:
        """Optuna优化"""
        def optuna_objective(trial):
            params = {}
            for param in parameter_space:
                if param.param_type == 'int':
                    params[param.name] = trial.suggest_int(
                        param.name, param.low, param.high, step=param.step
                    )
                elif param.param_type == 'float':
                    params[param.name] = trial.suggest_float(
                        param.name, param.low, param.high, step=param.step
                    )
                elif param.param_type == 'categorical':
                    params[param.name] = trial.suggest_categorical(
                        param.name, param.choices
                    )
            
            try:
                return self._safe_objective_call(objective_function, params)
            except Exception as e:
                logger.error(f"Optuna评估失败 {params}: {e}")
                return float('-inf')
        
        # 创建研究
        study = optuna.create_study(direction='maximize')
        
        # 运行优化
        study.optimize(optuna_objective, n_trials=n_trials, timeout=timeout)
        
        # 收集结果
        results = []
        for trial in study.trials:
            if trial.state == optuna.trial.TrialState.COMPLETE:
                results.append({
                    'params': trial.params,
                    'score': trial.value,
                    'trial': trial.number
                })
        
        return OptimizationResult(
            best_params=study.best_params,
            best_score=study.best_value,
            optimization_history=results,
            total_evaluations=len(study.trials),
            optimization_time=0.0,
            convergence_info={'method': 'optuna', 'n_completed': len(results)}
        )
    
    def _sample_random_params(self, parameter_space: List[ParameterSpace]) -> Dict[str, Any]:
        """随机采样参数"""
        params = {}
        for param in parameter_space:
            if param.param_type == 'categorical':
                params[param.name] = np.random.choice(param.choices)
            elif param.param_type == 'int':
                params[param.name] = np.random.randint(param.low, param.high + 1)
            elif param.param_type == 'float':
                params[param.name] = np.random.uniform(param.low, param.high)
        return params
    
    def _optimize_acquisition_function(
        self,
        gp: GaussianProcessRegressor,
        numeric_params: List[ParameterSpace],
        categorical_params: List[ParameterSpace],
        current_best: float
    ) -> Dict[str, Any]:
        """优化采集函数"""
        def expected_improvement(x):
            x = x.reshape(1, -1)
            mu, sigma = gp.predict(x, return_std=True)
            
            if sigma == 0:
                return 0
            
            improvement = mu - current_best
            z = improvement / sigma
            ei = improvement * stats.norm.cdf(z) + sigma * stats.norm.pdf(z)
            return -ei[0]  # 最小化
        
        # 优化数值参数
        bounds = [(p.low, p.high) for p in numeric_params]
        
        best_ei = float('inf')
        best_x = None
        
        # 多次随机初始化
        for _ in range(10):
            x0 = [np.random.uniform(p.low, p.high) for p in numeric_params]
            
            try:
                result = minimize(expected_improvement, x0, bounds=bounds, method='L-BFGS-B')
                if result.fun < best_ei:
                    best_ei = result.fun
                    best_x = result.x
            except:
                continue
        
        if best_x is None:
            best_x = [np.random.uniform(p.low, p.high) for p in numeric_params]
        
        # 构建完整参数
        params = {}
        for i, param in enumerate(numeric_params):
            if param.param_type == 'int':
                params[param.name] = int(round(best_x[i]))
            else:
                params[param.name] = best_x[i]
        
        # 随机选择分类参数
        for param in categorical_params:
            params[param.name] = np.random.choice(param.choices)
        
        return params
    
    def _safe_objective_call(self, objective_function: Callable, params: Dict[str, Any]) -> float:
        """安全调用目标函数"""
        try:
            return objective_function(params)
        except Exception as e:
            logger.error(f"目标函数调用失败: {e}")
            return float('-inf')
    
    def multi_objective_optimization(
        self,
        objective_functions: List[Callable],
        parameter_space: List[ParameterSpace],
        weights: Optional[List[float]] = None,
        method: OptimizationMethod = OptimizationMethod.OPTUNA,
        n_trials: int = 100,
        **kwargs
    ) -> OptimizationResult:
        """
        多目标优化
        
        Args:
            objective_functions: 目标函数列表
            parameter_space: 参数空间
            weights: 目标权重
            method: 优化方法
            n_trials: 试验次数
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        if weights is None:
            weights = [1.0 / len(objective_functions)] * len(objective_functions)
        
        def combined_objective(params):
            scores = []
            for obj_func in objective_functions:
                try:
                    score = obj_func(params)
                    scores.append(score)
                except:
                    scores.append(float('-inf'))
            
            # 加权组合
            return sum(w * s for w, s in zip(weights, scores))
        
        return self.optimize_parameters(
            combined_objective, parameter_space, method, n_trials, **kwargs
        )
    
    def get_optimization_history(self) -> List[OptimizationResult]:
        """获取优化历史"""
        return self.optimization_history.copy()
    
    def get_best_results(self) -> Dict[str, OptimizationResult]:
        """获取最佳结果"""
        return self.best_results.copy()
    
    def clear_history(self):
        """清空历史记录"""
        self.optimization_history.clear()
        self.best_results.clear()
        logger.info("优化历史已清空")
