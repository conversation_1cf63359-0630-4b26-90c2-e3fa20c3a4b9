"""
缓存管理器模块。

提供内存缓存、持久化缓存和缓存策略管理功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Optional, Dict, List
import logging
import time
import pickle
import hashlib
from pathlib import Path
import threading
from collections import OrderedDict
import json

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器。"""
    
    def __init__(
        self, 
        max_size: int = 1000,
        default_ttl: int = 3600,
        persistent: bool = False,
        cache_dir: str = "cache"
    ):
        """
        初始化缓存管理器。
        
        Args:
            max_size: 最大缓存项数
            default_ttl: 默认过期时间（秒）
            persistent: 是否启用持久化
            cache_dir: 缓存目录
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.persistent = persistent
        self.cache_dir = Path(cache_dir)
        
        # 内存缓存
        self._cache = OrderedDict()
        self._expiry_times = {}
        self._access_times = {}
        
        # 统计信息
        self._hits = 0
        self._misses = 0
        
        # 线程锁
        self._lock = threading.RLock()
        
        # 创建缓存目录
        if self.persistent:
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            self._load_persistent_cache()
    
    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值。
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或过期则返回None
        """
        with self._lock:
            current_time = time.time()
            
            # 检查内存缓存
            if key in self._cache:
                # 检查是否过期
                if key in self._expiry_times and current_time > self._expiry_times[key]:
                    self._remove_key(key)
                    self._misses += 1
                    return None
                
                # 更新访问时间
                self._access_times[key] = current_time
                
                # 移动到末尾（LRU策略）
                value = self._cache.pop(key)
                self._cache[key] = value
                
                self._hits += 1
                return value
            
            # 检查持久化缓存
            if self.persistent:
                persistent_value = self._get_persistent(key)
                if persistent_value is not None:
                    # 加载到内存缓存
                    self._cache[key] = persistent_value
                    self._access_times[key] = current_time
                    self._hits += 1
                    return persistent_value
            
            self._misses += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存值。
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示使用默认值
            
        Returns:
            是否设置成功
        """
        try:
            with self._lock:
                current_time = time.time()
                ttl = ttl or self.default_ttl
                
                # 检查缓存大小限制
                if len(self._cache) >= self.max_size and key not in self._cache:
                    self._evict_lru()
                
                # 设置缓存
                self._cache[key] = value
                self._expiry_times[key] = current_time + ttl
                self._access_times[key] = current_time
                
                # 持久化
                if self.persistent:
                    self._set_persistent(key, value, ttl)
                
                logger.debug(f"缓存设置成功: {key}")
                return True
                
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """
        删除缓存项。
        
        Args:
            key: 缓存键
            
        Returns:
            是否删除成功
        """
        try:
            with self._lock:
                if key in self._cache:
                    self._remove_key(key)
                
                # 删除持久化缓存
                if self.persistent:
                    self._delete_persistent(key)
                
                return True
                
        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            return False
    
    def clear(self) -> bool:
        """
        清空所有缓存。
        
        Returns:
            是否清空成功
        """
        try:
            with self._lock:
                self._cache.clear()
                self._expiry_times.clear()
                self._access_times.clear()
                
                # 清空持久化缓存
                if self.persistent:
                    self._clear_persistent()
                
                logger.info("缓存已清空")
                return True
                
        except Exception as e:
            logger.error(f"清空缓存失败: {e}")
            return False
    
    def size(self) -> int:
        """
        获取缓存大小。
        
        Returns:
            缓存项数量
        """
        with self._lock:
            return len(self._cache)
    
    def keys(self) -> List[str]:
        """
        获取所有缓存键。
        
        Returns:
            缓存键列表
        """
        with self._lock:
            return list(self._cache.keys())
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取缓存统计信息。
        
        Returns:
            统计信息字典
        """
        with self._lock:
            total_requests = self._hits + self._misses
            hit_rate = self._hits / total_requests if total_requests > 0 else 0
            
            return {
                'hits': self._hits,
                'misses': self._misses,
                'hit_rate': hit_rate,
                'cache_size': len(self._cache),
                'max_size': self.max_size
            }
    
    def cleanup_expired(self) -> int:
        """
        清理过期缓存项。
        
        Returns:
            清理的项目数量
        """
        with self._lock:
            current_time = time.time()
            expired_keys = []
            
            for key, expiry_time in self._expiry_times.items():
                if current_time > expiry_time:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
                if self.persistent:
                    self._delete_persistent(key)
            
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")
            return len(expired_keys)
    
    def _remove_key(self, key: str):
        """移除缓存键。"""
        self._cache.pop(key, None)
        self._expiry_times.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_lru(self):
        """驱逐最近最少使用的缓存项。"""
        if not self._cache:
            return
        
        # 找到最久未访问的键
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove_key(lru_key)
        
        if self.persistent:
            self._delete_persistent(lru_key)
        
        logger.debug(f"驱逐LRU缓存项: {lru_key}")
    
    def _get_cache_file_path(self, key: str) -> Path:
        """获取缓存文件路径。"""
        # 使用MD5哈希避免文件名问题
        key_hash = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def _get_persistent(self, key: str) -> Optional[Any]:
        """从持久化存储获取缓存。"""
        try:
            cache_file = self._get_cache_file_path(key)
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 检查是否过期
            if time.time() > cache_data['expiry_time']:
                cache_file.unlink()
                return None
            
            return cache_data['value']
            
        except Exception as e:
            logger.error(f"读取持久化缓存失败: {e}")
            return None
    
    def _set_persistent(self, key: str, value: Any, ttl: int):
        """设置持久化缓存。"""
        try:
            cache_file = self._get_cache_file_path(key)
            cache_data = {
                'value': value,
                'expiry_time': time.time() + ttl,
                'created_time': time.time()
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
                
        except Exception as e:
            logger.error(f"写入持久化缓存失败: {e}")
    
    def _delete_persistent(self, key: str):
        """删除持久化缓存。"""
        try:
            cache_file = self._get_cache_file_path(key)
            if cache_file.exists():
                cache_file.unlink()
                
        except Exception as e:
            logger.error(f"删除持久化缓存失败: {e}")
    
    def _clear_persistent(self):
        """清空持久化缓存。"""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
                
        except Exception as e:
            logger.error(f"清空持久化缓存失败: {e}")
    
    def _load_persistent_cache(self):
        """加载持久化缓存到内存。"""
        try:
            current_time = time.time()
            loaded_count = 0
            
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)
                    
                    # 检查是否过期
                    if current_time > cache_data['expiry_time']:
                        cache_file.unlink()
                        continue
                    
                    # 重建键（这里简化处理，实际应该存储键映射）
                    # 由于我们使用哈希作为文件名，这里无法直接恢复键
                    # 在实际应用中，应该维护一个键到哈希的映射文件
                    
                    loaded_count += 1
                    
                except Exception as e:
                    logger.warning(f"加载缓存文件失败 {cache_file}: {e}")
                    continue
            
            logger.info(f"加载了 {loaded_count} 个持久化缓存项")
            
        except Exception as e:
            logger.error(f"加载持久化缓存失败: {e}")


def cache_result(ttl: int = 3600, key_func: Optional[callable] = None):
    """
    装饰器：缓存函数结果。
    
    Args:
        ttl: 缓存过期时间
        key_func: 生成缓存键的函数
        
    Returns:
        装饰后的函数
    """
    def decorator(func):
        cache = CacheManager()
        
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {cache_key}")
            
            return result
        
        return wrapper
    return decorator


class DataFrameCache:
    """专门用于DataFrame的缓存。"""
    
    def __init__(self, cache_manager: CacheManager):
        """
        初始化DataFrame缓存。
        
        Args:
            cache_manager: 缓存管理器实例
        """
        self.cache_manager = cache_manager
    
    def get_dataframe(self, key: str) -> Optional[pd.DataFrame]:
        """
        获取缓存的DataFrame。
        
        Args:
            key: 缓存键
            
        Returns:
            DataFrame或None
        """
        cached_data = self.cache_manager.get(key)
        if cached_data is not None and isinstance(cached_data, dict):
            try:
                return pd.DataFrame(cached_data['data'], 
                                  index=cached_data.get('index'),
                                  columns=cached_data.get('columns'))
            except Exception as e:
                logger.error(f"恢复DataFrame失败: {e}")
                return None
        return None
    
    def set_dataframe(self, key: str, df: pd.DataFrame, ttl: Optional[int] = None) -> bool:
        """
        缓存DataFrame。
        
        Args:
            key: 缓存键
            df: DataFrame
            ttl: 过期时间
            
        Returns:
            是否设置成功
        """
        try:
            # 将DataFrame转换为可序列化的格式
            cache_data = {
                'data': df.to_dict('records'),
                'index': df.index.tolist(),
                'columns': df.columns.tolist(),
                'dtypes': df.dtypes.to_dict()
            }
            
            return self.cache_manager.set(key, cache_data, ttl)
            
        except Exception as e:
            logger.error(f"缓存DataFrame失败: {e}")
            return False
