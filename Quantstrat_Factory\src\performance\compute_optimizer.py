"""
计算性能优化器模块。

提供算法优化、硬件加速和计算缓存功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, Dict, List, Optional, Union
import logging
import time
from functools import wraps, lru_cache
import hashlib
import pickle

logger = logging.getLogger(__name__)

# 尝试导入加速库
try:
    import numba
    from numba import jit, cuda
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    logger.warning("Numba未安装，JIT编译功能将不可用")

try:
    import cupy as cp
    CUPY_AVAILABLE = True
except ImportError:
    CUPY_AVAILABLE = False
    logger.warning("CuPy未安装，GPU加速功能将不可用")


class ComputeOptimizer:
    """计算性能优化器。"""
    
    def __init__(self):
        """初始化计算优化器。"""
        self.optimization_cache = {}
        self.performance_stats = {}
        
        # 检查可用的加速库
        self.numba_available = NUMBA_AVAILABLE
        self.cupy_available = CUPY_AVAILABLE
        
        logger.info(f"计算优化器初始化完成 - Numba: {self.numba_available}, CuPy: {self.cupy_available}")
    
    def optimize_numpy_operations(self, func: Callable) -> Callable:
        """
        优化NumPy操作。
        
        Args:
            func: 要优化的函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 检查输入是否为NumPy数组
            numpy_args = []
            for arg in args:
                if isinstance(arg, (list, tuple)):
                    numpy_args.append(np.array(arg))
                else:
                    numpy_args.append(arg)
            
            # 使用优化的NumPy操作
            with np.errstate(all='ignore'):  # 忽略数值警告
                result = func(*numpy_args, **kwargs)
            
            return result
        
        return wrapper
    
    def vectorize_operation(self, func: Callable, signature: Optional[str] = None) -> Callable:
        """
        向量化操作。
        
        Args:
            func: 要向量化的函数
            signature: 函数签名（用于Numba）
            
        Returns:
            向量化的函数
        """
        if self.numba_available:
            try:
                if signature:
                    vectorized_func = numba.vectorize([signature], nopython=True)(func)
                else:
                    vectorized_func = numba.vectorize(nopython=True)(func)
                
                logger.info(f"函数 {func.__name__} 已使用Numba向量化")
                return vectorized_func
            except Exception as e:
                logger.warning(f"Numba向量化失败，使用NumPy向量化: {e}")
        
        # 回退到NumPy向量化
        return np.vectorize(func)
    
    def jit_compile(self, func: Callable, nopython: bool = True) -> Callable:
        """
        JIT编译函数。
        
        Args:
            func: 要编译的函数
            nopython: 是否使用nopython模式
            
        Returns:
            编译后的函数
        """
        if not self.numba_available:
            logger.warning("Numba不可用，返回原函数")
            return func
        
        try:
            compiled_func = jit(nopython=nopython)(func)
            logger.info(f"函数 {func.__name__} 已JIT编译")
            return compiled_func
        except Exception as e:
            logger.error(f"JIT编译失败: {e}")
            return func
    
    def gpu_accelerate(self, func: Callable, data: np.ndarray) -> np.ndarray:
        """
        GPU加速计算。
        
        Args:
            func: 计算函数
            data: 输入数据
            
        Returns:
            计算结果
        """
        if not self.cupy_available:
            logger.warning("CuPy不可用，使用CPU计算")
            return func(data)
        
        try:
            # 将数据转移到GPU
            gpu_data = cp.asarray(data)
            
            # 在GPU上执行计算
            gpu_result = func(gpu_data)
            
            # 将结果转移回CPU
            result = cp.asnumpy(gpu_result)
            
            logger.info(f"GPU加速计算完成，数据大小: {data.shape}")
            return result
            
        except Exception as e:
            logger.error(f"GPU加速失败，回退到CPU: {e}")
            return func(data)
    
    def optimize_pandas_operations(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化Pandas操作。
        
        Args:
            df: 输入DataFrame
            
        Returns:
            优化后的DataFrame
        """
        try:
            optimized_df = df.copy()
            
            # 使用分类数据类型
            for col in optimized_df.select_dtypes(include=['object']).columns:
                if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                    optimized_df[col] = optimized_df[col].astype('category')
            
            # 优化数值类型
            for col in optimized_df.select_dtypes(include=['int64']).columns:
                optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
            
            for col in optimized_df.select_dtypes(include=['float64']).columns:
                optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
            
            return optimized_df
            
        except Exception as e:
            logger.error(f"Pandas操作优化失败: {e}")
            return df
    
    def cache_computation(self, func: Callable, cache_size: int = 128) -> Callable:
        """
        缓存计算结果。
        
        Args:
            func: 要缓存的函数
            cache_size: 缓存大小
            
        Returns:
            带缓存的函数
        """
        @lru_cache(maxsize=cache_size)
        def cached_func(*args, **kwargs):
            # 将kwargs转换为可哈希的形式
            kwargs_tuple = tuple(sorted(kwargs.items()))
            cache_key = (args, kwargs_tuple)
            
            return func(*args, **kwargs)
        
        return cached_func
    
    def parallel_compute(
        self, 
        func: Callable, 
        data_chunks: List[Any], 
        use_processes: bool = False
    ) -> List[Any]:
        """
        并行计算。
        
        Args:
            func: 计算函数
            data_chunks: 数据块列表
            use_processes: 是否使用进程池
            
        Returns:
            计算结果列表
        """
        from .parallel_processor import ParallelProcessor
        
        processor = ParallelProcessor()
        
        if use_processes:
            return processor.process_with_processes(func, data_chunks)
        else:
            return processor.process_with_threads(func, data_chunks)
    
    def optimize_matrix_operations(self, matrix: np.ndarray) -> np.ndarray:
        """
        优化矩阵操作。
        
        Args:
            matrix: 输入矩阵
            
        Returns:
            优化后的矩阵
        """
        try:
            # 使用更高效的数据类型
            if matrix.dtype == np.float64:
                # 检查是否可以使用float32
                if np.allclose(matrix, matrix.astype(np.float32)):
                    matrix = matrix.astype(np.float32)
            
            # 确保内存连续性
            if not matrix.flags['C_CONTIGUOUS']:
                matrix = np.ascontiguousarray(matrix)
            
            return matrix
            
        except Exception as e:
            logger.error(f"矩阵操作优化失败: {e}")
            return matrix
    
    def benchmark_function(self, func: Callable, *args, iterations: int = 100, **kwargs) -> Dict[str, float]:
        """
        基准测试函数性能。
        
        Args:
            func: 要测试的函数
            *args: 函数参数
            iterations: 迭代次数
            **kwargs: 函数关键字参数
            
        Returns:
            性能统计信息
        """
        try:
            times = []
            
            for _ in range(iterations):
                start_time = time.perf_counter()
                result = func(*args, **kwargs)
                end_time = time.perf_counter()
                times.append(end_time - start_time)
            
            times = np.array(times)
            
            benchmark_stats = {
                'mean_time': np.mean(times),
                'std_time': np.std(times),
                'min_time': np.min(times),
                'max_time': np.max(times),
                'median_time': np.median(times),
                'iterations': iterations
            }
            
            logger.info(f"函数 {func.__name__} 基准测试完成: "
                       f"平均时间 {benchmark_stats['mean_time']:.6f}s")
            
            return benchmark_stats
            
        except Exception as e:
            logger.error(f"基准测试失败: {e}")
            return {}
    
    def create_optimized_rolling_function(self, window: int) -> Callable:
        """
        创建优化的滚动窗口函数。
        
        Args:
            window: 窗口大小
            
        Returns:
            优化的滚动函数
        """
        if self.numba_available:
            @jit(nopython=True)
            def rolling_mean_numba(data):
                n = len(data)
                result = np.empty(n)
                result[:window-1] = np.nan
                
                for i in range(window-1, n):
                    result[i] = np.mean(data[i-window+1:i+1])
                
                return result
            
            return rolling_mean_numba
        else:
            def rolling_mean_numpy(data):
                return pd.Series(data).rolling(window=window).mean().values
            
            return rolling_mean_numpy
    
    def optimize_correlation_calculation(self, data: np.ndarray) -> np.ndarray:
        """
        优化相关性计算。
        
        Args:
            data: 输入数据矩阵
            
        Returns:
            相关性矩阵
        """
        try:
            if self.numba_available:
                return self._correlation_numba(data)
            else:
                return np.corrcoef(data.T)
                
        except Exception as e:
            logger.error(f"相关性计算优化失败: {e}")
            return np.corrcoef(data.T)
    
    @staticmethod
    def _correlation_numba(data: np.ndarray) -> np.ndarray:
        """使用Numba优化的相关性计算。"""
        if NUMBA_AVAILABLE:
            @jit(nopython=True)
            def correlation_matrix(X):
                n, p = X.shape
                corr = np.empty((p, p))
                
                for i in range(p):
                    for j in range(i, p):
                        if i == j:
                            corr[i, j] = 1.0
                        else:
                            # 计算皮尔逊相关系数
                            x = X[:, i]
                            y = X[:, j]
                            
                            mean_x = np.mean(x)
                            mean_y = np.mean(y)
                            
                            num = np.sum((x - mean_x) * (y - mean_y))
                            den = np.sqrt(np.sum((x - mean_x)**2) * np.sum((y - mean_y)**2))
                            
                            if den == 0:
                                corr[i, j] = 0.0
                            else:
                                corr[i, j] = num / den
                            
                            corr[j, i] = corr[i, j]
                
                return corr
            
            return correlation_matrix(data)
        else:
            return np.corrcoef(data.T)
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """
        获取优化统计信息。
        
        Returns:
            优化统计字典
        """
        stats = {
            'numba_available': self.numba_available,
            'cupy_available': self.cupy_available,
            'cache_size': len(self.optimization_cache),
            'performance_stats': self.performance_stats.copy()
        }
        
        return stats


def optimize_computation(use_jit: bool = True, use_cache: bool = True, cache_size: int = 128):
    """
    装饰器：优化计算函数。
    
    Args:
        use_jit: 是否使用JIT编译
        use_cache: 是否使用缓存
        cache_size: 缓存大小
        
    Returns:
        装饰后的函数
    """
    def decorator(func):
        optimizer = ComputeOptimizer()
        
        # 应用JIT编译
        if use_jit and optimizer.numba_available:
            func = optimizer.jit_compile(func)
        
        # 应用缓存
        if use_cache:
            func = optimizer.cache_computation(func, cache_size)
        
        return func
    
    return decorator


class FastMath:
    """快速数学计算工具。"""
    
    @staticmethod
    def fast_exp(x: np.ndarray) -> np.ndarray:
        """快速指数函数。"""
        if NUMBA_AVAILABLE:
            @jit(nopython=True)
            def exp_numba(arr):
                return np.exp(arr)
            return exp_numba(x)
        else:
            return np.exp(x)
    
    @staticmethod
    def fast_log(x: np.ndarray) -> np.ndarray:
        """快速对数函数。"""
        if NUMBA_AVAILABLE:
            @jit(nopython=True)
            def log_numba(arr):
                return np.log(arr)
            return log_numba(x)
        else:
            return np.log(x)
    
    @staticmethod
    def fast_sqrt(x: np.ndarray) -> np.ndarray:
        """快速平方根函数。"""
        if NUMBA_AVAILABLE:
            @jit(nopython=True)
            def sqrt_numba(arr):
                return np.sqrt(arr)
            return sqrt_numba(x)
        else:
            return np.sqrt(x)
    
    @staticmethod
    def fast_sum(x: np.ndarray, axis: Optional[int] = None) -> Union[float, np.ndarray]:
        """快速求和函数。"""
        if NUMBA_AVAILABLE and axis is None:
            @jit(nopython=True)
            def sum_numba(arr):
                return np.sum(arr)
            return sum_numba(x)
        else:
            return np.sum(x, axis=axis)
    
    @staticmethod
    def fast_mean(x: np.ndarray, axis: Optional[int] = None) -> Union[float, np.ndarray]:
        """快速均值函数。"""
        if NUMBA_AVAILABLE and axis is None:
            @jit(nopython=True)
            def mean_numba(arr):
                return np.mean(arr)
            return mean_numba(x)
        else:
            return np.mean(x, axis=axis)


class AlgorithmOptimizer:
    """算法优化器。"""
    
    @staticmethod
    def optimize_sorting(data: np.ndarray, algorithm: str = 'auto') -> np.ndarray:
        """
        优化排序算法。
        
        Args:
            data: 输入数据
            algorithm: 排序算法类型
            
        Returns:
            排序后的数据
        """
        if algorithm == 'auto':
            # 根据数据大小选择算法
            if len(data) < 1000:
                return np.sort(data, kind='quicksort')
            else:
                return np.sort(data, kind='mergesort')
        else:
            return np.sort(data, kind=algorithm)
    
    @staticmethod
    def optimize_search(data: np.ndarray, target: float) -> int:
        """
        优化搜索算法。
        
        Args:
            data: 已排序的数据
            target: 目标值
            
        Returns:
            目标值的索引
        """
        return np.searchsorted(data, target)
    
    @staticmethod
    def optimize_unique(data: np.ndarray) -> np.ndarray:
        """
        优化去重算法。
        
        Args:
            data: 输入数据
            
        Returns:
            去重后的数据
        """
        if len(data) < 10000:
            return np.unique(data)
        else:
            # 对于大数据，使用pandas可能更快
            return pd.Series(data).drop_duplicates().values
