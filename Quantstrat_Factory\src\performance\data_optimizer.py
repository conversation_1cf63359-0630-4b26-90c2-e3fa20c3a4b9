"""
数据优化器模块。

提供数据类型优化、压缩、向量化计算等功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, List, Union, Iterator
import logging
import pickle
import gzip
from functools import wraps
import time

logger = logging.getLogger(__name__)


class DataOptimizer:
    """数据优化器。"""
    
    def __init__(self):
        """初始化数据优化器。"""
        self.optimization_stats = {}
    
    def optimize_dtypes(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化DataFrame的数据类型以减少内存使用。
        
        Args:
            df: 输入DataFrame
            
        Returns:
            优化后的DataFrame
        """
        try:
            original_memory = df.memory_usage(deep=True).sum()
            optimized_df = df.copy()
            
            for col in optimized_df.columns:
                col_type = optimized_df[col].dtype
                
                if col_type == 'object':
                    # 尝试转换为数值类型
                    try:
                        optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
                    except (ValueError, TypeError):
                        # 如果不能转换为数值，检查是否可以转换为分类类型
                        if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                            optimized_df[col] = optimized_df[col].astype('category')
                
                elif col_type in ['int64', 'int32']:
                    # 优化整数类型
                    optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
                
                elif col_type in ['float64', 'float32']:
                    # 优化浮点数类型
                    optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
            
            optimized_memory = optimized_df.memory_usage(deep=True).sum()
            memory_reduction = (original_memory - optimized_memory) / original_memory * 100
            
            self.optimization_stats['dtype_optimization'] = {
                'original_memory_mb': original_memory / 1024 / 1024,
                'optimized_memory_mb': optimized_memory / 1024 / 1024,
                'reduction_percent': memory_reduction
            }
            
            logger.info(f"数据类型优化完成，内存减少 {memory_reduction:.2f}%")
            return optimized_df
            
        except Exception as e:
            logger.error(f"数据类型优化失败: {e}")
            return df
    
    def optimize_categorical_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化分类数据。
        
        Args:
            df: 输入DataFrame
            
        Returns:
            优化后的DataFrame
        """
        try:
            optimized_df = df.copy()
            
            for col in optimized_df.columns:
                if optimized_df[col].dtype == 'object':
                    # 计算唯一值比例
                    unique_ratio = optimized_df[col].nunique() / len(optimized_df)
                    
                    # 如果唯一值比例小于50%，转换为分类类型
                    if unique_ratio < 0.5:
                        optimized_df[col] = optimized_df[col].astype('category')
                        logger.info(f"列 {col} 转换为分类类型，唯一值比例: {unique_ratio:.2%}")
            
            return optimized_df
            
        except Exception as e:
            logger.error(f"分类数据优化失败: {e}")
            return df
    
    def compress_data(self, data: Any) -> bytes:
        """
        压缩数据。
        
        Args:
            data: 要压缩的数据
            
        Returns:
            压缩后的字节数据
        """
        try:
            # 序列化数据
            serialized_data = pickle.dumps(data)
            
            # 使用gzip压缩
            compressed_data = gzip.compress(serialized_data)
            
            compression_ratio = len(compressed_data) / len(serialized_data)
            logger.info(f"数据压缩完成，压缩比: {compression_ratio:.2%}")
            
            return compressed_data
            
        except Exception as e:
            logger.error(f"数据压缩失败: {e}")
            return b''
    
    def decompress_data(self, compressed_data: bytes) -> Any:
        """
        解压缩数据。
        
        Args:
            compressed_data: 压缩的字节数据
            
        Returns:
            解压缩后的数据
        """
        try:
            # 解压缩
            decompressed_data = gzip.decompress(compressed_data)
            
            # 反序列化
            data = pickle.loads(decompressed_data)
            
            return data
            
        except Exception as e:
            logger.error(f"数据解压缩失败: {e}")
            return None
    
    def process_in_chunks(
        self, 
        data: pd.DataFrame, 
        process_func: Callable, 
        chunk_size: int = 10000
    ) -> List[Any]:
        """
        分块处理大型数据。
        
        Args:
            data: 输入数据
            process_func: 处理函数
            chunk_size: 块大小
            
        Returns:
            处理结果列表
        """
        try:
            results = []
            
            for i in range(0, len(data), chunk_size):
                chunk = data.iloc[i:i + chunk_size]
                result = process_func(chunk)
                results.append(result)
                
                if i % (chunk_size * 10) == 0:
                    logger.info(f"已处理 {i + len(chunk)} / {len(data)} 行")
            
            logger.info(f"分块处理完成，共处理 {len(results)} 个块")
            return results
            
        except Exception as e:
            logger.error(f"分块处理失败: {e}")
            return []
    
    def vectorized_calculation(self, data: np.ndarray) -> np.ndarray:
        """
        向量化计算示例。
        
        Args:
            data: 输入数组
            
        Returns:
            计算结果
        """
        try:
            # 向量化计算：计算每个元素的平方根加上其平方
            result = np.sqrt(data) + np.square(data)
            return result
            
        except Exception as e:
            logger.error(f"向量化计算失败: {e}")
            return np.array([])
    
    def loop_calculation(self, data: np.ndarray) -> np.ndarray:
        """
        循环计算示例（用于性能对比）。
        
        Args:
            data: 输入数组
            
        Returns:
            计算结果
        """
        try:
            result = np.zeros_like(data)
            
            for i in range(len(data)):
                result[i] = np.sqrt(data[i]) + np.square(data[i])
            
            return result
            
        except Exception as e:
            logger.error(f"循环计算失败: {e}")
            return np.array([])
    
    def create_efficient_index(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """
        创建高效索引。
        
        Args:
            df: 输入DataFrame
            columns: 索引列
            
        Returns:
            带索引的DataFrame
        """
        try:
            if len(columns) == 1:
                indexed_df = df.set_index(columns[0])
            else:
                indexed_df = df.set_index(columns)
            
            logger.info(f"创建索引完成，索引列: {columns}")
            return indexed_df
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            return df
    
    def optimize_string_operations(self, series: pd.Series) -> pd.Series:
        """
        优化字符串操作。
        
        Args:
            series: 字符串Series
            
        Returns:
            优化后的Series
        """
        try:
            if series.dtype != 'object':
                return series
            
            # 使用字符串访问器进行优化
            if series.str.len().max() < 50:  # 短字符串
                # 转换为分类类型
                return series.astype('category')
            else:
                # 保持原样或进行其他优化
                return series
                
        except Exception as e:
            logger.error(f"字符串操作优化失败: {e}")
            return series
    
    def batch_apply(
        self, 
        df: pd.DataFrame, 
        func: Callable, 
        batch_size: int = 1000
    ) -> pd.DataFrame:
        """
        批量应用函数。
        
        Args:
            df: 输入DataFrame
            func: 应用的函数
            batch_size: 批次大小
            
        Returns:
            处理后的DataFrame
        """
        try:
            results = []
            
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i + batch_size]
                batch_result = func(batch)
                results.append(batch_result)
            
            return pd.concat(results, ignore_index=True)
            
        except Exception as e:
            logger.error(f"批量应用失败: {e}")
            return df
    
    def get_optimization_stats(self) -> dict:
        """
        获取优化统计信息。
        
        Returns:
            优化统计字典
        """
        return self.optimization_stats.copy()
    
    def benchmark_operation(self, func: Callable, *args, **kwargs) -> dict:
        """
        基准测试操作。
        
        Args:
            func: 要测试的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            基准测试结果
        """
        try:
            start_time = time.time()
            start_memory = self._get_memory_usage()
            
            result = func(*args, **kwargs)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            benchmark_result = {
                'execution_time': end_time - start_time,
                'memory_usage': end_memory - start_memory,
                'function_name': func.__name__,
                'result_size': len(result) if hasattr(result, '__len__') else 1
            }
            
            return benchmark_result
            
        except Exception as e:
            logger.error(f"基准测试失败: {e}")
            return {}
    
    def _get_memory_usage(self) -> float:
        """获取当前内存使用量（MB）。"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            logger.warning("psutil未安装，无法获取内存使用量")
            return 0.0
        except Exception as e:
            logger.error(f"获取内存使用量失败: {e}")
            return 0.0


def optimize_dataframe(func):
    """
    装饰器：自动优化DataFrame返回值。
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        if isinstance(result, pd.DataFrame):
            optimizer = DataOptimizer()
            optimized_result = optimizer.optimize_dtypes(result)
            return optimized_result
        
        return result
    
    return wrapper


def time_execution(func):
    """
    装饰器：测量函数执行时间。
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        execution_time = end_time - start_time
        logger.info(f"函数 {func.__name__} 执行时间: {execution_time:.4f} 秒")
        
        return result
    
    return wrapper
