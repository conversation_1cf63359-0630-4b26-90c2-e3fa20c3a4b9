"""
内存管理器模块。

提供内存监控、优化和垃圾回收功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, Dict, Optional
import logging
import gc
import sys
import time
from functools import wraps

logger = logging.getLogger(__name__)

# 尝试导入psutil进行系统内存监控
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil未安装，某些内存监控功能将不可用")


class MemoryManager:
    """内存管理器。"""
    
    def __init__(self):
        """初始化内存管理器。"""
        self.memory_stats = {}
        self.gc_stats = {}
    
    def get_memory_usage(self) -> Dict[str, Any]:
        """
        获取当前内存使用情况。
        
        Returns:
            内存使用信息字典
        """
        memory_info = {}
        
        try:
            if PSUTIL_AVAILABLE:
                # 系统内存信息
                system_memory = psutil.virtual_memory()
                memory_info.update({
                    'total': system_memory.total,
                    'available': system_memory.available,
                    'percent': system_memory.percent,
                    'used': system_memory.used,
                    'free': system_memory.free
                })
                
                # 当前进程内存信息
                process = psutil.Process()
                process_memory = process.memory_info()
                memory_info.update({
                    'process_rss': process_memory.rss,
                    'process_vms': process_memory.vms,
                    'process_percent': process.memory_percent()
                })
            
            # Python对象内存信息
            memory_info.update({
                'python_objects': sys.getsizeof(gc.get_objects()),
                'gc_counts': gc.get_count()
            })
            
        except Exception as e:
            logger.error(f"获取内存使用情况失败: {e}")
        
        return memory_info
    
    def optimize_memory(self, data: Any) -> Any:
        """
        优化数据的内存使用。
        
        Args:
            data: 输入数据
            
        Returns:
            优化后的数据
        """
        try:
            if isinstance(data, pd.DataFrame):
                return self._optimize_dataframe_memory(data)
            elif isinstance(data, pd.Series):
                return self._optimize_series_memory(data)
            elif isinstance(data, np.ndarray):
                return self._optimize_array_memory(data)
            else:
                return data
                
        except Exception as e:
            logger.error(f"内存优化失败: {e}")
            return data
    
    def _optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用。"""
        original_memory = df.memory_usage(deep=True).sum()
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            optimized_df[col] = self._optimize_series_memory(optimized_df[col])
        
        optimized_memory = optimized_df.memory_usage(deep=True).sum()
        reduction = (original_memory - optimized_memory) / original_memory * 100
        
        logger.info(f"DataFrame内存优化完成，减少 {reduction:.2f}%")
        return optimized_df
    
    def _optimize_series_memory(self, series: pd.Series) -> pd.Series:
        """优化Series内存使用。"""
        try:
            if series.dtype == 'object':
                # 尝试转换为数值类型
                try:
                    return pd.to_numeric(series, downcast='integer')
                except (ValueError, TypeError):
                    # 检查是否适合分类类型
                    if series.nunique() / len(series) < 0.5:
                        return series.astype('category')
                    return series
            
            elif series.dtype in ['int64', 'int32']:
                return pd.to_numeric(series, downcast='integer')
            
            elif series.dtype in ['float64', 'float32']:
                return pd.to_numeric(series, downcast='float')
            
            else:
                return series
                
        except Exception as e:
            logger.error(f"Series内存优化失败: {e}")
            return series
    
    def _optimize_array_memory(self, array: np.ndarray) -> np.ndarray:
        """优化NumPy数组内存使用。"""
        try:
            if array.dtype == np.float64:
                # 检查是否可以降级为float32
                if np.allclose(array, array.astype(np.float32)):
                    return array.astype(np.float32)
            
            elif array.dtype == np.int64:
                # 检查是否可以降级为更小的整数类型
                if array.min() >= np.iinfo(np.int32).min and array.max() <= np.iinfo(np.int32).max:
                    return array.astype(np.int32)
                elif array.min() >= np.iinfo(np.int16).min and array.max() <= np.iinfo(np.int16).max:
                    return array.astype(np.int16)
                elif array.min() >= np.iinfo(np.int8).min and array.max() <= np.iinfo(np.int8).max:
                    return array.astype(np.int8)
            
            return array
            
        except Exception as e:
            logger.error(f"数组内存优化失败: {e}")
            return array
    
    def force_garbage_collection(self) -> int:
        """
        强制执行垃圾回收。
        
        Returns:
            回收的对象数量
        """
        try:
            # 记录回收前的内存使用
            before_memory = self.get_memory_usage()
            
            # 执行垃圾回收
            collected = gc.collect()
            
            # 记录回收后的内存使用
            after_memory = self.get_memory_usage()
            
            # 计算释放的内存
            if PSUTIL_AVAILABLE and 'process_rss' in before_memory and 'process_rss' in after_memory:
                freed_memory = before_memory['process_rss'] - after_memory['process_rss']
                logger.info(f"垃圾回收完成，回收 {collected} 个对象，释放 {freed_memory / 1024 / 1024:.2f} MB")
            else:
                logger.info(f"垃圾回收完成，回收 {collected} 个对象")
            
            # 更新统计信息
            self.gc_stats = {
                'objects_collected': collected,
                'memory_before': before_memory,
                'memory_after': after_memory,
                'timestamp': time.time()
            }
            
            return collected
            
        except Exception as e:
            logger.error(f"垃圾回收失败: {e}")
            return 0
    
    def monitor_memory_usage(self, func: Callable) -> Callable:
        """
        装饰器：监控函数的内存使用。
        
        Args:
            func: 被监控的函数
            
        Returns:
            装饰后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时的内存使用
            start_memory = self.get_memory_usage()
            start_time = time.time()
            
            try:
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录结束时的内存使用
                end_memory = self.get_memory_usage()
                end_time = time.time()
                
                # 计算内存增量
                if PSUTIL_AVAILABLE and 'process_rss' in start_memory and 'process_rss' in end_memory:
                    memory_delta = end_memory['process_rss'] - start_memory['process_rss']
                    logger.info(f"函数 {func.__name__} 内存使用: {memory_delta / 1024 / 1024:.2f} MB, "
                               f"执行时间: {end_time - start_time:.2f} 秒")
                
                return result
                
            except Exception as e:
                logger.error(f"函数 {func.__name__} 执行失败: {e}")
                raise
        
        return wrapper
    
    def profile_memory(self, func: Callable, *args, **kwargs) -> Dict[str, Any]:
        """
        分析函数的内存使用情况。
        
        Args:
            func: 要分析的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            内存分析结果
        """
        try:
            # 强制垃圾回收以获得准确的基线
            gc.collect()
            
            # 记录基线内存
            baseline_memory = self.get_memory_usage()
            
            # 执行函数
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            # 记录峰值内存
            peak_memory = self.get_memory_usage()
            
            # 再次垃圾回收
            gc.collect()
            final_memory = self.get_memory_usage()
            
            # 计算内存指标
            profile_result = {
                'function_name': func.__name__,
                'execution_time': end_time - start_time,
                'baseline_memory': baseline_memory,
                'peak_memory': peak_memory,
                'final_memory': final_memory
            }
            
            if PSUTIL_AVAILABLE and 'process_rss' in baseline_memory:
                profile_result.update({
                    'peak_memory_mb': peak_memory['process_rss'] / 1024 / 1024,
                    'memory_increment_mb': (peak_memory['process_rss'] - baseline_memory['process_rss']) / 1024 / 1024,
                    'memory_retained_mb': (final_memory['process_rss'] - baseline_memory['process_rss']) / 1024 / 1024
                })
            
            return profile_result
            
        except Exception as e:
            logger.error(f"内存分析失败: {e}")
            return {}
    
    def set_memory_limit(self, limit_mb: int) -> bool:
        """
        设置内存使用限制。
        
        Args:
            limit_mb: 内存限制（MB）
            
        Returns:
            是否设置成功
        """
        try:
            if not PSUTIL_AVAILABLE:
                logger.warning("psutil未安装，无法设置内存限制")
                return False
            
            import resource
            
            # 设置内存限制
            limit_bytes = limit_mb * 1024 * 1024
            resource.setrlimit(resource.RLIMIT_AS, (limit_bytes, limit_bytes))
            
            logger.info(f"内存限制设置为 {limit_mb} MB")
            return True
            
        except Exception as e:
            logger.error(f"设置内存限制失败: {e}")
            return False
    
    def check_memory_threshold(self, threshold_percent: float = 80.0) -> bool:
        """
        检查内存使用是否超过阈值。
        
        Args:
            threshold_percent: 内存使用阈值百分比
            
        Returns:
            是否超过阈值
        """
        try:
            memory_info = self.get_memory_usage()
            
            if PSUTIL_AVAILABLE and 'percent' in memory_info:
                current_percent = memory_info['percent']
                
                if current_percent > threshold_percent:
                    logger.warning(f"内存使用超过阈值: {current_percent:.1f}% > {threshold_percent}%")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"检查内存阈值失败: {e}")
            return False
    
    def auto_cleanup(self, threshold_percent: float = 80.0) -> bool:
        """
        自动清理内存。
        
        Args:
            threshold_percent: 触发清理的内存阈值
            
        Returns:
            是否执行了清理
        """
        try:
            if self.check_memory_threshold(threshold_percent):
                logger.info("内存使用过高，执行自动清理")
                
                # 强制垃圾回收
                collected = self.force_garbage_collection()
                
                # 再次检查内存使用
                if not self.check_memory_threshold(threshold_percent):
                    logger.info("自动清理成功")
                    return True
                else:
                    logger.warning("自动清理后内存使用仍然过高")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"自动清理失败: {e}")
            return False
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        获取内存管理统计信息。
        
        Returns:
            统计信息字典
        """
        stats = {
            'current_memory': self.get_memory_usage(),
            'gc_stats': self.gc_stats.copy()
        }
        
        return stats


def memory_efficient(auto_cleanup: bool = True, threshold: float = 80.0):
    """
    装饰器：使函数更加内存高效。
    
    Args:
        auto_cleanup: 是否自动清理内存
        threshold: 内存清理阈值
        
    Returns:
        装饰后的函数
    """
    def decorator(func):
        memory_manager = MemoryManager()
        
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 执行前检查内存
            if auto_cleanup:
                memory_manager.auto_cleanup(threshold)
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 执行后清理
            if auto_cleanup:
                memory_manager.auto_cleanup(threshold)
            
            return result
        
        return wrapper
    return decorator


class MemoryPool:
    """内存池管理器。"""
    
    def __init__(self, pool_size: int = 100):
        """
        初始化内存池。
        
        Args:
            pool_size: 池大小
        """
        self.pool_size = pool_size
        self.pool = []
        self.in_use = set()
    
    def get_array(self, shape: tuple, dtype: np.dtype = np.float64) -> np.ndarray:
        """
        从池中获取数组。
        
        Args:
            shape: 数组形状
            dtype: 数据类型
            
        Returns:
            数组对象
        """
        # 查找可重用的数组
        for i, array in enumerate(self.pool):
            if (array.shape == shape and 
                array.dtype == dtype and 
                i not in self.in_use):
                
                self.in_use.add(i)
                array.fill(0)  # 清零
                return array
        
        # 创建新数组
        new_array = np.zeros(shape, dtype=dtype)
        
        if len(self.pool) < self.pool_size:
            self.pool.append(new_array)
            self.in_use.add(len(self.pool) - 1)
        
        return new_array
    
    def return_array(self, array: np.ndarray):
        """
        将数组返回到池中。
        
        Args:
            array: 要返回的数组
        """
        for i, pool_array in enumerate(self.pool):
            if pool_array is array and i in self.in_use:
                self.in_use.remove(i)
                break
