"""
并行处理器模块。

提供多线程、多进程和异步处理功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, List, Iterator, Optional
import logging
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import multiprocessing as mp
import asyncio
import time
from functools import partial

logger = logging.getLogger(__name__)


class ParallelProcessor:
    """并行处理器。"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化并行处理器。
        
        Args:
            max_workers: 最大工作线程/进程数
        """
        self.max_workers = max_workers or mp.cpu_count()
        self.processing_stats = {}
    
    def process_with_threads(
        self, 
        func: Callable, 
        data_list: List[Any], 
        max_workers: Optional[int] = None
    ) -> List[Any]:
        """
        使用线程池处理数据。
        
        Args:
            func: 处理函数
            data_list: 数据列表
            max_workers: 最大线程数
            
        Returns:
            处理结果列表
        """
        try:
            workers = max_workers or self.max_workers
            start_time = time.time()
            
            with ThreadPoolExecutor(max_workers=workers) as executor:
                # 提交所有任务
                future_to_data = {executor.submit(func, data): data for data in data_list}
                
                # 收集结果
                results = []
                for future in as_completed(future_to_data):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"线程处理失败: {e}")
                        results.append(None)
            
            # 按原始顺序排序结果
            data_to_result = {future_to_data[future]: result 
                            for future, result in zip(future_to_data.keys(), results)}
            ordered_results = [data_to_result[data] for data in data_list]
            
            processing_time = time.time() - start_time
            self.processing_stats['thread_processing'] = {
                'items_processed': len(data_list),
                'processing_time': processing_time,
                'items_per_second': len(data_list) / processing_time,
                'workers_used': workers
            }
            
            logger.info(f"线程处理完成: {len(data_list)} 项，用时 {processing_time:.2f} 秒")
            return ordered_results
            
        except Exception as e:
            logger.error(f"线程处理失败: {e}")
            return []
    
    def process_with_processes(
        self, 
        func: Callable, 
        data_list: List[Any], 
        max_workers: Optional[int] = None
    ) -> List[Any]:
        """
        使用进程池处理数据。
        
        Args:
            func: 处理函数
            data_list: 数据列表
            max_workers: 最大进程数
            
        Returns:
            处理结果列表
        """
        try:
            workers = max_workers or self.max_workers
            start_time = time.time()
            
            with ProcessPoolExecutor(max_workers=workers) as executor:
                # 提交所有任务
                futures = [executor.submit(func, data) for data in data_list]
                
                # 收集结果
                results = []
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        logger.error(f"进程处理失败: {e}")
                        results.append(None)
            
            # 按提交顺序排序结果
            ordered_results = [future.result() for future in futures]
            
            processing_time = time.time() - start_time
            self.processing_stats['process_processing'] = {
                'items_processed': len(data_list),
                'processing_time': processing_time,
                'items_per_second': len(data_list) / processing_time,
                'workers_used': workers
            }
            
            logger.info(f"进程处理完成: {len(data_list)} 项，用时 {processing_time:.2f} 秒")
            return ordered_results
            
        except Exception as e:
            logger.error(f"进程处理失败: {e}")
            return []
    
    def process_in_batches(
        self, 
        data: pd.DataFrame, 
        batch_func: Callable, 
        batch_size: int = 1000,
        use_processes: bool = False
    ) -> List[Any]:
        """
        分批并行处理数据。
        
        Args:
            data: 输入数据
            batch_func: 批处理函数
            batch_size: 批次大小
            use_processes: 是否使用进程池
            
        Returns:
            处理结果列表
        """
        try:
            # 分割数据为批次
            batches = []
            for i in range(0, len(data), batch_size):
                batch = data.iloc[i:i + batch_size]
                batches.append(batch)
            
            logger.info(f"数据分为 {len(batches)} 个批次，每批 {batch_size} 行")
            
            # 选择处理方式
            if use_processes:
                results = self.process_with_processes(batch_func, batches)
            else:
                results = self.process_with_threads(batch_func, batches)
            
            return results
            
        except Exception as e:
            logger.error(f"批处理失败: {e}")
            return []
    
    def process_async(self, async_func: Callable, data_list: List[Any]) -> List[Any]:
        """
        异步处理数据。
        
        Args:
            async_func: 异步处理函数
            data_list: 数据列表
            
        Returns:
            处理结果列表
        """
        try:
            async def run_async_tasks():
                tasks = [async_func(data) for data in data_list]
                results = await asyncio.gather(*tasks, return_exceptions=True)
                return results
            
            # 运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(run_async_tasks())
                return results
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"异步处理失败: {e}")
            return []
    
    def parallel_apply(
        self, 
        df: pd.DataFrame, 
        func: Callable, 
        axis: int = 0,
        use_processes: bool = False
    ) -> pd.Series:
        """
        并行应用函数到DataFrame。
        
        Args:
            df: 输入DataFrame
            func: 应用的函数
            axis: 应用轴（0为行，1为列）
            use_processes: 是否使用进程池
            
        Returns:
            应用结果
        """
        try:
            if axis == 0:
                # 按行处理
                data_list = [df.iloc[i] for i in range(len(df))]
            else:
                # 按列处理
                data_list = [df.iloc[:, i] for i in range(df.shape[1])]
            
            # 选择处理方式
            if use_processes:
                results = self.process_with_processes(func, data_list)
            else:
                results = self.process_with_threads(func, data_list)
            
            return pd.Series(results)
            
        except Exception as e:
            logger.error(f"并行应用失败: {e}")
            return pd.Series()
    
    def parallel_groupby_apply(
        self, 
        df: pd.DataFrame, 
        groupby_cols: List[str], 
        func: Callable,
        use_processes: bool = False
    ) -> pd.DataFrame:
        """
        并行分组应用函数。
        
        Args:
            df: 输入DataFrame
            groupby_cols: 分组列
            func: 应用的函数
            use_processes: 是否使用进程池
            
        Returns:
            处理结果DataFrame
        """
        try:
            # 分组
            grouped = df.groupby(groupby_cols)
            groups = [group for name, group in grouped]
            
            logger.info(f"数据分为 {len(groups)} 个组")
            
            # 并行处理每个组
            if use_processes:
                results = self.process_with_processes(func, groups)
            else:
                results = self.process_with_threads(func, groups)
            
            # 合并结果
            if results and all(isinstance(r, pd.DataFrame) for r in results if r is not None):
                combined_result = pd.concat([r for r in results if r is not None], 
                                          ignore_index=True)
                return combined_result
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"并行分组应用失败: {e}")
            return pd.DataFrame()
    
    def parallel_map_reduce(
        self, 
        data: List[Any], 
        map_func: Callable, 
        reduce_func: Callable,
        use_processes: bool = False
    ) -> Any:
        """
        并行Map-Reduce处理。
        
        Args:
            data: 输入数据
            map_func: Map函数
            reduce_func: Reduce函数
            use_processes: 是否使用进程池
            
        Returns:
            最终结果
        """
        try:
            # Map阶段
            if use_processes:
                map_results = self.process_with_processes(map_func, data)
            else:
                map_results = self.process_with_threads(map_func, data)
            
            # 过滤None结果
            valid_results = [r for r in map_results if r is not None]
            
            # Reduce阶段
            if not valid_results:
                return None
            
            final_result = valid_results[0]
            for result in valid_results[1:]:
                final_result = reduce_func(final_result, result)
            
            return final_result
            
        except Exception as e:
            logger.error(f"Map-Reduce处理失败: {e}")
            return None
    
    def get_processing_stats(self) -> dict:
        """
        获取处理统计信息。
        
        Returns:
            统计信息字典
        """
        return self.processing_stats.copy()
    
    def benchmark_parallel_vs_serial(
        self, 
        func: Callable, 
        data_list: List[Any],
        use_processes: bool = False
    ) -> dict:
        """
        基准测试：并行 vs 串行处理。
        
        Args:
            func: 处理函数
            data_list: 数据列表
            use_processes: 是否使用进程池
            
        Returns:
            基准测试结果
        """
        try:
            # 串行处理
            start_time = time.time()
            serial_results = [func(data) for data in data_list]
            serial_time = time.time() - start_time
            
            # 并行处理
            start_time = time.time()
            if use_processes:
                parallel_results = self.process_with_processes(func, data_list)
            else:
                parallel_results = self.process_with_threads(func, data_list)
            parallel_time = time.time() - start_time
            
            # 计算加速比
            speedup = serial_time / parallel_time if parallel_time > 0 else 0
            
            benchmark_result = {
                'serial_time': serial_time,
                'parallel_time': parallel_time,
                'speedup': speedup,
                'efficiency': speedup / self.max_workers,
                'items_processed': len(data_list),
                'processing_type': 'process' if use_processes else 'thread'
            }
            
            logger.info(f"基准测试完成 - 串行: {serial_time:.2f}s, "
                       f"并行: {parallel_time:.2f}s, 加速比: {speedup:.2f}x")
            
            return benchmark_result
            
        except Exception as e:
            logger.error(f"基准测试失败: {e}")
            return {}


def parallel_function(use_processes: bool = False, max_workers: Optional[int] = None):
    """
    装饰器：将函数转换为并行处理版本。
    
    Args:
        use_processes: 是否使用进程池
        max_workers: 最大工作数
        
    Returns:
        装饰后的函数
    """
    def decorator(func):
        processor = ParallelProcessor(max_workers)
        
        def wrapper(data_list, *args, **kwargs):
            # 创建部分应用的函数
            partial_func = partial(func, *args, **kwargs)
            
            if use_processes:
                return processor.process_with_processes(partial_func, data_list)
            else:
                return processor.process_with_threads(partial_func, data_list)
        
        return wrapper
    return decorator


class DataFrameParallelProcessor:
    """专门用于DataFrame的并行处理器。"""
    
    def __init__(self, processor: ParallelProcessor):
        """
        初始化DataFrame并行处理器。
        
        Args:
            processor: 并行处理器实例
        """
        self.processor = processor
    
    def parallel_rolling_apply(
        self, 
        df: pd.DataFrame, 
        window: int, 
        func: Callable,
        use_processes: bool = False
    ) -> pd.DataFrame:
        """
        并行滚动窗口应用。
        
        Args:
            df: 输入DataFrame
            window: 窗口大小
            func: 应用函数
            use_processes: 是否使用进程池
            
        Returns:
            处理结果DataFrame
        """
        try:
            # 创建滚动窗口
            windows = []
            for i in range(window - 1, len(df)):
                window_data = df.iloc[i - window + 1:i + 1]
                windows.append(window_data)
            
            # 并行处理窗口
            if use_processes:
                results = self.processor.process_with_processes(func, windows)
            else:
                results = self.processor.process_with_threads(func, windows)
            
            # 构建结果DataFrame
            result_df = pd.DataFrame(results, index=df.index[window - 1:])
            
            return result_df
            
        except Exception as e:
            logger.error(f"并行滚动应用失败: {e}")
            return pd.DataFrame()
    
    def parallel_resample_apply(
        self, 
        df: pd.DataFrame, 
        freq: str, 
        func: Callable,
        use_processes: bool = False
    ) -> pd.DataFrame:
        """
        并行重采样应用。
        
        Args:
            df: 输入DataFrame（需要时间索引）
            freq: 重采样频率
            func: 应用函数
            use_processes: 是否使用进程池
            
        Returns:
            处理结果DataFrame
        """
        try:
            # 重采样分组
            resampled = df.resample(freq)
            groups = [group for name, group in resampled]
            
            # 并行处理每个组
            if use_processes:
                results = self.processor.process_with_processes(func, groups)
            else:
                results = self.processor.process_with_threads(func, groups)
            
            # 合并结果
            if results and all(isinstance(r, (pd.Series, pd.DataFrame)) for r in results if r is not None):
                combined_result = pd.concat([r for r in results if r is not None])
                return combined_result
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"并行重采样应用失败: {e}")
            return pd.DataFrame()
