"""
资源使用优化器模块。

提供内存、CPU、磁盘使用效率优化功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, Dict, List, Optional, Union
import logging
import time
import os
import gc
import threading
from functools import wraps
from pathlib import Path
import tempfile
import shutil

logger = logging.getLogger(__name__)

# 尝试导入系统监控库
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil未安装，系统监控功能将不可用")


class ResourceOptimizer:
    """资源使用优化器。"""
    
    def __init__(self):
        """初始化资源优化器。"""
        self.resource_stats = {}
        self.optimization_history = []
        self.temp_files = []
        
        # 启动资源监控线程
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
    
    def optimize_memory_usage(self, func: Callable) -> Callable:
        """
        优化内存使用装饰器。
        
        Args:
            func: 要优化的函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时的内存使用
            initial_memory = self._get_memory_usage()
            
            try:
                # 执行前清理
                self._cleanup_memory()
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 执行后清理
                self._cleanup_memory()
                
                # 记录内存使用情况
                final_memory = self._get_memory_usage()
                memory_delta = final_memory - initial_memory
                
                self._record_memory_usage(func.__name__, memory_delta)
                
                return result
                
            except Exception as e:
                logger.error(f"内存优化执行失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def optimize_cpu_usage(self, max_cpu_percent: float = 80.0):
        """
        优化CPU使用装饰器。
        
        Args:
            max_cpu_percent: 最大CPU使用率
            
        Returns:
            装饰器函数
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 检查CPU使用率
                if self._get_cpu_usage() > max_cpu_percent:
                    logger.warning(f"CPU使用率过高，延迟执行 {func.__name__}")
                    time.sleep(0.1)  # 短暂延迟
                
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                self._record_cpu_usage(func.__name__, execution_time)
                
                return result
            
            return wrapper
        return decorator
    
    def optimize_disk_usage(self, func: Callable) -> Callable:
        """
        优化磁盘使用装饰器。
        
        Args:
            func: 要优化的函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 记录开始时的磁盘使用
            initial_disk = self._get_disk_usage()
            
            try:
                # 清理临时文件
                self._cleanup_temp_files()
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 记录磁盘使用情况
                final_disk = self._get_disk_usage()
                disk_delta = final_disk - initial_disk
                
                self._record_disk_usage(func.__name__, disk_delta)
                
                return result
                
            except Exception as e:
                logger.error(f"磁盘优化执行失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def create_temp_file(self, suffix: str = '.tmp', prefix: str = 'quantstrat_') -> str:
        """
        创建临时文件。
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            临时文件路径
        """
        try:
            temp_fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
            os.close(temp_fd)  # 关闭文件描述符
            
            self.temp_files.append(temp_path)
            logger.debug(f"创建临时文件: {temp_path}")
            
            return temp_path
            
        except Exception as e:
            logger.error(f"创建临时文件失败: {e}")
            return ""
    
    def create_temp_dir(self, prefix: str = 'quantstrat_') -> str:
        """
        创建临时目录。
        
        Args:
            prefix: 目录前缀
            
        Returns:
            临时目录路径
        """
        try:
            temp_dir = tempfile.mkdtemp(prefix=prefix)
            self.temp_files.append(temp_dir)
            logger.debug(f"创建临时目录: {temp_dir}")
            
            return temp_dir
            
        except Exception as e:
            logger.error(f"创建临时目录失败: {e}")
            return ""
    
    def optimize_dataframe_memory(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        优化DataFrame内存使用。
        
        Args:
            df: 输入DataFrame
            
        Returns:
            优化后的DataFrame
        """
        try:
            original_memory = df.memory_usage(deep=True).sum()
            optimized_df = df.copy()
            
            # 优化数据类型
            for col in optimized_df.columns:
                col_type = optimized_df[col].dtype
                
                if col_type == 'object':
                    # 尝试转换为数值类型
                    try:
                        optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
                    except (ValueError, TypeError):
                        # 检查是否适合分类类型
                        if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                            optimized_df[col] = optimized_df[col].astype('category')
                
                elif col_type in ['int64', 'int32']:
                    optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
                
                elif col_type in ['float64', 'float32']:
                    optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
            
            optimized_memory = optimized_df.memory_usage(deep=True).sum()
            memory_reduction = (original_memory - optimized_memory) / original_memory * 100
            
            logger.info(f"DataFrame内存优化完成，减少 {memory_reduction:.2f}%")
            return optimized_df
            
        except Exception as e:
            logger.error(f"DataFrame内存优化失败: {e}")
            return df
    
    def optimize_file_operations(self, file_path: str, operation: str = 'read') -> Any:
        """
        优化文件操作。
        
        Args:
            file_path: 文件路径
            operation: 操作类型 ('read', 'write')
            
        Returns:
            优化后的操作结果
        """
        try:
            file_path = Path(file_path)
            
            if operation == 'read':
                # 检查文件大小，选择合适的读取方式
                file_size = file_path.stat().st_size
                
                if file_size > 100 * 1024 * 1024:  # 100MB
                    logger.info(f"大文件读取优化: {file_path}")
                    return self._read_large_file(file_path)
                else:
                    return self._read_normal_file(file_path)
            
            elif operation == 'write':
                return self._optimize_write_operation(file_path)
                
        except Exception as e:
            logger.error(f"文件操作优化失败: {e}")
            return None
    
    def _read_large_file(self, file_path: Path) -> pd.DataFrame:
        """读取大文件。"""
        try:
            # 使用分块读取
            chunk_size = 10000
            chunks = []
            
            for chunk in pd.read_csv(file_path, chunksize=chunk_size):
                # 优化每个块的内存使用
                optimized_chunk = self.optimize_dataframe_memory(chunk)
                chunks.append(optimized_chunk)
            
            # 合并所有块
            result = pd.concat(chunks, ignore_index=True)
            logger.info(f"大文件读取完成: {len(result)} 行")
            
            return result
            
        except Exception as e:
            logger.error(f"大文件读取失败: {e}")
            return pd.DataFrame()
    
    def _read_normal_file(self, file_path: Path) -> pd.DataFrame:
        """读取普通文件。"""
        try:
            df = pd.read_csv(file_path)
            return self.optimize_dataframe_memory(df)
            
        except Exception as e:
            logger.error(f"文件读取失败: {e}")
            return pd.DataFrame()
    
    def _optimize_write_operation(self, file_path: Path) -> bool:
        """优化写入操作。"""
        try:
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 检查磁盘空间
            if not self._check_disk_space(file_path.parent):
                logger.error("磁盘空间不足")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"写入操作优化失败: {e}")
            return False
    
    def _check_disk_space(self, path: Path, min_free_gb: float = 1.0) -> bool:
        """检查磁盘空间。"""
        try:
            if PSUTIL_AVAILABLE:
                disk_usage = psutil.disk_usage(str(path))
                free_gb = disk_usage.free / (1024 ** 3)
                return free_gb >= min_free_gb
            else:
                return True  # 无法检查时假设有足够空间
                
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
            return True
    
    def _cleanup_memory(self):
        """清理内存。"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            if collected > 0:
                logger.debug(f"垃圾回收: {collected} 个对象")
                
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
    
    def _cleanup_temp_files(self):
        """清理临时文件。"""
        try:
            cleaned_count = 0
            
            for temp_path in self.temp_files[:]:  # 复制列表以避免修改时迭代
                try:
                    if os.path.exists(temp_path):
                        if os.path.isfile(temp_path):
                            os.remove(temp_path)
                        elif os.path.isdir(temp_path):
                            shutil.rmtree(temp_path)
                        
                        cleaned_count += 1
                        self.temp_files.remove(temp_path)
                        
                except Exception as e:
                    logger.warning(f"清理临时文件失败 {temp_path}: {e}")
            
            if cleaned_count > 0:
                logger.debug(f"清理了 {cleaned_count} 个临时文件")
                
        except Exception as e:
            logger.error(f"临时文件清理失败: {e}")
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量（MB）。"""
        try:
            if PSUTIL_AVAILABLE:
                process = psutil.Process()
                return process.memory_info().rss / (1024 * 1024)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"获取内存使用量失败: {e}")
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率。"""
        try:
            if PSUTIL_AVAILABLE:
                return psutil.cpu_percent(interval=0.1)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"获取CPU使用率失败: {e}")
            return 0.0
    
    def _get_disk_usage(self) -> float:
        """获取磁盘使用量（GB）。"""
        try:
            if PSUTIL_AVAILABLE:
                disk_usage = psutil.disk_usage('.')
                return disk_usage.used / (1024 ** 3)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"获取磁盘使用量失败: {e}")
            return 0.0
    
    def _record_memory_usage(self, func_name: str, memory_delta: float):
        """记录内存使用情况。"""
        if 'memory' not in self.resource_stats:
            self.resource_stats['memory'] = {}
        
        if func_name not in self.resource_stats['memory']:
            self.resource_stats['memory'][func_name] = []
        
        self.resource_stats['memory'][func_name].append({
            'timestamp': time.time(),
            'memory_delta': memory_delta
        })
    
    def _record_cpu_usage(self, func_name: str, execution_time: float):
        """记录CPU使用情况。"""
        if 'cpu' not in self.resource_stats:
            self.resource_stats['cpu'] = {}
        
        if func_name not in self.resource_stats['cpu']:
            self.resource_stats['cpu'][func_name] = []
        
        self.resource_stats['cpu'][func_name].append({
            'timestamp': time.time(),
            'execution_time': execution_time
        })
    
    def _record_disk_usage(self, func_name: str, disk_delta: float):
        """记录磁盘使用情况。"""
        if 'disk' not in self.resource_stats:
            self.resource_stats['disk'] = {}
        
        if func_name not in self.resource_stats['disk']:
            self.resource_stats['disk'][func_name] = []
        
        self.resource_stats['disk'][func_name].append({
            'timestamp': time.time(),
            'disk_delta': disk_delta
        })
    
    def _monitor_resources(self):
        """资源监控线程。"""
        while self.monitoring_active:
            try:
                if PSUTIL_AVAILABLE:
                    # 记录系统资源使用情况
                    memory_percent = psutil.virtual_memory().percent
                    cpu_percent = psutil.cpu_percent()
                    disk_percent = psutil.disk_usage('.').percent
                    
                    # 如果资源使用过高，触发清理
                    if memory_percent > 85:
                        logger.warning(f"内存使用过高: {memory_percent:.1f}%")
                        self._cleanup_memory()
                    
                    if disk_percent > 90:
                        logger.warning(f"磁盘使用过高: {disk_percent:.1f}%")
                        self._cleanup_temp_files()
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"资源监控失败: {e}")
                time.sleep(60)  # 出错时延长检查间隔
    
    def get_resource_report(self) -> Dict[str, Any]:
        """
        获取资源使用报告。
        
        Returns:
            资源使用报告
        """
        report = {
            'current_usage': {},
            'statistics': self.resource_stats.copy(),
            'temp_files_count': len(self.temp_files),
            'recommendations': []
        }
        
        # 获取当前资源使用情况
        if PSUTIL_AVAILABLE:
            report['current_usage'] = {
                'memory_percent': psutil.virtual_memory().percent,
                'cpu_percent': psutil.cpu_percent(),
                'disk_percent': psutil.disk_usage('.').percent,
                'process_memory_mb': self._get_memory_usage()
            }
        
        # 生成优化建议
        report['recommendations'] = self._generate_resource_recommendations()
        
        return report
    
    def _generate_resource_recommendations(self) -> List[str]:
        """生成资源优化建议。"""
        recommendations = []
        
        if PSUTIL_AVAILABLE:
            memory_percent = psutil.virtual_memory().percent
            disk_percent = psutil.disk_usage('.').percent
            
            if memory_percent > 80:
                recommendations.append("内存使用率过高，建议清理缓存或增加内存")
            
            if disk_percent > 85:
                recommendations.append("磁盘使用率过高，建议清理临时文件或增加存储空间")
            
            if len(self.temp_files) > 100:
                recommendations.append("临时文件过多，建议定期清理")
        
        return recommendations
    
    def cleanup_all(self):
        """清理所有资源。"""
        try:
            self._cleanup_memory()
            self._cleanup_temp_files()
            logger.info("资源清理完成")
            
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def stop_monitoring(self):
        """停止资源监控。"""
        self.monitoring_active = False
        logger.info("资源监控已停止")


# 全局资源优化器实例
resource_optimizer = ResourceOptimizer()

# 装饰器快捷方式
optimize_memory = resource_optimizer.optimize_memory_usage
optimize_cpu = resource_optimizer.optimize_cpu_usage
optimize_disk = resource_optimizer.optimize_disk_usage
