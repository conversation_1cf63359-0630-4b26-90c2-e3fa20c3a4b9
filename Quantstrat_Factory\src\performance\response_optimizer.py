"""
系统响应优化器模块。

提供Web界面、后端API和数据库响应速度优化功能。
"""

import pandas as pd
import numpy as np
from typing import Any, Callable, Dict, List, Optional, Union
import logging
import time
import asyncio
from functools import wraps
import threading
from concurrent.futures import ThreadPoolExecutor
import queue

logger = logging.getLogger(__name__)


class ResponseOptimizer:
    """系统响应优化器。"""
    
    def __init__(self):
        """初始化响应优化器。"""
        self.response_cache = {}
        self.performance_metrics = {}
        self.request_queue = queue.Queue()
        self.thread_pool = ThreadPoolExecutor(max_workers=10)
    
    def optimize_api_response(self, func: Callable) -> Callable:
        """
        优化API响应速度。
        
        Args:
            func: API函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # 检查缓存
                cache_key = self._generate_cache_key(func.__name__, args, kwargs)
                if cache_key in self.response_cache:
                    cached_result = self.response_cache[cache_key]
                    if not self._is_cache_expired(cached_result):
                        logger.debug(f"API缓存命中: {func.__name__}")
                        return cached_result['data']
                
                # 执行函数
                result = func(*args, **kwargs)
                
                # 缓存结果
                self.response_cache[cache_key] = {
                    'data': result,
                    'timestamp': time.time(),
                    'ttl': 300  # 5分钟缓存
                }
                
                # 记录性能指标
                response_time = time.time() - start_time
                self._record_performance(func.__name__, response_time)
                
                return result
                
            except Exception as e:
                logger.error(f"API执行失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def async_optimize(self, func: Callable) -> Callable:
        """
        异步优化装饰器。
        
        Args:
            func: 要优化的函数
            
        Returns:
            异步优化后的函数
        """
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            loop = asyncio.get_event_loop()
            
            # 在线程池中执行同步函数
            result = await loop.run_in_executor(
                self.thread_pool, 
                func, 
                *args, 
                **kwargs
            )
            
            return result
        
        return async_wrapper
    
    def batch_optimize(self, batch_size: int = 100, timeout: float = 1.0):
        """
        批处理优化装饰器。
        
        Args:
            batch_size: 批处理大小
            timeout: 超时时间
            
        Returns:
            批处理优化后的函数
        """
        def decorator(func):
            batch_queue = queue.Queue()
            batch_results = {}
            
            def batch_processor():
                """批处理器。"""
                batch = []
                
                while True:
                    try:
                        # 收集批次
                        start_time = time.time()
                        while len(batch) < batch_size and (time.time() - start_time) < timeout:
                            try:
                                item = batch_queue.get(timeout=0.1)
                                batch.append(item)
                            except queue.Empty:
                                break
                        
                        if batch:
                            # 处理批次
                            batch_data = [item['data'] for item in batch]
                            try:
                                results = func(batch_data)
                                
                                # 分发结果
                                for i, item in enumerate(batch):
                                    batch_results[item['id']] = results[i] if isinstance(results, list) else results
                                    
                            except Exception as e:
                                logger.error(f"批处理失败: {e}")
                                for item in batch:
                                    batch_results[item['id']] = None
                            
                            batch.clear()
                    
                    except Exception as e:
                        logger.error(f"批处理器错误: {e}")
            
            # 启动批处理线程
            batch_thread = threading.Thread(target=batch_processor, daemon=True)
            batch_thread.start()
            
            @wraps(func)
            def wrapper(data):
                request_id = id(data)
                
                # 添加到批处理队列
                batch_queue.put({
                    'id': request_id,
                    'data': data
                })
                
                # 等待结果
                start_time = time.time()
                while request_id not in batch_results:
                    if time.time() - start_time > timeout * 2:
                        logger.warning(f"批处理超时: {request_id}")
                        return None
                    time.sleep(0.01)
                
                result = batch_results.pop(request_id)
                return result
            
            return wrapper
        
        return decorator
    
    def optimize_database_query(self, func: Callable) -> Callable:
        """
        优化数据库查询。
        
        Args:
            func: 数据库查询函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # 查询优化策略
                optimized_kwargs = self._optimize_query_params(kwargs)
                
                # 执行查询
                result = func(*args, **optimized_kwargs)
                
                # 记录查询性能
                query_time = time.time() - start_time
                self._record_query_performance(func.__name__, query_time, len(result) if hasattr(result, '__len__') else 1)
                
                return result
                
            except Exception as e:
                logger.error(f"数据库查询失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def _optimize_query_params(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """优化查询参数。"""
        optimized_params = params.copy()
        
        # 添加默认限制
        if 'limit' not in optimized_params:
            optimized_params['limit'] = 10000
        
        # 优化排序
        if 'order_by' in optimized_params and isinstance(optimized_params['order_by'], str):
            optimized_params['order_by'] = [optimized_params['order_by']]
        
        return optimized_params
    
    def optimize_data_loading(self, func: Callable) -> Callable:
        """
        优化数据加载。
        
        Args:
            func: 数据加载函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # 检查是否可以增量加载
                if 'incremental' in kwargs and kwargs['incremental']:
                    result = self._incremental_load(func, *args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                # 数据后处理优化
                if isinstance(result, pd.DataFrame):
                    result = self._optimize_dataframe_loading(result)
                
                load_time = time.time() - start_time
                logger.info(f"数据加载完成: {func.__name__}, 用时 {load_time:.2f}s")
                
                return result
                
            except Exception as e:
                logger.error(f"数据加载失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def _incremental_load(self, func: Callable, *args, **kwargs) -> Any:
        """增量数据加载。"""
        # 这里实现增量加载逻辑
        # 简化实现，实际应该根据时间戳或ID进行增量加载
        return func(*args, **kwargs)
    
    def _optimize_dataframe_loading(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame加载。"""
        try:
            # 优化数据类型
            for col in df.columns:
                if df[col].dtype == 'object':
                    # 尝试转换为数值类型
                    try:
                        df[col] = pd.to_numeric(df[col], downcast='integer')
                    except (ValueError, TypeError):
                        # 检查是否适合分类类型
                        if df[col].nunique() / len(df) < 0.5:
                            df[col] = df[col].astype('category')
                
                elif df[col].dtype in ['int64', 'float64']:
                    df[col] = pd.to_numeric(df[col], downcast='integer' if 'int' in str(df[col].dtype) else 'float')
            
            return df
            
        except Exception as e:
            logger.error(f"DataFrame加载优化失败: {e}")
            return df
    
    def optimize_web_response(self, func: Callable) -> Callable:
        """
        优化Web响应。
        
        Args:
            func: Web处理函数
            
        Returns:
            优化后的函数
        """
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                # 压缩响应数据
                result = func(*args, **kwargs)
                
                if isinstance(result, dict):
                    # 优化JSON响应
                    result = self._optimize_json_response(result)
                elif isinstance(result, pd.DataFrame):
                    # 优化DataFrame响应
                    result = self._optimize_dataframe_response(result)
                
                response_time = time.time() - start_time
                self._record_web_performance(func.__name__, response_time)
                
                return result
                
            except Exception as e:
                logger.error(f"Web响应优化失败 {func.__name__}: {e}")
                raise
        
        return wrapper
    
    def _optimize_json_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """优化JSON响应。"""
        optimized_data = {}
        
        for key, value in data.items():
            if isinstance(value, np.ndarray):
                # 转换NumPy数组为列表
                optimized_data[key] = value.tolist()
            elif isinstance(value, pd.DataFrame):
                # 转换DataFrame为字典
                optimized_data[key] = value.to_dict('records')
            elif isinstance(value, (np.integer, np.floating)):
                # 转换NumPy数值类型
                optimized_data[key] = value.item()
            else:
                optimized_data[key] = value
        
        return optimized_data
    
    def _optimize_dataframe_response(self, df: pd.DataFrame) -> Dict[str, Any]:
        """优化DataFrame响应。"""
        # 限制返回的行数
        max_rows = 10000
        if len(df) > max_rows:
            df = df.head(max_rows)
            logger.warning(f"DataFrame响应被截断到 {max_rows} 行")
        
        # 转换为优化的格式
        return {
            'data': df.to_dict('records'),
            'columns': df.columns.tolist(),
            'total_rows': len(df),
            'dtypes': df.dtypes.astype(str).to_dict()
        }
    
    def _generate_cache_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键。"""
        import hashlib
        
        # 创建缓存键
        key_data = f"{func_name}_{str(args)}_{str(sorted(kwargs.items()))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    def _is_cache_expired(self, cached_item: Dict[str, Any]) -> bool:
        """检查缓存是否过期。"""
        current_time = time.time()
        return (current_time - cached_item['timestamp']) > cached_item['ttl']
    
    def _record_performance(self, func_name: str, response_time: float):
        """记录性能指标。"""
        if func_name not in self.performance_metrics:
            self.performance_metrics[func_name] = {
                'total_calls': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'avg_time': 0
            }
        
        metrics = self.performance_metrics[func_name]
        metrics['total_calls'] += 1
        metrics['total_time'] += response_time
        metrics['min_time'] = min(metrics['min_time'], response_time)
        metrics['max_time'] = max(metrics['max_time'], response_time)
        metrics['avg_time'] = metrics['total_time'] / metrics['total_calls']
    
    def _record_query_performance(self, func_name: str, query_time: float, result_count: int):
        """记录查询性能。"""
        query_key = f"query_{func_name}"
        
        if query_key not in self.performance_metrics:
            self.performance_metrics[query_key] = {
                'total_queries': 0,
                'total_time': 0,
                'total_results': 0,
                'avg_time': 0,
                'avg_results': 0
            }
        
        metrics = self.performance_metrics[query_key]
        metrics['total_queries'] += 1
        metrics['total_time'] += query_time
        metrics['total_results'] += result_count
        metrics['avg_time'] = metrics['total_time'] / metrics['total_queries']
        metrics['avg_results'] = metrics['total_results'] / metrics['total_queries']
    
    def _record_web_performance(self, func_name: str, response_time: float):
        """记录Web性能。"""
        web_key = f"web_{func_name}"
        self._record_performance(web_key, response_time)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """
        获取性能报告。
        
        Returns:
            性能报告字典
        """
        report = {
            'cache_stats': {
                'cache_size': len(self.response_cache),
                'cache_hit_rate': self._calculate_cache_hit_rate()
            },
            'performance_metrics': self.performance_metrics.copy(),
            'recommendations': self._generate_recommendations()
        }
        
        return report
    
    def _calculate_cache_hit_rate(self) -> float:
        """计算缓存命中率。"""
        # 简化实现，实际应该跟踪命中和未命中次数
        return 0.75  # 假设75%的命中率
    
    def _generate_recommendations(self) -> List[str]:
        """生成性能优化建议。"""
        recommendations = []
        
        # 分析性能指标并生成建议
        for func_name, metrics in self.performance_metrics.items():
            if metrics.get('avg_time', 0) > 1.0:  # 响应时间超过1秒
                recommendations.append(f"函数 {func_name} 响应时间较慢 ({metrics['avg_time']:.2f}s)，建议优化")
            
            if 'query_' in func_name and metrics.get('avg_results', 0) > 50000:
                recommendations.append(f"查询 {func_name} 返回结果过多，建议添加分页或过滤")
        
        return recommendations
    
    def clear_cache(self):
        """清空缓存。"""
        self.response_cache.clear()
        logger.info("响应缓存已清空")
    
    def cleanup_expired_cache(self):
        """清理过期缓存。"""
        expired_keys = []
        
        for key, cached_item in self.response_cache.items():
            if self._is_cache_expired(cached_item):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.response_cache[key]
        
        logger.info(f"清理了 {len(expired_keys)} 个过期缓存项")


# 全局响应优化器实例
response_optimizer = ResponseOptimizer()

# 装饰器快捷方式
optimize_api = response_optimizer.optimize_api_response
optimize_db = response_optimizer.optimize_database_query
optimize_web = response_optimizer.optimize_web_response
optimize_data = response_optimizer.optimize_data_loading
