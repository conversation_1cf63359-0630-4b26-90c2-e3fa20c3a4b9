"""
实验追踪器模块。

提供实验管理、参数记录、指标追踪和结果比较功能。
"""

import json
import uuid
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field, asdict
from datetime import datetime
from pathlib import Path
import pandas as pd
import pickle
import hashlib


logger = logging.getLogger(__name__)


@dataclass
class ExperimentConfig:
    """实验配置。"""
    name: str
    description: str
    tags: Dict[str, str] = field(default_factory=dict)
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return asdict(self)


@dataclass
class ExperimentMetrics:
    """实验指标。"""
    metrics: Dict[str, float] = field(default_factory=dict)
    step: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return {
            'metrics': self.metrics,
            'step': self.step,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class Experiment:
    """实验对象。"""
    experiment_id: str
    config: ExperimentConfig
    status: str = "running"  # running, completed, failed
    start_time: datetime = field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    metrics_history: List[ExperimentMetrics] = field(default_factory=list)
    artifacts: Dict[str, str] = field(default_factory=dict)  # name -> path
    notes: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典。"""
        return {
            'experiment_id': self.experiment_id,
            'config': self.config.to_dict(),
            'status': self.status,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'metrics_history': [m.to_dict() for m in self.metrics_history],
            'artifacts': self.artifacts,
            'notes': self.notes
        }
    
    def get_latest_metrics(self) -> Optional[Dict[str, float]]:
        """获取最新指标。"""
        if not self.metrics_history:
            return None
        return self.metrics_history[-1].metrics
    
    def get_best_metric(self, metric_name: str, maximize: bool = True) -> Optional[float]:
        """获取最佳指标值。"""
        values = [m.metrics.get(metric_name) for m in self.metrics_history]
        values = [v for v in values if v is not None]
        
        if not values:
            return None
        
        return max(values) if maximize else min(values)


class ExperimentTracker:
    """实验追踪器。"""
    
    def __init__(self, tracking_dir: str = "experiments"):
        """
        初始化实验追踪器。
        
        Args:
            tracking_dir: 实验追踪目录
        """
        self.tracking_dir = Path(tracking_dir)
        self.tracking_dir.mkdir(exist_ok=True)
        
        self.experiments: Dict[str, Experiment] = {}
        self.current_experiment: Optional[Experiment] = None
        
        # 加载已有实验
        self._load_experiments()
    
    def create_experiment(self, 
                         name: str,
                         description: str = "",
                         tags: Optional[Dict[str, str]] = None,
                         parameters: Optional[Dict[str, Any]] = None) -> str:
        """
        创建新实验。
        
        Args:
            name: 实验名称
            description: 实验描述
            tags: 实验标签
            parameters: 实验参数
            
        Returns:
            实验ID
        """
        experiment_id = str(uuid.uuid4())
        
        config = ExperimentConfig(
            name=name,
            description=description,
            tags=tags or {},
            parameters=parameters or {}
        )
        
        experiment = Experiment(
            experiment_id=experiment_id,
            config=config
        )
        
        self.experiments[experiment_id] = experiment
        self.current_experiment = experiment
        
        # 保存实验
        self._save_experiment(experiment)
        
        logger.info(f"创建实验: {name} (ID: {experiment_id})")
        return experiment_id
    
    def set_current_experiment(self, experiment_id: str):
        """设置当前实验。"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        self.current_experiment = self.experiments[experiment_id]
        logger.info(f"切换到实验: {experiment_id}")
    
    def log_parameter(self, key: str, value: Any):
        """记录参数。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        self.current_experiment.config.parameters[key] = value
        self._save_experiment(self.current_experiment)
    
    def log_parameters(self, parameters: Dict[str, Any]):
        """批量记录参数。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        self.current_experiment.config.parameters.update(parameters)
        self._save_experiment(self.current_experiment)
    
    def log_metric(self, key: str, value: float, step: int = 0):
        """记录指标。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        # 查找是否已有相同step的指标
        existing_metrics = None
        for metrics in self.current_experiment.metrics_history:
            if metrics.step == step:
                existing_metrics = metrics
                break
        
        if existing_metrics:
            existing_metrics.metrics[key] = value
            existing_metrics.timestamp = datetime.now()
        else:
            metrics = ExperimentMetrics(
                metrics={key: value},
                step=step
            )
            self.current_experiment.metrics_history.append(metrics)
        
        self._save_experiment(self.current_experiment)
    
    def log_metrics(self, metrics: Dict[str, float], step: int = 0):
        """批量记录指标。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        # 查找是否已有相同step的指标
        existing_metrics = None
        for metric_entry in self.current_experiment.metrics_history:
            if metric_entry.step == step:
                existing_metrics = metric_entry
                break
        
        if existing_metrics:
            existing_metrics.metrics.update(metrics)
            existing_metrics.timestamp = datetime.now()
        else:
            metric_entry = ExperimentMetrics(
                metrics=metrics.copy(),
                step=step
            )
            self.current_experiment.metrics_history.append(metric_entry)
        
        self._save_experiment(self.current_experiment)
    
    def log_artifact(self, name: str, artifact_path: str):
        """记录工件。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        # 复制工件到实验目录
        experiment_dir = self._get_experiment_dir(self.current_experiment.experiment_id)
        artifacts_dir = experiment_dir / "artifacts"
        artifacts_dir.mkdir(exist_ok=True)
        
        # 生成唯一的工件文件名
        artifact_file = artifacts_dir / f"{name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 复制文件
        import shutil
        shutil.copy2(artifact_path, artifact_file)
        
        self.current_experiment.artifacts[name] = str(artifact_file)
        self._save_experiment(self.current_experiment)
        
        logger.info(f"记录工件: {name} -> {artifact_file}")
    
    def save_model(self, model: Any, model_name: str):
        """保存模型。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        experiment_dir = self._get_experiment_dir(self.current_experiment.experiment_id)
        models_dir = experiment_dir / "models"
        models_dir.mkdir(exist_ok=True)
        
        model_file = models_dir / f"{model_name}.pkl"
        
        with open(model_file, 'wb') as f:
            pickle.dump(model, f)
        
        self.current_experiment.artifacts[f"model_{model_name}"] = str(model_file)
        self._save_experiment(self.current_experiment)
        
        logger.info(f"保存模型: {model_name} -> {model_file}")
    
    def load_model(self, experiment_id: str, model_name: str) -> Any:
        """加载模型。"""
        if experiment_id not in self.experiments:
            raise ValueError(f"实验不存在: {experiment_id}")
        
        experiment = self.experiments[experiment_id]
        artifact_key = f"model_{model_name}"
        
        if artifact_key not in experiment.artifacts:
            raise ValueError(f"模型不存在: {model_name}")
        
        model_path = experiment.artifacts[artifact_key]
        
        with open(model_path, 'rb') as f:
            model = pickle.load(f)
        
        return model
    
    def end_experiment(self, status: str = "completed", notes: str = ""):
        """结束实验。"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        self.current_experiment.status = status
        self.current_experiment.end_time = datetime.now()
        self.current_experiment.notes = notes
        
        self._save_experiment(self.current_experiment)
        
        logger.info(f"实验结束: {self.current_experiment.experiment_id} (状态: {status})")
        self.current_experiment = None
    
    def get_experiment(self, experiment_id: str) -> Optional[Experiment]:
        """获取实验。"""
        return self.experiments.get(experiment_id)
    
    def list_experiments(self, 
                        tags: Optional[Dict[str, str]] = None,
                        status: Optional[str] = None) -> List[Experiment]:
        """列出实验。"""
        experiments = list(self.experiments.values())
        
        # 按标签过滤
        if tags:
            experiments = [
                exp for exp in experiments
                if all(exp.config.tags.get(k) == v for k, v in tags.items())
            ]
        
        # 按状态过滤
        if status:
            experiments = [exp for exp in experiments if exp.status == status]
        
        # 按开始时间排序
        experiments.sort(key=lambda x: x.start_time, reverse=True)
        
        return experiments
    
    def compare_experiments(self, experiment_ids: List[str]) -> pd.DataFrame:
        """比较实验。"""
        comparison_data = []
        
        for exp_id in experiment_ids:
            if exp_id not in self.experiments:
                continue
            
            experiment = self.experiments[exp_id]
            latest_metrics = experiment.get_latest_metrics() or {}
            
            row = {
                'experiment_id': exp_id,
                'name': experiment.config.name,
                'status': experiment.status,
                'start_time': experiment.start_time,
                'duration': self._calculate_duration(experiment)
            }
            
            # 添加参数
            for key, value in experiment.config.parameters.items():
                row[f"param_{key}"] = value
            
            # 添加指标
            for key, value in latest_metrics.items():
                row[f"metric_{key}"] = value
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def get_experiment_summary(self) -> Dict[str, Any]:
        """获取实验摘要。"""
        total_experiments = len(self.experiments)
        completed_experiments = len([e for e in self.experiments.values() if e.status == "completed"])
        running_experiments = len([e for e in self.experiments.values() if e.status == "running"])
        failed_experiments = len([e for e in self.experiments.values() if e.status == "failed"])
        
        return {
            'total_experiments': total_experiments,
            'completed_experiments': completed_experiments,
            'running_experiments': running_experiments,
            'failed_experiments': failed_experiments,
            'success_rate': completed_experiments / total_experiments if total_experiments > 0 else 0
        }
    
    def _get_experiment_dir(self, experiment_id: str) -> Path:
        """获取实验目录。"""
        experiment_dir = self.tracking_dir / experiment_id
        experiment_dir.mkdir(exist_ok=True)
        return experiment_dir
    
    def _save_experiment(self, experiment: Experiment):
        """保存实验。"""
        experiment_dir = self._get_experiment_dir(experiment.experiment_id)
        experiment_file = experiment_dir / "experiment.json"
        
        with open(experiment_file, 'w', encoding='utf-8') as f:
            json.dump(experiment.to_dict(), f, indent=2, ensure_ascii=False)
    
    def _load_experiments(self):
        """加载已有实验。"""
        if not self.tracking_dir.exists():
            return
        
        for experiment_dir in self.tracking_dir.iterdir():
            if not experiment_dir.is_dir():
                continue
            
            experiment_file = experiment_dir / "experiment.json"
            if not experiment_file.exists():
                continue
            
            try:
                with open(experiment_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                experiment = self._dict_to_experiment(data)
                self.experiments[experiment.experiment_id] = experiment
                
            except Exception as e:
                logger.warning(f"加载实验失败 {experiment_dir}: {e}")
    
    def _dict_to_experiment(self, data: Dict[str, Any]) -> Experiment:
        """字典转实验对象。"""
        config = ExperimentConfig(**data['config'])
        
        metrics_history = []
        for m_data in data.get('metrics_history', []):
            metrics = ExperimentMetrics(
                metrics=m_data['metrics'],
                step=m_data['step'],
                timestamp=datetime.fromisoformat(m_data['timestamp'])
            )
            metrics_history.append(metrics)
        
        experiment = Experiment(
            experiment_id=data['experiment_id'],
            config=config,
            status=data.get('status', 'running'),
            start_time=datetime.fromisoformat(data['start_time']),
            end_time=datetime.fromisoformat(data['end_time']) if data.get('end_time') else None,
            metrics_history=metrics_history,
            artifacts=data.get('artifacts', {}),
            notes=data.get('notes', '')
        )
        
        return experiment
    
    def _calculate_duration(self, experiment: Experiment) -> Optional[float]:
        """计算实验持续时间。"""
        if experiment.end_time:
            return (experiment.end_time - experiment.start_time).total_seconds()
        elif experiment.status == "running":
            return (datetime.now() - experiment.start_time).total_seconds()
        else:
            return None


# 全局实验追踪器实例
experiment_tracker = ExperimentTracker()


# 便捷函数
def start_experiment(name: str, description: str = "", **kwargs) -> str:
    """开始新实验。"""
    return experiment_tracker.create_experiment(name, description, **kwargs)


def log_param(key: str, value: Any):
    """记录参数。"""
    experiment_tracker.log_parameter(key, value)


def log_metric(key: str, value: float, step: int = 0):
    """记录指标。"""
    experiment_tracker.log_metric(key, value, step)


def end_experiment(status: str = "completed", notes: str = ""):
    """结束实验。"""
    experiment_tracker.end_experiment(status, notes)


# 示例使用
if __name__ == "__main__":
    print("=== 实验追踪系统示例 ===")
    
    # 创建实验
    exp_id = start_experiment(
        name="策略优化实验",
        description="测试不同参数对策略性能的影响",
        tags={"strategy": "mean_reversion", "version": "v1.0"}
    )
    
    # 记录参数
    log_param("lookback_window", 20)
    log_param("threshold", 2.0)
    log_param("position_size", 0.1)
    
    # 模拟实验过程
    import random
    for step in range(10):
        # 模拟指标
        sharpe_ratio = random.uniform(0.5, 2.0)
        max_drawdown = random.uniform(0.05, 0.2)
        total_return = random.uniform(0.1, 0.3)
        
        log_metric("sharpe_ratio", sharpe_ratio, step)
        log_metric("max_drawdown", max_drawdown, step)
        log_metric("total_return", total_return, step)
    
    # 结束实验
    end_experiment("completed", "实验成功完成")
    
    # 查看实验摘要
    summary = experiment_tracker.get_experiment_summary()
    print(f"实验摘要: {summary}")
    
    print("✅ 实验追踪系统示例完成")
