# 因子实验室 (Factor Lab)

## 1. 项目简介

因子实验室是一个功能强大的交互式 Web 应用，旨在帮助量化研究人员对股票因子进行快速验证和深入分析。它将宏观的市场模式识别与微观的因子有效性评估相结合，提供了一个从数据加载、因子计算、复合因子构建、因子评估到可视化分析的完整工作流。

## 2. 安装与运行

### 2.1 环境准备

确保您的 Python 环境已安装以下依赖：
- `dash`
- `dash-bootstrap-components`
- `pandas`
- `numpy`
- `plotly`
- `openpyxl` (用于读取 Excel 文件，如果数据源是 Excel)
- `pyarrow` (用于读取 Parquet 文件)

您可以使用 pip 安装这些依赖：
```bash
pip install dash dash-bootstrap-components pandas numpy plotly openpyxl pyarrow
```

### 2.2 数据准备

1.  **日线数据**: 确保在 `D:/PY/Data/cleaned/daily/daily_basics.parquet` 文件中存放了股票的日线数据。文件应包含 `datetime`, `symbol`, `open`, `high`, `low`, `close`, `volume` 等列。
2.  **股票列表**: 确保在 `D:/PY/Data/stock_list.txt` 文件中包含了所有需要分析的股票代码，每行一个。
3.  **特征库**: 如果您使用文件型因子，请确保在 `D:/PY/Data/features/` 目录下有相应的因子数据文件（.parquet格式）。
4.  **配置文件**: 确保项目根目录下的配置文件存在，并且其中配置了正确的数据路径。

### 2.3 运行应用

在命令行中，导航到项目根目录，然后运行 Factor Lab：
```bash
cd D:/PY/Quantstrat_Factory/
python -m src.research.factor_lab.web_app.app
```
应用启动后，您可以在浏览器中访问 `http://127.0.0.1:8051/`。

## 3. 核心功能使用说明

### 3.1 控制面板

左侧是控制面板，用于配置分析参数。

*   **日期范围**: 选择分析数据的起始和结束日期。默认日期范围为 `2020-10-09` 至 `2020-10-31`。
*   **股票池**:
    *   **静态股票池**: 选择预定义的股票池，如“沪深300”、“中证500”或“全市场”。
    *   **动态筛选池**: 选择此选项后，点击下方的“配置动态池”按钮，可以打开动态股票池筛选器，根据自定义的量价模式生成股票池。
*   **选择因子**: 选择一个或多个因子进行分析。因子分为“计算型”（如 MOM, VOL）和“文件型”（从特征库加载）。
*   **自定义复合因子**: 在文本框中输入自定义的复合因子表达式，每行一个。例如：`COMP_A = MOM + VOL`。
*   **因子评估设置**:
    *   **启用因子评估**: 勾选此项以启用因子IC计算和分层回测。
    *   **IC计算周期 (天)**: 设置计算因子IC时未来收益率的周期。
    *   **分层数量**: 设置分层回测时将股票分为多少个分层。
*   **叠加股票代码**: 在“全市场”或“动态筛选池”模式下，您可以在图表中叠加显示特定股票的因子时序图，以便进行对比分析。
*   **开始分析按钮**: 配置完成后，点击此按钮开始执行因子分析。

### 3.2 动态股票池筛选器 (高级筛选)

点击“配置动态池”按钮将打开一个模态框，您可以在其中定义复杂的量价模式来筛选股票。

*   **日期范围**: 为模式筛选指定日期范围。
*   **分段 (Segment)**: 一个模式可以由多个连续的分段组成。
    *   点击“增加分段”按钮可以添加新的分段。
    *   每个分段可以设置其**天数**。
    *   每个分段内可以定义多条**规则**。
    *   **规则**: 每条规则包含：
        *   **指标**: 如“涨跌幅 (pct_chg)”、“成交量比率 (volume_ratio)”、“Aroon Up (aroon_up)”、“Aroon Down (aroon_down)”。
        *   **操作符**: 如 `>, <, >=, <=, ==, !=`。
        *   **值**: 规则的阈值。
        *   点击“增加规则”按钮可以添加新的规则。
        *   点击“删除”按钮可以删除对应的规则。
    *   **规则逻辑**: 选择分段内多条规则之间的逻辑关系（“AND”或“OR”）。
    *   点击“删除分段”按钮可以删除整个分段。
*   **生成股票池**: 配置完模式后，点击此按钮将执行筛选，并将符合模式的股票列表存储为“动态筛选池”，供主界面的因子分析使用。

### 3.3 右侧内容区

右侧区域显示分析结果。

*   **因子评估报告**: 如果启用了因子评估，这里将显示每个因子的 IC 均值、标准差、ICIR，以及分层回测的净值曲线和性能统计表（年化收益率、年化波动率、夏普比率）。
*   **可视化分析**: 显示因子时序图。在“全市场”或“动态筛选池”模式下，默认显示因子均值时序图，并支持叠加个股。
*   **详细数据**: 显示原始的分析数据表格。

## 4. 未来展望

*   **扩展因子知识库**: 将 `FACTOR_DEFINITIONS` 外部化到配置文件，方便用户自定义和扩充。
*   **更多评估指标**: 增加最大回撤、胜率、赔率等更丰富的分层回测指标。
*   **性能优化**: 针对大数据量下的加载和计算性能进行优化。
*   **更多可视化类型**: 增加因子分布图、IC时序图等。
*   **分钟级数据分析**: 扩展平台支持分钟级因子和评估。
