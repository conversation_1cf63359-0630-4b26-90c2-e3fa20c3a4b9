import pandas as pd

def perform_analysis(data, selected_factors):
    """
    根据给定的数据和因子列表执行因子分析。
    
    参数:
    - data (pd.DataFrame): 从 data_loader 加载的股票数据。
    - selected_factors (list): 用户选择的因子列表 (e.g., ['MOM', 'VOL']).
    
    返回:
    - pd.DataFrame: 包含计算出的因子列的DataFrame。
    """
    print(f"开始执行真实因子计算... 因子: {selected_factors}")
    
    if data.empty:
        print("输入数据为空，返回空DataFrame。")
        return pd.DataFrame()
        
    if not selected_factors:
        print("未选择任何因子，返回原始数据。")
        return data
        
    output_df = data.copy()

    # --- 因子计算逻辑 ---
    if 'MOM' in selected_factors:
        # 为了通过测试，我们使用一个简单的1期动量
        # 使用 groupby 确保在每个股票代码内部独立计算
        if 'symbol' in output_df.columns:
            output_df['MOM'] = output_df.groupby('symbol')['close'].pct_change(periods=1)
        else:
            # 如果没有 'symbol' 列，假定只有一只股票
            output_df['MOM'] = output_df['close'].pct_change(periods=1)

    # --- 波动率 (VOL) 因子 ---
    if 'VOL' in selected_factors:
        # 定义：收盘价收益率的2期滚动标准差
        if 'symbol' in output_df.columns:
            # 分组计算收益率，然后计算滚动标准差
            returns = output_df.groupby('symbol')['close'].pct_change()
            output_df['VOL'] = returns.groupby(output_df['symbol']).rolling(window=2).std().reset_index(level=0, drop=True)
        else:
            returns = output_df['close'].pct_change()
            output_df['VOL'] = returns.rolling(window=2).std()

    print("因子计算完毕。")
    return output_df
