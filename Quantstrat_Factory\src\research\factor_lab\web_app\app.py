import dash
from dash import dcc, html, dash_table
from dash.dependencies import Input, Output, State, ALL, MATCH
import dash_bootstrap_components as dbc # 仍然保留dbc，因为modal组件本身是dbc的
import json
from datetime import date, datetime
import pandas as pd
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
import re

# 导入我们的新模块
try:
    # 尝试相对导入（当作为模块运行时）
    from .data_loader import load_data
    from .analysis_performer import perform_analysis
    from .composite_factor_engine import calculate_composite_factors
    from .factor_evaluator import calculate_ic, perform_quantile_analysis
    from .sieve_engine import run_sieve
except ImportError:
    # 回退到绝对导入（当直接运行时）
    from data_loader import load_data
    from analysis_performer import perform_analysis
    from composite_factor_engine import calculate_composite_factors
    from factor_evaluator import calculate_ic, perform_quantile_analysis
    from sieve_engine import run_sieve

# 外部样式表
external_stylesheets = [dbc.themes.BOOTSTRAP]

app = dash.Dash(__name__, external_stylesheets=external_stylesheets, suppress_callback_exceptions=True)

# --- 因子定义知识库 ---
FACTOR_DEFINITIONS = {
    'MOM': "**动量 (Momentum)**: 计算周期为1的日收益率，即 `(今日收盘价 / 昨日收盘价) - 1`。",
    'VOL': "**波动率 (Volatility)**: 计算周期为2的日收益率的标准差，衡量价格波动的剧烈程度。",
    'MA_5': "**5日移动平均线 (MA_5)**: 最近5个交易日收盘价的算术平均值。",
    'MA_10': "**10日移动平均线 (MA_10)**: 最近10个交易日收盘价的算术平均值。",
    'RSI_14': "**14日相对强弱指数 (RSI_14)**: 一种衡量股价近期涨跌速度和幅度的技术指标，值域在0-100之间。",
}

# --- 动态因子加载 ---
def get_available_factors():
    """扫描特征库并返回可用的日线因子列表。"""
    factors = []
    calculated_factors = ['MOM', 'VOL']
    for factor in calculated_factors:
        factors.append({'label': f'{factor} (计算型)', 'value': factor})

    try:
        # 使用新的统一特征路径 - 扫描平面结构的特征文件
        feature_store_path = Path("D:/PY/Data/features")

        if feature_store_path.exists():
            # 扫描所有 .parquet 文件（排除分钟特征目录）
            for feature_file in feature_store_path.glob("*.parquet"):
                if feature_file.name != "feature_last_update.json":
                    factor_name = feature_file.stem  # 去掉 .parquet 扩展名
                    # 根据文件名推断类别
                    if factor_name.startswith('MA_') or factor_name.startswith('RSI_'):
                        category = 'technical'
                    elif factor_name in ['pct_chg', 'volume_ratio']:
                        category = 'basic'
                    elif factor_name.startswith('aroon_'):
                        category = 'technical'
                    else:
                        category = 'other'

                    label = f'{factor_name} ({category})'
                    factors.append({'label': label, 'value': factor_name})
    except Exception as e:
        print(f"扫描特征库时出错: {e}")
        
    return sorted(factors, key=lambda x: x['label'])

def get_all_stock_symbols():
    """读取股票列表文件并返回所有股票代码作为下拉菜单选项。"""
    # 使用相对路径，从Data目录查找
    project_root = Path(__file__).resolve().parents[4]
    stock_list_path = project_root.parent / 'Data' / 'stock_list.txt'
    symbols = []
    if stock_list_path.exists():
        with open(stock_list_path, 'r', encoding='utf-8') as f:
            for line in f:
                symbol = line.strip()
                if symbol:
                    symbols.append({'label': symbol, 'value': symbol})
    else:
        print(f"警告: 股票清单文件 {stock_list_path} 不存在。无法加载股票代码选项。")
    return sorted(symbols, key=lambda x: x['label'])

# --- 动态筛选模态框布局 ---
def create_sieve_rule(segment_index, rule_index):
    return dbc.Row(id={'type': 'sieve-rule', 'index': f'{segment_index}-{rule_index}'}, className="g-1 mb-1 align-items-center", children=[
        dbc.Col(dcc.Dropdown(
            id={'type': 'sieve-metric-dropdown', 'index': f'{segment_index}-{rule_index}'},
            options=[
                {'label': '涨跌幅 (pct_chg)', 'value': 'pct_chg'},
                {'label': '成交量比率 (volume_ratio)', 'value': 'volume_ratio'},
                {'label': 'Aroon Up (aroon_up)', 'value': 'aroon_up'},
                {'label': 'Aroon Down (aroon_down)', 'value': 'aroon_down'},
            ],
            placeholder='选择指标',
        ), width=5),
        dbc.Col(dcc.Dropdown(
            id={'type': 'sieve-operator-dropdown', 'index': f'{segment_index}-{rule_index}'},
            options=[
                {'label': '>', 'value': '>'}, {'label': '<', 'value': '<'},
                {'label': '>=', 'value': '>='}, {'label': '!=', 'value': '!='},
                {'label': '==', 'value': '=='}, {'label': '<=', 'value': '<='},
            ],
            placeholder='操作符',
        ), width=2),
        dbc.Col(dcc.Input(id={'type': 'sieve-value-input', 'index': f'{segment_index}-{rule_index}'}, type='number', placeholder='值', className="form-control"), width=3),
        dbc.Col(html.Button('删除', id={'type': 'remove-rule-button', 'index': f'{segment_index}-{rule_index}'}, n_clicks=0, className="btn btn-danger btn-sm"), width=2)
    ])

def create_sieve_segment(index):
    return dbc.Card(id={'type': 'sieve-segment', 'index': index}, className="mb-3", children=[
        dbc.CardHeader(html.H6(f'分段 {index + 1}')),
        dbc.CardBody([
            dbc.Row(className="g-1 mb-2 align-items-center", children=[
                dbc.Col(html.Label('分段天数:'), width="auto"),
                dbc.Col(dcc.Input(id={'type': 'sieve-segment-length', 'index': index}, type='number', value=5, min=1, step=1, className="form-control"), width=3),
            ]),
            html.Hr(),
            html.Div(id={'type': 'sieve-rules-container', 'index': index}, children=[create_sieve_rule(index, 0)]),
            html.Button('增加规则', id={'type': 'add-rule-button', 'index': index}, n_clicks=0, className="btn btn-secondary btn-sm mt-2"),
            dbc.Row(className="g-1 mt-2 align-items-center", children=[
                dbc.Col(html.Label('规则逻辑:'), width="auto"),
                dbc.Col(dcc.RadioItems(
                    id={'type': 'sieve-logic-radioitems', 'index': index},
                    options=[{'label': 'AND', 'value': 'AND'}, {'label': 'OR', 'value': 'OR'}],
                    value='AND', inline=True, className="ms-2"
                ), width="auto"),
            ]),
            html.Button('删除分段', id={'type': 'remove-segment-button', 'index': index}, n_clicks=0, className="btn btn-danger btn-sm mt-3")
        ])
    ])

sieve_modal_layout = dbc.Modal(
    [
        dbc.ModalHeader(dbc.ModalTitle("动态股票池筛选器")),
        dbc.ModalBody(
            [
                html.P("动态池将使用主控制面板中设置的全局日期范围。"),
                html.Hr(),
                html.Div(id='sieve-segments-container', children=[create_sieve_segment(0)]),
                html.Button('增加分段', id='add-segment-button', n_clicks=0, className="btn btn-primary mt-4 w-100")
            ]
        ),
        dbc.ModalFooter(
            [
                html.Button("生成股票池", id="run-sieve-btn", n_clicks=0, className="btn btn-primary me-2"),
                html.Button("关闭", id="close-sieve-modal-btn", n_clicks=0, className="btn btn-secondary")
            ]
        ),
    ],
    id="sieve-modal",
    is_open=False,
    size="lg",
    backdrop=True,
    scrollable=True
)

factor_selection_modal_layout = dbc.Modal(
    [
        dbc.ModalHeader(dbc.ModalTitle("选择因子")),
        dbc.ModalBody([
            dcc.Checklist(
                id='factor-checklist',
                options=get_available_factors(),
                value=['MOM'], # 默认值
                inline=False,
                labelStyle={'display': 'block'} # 使每个选项独占一行
            )
        ]),
        dbc.ModalFooter([
            html.Button("确定", id="confirm-factor-selection-btn", n_clicks=0, className="btn btn-primary me-2"),
            html.Button("取消", id="cancel-factor-selection-btn", n_clicks=0, className="btn btn-secondary")
        ]),
    ],
    id="factor-selection-modal",
    is_open=False,
    size="lg",
    backdrop=True,
    scrollable=True
)

# --- 应用布局 ---
app.layout = html.Div(style={'display': 'flex', 'flex-direction': 'row', 'height': '100vh'}, children=[
    # 左侧控制面板
    html.Div(
        id='control-panel',
        style={'width': '20%', 'padding': '20px', 'background-color': '#f2f2f2'},
        children=[
            html.H3('控制面板'),
            html.Hr(),
            
            html.Label('日期范围:'),
            dcc.DatePickerRange(
                id='date-picker-range',
                min_date_allowed=date(2015, 1, 1),
                max_date_allowed=date.today(),
                initial_visible_month=date.today(),
                start_date=date(2020, 10, 9),
                end_date=date.today(),
                style={'marginTop': '5px'}
            ),
            
            html.Label('股票池:', style={'marginTop': '20px'}),
            dcc.Dropdown(
                id='stock-pool-dropdown',
                options=[
                    {'label': '沪深300', 'value': 'CSI300'},
                    {'label': '中证500', 'value': 'CSI500'},
                    {'label': '全市场', 'value': 'ALL'},
                    {'label': '动态筛选池', 'value': 'dynamic'}
                ],
                value='CSI300',
                style={'marginTop': '5px'}
            ),
            html.Button("配置动态池", id="open-sieve-modal-btn", n_clicks=0, className="btn btn-info mt-2 w-100"),
            dcc.Dropdown(
                id='dynamic-stock-list-dropdown',
                placeholder='从动态池选择股票以显示K线图...',
                style={'marginTop': '5px'}
            ),
            
            html.Label('选择因子:', style={'marginTop': '20px'}),
            html.Button("选择因子", id="open-factor-selection-modal-btn", n_clicks=0, className="btn btn-info mt-2 w-100"),
            html.Div(id='selected-factors-output', style={'marginTop': '5px', 'fontSize': '0.9em', 'color': '#555'}),
            
            html.Label('自定义复合因子 (每行一个):', style={'marginTop': '20px'}),
            dcc.Textarea(
                id='composite-factor-textarea',
                placeholder='例如:\nCOMP_A = MOM + VOL\nCOMP_B = (MA_5 - MA_20) / MA_20',
                style={'width': '100%', 'height': 100, 'marginTop': '5px'}
            ),
            
            html.Hr(style={'marginTop': '20px'}),
            html.Div(id='factor-description-output', style={'marginTop': '15px', 'padding': '10px', 'backgroundColor': '#e9e9e9', 'borderRadius': '5px'}),
            
            html.Hr(style={'marginTop': '20px'}),
            html.H4('因子评估设置'),
            dcc.Checklist(
                id='enable-evaluation-checklist',
                options=[{'label': ' 启用因子评估', 'value': 'ENABLE'}],
                value=[],
                style={'marginTop': '10px'}
            ),
            html.Label('IC计算周期 (天):', style={'marginTop': '10px'}),
            dcc.Input( # Changed from dbc.Input
                id='ic-period-input',
                type='number',
                value=1,
                min=1,
                step=1,
                style={'width': '100%', 'marginTop': '5px'}
            ),
            html.Label('分层数量:', style={'marginTop': '10px'}),
            dcc.Input( # Changed from dbc.Input
                id='quantile-input',
                type='number',
                value=5,
                min=2,
                max=20,
                step=1,
                style={'width': '100%', 'marginTop': '5px'}
            ),
            
            html.Label('叠加股票代码:', style={'marginTop': '20px'}),
            dcc.Dropdown(
                id='overlay-symbol-dropdown',
                options=get_all_stock_symbols(),
                multi=True,
                placeholder='选择或输入股票代码...',
                style={'marginTop': '5px'}
            ),
            
            html.Button('开始分析', id='run-analysis-button', n_clicks=0, className="btn btn-success mt-3 w-100")
        ]
    ),
    
    # 右侧主内容区
    dcc.Loading(
        id="loading-main-content",
        type="default",
        children=html.Div(
            id='main-content',
            style={'flex': '1', 'padding': '20px', 'overflow': 'auto'},
            children=[
                html.H3('动态池个股K线分析'),
                dcc.Graph(
                    id='stock-kline-graph',
                    figure=px.line(title="请先生成动态股票池，然后选择股票查看K线分析").update_layout(
                        width=None,
                        height=500,
                        autosize=True,
                        margin=dict(l=40, r=40, t=60, b=40)
                    ),
                    style={'width': '100%', 'height': '500px'},
                    config={'responsive': True, 'displayModeBar': True}
                ),
                html.Button("导出匹配区间为CSV", id="export-intervals-csv-btn", className="btn btn-sm btn-primary mt-2 mb-2"),
                dcc.Download(id="download-intervals-csv"),
                html.Hr(),
                html.H3('因子评估报告'),
                html.Div(id='evaluation-report-output'),
                html.Hr(),
                html.H3('可视化分析'),
                dcc.Graph(
                    id='analysis-graph',
                    figure=px.line(title="请点击'开始分析'按钮进行因子分析"),
                    style={'width': '100%', 'height': '500px'},
                    config={'responsive': True, 'displayModeBar': True}
                ),
                html.Hr(),
                html.Button("导出详细数据为CSV", id="export-analysis-csv-btn", className="btn btn-sm btn-primary mt-2 mb-2"),
                dcc.Download(id="download-analysis-csv")
            ]
        )
    ),
    dcc.Store(id='analysis-data-store'),
    dcc.Store(id='dynamic-pool-store'),
    dcc.Store(id='selected-factors-store', data=['MOM']), # 新增一个Store来存储选择的因子
    sieve_modal_layout,
    factor_selection_modal_layout # 添加因子选择模态框
])

@app.callback(
    Output('sieve-modal', 'is_open'),
    [Input('open-sieve-modal-btn', 'n_clicks'),
     Input('close-sieve-modal-btn', 'n_clicks'),
     Input('run-sieve-btn', 'n_clicks')],
    [State('sieve-modal', 'is_open')]
)
def toggle_sieve_modal(open_n, close_n, run_n, is_open):
    ctx = dash.callback_context
    if not ctx.triggered:
        return is_open
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if button_id == 'open-sieve-modal-btn' and open_n:
        return not is_open
    elif (button_id == 'close-sieve-modal-btn' and close_n) or (button_id == 'run-sieve-btn' and run_n):
        return False
    return is_open

@app.callback(
    Output('factor-selection-modal', 'is_open'),
    [Input('open-factor-selection-modal-btn', 'n_clicks'),
     Input('confirm-factor-selection-btn', 'n_clicks'),
     Input('cancel-factor-selection-btn', 'n_clicks')],
    [State('factor-selection-modal', 'is_open')]
)
def toggle_factor_selection_modal(open_n, confirm_n, cancel_n, is_open):
    ctx = dash.callback_context
    if not ctx.triggered:
        return is_open
    
    button_id = ctx.triggered[0]['prop_id'].split('.')[0]
    
    if button_id == 'open-factor-selection-modal-btn' and open_n:
        return True # 明确打开模态框
    elif (button_id == 'confirm-factor-selection-btn' and confirm_n) or \
         (button_id == 'cancel-factor-selection-btn' and cancel_n):
        return False # 明确关闭模态框
    return is_open

@app.callback(
    [Output('selected-factors-store', 'data'),
     Output('selected-factors-output', 'children')],
    Input('confirm-factor-selection-btn', 'n_clicks'),
    State('factor-checklist', 'value'),
    State('selected-factors-store', 'data')
)
def update_selected_factors(n_clicks, selected_factors_from_modal, stored_factors):
    if n_clicks > 0:
        if selected_factors_from_modal:
            return selected_factors_from_modal, f"已选择: {', '.join(selected_factors_from_modal)}"
        else:
            return [], "未选择任何因子"
    return stored_factors, f"已选择: {', '.join(stored_factors)}" if stored_factors else "未选择任何因子"

@app.callback(
    Output('factor-checklist', 'value'),
    Input('open-factor-selection-modal-btn', 'n_clicks'),
    State('selected-factors-store', 'data')
)
def populate_factor_checklist(open_n, stored_factors):
    if open_n > 0:
        return stored_factors
    return dash.no_update

@app.callback(
    Output('sieve-segments-container', 'children'),
    Input('add-segment-button', 'n_clicks'),
    State('sieve-segments-container', 'children')
)
def manage_segments(n_clicks, segments):
    if n_clicks > 0:
        new_index = len(segments)
        segments.append(create_sieve_segment(new_index))
    return segments

@app.callback(
    Output({'type': 'sieve-rules-container', 'index': MATCH}, 'children'),
    Input({'type': 'add-rule-button', 'index': MATCH}, 'n_clicks'),
    State({'type': 'sieve-rules-container', 'index': MATCH}, 'children')
)
def manage_rules(n_clicks, rules):
    if n_clicks > 0:
        segment_index = dash.callback_context.inputs_list[0]['id']['index']
        new_rule_index = len(rules)
        rules.append(create_sieve_rule(segment_index, new_rule_index))
    return rules

@app.callback(
    Output({'type': 'sieve-rule', 'index': MATCH}, 'style'),
    Input({'type': 'remove-rule-button', 'index': MATCH}, 'n_clicks')
)
def remove_rule(n_clicks):
    if n_clicks > 0:
        return {'display': 'none'}
    return {'marginBottom': '5px'}

@app.callback(
    Output({'type': 'sieve-segment', 'index': MATCH}, 'style'),
    Input({'type': 'remove-segment-button', 'index': MATCH}, 'n_clicks')
)
def remove_segment(n_clicks):
    if n_clicks > 0:
        return {'display': 'none'}
    return {'border': '1px solid #ccc', 'padding': '10px', 'marginBottom': '10px'}

@app.callback(
    Output('dynamic-pool-store', 'data'),
    Input('run-sieve-btn', 'n_clicks'),
    [State('date-picker-range', 'start_date'),
     State('date-picker-range', 'end_date'),
     State({'type': 'sieve-segment', 'index': ALL}, 'id'),
     State({'type': 'sieve-segment-length', 'index': ALL}, 'value'),
     State({'type': 'sieve-logic-radioitems', 'index': ALL}, 'value'),
     State({'type': 'sieve-metric-dropdown', 'index': ALL}, 'value'),
     State({'type': 'sieve-operator-dropdown', 'index': ALL}, 'value'),
     State({'type': 'sieve-value-input', 'index': ALL}, 'value')]
)
def run_sieve_and_store_results(n_clicks, start_date, end_date, segment_ids, lengths, logics, metrics, operators, values):
    if n_clicks == 0:
        return dash.no_update

    # 辅助函数：解析组件ID中的分段和规则索引
    def parse_id_index(component_id):
        if isinstance(component_id, dict) and 'index' in component_id:
            parts = component_id['index'].split('-')
            return int(parts[0]), int(parts[1]) if len(parts) > 1 else None
        return None, None

    # 1. 收集所有规则数据，并按分段索引分组
    all_metric_states = dash.callback_context.states_list[5]
    all_operator_states = dash.callback_context.states_list[6]
    all_value_states = dash.callback_context.states_list[7]

    rules_by_segment_index = {}
    for i in range(len(all_metric_states)):
        metric_state = all_metric_states[i]
        operator_state = all_operator_states[i]
        value_state = all_value_states[i]

        segment_idx, rule_idx = parse_id_index(metric_state['id'])

        if segment_idx is not None:
            if segment_idx not in rules_by_segment_index:
                rules_by_segment_index[segment_idx] = []
            
            if metric_state['value'] is not None and \
               operator_state['value'] is not None and \
               value_state['value'] is not None:
                rules_by_segment_index[segment_idx].append({
                    'metric': metric_state['value'],
                    'operator': operator_state['value'],
                    'value': value_state['value']
                })

    # 2. 收集所有分段数据
    final_segments = []
    final_conditions = []

    for i, seg_id_dict in enumerate(segment_ids):
        segment_index = seg_id_dict['index']
        
        if i < len(lengths) and i < len(logics):
            segment_length = lengths[i]
            segment_logic = logics[i]
            
            if segment_length is not None and segment_length > 0:
                final_segments.append(segment_length)
                final_conditions.append({
                    'logic': segment_logic,
                    'rules': rules_by_segment_index.get(segment_index, [])
                })

    # 3. 调用 sieve_engine
    # 配置文件位于项目根目录 Quantstrat_Factory/config/app.yaml
    config_path = str(Path(__file__).resolve().parents[4] / 'config' / 'app.yaml')
    
    # 设置 return_detail=True 来获取详细的匹配区间
    matched_stocks, detailed_results = run_sieve(
        start_date=start_date,
        end_date=end_date,
        segments=final_segments,
        conditions=final_conditions,
        config_path=config_path,
        return_detail=True
    )
    
    # 返回详细结果以便在K线图上标记
    return detailed_results

@app.callback(
    [Output('analysis-graph', 'figure'),
     Output('evaluation-report-output', 'children'),
     Output('analysis-data-store', 'data')],
    [Input('run-analysis-button', 'n_clicks'),
     Input('overlay-symbol-dropdown', 'value'),
     Input('dynamic-pool-store', 'data')],
    [State('date-picker-range', 'start_date'),
     State('date-picker-range', 'end_date'),
     State('stock-pool-dropdown', 'value'),
     State('selected-factors-store', 'data'), # 从Store中获取选择的因子
     State('composite-factor-textarea', 'value'),
     State('enable-evaluation-checklist', 'value'),
     State('ic-period-input', 'value'),
     State('quantile-input', 'value'),
     State('analysis-data-store', 'data'),
     State('analysis-graph', 'figure')]
)
def update_output(n_clicks, overlay_symbols, dynamic_pool_data, start_date, end_date, stock_pool, selected_factors, composite_text, evaluation_enabled, ic_period, quantiles, stored_data, current_figure):
    ctx = dash.callback_context
    if not ctx.triggered:
        return dash.no_update, dash.no_update, None

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    final_stock_pool = stock_pool
    if stock_pool == 'dynamic':
        # 如果是动态池，股票列表是 dynamic_pool_data 的键
        if dynamic_pool_data:
            final_stock_pool = list(dynamic_pool_data.keys())
        else:
            final_stock_pool = []
    
    if trigger_id == 'run-analysis-button':
        # --- This logic is now duplicated, needs to be refactored ---
        # For now, we just copy-paste the logic to make it work
        all_required_factors = set(selected_factors)
        composite_definitions = {}

        if composite_text:
            lines = [line.strip() for line in composite_text.split('\n') if line.strip()]
            for line in lines:
                if '=' in line:
                    name, expr = line.split('=', 1)
                    composite_definitions[name.strip()] = expr.strip()
                    dependencies = re.findall(r'\b[A-Za-z_][A-Za-z0-9_]*\b', expr)
                    all_required_factors.update(dependencies)
        
        calculated_factors = [f for f in all_required_factors if f in ['MOM', 'VOL']]
        file_based_factors = [f for f in all_required_factors if f not in ['MOM', 'VOL']]
        
        fwd_periods = []
        if 'ENABLE' in evaluation_enabled and ic_period and ic_period > 0:
            fwd_periods.append(ic_period)
        
        data = load_data(final_stock_pool, start_date, end_date, file_based_factors, fwd_periods)
        
        analysis_result = perform_analysis(data, calculated_factors)

        if composite_definitions:
            analysis_result = calculate_composite_factors(analysis_result, composite_definitions)

        evaluation_report = []
        if 'ENABLE' in evaluation_enabled and ic_period and ic_period > 0:
            fwd_return_col = f'fwd_return_{ic_period}d'
            if fwd_return_col in analysis_result.columns:
                factors_to_evaluate = list(all_required_factors) + list(composite_definitions.keys())
                
                ic_reports = []
                for factor in factors_to_evaluate:
                    if factor in analysis_result.columns:
                        ic_series = calculate_ic(analysis_result, factor, fwd_return_col)
                        ic_mean = ic_series.mean()
                        ic_std = ic_series.std()
                        icir = ic_mean / ic_std if ic_std != 0 else 0
                        
                        report_card = html.Div([
                            html.H5(f"IC 分析: {factor}"),
                            html.P(f"IC 均值: {ic_mean:.4f}"),
                            html.P(f"IC 标准差: {ic_std:.4f}"),
                            html.P(f"ICIR: {icir:.4f}")
                        ], style={'border': '1px solid #ddd', 'padding': '10px', 'marginBottom': '10px'})
                        
                        q_net_value, q_stats = perform_quantile_analysis(
                            analysis_result, factor, fwd_return_col, quantiles
                        )
                        
                        q_fig = px.line(
                            q_net_value, 
                            title=f"分层回测净值曲线: {factor}",
                            labels={'value': '净值', 'datetime': '日期', 'variable': '分层'}
                        )
                        
                        if '年化收益率' in q_stats.columns:
                            q_stats['年化收益率'] = q_stats['年化收益率'].apply(lambda x: f"{x * 100:.2f}%")
                        if '年化波动率' in q_stats.columns:
                            q_stats['年化波动率'] = q_stats['年化波动率'].apply(lambda x: f"{x * 100:.2f}%")
                        if '夏普比率' in q_stats.columns:
                            q_stats['夏普比率'] = q_stats['夏普比率'].apply(lambda x: f"{x:.4f}")
                        if '最大回撤' in q_stats.columns:
                            q_stats['最大回撤'] = q_stats['最大回撤'].apply(lambda x: f"{x:.4f}")
                        if 'calmar比率' in q_stats.columns:
                            q_stats['calmar比率'] = q_stats['calmar比率'].apply(lambda x: f"{x:.4f}")
                        if 'Calmar比率' in q_stats.columns:
                            q_stats['Calmar比率'] = q_stats['Calmar比率'].apply(lambda x: f"{x:.4f}")
                        if '胜率' in q_stats.columns:
                            q_stats['胜率'] = q_stats['胜率'].apply(lambda x: f"{x:.4f}")
                        if '盈亏比' in q_stats.columns:
                            q_stats['盈亏比'] = q_stats['盈亏比'].apply(lambda x: f"{x:.4f}")

                        q_table = dash_table.DataTable(
                            columns=[{"name": i, "id": i} for i in q_stats.columns],
                            data=q_stats.to_dict('records'),
                            style_table={'overflowX': 'auto'},
                        )

                        full_report_div = html.Div([
                            report_card,
                            html.H5(f"分层回测: {factor}"),
                            dcc.Graph(
                                figure=q_fig,
                                style={'width': '100%', 'height': '500px'},
                                config={'responsive': True, 'displayModeBar': True}
                            ),
                            q_table,
                            html.Hr()
                        ])
                        ic_reports.append(full_report_div)

                evaluation_report = ic_reports
            else:
                evaluation_report = [html.P(f"错误: 未能找到未来收益率列 '{fwd_return_col}'。")]
        else:
            evaluation_report = [html.P("因子评估未启用。")]

        if isinstance(analysis_result, pd.DataFrame) and not analysis_result.empty:
            
            symbol_col = 'symbol' if 'symbol' in analysis_result.columns else 'code'
            factor_cols = [c for c in analysis_result.columns if c not in ['datetime', symbol_col]]
            
            if not factor_cols:
                figure_output = px.line(title="没有可供可视化的因子。请选择或定义因子。").update_layout(
                    height=500,
                    margin=dict(l=0, r=0, t=40, b=40),
                    autosize=True,
                    showlegend=True
                )
            else:
                if stock_pool == 'ALL' or stock_pool == 'dynamic':
                    analysis_result['datetime'] = pd.to_datetime(analysis_result['datetime'])
                    
                    # 强制转换 factor_cols 中的列为数值类型，以避免 TypeError
                    for col in factor_cols:
                        # 尝试转换为数值，无法转换的设为 NaN
                        analysis_result[col] = pd.to_numeric(analysis_result[col], errors='coerce')
                        # 再次确保转换为浮点数，处理可能存在的其他非数值类型
                        if analysis_result[col].dtype != 'float64':
                            analysis_result[col] = analysis_result[col].astype(float)
                    
                    aggregated_df = analysis_result.groupby('datetime')[factor_cols].mean().reset_index()
                    melted_df = aggregated_df.melt(
                        id_vars=['datetime'], 
                        value_vars=factor_cols,
                        var_name='factor',
                        value_name='value'
                    )
                    figure_output = px.line(
                        melted_df, 
                        x='datetime', 
                        y='value', 
                        color='factor',
                        title='因子均值时序图',
                        labels={'datetime': '日期', 'value': '因子均值', 'factor': '因子'}
                    )
                    figure_output.update_layout(legend_title_text='因子')
                else:
                    melted_df = analysis_result.melt(
                        id_vars=['datetime', symbol_col], 
                        value_vars=factor_cols,
                        var_name='factor',
                        value_name='value'
                    )
                    melted_df['series'] = melted_df[symbol_col] + '_' + melted_df['factor']
                    figure_output = px.line(
                        melted_df,
                        x='datetime',
                        y='value',
                        color='series',
                        title='因子时序图',
                        labels={'datetime': '日期', 'value': '因子值', 'series': '序列 (股票_因子)'}
                    )
                    figure_output.update_layout(
                        legend_title_text='序列',
                        width=None,
                        autosize=True
                    )

            store_output = analysis_result.to_json(date_format='iso', orient='split')
            report_output = evaluation_report

        elif isinstance(analysis_result, pd.DataFrame) and analysis_result.empty:
            figure_output = px.line(title="分析完成，但没有符合条件的数据。").update_layout(
                height=500,
                margin=dict(l=0, r=0, t=40, b=40),
                autosize=True,
                showlegend=True
            )
            report_output = evaluation_report
            store_output = None
        else:
            figure_output = px.line(title="分析出错").update_layout(
                height=500,
                margin=dict(l=0, r=0, t=40, b=40),
                autosize=True,
                showlegend=True
            )
            report_output = evaluation_report
            store_output = None
        
        return figure_output, report_output, store_output

    elif trigger_id == 'overlay-symbol-dropdown' and (stock_pool == 'ALL' or stock_pool == 'dynamic') and stored_data:
        analysis_result = pd.read_json(stored_data, orient='split', convert_dates=['datetime'])
        symbol_col = 'symbol' if 'symbol' in analysis_result.columns else 'code'
        factor_cols = [c for c in analysis_result.columns if c not in ['datetime', symbol_col]]

        if not overlay_symbols:
            return dash.no_update, dash.no_update, dash.no_update, dash.no_update

        fig = go.Figure(current_figure) if current_figure else px.line(title="没有可供叠加的图表。")

        for symbol in overlay_symbols:
            symbol_df = analysis_result[analysis_result[symbol_col] == symbol]
            if not symbol_df.empty:
                melted_symbol_df = symbol_df.melt(
                    id_vars=['datetime', symbol_col], 
                    value_vars=factor_cols,
                    var_name='factor',
                    value_name='value'
                )
                
                for factor in factor_cols:
                    trace_df = melted_symbol_df[melted_symbol_df['factor'] == factor]
                    if not trace_df.empty:
                        fig.add_trace(go.Scatter(
                            x=trace_df['datetime'], 
                            y=trace_df['value'], 
                            mode='lines', 
                            name=f'{symbol}_{factor}_叠加',
                            line=dict(dash='dash')
                        ))
            else:
                print(f"警告: 未找到股票 {symbol} 的数据。")
        
        return fig, dash.no_update, dash.no_update

    return dash.no_update, dash.no_update, dash.no_update

@app.callback(
    Output('dynamic-stock-list-dropdown', 'options'),
    Input('dynamic-pool-store', 'data')
)
def update_dynamic_stock_list_dropdown(dynamic_pool_data):
    if not dynamic_pool_data:
        return []
    
    # 从 detailed_results 字典的键创建下拉选项
    stock_symbols = sorted(list(dynamic_pool_data.keys()))
    options = [{'label': symbol, 'value': symbol} for symbol in stock_symbols]
    return options

@app.callback(
    Output('stock-kline-graph', 'figure'),
    [Input('dynamic-stock-list-dropdown', 'value'),
     Input('dynamic-pool-store', 'data')],
    [State('date-picker-range', 'start_date'),
     State('date-picker-range', 'end_date')]
)
def display_stock_kline(selected_symbol, dynamic_pool_data, start_date, end_date):
    if not selected_symbol or not dynamic_pool_data:
        return go.Figure(layout={
            'title': '请先生成动态池并选择一只股票',
            'height': 500,
            'margin': dict(l=0, r=0, t=40, b=40),
            'autosize': True,
            'showlegend': True
        })

    # 1. 加载选中股票的日K数据
    # 使用 data_loader 加载基础数据，不选任何额外因子
    stock_df = load_data(
        stock_pool=[selected_symbol], 
        start_date=start_date, 
        end_date=end_date,
        selected_factors=[]
    )

    if stock_df.empty:
        return go.Figure(layout={
            'title': f'无法加载 {selected_symbol} 的K线数据',
            'height': 500,
            'margin': dict(l=0, r=0, t=40, b=40),
            'autosize': True,
            'showlegend': True
        })

    # 确保数据按日期排序
    stock_df = stock_df.sort_values('datetime')

    # 2. 创建K线图
    fig = go.Figure(data=[go.Candlestick(
        x=stock_df['datetime'],
        open=stock_df['open'],
        high=stock_df['high'],
        low=stock_df['low'],
        close=stock_df['close'],
        name='K线',
        increasing_line_color='red', # 上涨为红色
        decreasing_line_color='green' # 下跌为绿色
    )])
    
    fig.update_layout(
        title=f'{selected_symbol} 日K线图与动态池匹配区间',
        xaxis_title='日期',
        yaxis_title='价格',
        xaxis_rangeslider_visible=False,  # 关闭范围滑块
        height=500,
        margin=dict(l=0, r=0, t=40, b=40),
        autosize=True,
        showlegend=True
    )

    # 3. 在图上标记符合条件的区间
    match_intervals = dynamic_pool_data.get(selected_symbol, [])
    for interval in match_intervals:
        interval_start, interval_end = interval
        fig.add_shape(
            type="rect",
            xref="x", yref="paper",
            x0=interval_start, y0=0,
            x1=interval_end, y1=1,
            fillcolor="LightSkyBlue",
            opacity=0.5,
            layer="below",
            line_width=0,
        )
        
    return fig

@app.callback(
    Output("download-intervals-csv", "data"),
    Input("export-intervals-csv-btn", "n_clicks"),
    State('dynamic-pool-store', 'data'),
    prevent_initial_call=True,
)
def export_match_intervals_to_csv(n_clicks, dynamic_pool_data):
    if not dynamic_pool_data:
        return dash.no_update

    table_data = []
    for stock, intervals in dynamic_pool_data.items():
        for start, end in intervals:
            table_data.append({
                '股票代码': stock,
                '开始日期': start,
                '结束日期': end
            })
            
    df = pd.DataFrame(table_data)
    return dcc.send_data_frame(df.to_csv, "matched_intervals.csv", index=False, encoding='utf_8_sig')

@app.callback(
    Output("download-analysis-csv", "data"),
    Input("export-analysis-csv-btn", "n_clicks"),
    State('analysis-data-store', 'data'),
    prevent_initial_call=True,
)
def export_analysis_data_to_csv(n_clicks, stored_data):
    if not stored_data:
        return dash.no_update

    # 从存储的数据中读取DataFrame
    analysis_result = pd.read_json(stored_data, orient='split', convert_dates=['datetime'])

    # 生成CSV文件
    return dcc.send_data_frame(analysis_result.to_csv, "factor_analysis_data.csv", index=False, encoding='utf_8_sig')

@app.callback(
    Output('factor-description-output', 'children'),
    Input('selected-factors-store', 'data') # 从Store中获取选择的因子
)
def update_factor_descriptions(selected_factors):
    if not selected_factors:
        return dcc.Markdown("请从上方选择一个或多个因子以查看其定义。")

    descriptions = []
    for factor in selected_factors:
        definition = FACTOR_DEFINITIONS.get(factor, f"**{factor}**: 暂无详细定义。")
        descriptions.append(dcc.Markdown(definition, style={'marginBottom': '10px'}))
    
    return descriptions

if __name__ == '__main__':
    try:
        app.run(debug=True, port=8051, host='0.0.0.0')
    except Exception as e:
        print(f"启动Dash服务器时发生错误: {e}")
        import traceback
        traceback.print_exc()
