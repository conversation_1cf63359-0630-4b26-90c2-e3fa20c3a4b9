import pandas as pd

def calculate_composite_factors(data, composite_definitions):
    """
    根据用户定义的表达式计算复合因子。

    参数:
    - data (pd.DataFrame): 包含基础因子列的DataFrame。
    - composite_definitions (dict): 一个字典，键是新的复合因子名，值是计算表达式字符串。
                                     e.g., {'COMP_A': 'MOM + VOL'}

    返回:
    - pd.DataFrame: 包含了新的复合因子列的DataFrame。
    """
    if not isinstance(composite_definitions, dict) or not composite_definitions:
        # 如果没有定义，直接返回原始数据
        return data

    output_df = data.copy()

    for factor_name, expression in composite_definitions.items():
        try:
            # 使用 pandas.eval() 来安全地执行表达式
            output_df[factor_name] = output_df.eval(expression)
            print(f"成功计算复合因子 '{factor_name}'。")
        except Exception as e:
            # 如果表达式无效（如引用了不存在的列），则捕获异常并打印警告
            print(f"警告: 计算复合因子 '{factor_name}' 失败。表达式: '{expression}'. 错误: {e}")
            # 继续处理下一个因子，不中断流程
            continue
            
    return output_df
