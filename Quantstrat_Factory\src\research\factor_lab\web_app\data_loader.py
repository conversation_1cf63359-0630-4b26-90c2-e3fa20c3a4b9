import pandas as pd
from pathlib import Path
import sys
import json
 
# 将项目根目录添加到sys.path，以便导入FeatureStoreClient
# web_app -> factor_lab -> research -> src -> Quantstrat_Factory
project_root = Path(__file__).resolve().parents[4]
sys.path.append(str(project_root))

# 添加 auditor 目录到路径
auditor_path = project_root / 'src' / 'data' / 'auditor'
sys.path.append(str(auditor_path))

from feature_store_client import FeatureStoreClient

# 导入 normalize_symbol 函数
from run_auditor import normalize_symbol

# 为了动态加载因子，我们预先定义可能的因子类别
FACTOR_CATEGORIES = ['technical', 'agg_alpha', 'agg_event', 'agg_risk']

def load_data(stock_pool, start_date, end_date, selected_factors, forward_return_periods=None):
    """
    使用 FeatureStoreClient 加载基础数据、指定因子和未来收益率。
    
    参数:
    - stock_pool (str): 股票池标识符.
    - start_date (str): 开始日期.
    - end_date (str): 结束日期.
    - selected_factors (list): 用户选择的因子名称列表.
    - forward_return_periods (list, optional): 需要计算的未来收益率周期列表, e.g., [1, 5, 10].
    
    返回:
    - pd.DataFrame: 包含所有请求数据的合并DataFrame。
    """
    print(f"开始加载真实数据... 股票池: {stock_pool}, 日期: {start_date} to {end_date}")
    
    if not start_date or not end_date:
        print("日期范围未提供，返回空DataFrame。")
        return pd.DataFrame()

    try:
        # 直接从统一的日K数据路径加载基础数据
        daily_basics_path = Path("D:/PY/Data/cleaned/daily/daily_basics.parquet")

        if not daily_basics_path.exists():
            print(f"基础数据文件不存在: {daily_basics_path}")
            return pd.DataFrame()

        # 1. 加载日线基础数据
        base_df = pd.read_parquet(daily_basics_path)
        
        if base_df.empty:
            print("基础数据加载失败或为空，无法继续。")
            return pd.DataFrame()
        # print(f"步骤1: 基础数据加载完成，共 {len(base_df)} 条记录。") # 移除调试打印

        # 标准化股票代码格式
        symbol_col = 'symbol' if 'symbol' in base_df.columns else 'code'
        if symbol_col in base_df.columns:
            base_df[symbol_col] = base_df[symbol_col].apply(normalize_symbol)
            # print(f"步骤1.1: 股票代码标准化完成。") # 移除调试打印
        else:
            print(f"警告: 基础数据中未找到股票代码列 ('symbol' 或 'code')，无法标准化。")

        # 确定合并时使用的索引列
        merge_on_cols = ['datetime', symbol_col]

        # 2. 动态加载并合并所选因子
        df = base_df.copy() # 从基础数据开始合并
        for factor_name in selected_factors:
            factor_df = None
            # 尝试从统一的特征数据路径加载因子
            feature_file_path = Path(f"D:/PY/Data/features/{factor_name}.parquet")
            try:
                if feature_file_path.exists():
                    factor_df = pd.read_parquet(feature_file_path)
                    # print(f"成功加载因子 '{factor_name}'。") # 移除调试打印
            except Exception as e:
                print(f"加载因子 '{factor_name}' 失败: {e}")
                continue
            
            if factor_df is not None and not factor_df.empty:
                # 合并前确保索引列存在
                if all(col in factor_df.columns for col in merge_on_cols):
                    df = pd.merge(df, factor_df, on=merge_on_cols, how='left')
                else:
                    print(f"警告: 因子 '{factor_name}' 的DataFrame缺少合并所需的列 {merge_on_cols}。")
            else:
                print(f"警告: 未能在任何已知类别中找到因子 '{factor_name}' 的数据。")
        # print(f"步骤2: 因子合并完成，共 {len(df)} 条记录。") # 移除调试打印

        # 3. 实现基于 stock_pool 的股票代码过滤
        symbol_col = 'symbol' if 'symbol' in df.columns else 'code' # 提前确定股票代码列名

        if stock_pool == 'ALL':
            # 使用相对路径，从Data目录查找
            project_root = Path(__file__).resolve().parents[4]
            stock_list_path = project_root.parent / 'Data' / 'stock_list.txt'  # 用户提供的全股票清单路径
            if stock_list_path.exists():
                with open(stock_list_path, 'r', encoding='utf-8') as f:
                    # 读取每一行并去除空白符，过滤空行，并标准化股票代码
                    target_symbols = [normalize_symbol(s.strip()) for s in f if s.strip()]
                if target_symbols:
                    original_rows = len(df)
                    df = df[df[symbol_col].isin(target_symbols)]
                    # print(f"根据股票池 '全市场' (来自 {stock_list_path}) 过滤数据，从 {original_rows} 行筛选到 {len(df)} 行。") # 移除调试打印
                else:
                    print(f"警告: '全市场' 股票清单文件 {stock_list_path} 为空或无法解析。不进行股票过滤。")
            else:
                print(f"警告: '全市场' 股票清单文件 {stock_list_path} 不存在。不进行股票过滤。")
        elif isinstance(stock_pool, list): # 处理传入的是股票代码列表的情况 (动态筛选池)
            target_symbols = stock_pool
            if target_symbols: # 只有当 target_symbols 不为空时才进行过滤
                if symbol_col in df.columns:
                    original_rows = len(df)
                    df = df[df[symbol_col].isin(target_symbols)]
                    # print(f"根据动态股票池过滤数据，从 {original_rows} 行筛选到 {len(df)} 行。") # 移除调试打印
                else:
                    print(f"警告: DataFrame中未找到股票代码列 ('symbol' 或 'code')，无法按股票池过滤。")
            else:
                print(f"警告: 动态股票池为空。不进行股票过滤。")
        else: # 处理其他预定义股票池 (如 'CSI300', 'CSI500')
            pools_path = Path(__file__).parent / 'stock_pools.json'
            with open(pools_path, 'r', encoding='utf-8') as f:
                stock_pools_data = json.load(f)
            
            raw_target_symbols = stock_pools_data.get(stock_pool)
            if raw_target_symbols: # 只有当 raw_target_symbols 不为空时才进行过滤
                # 对从 JSON 读取的股票代码进行标准化
                target_symbols = [normalize_symbol(s.strip()) for s in raw_target_symbols if s.strip()]
                if target_symbols:
                    if symbol_col in df.columns:
                        original_rows = len(df)
                        df = df[df[symbol_col].isin(target_symbols)]
                        # print(f"根据股票池 '{stock_pool}' 过滤数据，从 {original_rows} 行筛选到 {len(df)} 行。") # 移除调试打印
                    else:
                        print(f"警告: DataFrame中未找到股票代码列 ('symbol' 或 'code')，无法按股票池过滤。")
                else:
                    print(f"警告: 股票池 '{stock_pool}' 经过标准化后为空。不进行股票过滤。")
            else:
                print(f"警告: 在 stock_pools.json 中未找到股票池 '{stock_pool}' 的定义或定义为空。不进行股票过滤。")
        # print(f"步骤3: 股票池过滤完成，共 {len(df)} 条记录。") # 移除调试打印

        # 3.5. 按日期范围过滤数据
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            original_rows = len(df)
            df = df[(df['datetime'] >= start_dt) & (df['datetime'] <= end_dt)]
            print(f"步骤3.5: 日期过滤完成 ({start_date} 到 {end_date})，从 {original_rows} 行筛选到 {len(df)} 行。")

        # 4. 计算未来收益率 (如果需要)
        if forward_return_periods and 'close' in df.columns:
            # 确保数据按股票和日期排序，这对于 shift 操作至关重要
            df.sort_values(by=[symbol_col, 'datetime'], inplace=True)
            
            for p in forward_return_periods:
                col_name = f'fwd_return_{p}d'
                print(f"正在计算未来 {p} 日收益率 ('{col_name}')...")
                # 使用 groupby().shift(-p) 来正确计算每个股票的未来价格
                df[col_name] = (df.groupby(symbol_col)['close'].shift(-p) / df['close']) - 1
        # print(f"步骤4: 未来收益率计算完成，共 {len(df)} 条记录。") # 移除调试打印
        
        print(f"数据加载与计算完成，最终返回 {len(df)} 条记录。")
        return df
        
    except FileNotFoundError as e:
        print(f"错误: 数据文件未找到 - {e}")
        return pd.DataFrame()
    except Exception as e:
        print(f"加载数据时发生未知错误: {e}")
        return pd.DataFrame()

if __name__ == '__main__':
    # 用于独立测试
    # 注意：需要确保 'Quantstrat_Factory/config.ini' 和特征库路径正确
    print("--- 开始独立测试 data_loader ---")
    # 使用一个较短的、可能存在数据的日期范围进行测试
    test_df = load_data('CSI300', '2020-10-10', '2020-10-15', ['MA_5', 'RSI_14'], forward_return_periods=[1, 5])
    
    if not test_df.empty:
        print("\n加载的数据样本:")
        print(test_df.head())
        print(f"\n数据形状: {test_df.shape}")
        print("列名:", test_df.columns.tolist())
    else:
        print("\n未能加载任何数据。")
    print("--- 结束独立测试 data_loader ---")
