import pandas as pd
import numpy as np
from typing import Literal, Dict, <PERSON><PERSON>
from scipy import stats
import warnings
from functools import lru_cache
import gc
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp


def validate_input_data(data: pd.DataFrame,
                       factor_col: str,
                       fwd_return_col: str,
                       min_samples: int = 10) -> <PERSON><PERSON>[bool, str]:
    """
    验证输入数据的完整性和有效性。

    参数:
    - data (pd.DataFrame): 输入数据
    - factor_col (str): 因子列名
    - fwd_return_col (str): 收益率列名
    - min_samples (int): 最小样本数要求

    返回:
    - Tuple[bool, str]: (是否有效, 错误信息)
    """
    # 基本列检查
    required_cols = ['datetime', 'symbol', factor_col, fwd_return_col]
    missing_cols = [col for col in required_cols if col not in data.columns]
    if missing_cols:
        return False, f"缺少必需的列: {missing_cols}"

    # 数据量检查
    if len(data) < min_samples:
        return False, f"数据量不足，需要至少{min_samples}条记录，实际{len(data)}条"

    # 检查关键列的数据类型
    if not pd.api.types.is_datetime64_any_dtype(data['datetime']):
        return False, "datetime列必须是日期时间类型"

    # 检查数值列
    for col in [factor_col, fwd_return_col]:
        if not pd.api.types.is_numeric_dtype(data[col]):
            return False, f"列'{col}'必须是数值类型"

    # 检查有效数据比例
    valid_factor = data[factor_col].notna().sum()
    valid_return = data[fwd_return_col].notna().sum()
    total_rows = len(data)

    if valid_factor / total_rows < 0.5:
        return False, f"因子列'{factor_col}'有效数据比例过低: {valid_factor/total_rows:.2%}"

    if valid_return / total_rows < 0.5:
        return False, f"收益率列'{fwd_return_col}'有效数据比例过低: {valid_return/total_rows:.2%}"

    # 检查极值比例
    factor_values = data[factor_col].dropna()
    if len(factor_values) > 0:
        q1, q99 = factor_values.quantile([0.01, 0.99])
        extreme_ratio = ((factor_values < q1) | (factor_values > q99)).mean()
        if extreme_ratio > 0.1:
            warnings.warn(f"因子'{factor_col}'存在较多极值 ({extreme_ratio:.2%})")

    return True, "数据验证通过"


def clean_outliers(data: pd.DataFrame,
                  factor_col: str,
                  fwd_return_col: str,
                  method: str = 'zscore',
                  threshold: float = 3.0) -> pd.DataFrame:
    """
    清理异常值。

    参数:
    - data (pd.DataFrame): 输入数据
    - factor_col (str): 因子列名
    - fwd_return_col (str): 收益率列名
    - method (str): 异常值检测方法 ('zscore', 'iqr')
    - threshold (float): 阈值

    返回:
    - pd.DataFrame: 清理后的数据
    """
    clean_data = data.copy()

    for col in [factor_col, fwd_return_col]:
        values = clean_data[col].dropna()
        if len(values) == 0:
            continue

        if method == 'zscore':
            z_scores = np.abs(stats.zscore(values, nan_policy='omit'))
            outlier_mask = z_scores > threshold
        elif method == 'iqr':
            Q1 = values.quantile(0.25)
            Q3 = values.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - threshold * IQR
            upper_bound = Q3 + threshold * IQR
            outlier_mask = (values < lower_bound) | (values > upper_bound)
        else:
            continue

        # 将异常值设为NaN
        outlier_indices = values[outlier_mask].index
        clean_data.loc[outlier_indices, col] = np.nan

        if len(outlier_indices) > 0:
            print(f"在列'{col}'中发现并处理了{len(outlier_indices)}个异常值")

    return clean_data

def calculate_ic(
    data: pd.DataFrame,
    factor_col: str,
    fwd_return_col: str,
    method: Literal['pearson', 'spearman'] = 'pearson',
    min_periods: int = 10,
    validate_data: bool = True,
    clean_outliers_flag: bool = True
) -> pd.Series:
    """
    计算因子在每个时间截面上的信息系数 (IC)。

    IC 值是因子值与未来收益率的截面相关系数。

    参数:
    - data (pd.DataFrame): 包含 'datetime', 'symbol', 因子列, 和未来收益率列的DataFrame。
    - factor_col (str): 要评估的因子列的名称。
    - fwd_return_col (str): 对应的未来收益率列的名称。
    - method (str): 计算相关系数的方法, 'pearson' (默认) 或 'spearman'。
    - min_periods (int): 计算IC所需的最小样本数，默认10。
    - validate_data (bool): 是否进行数据验证，默认True。
    - clean_outliers_flag (bool): 是否清理异常值，默认True。

    返回:
    - pd.Series: 以日期为索引，IC值为值的Series。
    """
    # 数据验证
    if validate_data:
        is_valid, error_msg = validate_input_data(data, factor_col, fwd_return_col, min_periods)
        if not is_valid:
            raise ValueError(f"数据验证失败: {error_msg}")

    # 数据清理
    clean_data = data.copy()
    if clean_outliers_flag:
        clean_data = clean_outliers(clean_data, factor_col, fwd_return_col)

    # 移除无效值
    clean_data = clean_data.dropna(subset=[factor_col, fwd_return_col])

    if clean_data.empty:
        print(f"警告: 清理后的数据为空，无法计算IC。")
        return pd.Series(dtype=float, name=f'ic_{method}')

    # 按日期分组，然后计算每组内因子与未来收益率的相关性
    def get_daily_ic(group):
        # 检查数据点数量
        if len(group) < min_periods:
            return np.nan

        # 移除极值（超过3倍标准差）
        factor_values = group[factor_col]
        return_values = group[fwd_return_col]

        # 计算Z-score并过滤极值
        factor_zscore = np.abs(stats.zscore(factor_values, nan_policy='omit'))
        return_zscore = np.abs(stats.zscore(return_values, nan_policy='omit'))

        # 保留Z-score小于3的数据点
        valid_mask = (factor_zscore < 3) & (return_zscore < 3)

        if valid_mask.sum() < min_periods:
            return np.nan

        clean_factor = factor_values[valid_mask]
        clean_return = return_values[valid_mask]

        # 计算相关系数
        try:
            if method == 'pearson':
                corr, _ = stats.pearsonr(clean_factor, clean_return)
            else:  # spearman
                corr, _ = stats.spearmanr(clean_factor, clean_return)
            return corr
        except:
            return np.nan

    # 使用 apply 并传递 include_groups=False 以避免 DeprecationWarning
    ic_series = clean_data.groupby('datetime').apply(get_daily_ic, include_groups=False)

    ic_series.name = f'ic_{method}'

    # 统计信息
    valid_ic_count = ic_series.dropna().shape[0]
    total_dates = ic_series.shape[0]

    print(f"成功计算因子 '{factor_col}' 的 {method} IC 值。")
    print(f"有效IC计算: {valid_ic_count}/{total_dates} 个交易日")

    return ic_series


def calculate_ic_statistics(ic_series: pd.Series) -> Dict[str, float]:
    """
    计算IC序列的统计指标。

    参数:
    - ic_series (pd.Series): IC时间序列

    返回:
    - Dict[str, float]: 包含各种IC统计指标的字典
    """
    if ic_series.empty:
        return {}

    # 移除NaN值
    valid_ic = ic_series.dropna()

    if len(valid_ic) == 0:
        return {}

    stats_dict = {
        'IC均值': valid_ic.mean(),
        'IC标准差': valid_ic.std(),
        'ICIR': valid_ic.mean() / valid_ic.std() if valid_ic.std() > 0 else 0,
        'IC胜率': (valid_ic > 0).mean(),
        'IC绝对值均值': valid_ic.abs().mean(),
        '正IC均值': valid_ic[valid_ic > 0].mean() if (valid_ic > 0).any() else 0,
        '负IC均值': valid_ic[valid_ic < 0].mean() if (valid_ic < 0).any() else 0,
        'IC最大值': valid_ic.max(),
        'IC最小值': valid_ic.min(),
        'IC偏度': valid_ic.skew(),
        'IC峰度': valid_ic.kurtosis(),
        '有效观测数': len(valid_ic)
    }

    # t检验：检验IC均值是否显著不为0
    if len(valid_ic) > 1:
        t_stat, p_value = stats.ttest_1samp(valid_ic, 0)
        stats_dict['t统计量'] = t_stat
        stats_dict['p值'] = p_value
        stats_dict['IC显著性'] = '显著' if p_value < 0.05 else '不显著'

    return stats_dict


def perform_quantile_analysis(
    data: pd.DataFrame, 
    factor_col: str, 
    fwd_return_col: str, 
    quantiles: int = 5
) -> (pd.DataFrame, pd.DataFrame):
    """
    执行因子分层回测分析。

    参数:
    - data (pd.DataFrame): 包含 'datetime', 'symbol', 因子列, 和未来收益率列的DataFrame。
    - factor_col (str): 用于分层的因子列名。
    - fwd_return_col (str): 用于计算收益的未来收益率列名。
    - quantiles (int): 分层数量。

    返回:
    - pd.DataFrame: 索引为日期，列为各分层净值的DataFrame。
    - pd.DataFrame: 索引为分层，列为各性能指标的DataFrame。
    """
    if not all(c in data.columns for c in ['datetime', 'symbol', factor_col, fwd_return_col]):
        raise ValueError(f"输入数据必须包含 'datetime', 'symbol', '{factor_col}', 和 '{fwd_return_col}' 列。")

    # 1. 每日对因子值进行分层
    # 使用 rank(pct=True) 可以得到百分位排名，然后用 pd.cut 分成 quantiles 组
    # ascending=False 意味着因子值越大，排名越靠前 (rank值越小)
    data['rank'] = data.groupby('datetime')[factor_col].rank(method='first', ascending=False)
    data['quantile'] = data.groupby('datetime')['rank'].transform(
        lambda x: pd.qcut(x, quantiles, labels=False, duplicates='drop') + 1
    )
    
    # 2. 计算每层每日的平均收益率
    daily_returns = data.groupby(['datetime', 'quantile'])[fwd_return_col].mean().unstack()
    daily_returns.columns = [f'q{int(c)}' for c in daily_returns.columns]

    # 3. 计算净值曲线
    net_value_df = (1 + daily_returns.fillna(0)).cumprod()

    # 4. 计算性能指标
    # 假设一年有252个交易日
    trading_days_per_year = 252

    # 计算各种性能指标
    stats_list = []

    for col in daily_returns.columns:
        returns = daily_returns[col].dropna()
        if len(returns) == 0:
            continue

        # 基础统计
        mean_return = returns.mean()
        std_return = returns.std()

        # 年化指标
        annualized_return = mean_return * trading_days_per_year
        annualized_volatility = std_return * np.sqrt(trading_days_per_year)

        # 夏普比率
        sharpe_ratio = (annualized_return / annualized_volatility
                       if annualized_volatility > 0 else 0)

        # 最大回撤
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # 胜率
        win_rate = (returns > 0).mean()

        # 盈亏比
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        profit_loss_ratio = (positive_returns.mean() / abs(negative_returns.mean())
                            if len(negative_returns) > 0 and negative_returns.mean() != 0
                            else 0)

        # Calmar比率
        calmar_ratio = (annualized_return / abs(max_drawdown)
                       if max_drawdown != 0 else 0)

        # 总收益率
        total_return = cumulative.iloc[-1] - 1 if len(cumulative) > 0 else 0

        stats_list.append({
            '分层': col,
            '总收益率': total_return,
            '年化收益率': annualized_return,
            '年化波动率': annualized_volatility,
            '夏普比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            'Calmar比率': calmar_ratio,
            '胜率': win_rate,
            '盈亏比': profit_loss_ratio,
            '交易天数': len(returns)
        })

    stats_df = pd.DataFrame(stats_list)

    # 添加多空组合分析
    if len(daily_returns.columns) >= 2:
        # 多空组合：最高分层做多，最低分层做空
        long_short_returns = (daily_returns.iloc[:, 0] -
                             daily_returns.iloc[:, -1]).dropna()

        if len(long_short_returns) > 0:
            ls_stats = _calculate_portfolio_stats(long_short_returns,
                                                 trading_days_per_year)
            ls_stats['分层'] = f'{daily_returns.columns[0]}-{daily_returns.columns[-1]}'
            stats_df = pd.concat([stats_df, pd.DataFrame([ls_stats])],
                               ignore_index=True)

    print(f"成功完成因子 '{factor_col}' 的 {quantiles} 分层回测。")
    return net_value_df, stats_df


def _calculate_portfolio_stats(returns: pd.Series,
                              trading_days_per_year: int = 252) -> Dict[str, float]:
    """
    计算投资组合的统计指标。

    参数:
    - returns (pd.Series): 收益率序列
    - trading_days_per_year (int): 年交易日数

    返回:
    - Dict[str, float]: 统计指标字典
    """
    if len(returns) == 0:
        return {}

    mean_return = returns.mean()
    std_return = returns.std()

    # 年化指标
    annualized_return = mean_return * trading_days_per_year
    annualized_volatility = std_return * np.sqrt(trading_days_per_year)

    # 夏普比率
    sharpe_ratio = (annualized_return / annualized_volatility
                   if annualized_volatility > 0 else 0)

    # 最大回撤
    cumulative = (1 + returns).cumprod()
    running_max = cumulative.expanding().max()
    drawdown = (cumulative - running_max) / running_max
    max_drawdown = drawdown.min()

    # 胜率
    win_rate = (returns > 0).mean()

    # 盈亏比
    positive_returns = returns[returns > 0]
    negative_returns = returns[returns < 0]
    profit_loss_ratio = (positive_returns.mean() / abs(negative_returns.mean())
                        if len(negative_returns) > 0 and negative_returns.mean() != 0
                        else 0)

    # Calmar比率
    calmar_ratio = (annualized_return / abs(max_drawdown)
                   if max_drawdown != 0 else 0)

    # 总收益率
    total_return = cumulative.iloc[-1] - 1 if len(cumulative) > 0 else 0

    return {
        '总收益率': total_return,
        '年化收益率': annualized_return,
        '年化波动率': annualized_volatility,
        '夏普比率': sharpe_ratio,
        '最大回撤': max_drawdown,
        'Calmar比率': calmar_ratio,
        '胜率': win_rate,
        '盈亏比': profit_loss_ratio,
        '交易天数': len(returns)
    }


def comprehensive_factor_evaluation(
    data: pd.DataFrame,
    factor_col: str,
    fwd_return_col: str,
    quantiles: int = 5,
    ic_method: Literal['pearson', 'spearman'] = 'pearson',
    min_periods: int = 10
) -> Dict[str, any]:
    """
    对因子进行综合评估，包括IC分析和分层回测。

    参数:
    - data (pd.DataFrame): 包含因子和收益率数据的DataFrame
    - factor_col (str): 因子列名
    - fwd_return_col (str): 未来收益率列名
    - quantiles (int): 分层数量
    - ic_method (str): IC计算方法
    - min_periods (int): 最小样本数

    返回:
    - Dict: 包含IC分析和分层回测结果的字典
    """
    print(f"开始对因子 '{factor_col}' 进行综合评估...")

    results = {}

    try:
        # 1. IC分析
        print("正在计算IC...")
        ic_series = calculate_ic(data, factor_col, fwd_return_col,
                               ic_method, min_periods)
        ic_stats = calculate_ic_statistics(ic_series)

        results['ic_series'] = ic_series
        results['ic_statistics'] = ic_stats

        # 2. 分层回测
        print("正在进行分层回测...")
        net_value_df, stats_df = perform_quantile_analysis(
            data, factor_col, fwd_return_col, quantiles)

        results['net_value'] = net_value_df
        results['quantile_stats'] = stats_df

        # 3. 因子有效性评估
        results['factor_effectiveness'] = _evaluate_factor_effectiveness(
            ic_stats, stats_df)

        print(f"因子 '{factor_col}' 综合评估完成。")

    except Exception as e:
        print(f"因子评估过程中发生错误: {e}")
        results['error'] = str(e)

    return results


def _evaluate_factor_effectiveness(ic_stats: Dict,
                                  quantile_stats: pd.DataFrame) -> Dict[str, any]:
    """
    评估因子的有效性。

    参数:
    - ic_stats (Dict): IC统计指标
    - quantile_stats (pd.DataFrame): 分层回测统计

    返回:
    - Dict: 因子有效性评估结果
    """
    effectiveness = {}

    if not ic_stats or quantile_stats.empty:
        effectiveness['overall_score'] = 0
        effectiveness['evaluation'] = "数据不足，无法评估"
        return effectiveness

    # IC有效性评分 (0-40分)
    ic_score = 0
    if 'ICIR' in ic_stats:
        icir = abs(ic_stats['ICIR'])
        if icir > 0.5:
            ic_score += 20
        elif icir > 0.3:
            ic_score += 15
        elif icir > 0.1:
            ic_score += 10
        elif icir > 0.05:
            ic_score += 5

    if 'IC胜率' in ic_stats:
        win_rate = ic_stats['IC胜率']
        if win_rate > 0.6:
            ic_score += 20
        elif win_rate > 0.55:
            ic_score += 15
        elif win_rate > 0.5:
            ic_score += 10
        elif win_rate > 0.45:
            ic_score += 5

    # 分层回测有效性评分 (0-40分)
    quantile_score = 0
    if len(quantile_stats) >= 2:
        # 检查收益率单调性
        returns = quantile_stats['年化收益率'].values
        if len(returns) >= 2:
            # 计算收益率的单调性
            monotonic_score = 0
            for i in range(len(returns) - 1):
                if returns[i] > returns[i + 1]:
                    monotonic_score += 1

            monotonic_ratio = monotonic_score / (len(returns) - 1)
            if monotonic_ratio > 0.8:
                quantile_score += 20
            elif monotonic_ratio > 0.6:
                quantile_score += 15
            elif monotonic_ratio > 0.4:
                quantile_score += 10
            elif monotonic_ratio > 0.2:
                quantile_score += 5

        # 检查多空组合表现
        ls_rows = quantile_stats[quantile_stats['分层'].str.contains('-', na=False)]
        if not ls_rows.empty:
            ls_sharpe = ls_rows['夏普比率'].iloc[0]
            if ls_sharpe > 1.0:
                quantile_score += 20
            elif ls_sharpe > 0.5:
                quantile_score += 15
            elif ls_sharpe > 0.2:
                quantile_score += 10
            elif ls_sharpe > 0:
                quantile_score += 5

    # 稳定性评分 (0-20分)
    stability_score = 0
    if 'IC标准差' in ic_stats and 'IC均值' in ic_stats:
        ic_stability = abs(ic_stats['IC均值']) / ic_stats['IC标准差'] if ic_stats['IC标准差'] > 0 else 0
        if ic_stability > 0.5:
            stability_score += 20
        elif ic_stability > 0.3:
            stability_score += 15
        elif ic_stability > 0.1:
            stability_score += 10
        elif ic_stability > 0.05:
            stability_score += 5

    # 总分计算
    total_score = ic_score + quantile_score + stability_score

    # 评估等级
    if total_score >= 80:
        evaluation = "优秀"
    elif total_score >= 60:
        evaluation = "良好"
    elif total_score >= 40:
        evaluation = "一般"
    elif total_score >= 20:
        evaluation = "较差"
    else:
        evaluation = "无效"

    effectiveness.update({
        'overall_score': total_score,
        'ic_score': ic_score,
        'quantile_score': quantile_score,
        'stability_score': stability_score,
        'evaluation': evaluation,
        'max_score': 100
    })

    return effectiveness


def calculate_ic_batch(
    data: pd.DataFrame,
    factor_col: str,
    fwd_return_col: str,
    method: Literal['pearson', 'spearman'] = 'pearson',
    batch_size: int = 1000,
    min_periods: int = 10
) -> pd.Series:
    """
    批处理计算IC，适用于大数据集，优化内存使用。

    参数:
    - data (pd.DataFrame): 输入数据
    - factor_col (str): 因子列名
    - fwd_return_col (str): 收益率列名
    - method (str): 相关系数计算方法
    - batch_size (int): 批处理大小
    - min_periods (int): 最小样本数

    返回:
    - pd.Series: IC时间序列
    """
    print(f"开始批处理IC计算，数据量: {len(data)}, 批大小: {batch_size}")

    # 按日期排序
    data_sorted = data.sort_values('datetime')
    unique_dates = data_sorted['datetime'].unique()

    ic_results = []

    # 分批处理日期
    for i in range(0, len(unique_dates), batch_size):
        batch_dates = unique_dates[i:i + batch_size]
        batch_data = data_sorted[data_sorted['datetime'].isin(batch_dates)]

        # 计算这批数据的IC
        batch_ic = calculate_ic(
            batch_data, factor_col, fwd_return_col,
            method, min_periods, validate_data=False, clean_outliers_flag=False
        )

        ic_results.append(batch_ic)

        # 清理内存
        del batch_data
        gc.collect()

        if (i // batch_size + 1) % 10 == 0:
            print(f"已处理 {i // batch_size + 1} 批数据")

    # 合并结果
    final_ic = pd.concat(ic_results, axis=0)
    final_ic = final_ic.sort_index()

    print(f"批处理IC计算完成，共计算 {len(final_ic)} 个时间点")
    return final_ic


def optimize_dataframe_memory(df: pd.DataFrame) -> pd.DataFrame:
    """
    优化DataFrame的内存使用。

    参数:
    - df (pd.DataFrame): 输入DataFrame

    返回:
    - pd.DataFrame: 优化后的DataFrame
    """
    original_memory = df.memory_usage(deep=True).sum() / 1024**2

    optimized_df = df.copy()

    # 优化数值列
    for col in optimized_df.select_dtypes(include=[np.number]).columns:
        col_min = optimized_df[col].min()
        col_max = optimized_df[col].max()

        if optimized_df[col].dtype == 'int64':
            if col_min >= -128 and col_max <= 127:
                optimized_df[col] = optimized_df[col].astype('int8')
            elif col_min >= -32768 and col_max <= 32767:
                optimized_df[col] = optimized_df[col].astype('int16')
            elif col_min >= -2147483648 and col_max <= 2147483647:
                optimized_df[col] = optimized_df[col].astype('int32')

        elif optimized_df[col].dtype == 'float64':
            optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')

    # 优化字符串列
    for col in optimized_df.select_dtypes(include=['object']).columns:
        if col != 'datetime':  # 保持datetime列不变
            num_unique_values = len(optimized_df[col].unique())
            num_total_values = len(optimized_df[col])
            if num_unique_values / num_total_values < 0.5:
                optimized_df[col] = optimized_df[col].astype('category')

    new_memory = optimized_df.memory_usage(deep=True).sum() / 1024**2
    print(f"内存优化: {original_memory:.2f}MB -> {new_memory:.2f}MB "
          f"(节省 {(1-new_memory/original_memory)*100:.1f}%)")

    return optimized_df
