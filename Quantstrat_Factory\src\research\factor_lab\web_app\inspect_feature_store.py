import pandas as pd
from pathlib import Path

def inspect_parquet_date_range(file_path):
    """读取一个Parquet文件并打印其包含的日期范围。"""
    try:
        path = Path(file_path)
        if not path.exists():
            print(f"错误: 文件未找到路径 {path}")
            return

        df = pd.read_parquet(path)
        
        if df.empty:
            print(f"文件为空: {path}")
            return
            
        if 'datetime' not in df.columns:
            print(f"错误: 在 {path} 中未找到 'datetime' 列。")
            print(f"可用列: {df.columns.tolist()}")
            return
            
        # 确保 datetime 列是日期时间类型
        df['datetime'] = pd.to_datetime(df['datetime'])
        min_date = df['datetime'].min()
        max_date = df['datetime'].max()
        
        print("\n--- 特征文件诊断报告 ---")
        print(f"文件: {path.name}")
        print(f"总记录数: {len(df)}")
        print(f"数据起始日期: {min_date.strftime('%Y-%m-%d')}")
        print(f"数据结束日期: {max_date.strftime('%Y-%m-%d')}")
        print("--------------------------\n")

    except Exception as e:
        print(f"读取或分析文件时发生错误: {e}")

if __name__ == "__main__":
    # 我们正在调试的特征文件的路径 - 使用新的统一路径
    feature_file_path = "D:/PY/Data/cleaned/daily/daily_basics.parquet"
    inspect_parquet_date_range(feature_file_path)
