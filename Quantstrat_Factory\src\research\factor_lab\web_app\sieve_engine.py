import pandas as pd
import numpy as np
import os
import logging
from concurrent.futures import ProcessPoolExecutor, as_completed
import time
import configparser
import sys
from pathlib import Path
from functools import reduce

# 将项目根目录添加到sys.path，以便导入FeatureStoreClient
project_root = Path(__file__).resolve().parents[4]
sys.path.append(str(project_root))

# 添加 auditor 目录到路径
auditor_path = project_root / 'src' / 'data' / 'auditor'
sys.path.append(str(auditor_path))

from feature_store_client import FeatureStoreClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("price_volume_sieve")

def _get_operator_fn(op_str: str):
    """Maps an operator string to a function."""
    ops = {
        '>': np.greater,
        '<': np.less,
        '>=': np.greater_equal,
        '<=': np.less_equal,
        '==': np.equal,
        '!=': np.not_equal
    }
    return ops.get(op_str)


def aroon(data: pd.DataFrame, period: int = 25):
    """
    Calculate Aroon Up and Aroon Down.
    """
    high = data['high']
    low = data['low']
    
    high_rolling = high.rolling(period + 1)
    low_rolling = low.rolling(period + 1)
    
    data['aroon_up'] = 100 * high_rolling.apply(lambda x: x.argmax() / period, raw=True)
    data['aroon_down'] = 100 * low_rolling.apply(lambda x: x.argmin() / period, raw=True)
    
    return data

def run_sieve(
    start_date: str,
    end_date: str,
    segments: list,
    conditions: list,
    config_path: str,
    m_value: int = 20,
    aroon_period: int = 25,
    return_detail: bool = False,
    excluded_stocks: list = None
):
    """
    Main function to run the price-volume sieve.

    Args:
        start_date (str): The start date for the analysis (YYYY-MM-DD).
        end_date (str): The end date for the analysis (YYYY-MM-DD).
        segments (list): A list of integers representing the length of each sub-interval.
        conditions (list): A list of dictionaries defining the conditions for each segment.
        config_path (str): Path to the config.ini file to get DATA_DIR.
        m_value (int): The lookback period for calculating the average volume.
        aroon_period (int): The lookback period for the Aroon indicator.
        return_detail (bool): If True, return detailed match information for visualization.

    Returns:
        list or tuple: List of matched stock symbols, or a tuple with the list and detailed results.
    """
    logger.info("Starting Price-Volume Sieve...")
    start_time = time.time()

    # --- 1. Parameter Validation ---
    if not segments or not conditions or len(segments) != len(conditions):
        logger.error("Segments and conditions must be non-empty and have the same length.")
        if return_detail:
            return [], {}
        else:
            return []

    # --- 2. Data Loading & Pre-calculation ---
    logger.info("Loading and pre-calculating data...")
    
    # Filter out excluded stocks
    if excluded_stocks is None:
        excluded_stocks = []
        
    # --- 2. Data Loading from unified paths ---
    logger.info("Loading data from unified paths...")
    try:
        # 使用统一的数据路径
        daily_basics_path = Path("D:/PY/Data/cleaned/daily/daily_basics.parquet")
        features_path = Path("D:/PY/Data/features")
        
        # Extract all unique metrics needed from conditions
        required_metrics = set()
        for condition in conditions:
            for rule in condition['rules']:
                required_metrics.add(rule['metric'])
        
        if not required_metrics:
            logger.warning("No metrics specified in conditions. Exiting.")
            if return_detail:
                return [], {}
            else:
                return []

        # Load each required metric as a DataFrame
        # Note: This assumes we know the category for each metric.
        # We will need a mapping or a search logic. For now, let's assume a default category.
        # A better approach would be to pass categories along with metrics.
        # For this fix, we'll assume they are in 'technical' or we can try to find them.
        
        metric_dfs = []
        # A simple mapping for now. This should be improved in a real system.
        metric_to_category = {
            'pct_chg': 'base', # pct_chg is in base category (daily_basics)
            'volume_ratio': 'technical',
            'aroon_up': 'technical',
            'aroon_down': 'technical'
        }

        # Load base data first, as it contains essential columns like 'close', 'volume'
        if daily_basics_path.exists():
            base_df = pd.read_parquet(daily_basics_path)
            if not base_df.empty:
                metric_dfs.append(base_df)
                logger.info(f"Base data loaded with columns: {list(base_df.columns)}")
            else:
                logger.warning("Base data is empty!")
        else:
            logger.error(f"Base data file not found: {daily_basics_path}")
            return []

        for metric in required_metrics:
            try:
                # Avoid reloading base metrics if they are part of daily_basics
                if metric in base_df.columns:
                    continue

                # Load from unified features path
                metric_file = features_path / f"{metric}.parquet"
                if metric_file.exists():
                    metric_df = pd.read_parquet(metric_file)
                    if not metric_df.empty:
                        metric_dfs.append(metric_df)
                    else:
                        logger.warning(f"Metric '{metric}' data is empty.")
                else:
                    logger.warning(f"Metric '{metric}' file not found: {metric_file}")
            except Exception as e:
                logger.error(f"Failed to load metric '{metric}': {e}")

        if not metric_dfs:
            logger.error("Failed to load any metric data. Exiting.")
            if return_detail:
                return [], {}
            else:
                return []

        # Merge all DataFrames into one
        # Use reduce for a clean merge of multiple dataframes
        merged_df = reduce(lambda left, right: pd.merge(left, right, on=['datetime', 'symbol'], how='outer'), metric_dfs)

        # Filter by date range
        if 'datetime' in merged_df.columns:
            merged_df['datetime'] = pd.to_datetime(merged_df['datetime'])
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            merged_df = merged_df[(merged_df['datetime'] >= start_dt) & (merged_df['datetime'] <= end_dt)]
            logger.info(f"After date filtering ({start_date} to {end_date}): {len(merged_df)} records")

        # Filter by excluded stocks if any
        if excluded_stocks:
            merged_df = merged_df[~merged_df['symbol'].isin(excluded_stocks)]

        if merged_df.empty:
            logger.warning("Merged DataFrame is empty. No data to process.")
            if return_detail:
                return [], {}
            else:
                return []

        # Group by stock symbol to create the all_data dictionary
        all_data = {symbol: group for symbol, group in merged_df.groupby('symbol')}

    except Exception as e:
        logger.error(f"An error occurred during data loading: {e}")
        if return_detail:
            return [], {}
        else:
            return []

    if not all_data:
        logger.warning("No data loaded for the specified date range. Exiting.")
        if return_detail:
            return [], {}
        else:
            return []

    logger.info(f"Successfully loaded and processed data for {len(all_data)} stocks.")

    # --- 3. Core Sieve Logic ---
    logger.info("Applying sieve logic to all stocks...")
    all_matches_symbols = []
    detailed_results = {}

    with ProcessPoolExecutor(max_workers=4) as executor:
        future_to_stock = {
            executor.submit(_apply_sieve_to_stock, df, segments, conditions): symbol
            for symbol, df in all_data.items()
        }

        for future in as_completed(future_to_stock):
            stock_symbol = future_to_stock[future]
            try:
                matches = future.result()
                if matches:
                    all_matches_symbols.append(stock_symbol)
                    if return_detail:
                        if stock_symbol not in detailed_results:
                            detailed_results[stock_symbol] = []
                        detailed_results[stock_symbol].extend(matches)
            except Exception as exc:
                logger.error(f'Sieve for {stock_symbol} generated an exception: {exc}')
    
    logger.info(f"Found {len(all_matches_symbols)} unique stocks matching the criteria.")

    end_time = time.time()
    logger.info(f"Sieve finished in {end_time - start_time:.2f} seconds.")

    if return_detail:
        return all_matches_symbols, detailed_results
    else:
        return all_matches_symbols


def _apply_sieve_to_stock(df: pd.DataFrame, segments: list, conditions: list):
    """
    Applies the multi-segment sieve logic to a single stock's DataFrame.
    """
    # 确保索引是 datetime 类型
    if not isinstance(df.index, pd.DatetimeIndex):
        if 'datetime' in df.columns:
            df = df.set_index('datetime').sort_index()
        else:
            # 如果没有 datetime 列，则无法继续
            logger.error(f"DataFrame for stock {df['symbol'].iloc[0]} has no datetime column or DatetimeIndex.")
            return []

    # --- Step 1: Generate boolean masks for each individual rule ---
    rule_masks = {}
    for i, seg_condition in enumerate(conditions):
        for j, rule in enumerate(seg_condition['rules']):
            metric = rule['metric']
            op_str = rule['operator']
            value = rule['value']
            
            op_fn = _get_operator_fn(op_str)
            if op_fn is None or metric not in df.columns:
                continue # Skip invalid rules
            
            rule_masks[f'seg{i}_rule{j}'] = op_fn(df[metric], value)

    # --- Step 2: Combine rule masks for each segment ---
    segment_masks = []
    for i, seg_condition in enumerate(conditions):
        logic = seg_condition.get('logic', 'AND').upper()
        
        # Collect all masks for the current segment
        current_segment_rule_masks = [
            mask for name, mask in rule_masks.items() if name.startswith(f'seg{i}_')
        ]
        
        if not current_segment_rule_masks:
            # If a segment has no valid rules, it's considered a match by default
            segment_masks.append(pd.Series(True, index=df.index))
            continue

        if logic == 'AND':
            combined_mask = np.logical_and.reduce(current_segment_rule_masks)
        elif logic == 'OR':
            combined_mask = np.logical_or.reduce(current_segment_rule_masks)
        else: # Default to AND
            combined_mask = np.logical_and.reduce(current_segment_rule_masks)
            
        segment_masks.append(pd.Series(combined_mask, index=df.index))

    # --- Step 3: Use rolling windows to find full pattern matches, considering only trading days ---
    # 计算每个区间需要的交易日数量
    trading_day_segments = segments.copy()
    
    # 初始化匹配掩码
    final_match_mask = pd.Series(False, index=df.index)
    
    # 对每个可能的结束日期进行检查
    for end_idx in range(len(df.index)):
        if end_idx < max(segments) - 1:  # 跳过前面不可能形成完整模式的日期
            continue
            
        end_date = df.index[end_idx]
        is_match = True
        
        # 检查每个子区间
        current_idx = end_idx
        for i, seg_len in enumerate(reversed(trading_day_segments)):  # 从最后一个区间开始向前检查
            # 获取当前区间的掩码
            segment_mask = segment_masks[len(segments) - i - 1]
            
            # 检查连续的交易日
            days_checked = 0
            days_matched = 0
            while days_checked < seg_len and current_idx >= 0:
                if segment_mask.iloc[current_idx]:
                    days_matched += 1
                days_checked += 1
                current_idx -= 1
                
            # 如果匹配的天数不足，则整体不匹配
            if days_matched < seg_len:
                is_match = False
                break
        
        if is_match:
            final_match_mask.iloc[end_idx] = True
    
    # --- Step 4: Extract match dates ---
    match_end_dates = df.index[final_match_mask]
    
    matches = []
    for end_date in match_end_dates:
        # 计算开始日期 - 向前查找足够的交易日
        start_date = None
        days_to_count = sum(trading_day_segments)
        days_counted = 0
        current_date = end_date
        
        # 从结束日期开始向前查找交易日
        while days_counted < days_to_count and current_date in df.index:
            days_counted += 1
            if days_counted == days_to_count:
                start_date = current_date
                break
            # 获取前一个交易日
            earlier_dates = df.index[df.index < current_date]
            if len(earlier_dates) > 0:
                current_date = earlier_dates[-1]  # 取最接近的前一个日期
            else:
                break  # 没有更早的日期了
        
        if start_date is not None:
            matches.append((start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')))
        
    return matches


# The function _load_and_filter_stock_data is no longer needed as its logic is now in run_sieve.
# It can be safely removed.
