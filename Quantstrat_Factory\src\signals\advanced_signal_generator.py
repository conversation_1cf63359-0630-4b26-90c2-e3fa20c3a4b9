"""
高级信号生成器

提供优化的信号生成算法、信号质量评估和实时信号生成功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Callable
import logging
from dataclasses import dataclass
from enum import Enum
from scipy import stats
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import cross_val_score
import warnings

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class SignalType(Enum):
    """信号类型"""
    BUY = 1
    SELL = -1
    HOLD = 0


class SignalStrength(Enum):
    """信号强度"""
    WEAK = 1
    MEDIUM = 2
    STRONG = 3


@dataclass
class Signal:
    """信号类"""
    symbol: str
    timestamp: pd.Timestamp
    signal_type: SignalType
    strength: SignalStrength
    confidence: float
    factors: Dict[str, float]
    metadata: Dict[str, Any]


class AdvancedSignalGenerator:
    """高级信号生成器"""
    
    def __init__(self):
        """初始化高级信号生成器"""
        self.signal_generators = {}
        self.signal_history = []
        self.quality_metrics = {}
        self.models = {}
        self.scalers = {}
        
        # 注册信号生成器
        self._register_signal_generators()
        
        logger.info("高级信号生成器初始化完成")
    
    def _register_signal_generators(self):
        """注册信号生成器"""
        self.signal_generators = {
            'momentum_signal': self.generate_momentum_signal,
            'mean_reversion_signal': self.generate_mean_reversion_signal,
            'trend_following_signal': self.generate_trend_following_signal,
            'volume_signal': self.generate_volume_signal,
            'volatility_signal': self.generate_volatility_signal,
            'composite_signal': self.generate_composite_signal,
            'ml_signal': self.generate_ml_signal
        }
    
    def generate_momentum_signal(
        self, 
        data: pd.DataFrame, 
        lookback_period: int = 20,
        threshold: float = 0.02
    ) -> List[Signal]:
        """
        生成动量信号
        
        Args:
            data: 市场数据
            lookback_period: 回看周期
            threshold: 信号阈值
            
        Returns:
            信号列表
        """
        signals = []
        
        # 计算动量指标
        momentum = data['close'].pct_change(lookback_period)
        
        # 计算信号强度
        momentum_std = momentum.rolling(window=60).std()
        
        for i in range(len(data)):
            if pd.isna(momentum.iloc[i]) or pd.isna(momentum_std.iloc[i]):
                continue
            
            current_momentum = momentum.iloc[i]
            current_std = momentum_std.iloc[i]
            
            # 标准化动量
            normalized_momentum = current_momentum / current_std if current_std != 0 else 0
            
            # 生成信号
            if normalized_momentum > threshold:
                signal_type = SignalType.BUY
                confidence = min(abs(normalized_momentum) / (threshold * 3), 1.0)
            elif normalized_momentum < -threshold:
                signal_type = SignalType.SELL
                confidence = min(abs(normalized_momentum) / (threshold * 3), 1.0)
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
            
            # 确定信号强度
            if confidence > 0.7:
                strength = SignalStrength.STRONG
            elif confidence > 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            signal = Signal(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.index[i],
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                factors={'momentum': current_momentum, 'normalized_momentum': normalized_momentum},
                metadata={'generator': 'momentum_signal', 'lookback_period': lookback_period}
            )
            
            signals.append(signal)
        
        return signals
    
    def generate_mean_reversion_signal(
        self, 
        data: pd.DataFrame,
        lookback_period: int = 20,
        threshold: float = 2.0
    ) -> List[Signal]:
        """
        生成均值回归信号
        
        Args:
            data: 市场数据
            lookback_period: 回看周期
            threshold: 标准差阈值
            
        Returns:
            信号列表
        """
        signals = []
        
        # 计算移动平均和标准差
        ma = data['close'].rolling(window=lookback_period).mean()
        std = data['close'].rolling(window=lookback_period).std()
        
        # 计算Z-score
        z_score = (data['close'] - ma) / std
        
        for i in range(len(data)):
            if pd.isna(z_score.iloc[i]):
                continue
            
            current_z = z_score.iloc[i]
            
            # 生成信号（均值回归）
            if current_z > threshold:
                signal_type = SignalType.SELL  # 价格过高，预期回归
                confidence = min(abs(current_z) / (threshold * 2), 1.0)
            elif current_z < -threshold:
                signal_type = SignalType.BUY  # 价格过低，预期回归
                confidence = min(abs(current_z) / (threshold * 2), 1.0)
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
            
            # 确定信号强度
            if confidence > 0.7:
                strength = SignalStrength.STRONG
            elif confidence > 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            signal = Signal(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.index[i],
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                factors={'z_score': current_z, 'ma': ma.iloc[i], 'std': std.iloc[i]},
                metadata={'generator': 'mean_reversion_signal', 'lookback_period': lookback_period}
            )
            
            signals.append(signal)
        
        return signals
    
    def generate_trend_following_signal(
        self, 
        data: pd.DataFrame,
        short_period: int = 10,
        long_period: int = 30
    ) -> List[Signal]:
        """
        生成趋势跟踪信号
        
        Args:
            data: 市场数据
            short_period: 短期均线周期
            long_period: 长期均线周期
            
        Returns:
            信号列表
        """
        signals = []
        
        # 计算短期和长期移动平均
        ma_short = data['close'].rolling(window=short_period).mean()
        ma_long = data['close'].rolling(window=long_period).mean()
        
        # 计算均线差值
        ma_diff = (ma_short - ma_long) / ma_long
        
        # 计算趋势强度
        trend_strength = ma_diff.rolling(window=10).std()
        
        for i in range(len(data)):
            if pd.isna(ma_diff.iloc[i]) or pd.isna(trend_strength.iloc[i]):
                continue
            
            current_diff = ma_diff.iloc[i]
            current_strength = trend_strength.iloc[i]
            
            # 生成信号
            if current_diff > 0:
                signal_type = SignalType.BUY
                confidence = min(abs(current_diff) / (current_strength * 2), 1.0) if current_strength != 0 else 0
            elif current_diff < 0:
                signal_type = SignalType.SELL
                confidence = min(abs(current_diff) / (current_strength * 2), 1.0) if current_strength != 0 else 0
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
            
            # 确定信号强度
            if confidence > 0.7:
                strength = SignalStrength.STRONG
            elif confidence > 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            signal = Signal(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.index[i],
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                factors={
                    'ma_short': ma_short.iloc[i], 
                    'ma_long': ma_long.iloc[i], 
                    'ma_diff': current_diff
                },
                metadata={
                    'generator': 'trend_following_signal', 
                    'short_period': short_period, 
                    'long_period': long_period
                }
            )
            
            signals.append(signal)
        
        return signals
    
    def generate_volume_signal(
        self, 
        data: pd.DataFrame,
        lookback_period: int = 20
    ) -> List[Signal]:
        """
        生成成交量信号
        
        Args:
            data: 市场数据
            lookback_period: 回看周期
            
        Returns:
            信号列表
        """
        signals = []
        
        # 计算成交量移动平均
        volume_ma = data['volume'].rolling(window=lookback_period).mean()
        volume_ratio = data['volume'] / volume_ma
        
        # 计算价格变化
        price_change = data['close'].pct_change()
        
        for i in range(len(data)):
            if pd.isna(volume_ratio.iloc[i]) or pd.isna(price_change.iloc[i]):
                continue
            
            current_volume_ratio = volume_ratio.iloc[i]
            current_price_change = price_change.iloc[i]
            
            # 量价配合信号
            if current_volume_ratio > 1.5 and current_price_change > 0:
                signal_type = SignalType.BUY
                confidence = min(current_volume_ratio / 3.0, 1.0)
            elif current_volume_ratio > 1.5 and current_price_change < 0:
                signal_type = SignalType.SELL
                confidence = min(current_volume_ratio / 3.0, 1.0)
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
            
            # 确定信号强度
            if confidence > 0.7:
                strength = SignalStrength.STRONG
            elif confidence > 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            signal = Signal(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.index[i],
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                factors={
                    'volume_ratio': current_volume_ratio,
                    'price_change': current_price_change,
                    'volume_ma': volume_ma.iloc[i]
                },
                metadata={'generator': 'volume_signal', 'lookback_period': lookback_period}
            )
            
            signals.append(signal)
        
        return signals
    
    def generate_volatility_signal(
        self, 
        data: pd.DataFrame,
        lookback_period: int = 20
    ) -> List[Signal]:
        """
        生成波动率信号
        
        Args:
            data: 市场数据
            lookback_period: 回看周期
            
        Returns:
            信号列表
        """
        signals = []
        
        # 计算波动率
        returns = data['close'].pct_change()
        volatility = returns.rolling(window=lookback_period).std()
        volatility_ma = volatility.rolling(window=lookback_period).mean()
        
        # 波动率比率
        vol_ratio = volatility / volatility_ma
        
        for i in range(len(data)):
            if pd.isna(vol_ratio.iloc[i]):
                continue
            
            current_vol_ratio = vol_ratio.iloc[i]
            
            # 基于波动率的信号
            if current_vol_ratio < 0.7:  # 低波动率，可能突破
                signal_type = SignalType.BUY
                confidence = (0.7 - current_vol_ratio) / 0.7
            elif current_vol_ratio > 1.5:  # 高波动率，可能回调
                signal_type = SignalType.SELL
                confidence = min((current_vol_ratio - 1.5) / 1.5, 1.0)
            else:
                signal_type = SignalType.HOLD
                confidence = 0.0
            
            # 确定信号强度
            if confidence > 0.7:
                strength = SignalStrength.STRONG
            elif confidence > 0.4:
                strength = SignalStrength.MEDIUM
            else:
                strength = SignalStrength.WEAK
            
            signal = Signal(
                symbol=data.get('symbol', 'UNKNOWN'),
                timestamp=data.index[i],
                signal_type=signal_type,
                strength=strength,
                confidence=confidence,
                factors={
                    'volatility': volatility.iloc[i],
                    'volatility_ma': volatility_ma.iloc[i],
                    'vol_ratio': current_vol_ratio
                },
                metadata={'generator': 'volatility_signal', 'lookback_period': lookback_period}
            )
            
            signals.append(signal)
        
        return signals
    
    def generate_composite_signal(
        self, 
        data: pd.DataFrame,
        weights: Optional[Dict[str, float]] = None
    ) -> List[Signal]:
        """
        生成复合信号
        
        Args:
            data: 市场数据
            weights: 各信号权重
            
        Returns:
            信号列表
        """
        if weights is None:
            weights = {
                'momentum_signal': 0.3,
                'mean_reversion_signal': 0.2,
                'trend_following_signal': 0.3,
                'volume_signal': 0.1,
                'volatility_signal': 0.1
            }
        
        # 生成各类信号
        all_signals = {}
        for signal_name, weight in weights.items():
            if signal_name in self.signal_generators and weight > 0:
                signals = self.signal_generators[signal_name](data)
                all_signals[signal_name] = signals
        
        # 合成信号
        composite_signals = []
        
        if not all_signals:
            return composite_signals
        
        # 获取时间索引
        timestamps = set()
        for signals in all_signals.values():
            timestamps.update([s.timestamp for s in signals])
        
        timestamps = sorted(timestamps)
        
        for timestamp in timestamps:
            # 收集该时间点的所有信号
            timestamp_signals = {}
            for signal_name, signals in all_signals.items():
                for signal in signals:
                    if signal.timestamp == timestamp:
                        timestamp_signals[signal_name] = signal
                        break
            
            if not timestamp_signals:
                continue
            
            # 计算加权信号
            weighted_signal = 0.0
            weighted_confidence = 0.0
            total_weight = 0.0
            
            factors = {}
            
            for signal_name, signal in timestamp_signals.items():
                weight = weights.get(signal_name, 0)
                if weight > 0:
                    signal_value = signal.signal_type.value * signal.confidence
                    weighted_signal += signal_value * weight
                    weighted_confidence += signal.confidence * weight
                    total_weight += weight
                    
                    factors[f'{signal_name}_signal'] = signal_value
                    factors[f'{signal_name}_confidence'] = signal.confidence
            
            if total_weight > 0:
                weighted_signal /= total_weight
                weighted_confidence /= total_weight
                
                # 确定复合信号类型
                if weighted_signal > 0.1:
                    signal_type = SignalType.BUY
                elif weighted_signal < -0.1:
                    signal_type = SignalType.SELL
                else:
                    signal_type = SignalType.HOLD
                
                # 确定信号强度
                if weighted_confidence > 0.7:
                    strength = SignalStrength.STRONG
                elif weighted_confidence > 0.4:
                    strength = SignalStrength.MEDIUM
                else:
                    strength = SignalStrength.WEAK
                
                composite_signal = Signal(
                    symbol=data.get('symbol', 'UNKNOWN'),
                    timestamp=timestamp,
                    signal_type=signal_type,
                    strength=strength,
                    confidence=weighted_confidence,
                    factors=factors,
                    metadata={'generator': 'composite_signal', 'weights': weights}
                )
                
                composite_signals.append(composite_signal)
        
        return composite_signals
    
    def generate_ml_signal(
        self, 
        data: pd.DataFrame,
        features: List[str],
        target_period: int = 5,
        model_type: str = 'random_forest'
    ) -> List[Signal]:
        """
        生成机器学习信号
        
        Args:
            data: 市场数据
            features: 特征列名
            target_period: 预测周期
            model_type: 模型类型
            
        Returns:
            信号列表
        """
        signals = []
        
        try:
            # 准备特征数据
            feature_data = data[features].copy()
            
            # 计算目标变量（未来收益）
            future_returns = data['close'].pct_change(target_period).shift(-target_period)
            
            # 创建分类标签
            target = np.where(future_returns > 0.02, 1,  # 买入
                            np.where(future_returns < -0.02, -1, 0))  # 卖出，持有
            
            # 移除缺失值
            valid_mask = ~(feature_data.isnull().any(axis=1) | pd.isna(target))
            feature_data = feature_data[valid_mask]
            target = target[valid_mask]
            
            if len(feature_data) < 100:  # 数据不足
                return signals
            
            # 训练模型
            if model_type == 'random_forest':
                model = RandomForestClassifier(n_estimators=100, random_state=42)
            elif model_type == 'gradient_boosting':
                model = GradientBoostingClassifier(n_estimators=100, random_state=42)
            else:
                model = RandomForestClassifier(n_estimators=100, random_state=42)
            
            # 标准化特征
            scaler = StandardScaler()
            feature_scaled = scaler.fit_transform(feature_data)
            
            # 训练模型
            model.fit(feature_scaled, target)
            
            # 预测
            predictions = model.predict(feature_scaled)
            probabilities = model.predict_proba(feature_scaled)
            
            # 生成信号
            for i, (idx, row) in enumerate(feature_data.iterrows()):
                prediction = predictions[i]
                prob = probabilities[i]
                
                if prediction == 1:
                    signal_type = SignalType.BUY
                    confidence = prob[model.classes_ == 1][0] if 1 in model.classes_ else 0
                elif prediction == -1:
                    signal_type = SignalType.SELL
                    confidence = prob[model.classes_ == -1][0] if -1 in model.classes_ else 0
                else:
                    signal_type = SignalType.HOLD
                    confidence = prob[model.classes_ == 0][0] if 0 in model.classes_ else 0
                
                # 确定信号强度
                if confidence > 0.7:
                    strength = SignalStrength.STRONG
                elif confidence > 0.5:
                    strength = SignalStrength.MEDIUM
                else:
                    strength = SignalStrength.WEAK
                
                signal = Signal(
                    symbol=data.get('symbol', 'UNKNOWN'),
                    timestamp=idx,
                    signal_type=signal_type,
                    strength=strength,
                    confidence=confidence,
                    factors={'ml_prediction': prediction, 'ml_probability': confidence},
                    metadata={
                        'generator': 'ml_signal', 
                        'model_type': model_type,
                        'features': features,
                        'target_period': target_period
                    }
                )
                
                signals.append(signal)
        
        except Exception as e:
            logger.error(f"机器学习信号生成失败: {e}")
        
        return signals
    
    def generate_signals(
        self, 
        data: pd.DataFrame,
        signal_types: List[str],
        **kwargs
    ) -> Dict[str, List[Signal]]:
        """
        批量生成信号
        
        Args:
            data: 市场数据
            signal_types: 信号类型列表
            **kwargs: 信号参数
            
        Returns:
            信号字典
        """
        results = {}
        
        for signal_type in signal_types:
            if signal_type in self.signal_generators:
                try:
                    signals = self.signal_generators[signal_type](data, **kwargs)
                    results[signal_type] = signals
                    logger.debug(f"生成 {signal_type} 信号: {len(signals)} 个")
                except Exception as e:
                    logger.error(f"生成 {signal_type} 信号失败: {e}")
                    results[signal_type] = []
            else:
                logger.warning(f"未知信号类型: {signal_type}")
                results[signal_type] = []
        
        return results
    
    def evaluate_signal_quality(
        self, 
        signals: List[Signal],
        actual_returns: pd.Series,
        evaluation_period: int = 5
    ) -> Dict[str, Any]:
        """
        评估信号质量
        
        Args:
            signals: 信号列表
            actual_returns: 实际收益率
            evaluation_period: 评估周期
            
        Returns:
            质量评估结果
        """
        if not signals:
            return {}
        
        # 转换为DataFrame便于分析
        signal_data = []
        for signal in signals:
            signal_data.append({
                'timestamp': signal.timestamp,
                'signal_type': signal.signal_type.value,
                'confidence': signal.confidence,
                'strength': signal.strength.value
            })
        
        signal_df = pd.DataFrame(signal_data)
        signal_df.set_index('timestamp', inplace=True)
        
        # 对齐数据
        aligned_data = pd.merge(
            signal_df, 
            actual_returns.to_frame('returns'), 
            left_index=True, 
            right_index=True, 
            how='inner'
        )
        
        if len(aligned_data) == 0:
            return {}
        
        # 计算前瞻收益
        aligned_data['forward_returns'] = aligned_data['returns'].shift(-evaluation_period).rolling(window=evaluation_period).sum()
        
        # 移除缺失值
        aligned_data = aligned_data.dropna()
        
        if len(aligned_data) == 0:
            return {}
        
        # 计算质量指标
        quality_metrics = {}
        
        # 信号准确率
        buy_signals = aligned_data[aligned_data['signal_type'] == 1]
        sell_signals = aligned_data[aligned_data['signal_type'] == -1]
        
        if len(buy_signals) > 0:
            buy_accuracy = (buy_signals['forward_returns'] > 0).mean()
            quality_metrics['buy_accuracy'] = buy_accuracy
        
        if len(sell_signals) > 0:
            sell_accuracy = (sell_signals['forward_returns'] < 0).mean()
            quality_metrics['sell_accuracy'] = sell_accuracy
        
        # 信号收益率
        signal_returns = aligned_data['signal_type'] * aligned_data['forward_returns']
        quality_metrics['average_signal_return'] = signal_returns.mean()
        quality_metrics['signal_sharpe'] = signal_returns.mean() / signal_returns.std() if signal_returns.std() != 0 else 0
        
        # 信号频率
        total_signals = len(aligned_data)
        buy_rate = len(buy_signals) / total_signals if total_signals > 0 else 0
        sell_rate = len(sell_signals) / total_signals if total_signals > 0 else 0
        hold_rate = 1 - buy_rate - sell_rate
        
        quality_metrics['signal_frequency'] = {
            'buy_rate': buy_rate,
            'sell_rate': sell_rate,
            'hold_rate': hold_rate
        }
        
        # 置信度分析
        high_confidence = aligned_data[aligned_data['confidence'] > 0.7]
        if len(high_confidence) > 0:
            high_conf_returns = high_confidence['signal_type'] * high_confidence['forward_returns']
            quality_metrics['high_confidence_return'] = high_conf_returns.mean()
        
        return quality_metrics
    
    def get_signal_generators(self) -> List[str]:
        """获取可用的信号生成器列表"""
        return list(self.signal_generators.keys())
    
    def add_signal_generator(self, name: str, generator_func: Callable):
        """
        添加自定义信号生成器
        
        Args:
            name: 生成器名称
            generator_func: 生成器函数
        """
        self.signal_generators[name] = generator_func
        logger.info(f"添加信号生成器: {name}")
    
    def get_signal_history(self) -> List[Signal]:
        """获取信号历史"""
        return self.signal_history.copy()
    
    def clear_signal_history(self):
        """清空信号历史"""
        self.signal_history.clear()
        logger.info("信号历史已清空")
