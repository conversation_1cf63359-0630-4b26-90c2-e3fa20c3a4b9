#!/usr/bin/env python3
"""
策略构建器运行脚本。

提供命令行接口来构建、管理和测试策略。
"""

import argparse
import sys
import logging
from pathlib import Path
import json

# 添加项目根目录到路径
project_root = Path(__file__).resolve().parents[3]
sys.path.insert(0, str(project_root))

from src.strategy.builder.strategy_builder import StrategyBuilder
from src.strategy.builder.strategy_template import StrategyTemplate
from src.strategy.builder.strategy_manager import StrategyManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def list_templates():
    """列出所有可用的策略模板。"""
    template_manager = StrategyTemplate()
    templates = template_manager.get_available_templates()
    
    print("\n可用的策略模板:")
    print("=" * 50)
    
    for template in templates:
        print(f"名称: {template['name']}")
        print(f"显示名称: {template['display_name']}")
        print(f"描述: {template['description']}")
        print(f"类别: {template['category']}")
        print(f"类型: {template['type']}")
        print("-" * 30)


def create_strategy_from_template(template_name: str, strategy_name: str):
    """从模板创建策略。"""
    try:
        # 初始化组件
        template_manager = StrategyTemplate()
        builder = StrategyBuilder()
        strategy_manager = StrategyManager()
        
        # 获取模板
        template = template_manager.get_template(template_name)
        if not template:
            print(f"错误: 模板 '{template_name}' 不存在")
            return False
        
        # 应用模板到构建器
        if not template_manager.apply_template(builder, template):
            print("错误: 应用模板失败")
            return False
        
        # 构建策略
        strategy = builder.build_strategy(strategy_name, f"基于模板 {template_name} 创建的策略")
        
        # 验证策略
        is_valid, errors = builder.validate_strategy(strategy)
        if not is_valid:
            print("策略验证失败:")
            for error in errors:
                print(f"  - {error}")
            return False
        
        # 保存策略
        if strategy_manager.save_strategy(strategy):
            print(f"成功创建策略: {strategy_name}")
            print_strategy_summary(strategy)
            return True
        else:
            print("保存策略失败")
            return False
            
    except Exception as e:
        logger.error(f"创建策略失败: {e}")
        return False


def create_custom_strategy(strategy_name: str, config_file: str = None):
    """创建自定义策略。"""
    try:
        builder = StrategyBuilder()
        strategy_manager = StrategyManager()
        
        if config_file:
            # 从配置文件创建
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 添加因子
            for factor in config.get('factors', []):
                builder.add_factor(factor)
            
            # 添加信号规则
            for signal in config.get('signals', []):
                builder.add_signal_rule(signal)
            
            # 添加风险控制
            for risk_control in config.get('risk_controls', []):
                builder.add_risk_control(risk_control)
        
        else:
            # 交互式创建
            print("创建自定义策略 (交互模式)")
            print("=" * 30)
            
            # 添加因子
            print("\n1. 添加因子:")
            while True:
                factor_name = input("因子名称 (回车结束): ").strip()
                if not factor_name:
                    break
                
                factor_type = input("因子类型 (technical/fundamental/sentiment): ").strip()
                if factor_type not in ['technical', 'fundamental', 'sentiment']:
                    print("无效的因子类型")
                    continue
                
                try:
                    weight = float(input("因子权重: "))
                except ValueError:
                    print("无效的权重值")
                    continue
                
                factor_config = {
                    'name': factor_name,
                    'type': factor_type,
                    'weight': weight
                }
                
                if builder.add_factor(factor_config):
                    print(f"成功添加因子: {factor_name}")
                else:
                    print(f"添加因子失败: {factor_name}")
            
            # 添加信号规则
            print("\n2. 添加信号规则:")
            while True:
                signal_name = input("信号名称 (回车结束): ").strip()
                if not signal_name:
                    break
                
                condition = input("信号条件: ").strip()
                action = input("信号动作 (buy/sell/hold): ").strip()
                
                if action not in ['buy', 'sell', 'hold']:
                    print("无效的信号动作")
                    continue
                
                signal_config = {
                    'name': signal_name,
                    'condition': condition,
                    'action': action
                }
                
                if builder.add_signal_rule(signal_config):
                    print(f"成功添加信号规则: {signal_name}")
                else:
                    print(f"添加信号规则失败: {signal_name}")
        
        # 构建策略
        description = input("\n策略描述: ").strip() if not config_file else config.get('description', '')
        strategy = builder.build_strategy(strategy_name, description)
        
        # 验证策略
        is_valid, errors = builder.validate_strategy(strategy)
        if not is_valid:
            print("\n策略验证失败:")
            for error in errors:
                print(f"  - {error}")
            
            # 询问是否仍要保存
            save_anyway = input("\n是否仍要保存策略? (y/n): ").strip().lower()
            if save_anyway != 'y':
                return False
        
        # 保存策略
        if strategy_manager.save_strategy(strategy):
            print(f"\n成功创建策略: {strategy_name}")
            print_strategy_summary(strategy)
            return True
        else:
            print("保存策略失败")
            return False
            
    except Exception as e:
        logger.error(f"创建自定义策略失败: {e}")
        return False


def list_strategies():
    """列出所有策略。"""
    strategy_manager = StrategyManager()
    strategies = strategy_manager.list_strategies()
    
    if not strategies:
        print("没有找到任何策略")
        return
    
    print(f"\n找到 {len(strategies)} 个策略:")
    print("=" * 80)
    
    for strategy in strategies:
        print(f"名称: {strategy['name']}")
        print(f"描述: {strategy['description']}")
        print(f"类别: {strategy['category']}")
        print(f"因子数量: {strategy['factor_count']}")
        print(f"信号数量: {strategy['signal_count']}")
        print(f"版本: {strategy['version']}")
        print(f"创建时间: {strategy.get('created_at', 'N/A')}")
        print("-" * 50)


def show_strategy_details(strategy_name: str):
    """显示策略详情。"""
    strategy_manager = StrategyManager()
    strategy = strategy_manager.load_strategy(strategy_name)
    
    if not strategy:
        print(f"策略 '{strategy_name}' 不存在")
        return
    
    print(f"\n策略详情: {strategy_name}")
    print("=" * 50)
    print_strategy_summary(strategy)
    
    # 显示因子详情
    print("\n因子详情:")
    for i, factor in enumerate(strategy.get('factors', []), 1):
        print(f"  {i}. {factor['name']} ({factor['type']}) - 权重: {factor['weight']}")
    
    # 显示信号详情
    print("\n信号规则详情:")
    for i, signal in enumerate(strategy.get('signals', []), 1):
        print(f"  {i}. {signal['name']}: {signal['condition']} -> {signal['action']}")
    
    # 显示风险控制详情
    risk_controls = strategy.get('risk_controls', [])
    if risk_controls:
        print("\n风险控制详情:")
        for i, risk in enumerate(risk_controls, 1):
            print(f"  {i}. {risk['name']} ({risk['type']})")


def print_strategy_summary(strategy):
    """打印策略摘要。"""
    print(f"描述: {strategy.get('description', 'N/A')}")
    print(f"类别: {strategy.get('category', 'N/A')}")
    print(f"因子数量: {len(strategy.get('factors', []))}")
    print(f"信号数量: {len(strategy.get('signals', []))}")
    print(f"风险控制数量: {len(strategy.get('risk_controls', []))}")
    print(f"版本: {strategy.get('version', 'N/A')}")


def export_strategy(strategy_name: str, output_path: str):
    """导出策略。"""
    strategy_manager = StrategyManager()
    
    if strategy_manager.export_strategy(strategy_name, output_path):
        print(f"成功导出策略到: {output_path}")
    else:
        print("导出策略失败")


def import_strategy(import_path: str, strategy_name: str = None):
    """导入策略。"""
    strategy_manager = StrategyManager()
    
    if strategy_manager.import_strategy(import_path, strategy_name):
        imported_name = strategy_name or "导入的策略"
        print(f"成功导入策略: {imported_name}")
    else:
        print("导入策略失败")


def main():
    """主函数。"""
    parser = argparse.ArgumentParser(description='策略构建器工具')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出模板
    subparsers.add_parser('list-templates', help='列出所有可用的策略模板')
    
    # 从模板创建策略
    create_parser = subparsers.add_parser('create-from-template', help='从模板创建策略')
    create_parser.add_argument('template_name', help='模板名称')
    create_parser.add_argument('strategy_name', help='策略名称')
    
    # 创建自定义策略
    custom_parser = subparsers.add_parser('create-custom', help='创建自定义策略')
    custom_parser.add_argument('strategy_name', help='策略名称')
    custom_parser.add_argument('--config', help='配置文件路径')
    
    # 列出策略
    subparsers.add_parser('list-strategies', help='列出所有策略')
    
    # 显示策略详情
    show_parser = subparsers.add_parser('show', help='显示策略详情')
    show_parser.add_argument('strategy_name', help='策略名称')
    
    # 导出策略
    export_parser = subparsers.add_parser('export', help='导出策略')
    export_parser.add_argument('strategy_name', help='策略名称')
    export_parser.add_argument('output_path', help='输出路径')
    
    # 导入策略
    import_parser = subparsers.add_parser('import', help='导入策略')
    import_parser.add_argument('import_path', help='导入路径')
    import_parser.add_argument('--name', help='新策略名称')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == 'list-templates':
            list_templates()
        
        elif args.command == 'create-from-template':
            if not create_strategy_from_template(args.template_name, args.strategy_name):
                return 1
        
        elif args.command == 'create-custom':
            if not create_custom_strategy(args.strategy_name, args.config):
                return 1
        
        elif args.command == 'list-strategies':
            list_strategies()
        
        elif args.command == 'show':
            show_strategy_details(args.strategy_name)
        
        elif args.command == 'export':
            export_strategy(args.strategy_name, args.output_path)
        
        elif args.command == 'import':
            import_strategy(args.import_path, args.name)
        
        return 0
        
    except Exception as e:
        logger.error(f"命令执行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
