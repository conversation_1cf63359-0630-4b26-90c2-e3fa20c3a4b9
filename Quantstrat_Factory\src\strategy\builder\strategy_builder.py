"""
策略构建器模块。

提供策略构建和验证功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
import logging
from datetime import datetime
import re

logger = logging.getLogger(__name__)


class StrategyBuilder:
    """策略构建器。"""
    
    def __init__(self):
        """初始化策略构建器。"""
        self.strategy_config = {}
        self.factors = []
        self.signals = []
        self.risk_controls = []
        
        # 重置配置
        self.reset()
    
    def reset(self):
        """重置构建器状态。"""
        self.strategy_config = {
            'name': '',
            'description': '',
            'category': 'custom',
            'created_at': datetime.now(),
            'version': '1.0'
        }
        self.factors = []
        self.signals = []
        self.risk_controls = []
    
    def add_factor(self, factor_config: Dict[str, Any]) -> bool:
        """
        添加因子到策略。
        
        Args:
            factor_config: 因子配置字典
                - name: 因子名称
                - type: 因子类型 (technical, fundamental, sentiment)
                - weight: 因子权重
                - parameters: 因子参数
                
        Returns:
            是否添加成功
        """
        try:
            # 验证必要字段
            required_fields = ['name', 'type', 'weight']
            for field in required_fields:
                if field not in factor_config:
                    logger.error(f"因子配置缺少必要字段: {field}")
                    return False
            
            # 验证因子类型
            valid_types = ['technical', 'fundamental', 'sentiment']
            if factor_config['type'] not in valid_types:
                logger.error(f"无效的因子类型: {factor_config['type']}")
                return False
            
            # 验证权重
            weight = factor_config['weight']
            if not isinstance(weight, (int, float)) or weight <= 0:
                logger.error(f"无效的因子权重: {weight}")
                return False
            
            # 添加默认参数
            if 'parameters' not in factor_config:
                factor_config['parameters'] = {}
            
            # 添加到因子列表
            self.factors.append(factor_config.copy())
            logger.info(f"成功添加因子: {factor_config['name']}")
            
            return True
            
        except Exception as e:
            logger.error(f"添加因子失败: {e}")
            return False
    
    def add_signal_rule(self, signal_config: Dict[str, Any]) -> bool:
        """
        添加信号规则到策略。
        
        Args:
            signal_config: 信号配置字典
                - name: 信号名称
                - condition: 信号条件表达式
                - action: 信号动作 (buy, sell, hold)
                - weight: 信号权重
                
        Returns:
            是否添加成功
        """
        try:
            # 验证必要字段
            required_fields = ['name', 'condition', 'action']
            for field in required_fields:
                if field not in signal_config:
                    logger.error(f"信号配置缺少必要字段: {field}")
                    return False
            
            # 验证信号动作
            valid_actions = ['buy', 'sell', 'hold']
            if signal_config['action'] not in valid_actions:
                logger.error(f"无效的信号动作: {signal_config['action']}")
                return False
            
            # 添加默认权重
            if 'weight' not in signal_config:
                signal_config['weight'] = 1.0
            
            # 验证权重
            weight = signal_config['weight']
            if not isinstance(weight, (int, float)) or weight <= 0:
                logger.error(f"无效的信号权重: {weight}")
                return False
            
            # 添加到信号列表
            self.signals.append(signal_config.copy())
            logger.info(f"成功添加信号规则: {signal_config['name']}")
            
            return True
            
        except Exception as e:
            logger.error(f"添加信号规则失败: {e}")
            return False
    
    def add_risk_control(self, risk_config: Dict[str, Any]) -> bool:
        """
        添加风险控制到策略。
        
        Args:
            risk_config: 风险控制配置字典
                - name: 风险控制名称
                - type: 风险控制类型 (position, stop_loss, volatility)
                - parameters: 风险控制参数
                
        Returns:
            是否添加成功
        """
        try:
            # 验证必要字段
            required_fields = ['name', 'type']
            for field in required_fields:
                if field not in risk_config:
                    logger.error(f"风险控制配置缺少必要字段: {field}")
                    return False
            
            # 验证风险控制类型
            valid_types = ['position', 'stop_loss', 'volatility', 'drawdown']
            if risk_config['type'] not in valid_types:
                logger.error(f"无效的风险控制类型: {risk_config['type']}")
                return False
            
            # 添加到风险控制列表
            self.risk_controls.append(risk_config.copy())
            logger.info(f"成功添加风险控制: {risk_config['name']}")
            
            return True
            
        except Exception as e:
            logger.error(f"添加风险控制失败: {e}")
            return False
    
    def build_strategy(self, name: str, description: str = '') -> Dict[str, Any]:
        """
        构建策略。
        
        Args:
            name: 策略名称
            description: 策略描述
            
        Returns:
            策略配置字典
        """
        try:
            # 更新策略配置
            self.strategy_config['name'] = name
            self.strategy_config['description'] = description or f"策略: {name}"
            self.strategy_config['created_at'] = datetime.now()
            
            # 构建完整的策略配置
            strategy = {
                **self.strategy_config,
                'factors': self.factors.copy(),
                'signals': self.signals.copy(),
                'risk_controls': self.risk_controls.copy()
            }
            
            logger.info(f"成功构建策略: {name}")
            return strategy
            
        except Exception as e:
            logger.error(f"构建策略失败: {e}")
            return {}
    
    def validate_strategy(self, strategy: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """
        验证策略配置。
        
        Args:
            strategy: 策略配置
            
        Returns:
            (是否有效, 错误列表)
        """
        errors = []
        
        try:
            # 验证基本信息
            if not strategy.get('name'):
                errors.append("策略名称不能为空")
            
            # 验证因子
            factors = strategy.get('factors', [])
            if not factors:
                errors.append("策略必须包含至少一个因子")
            else:
                # 验证因子权重总和
                total_weight = sum(factor.get('weight', 0) for factor in factors)
                if abs(total_weight - 1.0) > 0.01:
                    errors.append(f"因子权重总和应为1.0，当前为{total_weight:.3f}")
                
                # 验证因子名称唯一性
                factor_names = [factor.get('name') for factor in factors]
                if len(factor_names) != len(set(factor_names)):
                    errors.append("因子名称必须唯一")
            
            # 验证信号规则
            signals = strategy.get('signals', [])
            if not signals:
                errors.append("策略必须包含至少一个信号规则")
            else:
                # 验证信号条件中引用的因子
                factor_names = {factor.get('name') for factor in factors}
                for signal in signals:
                    condition = signal.get('condition', '')
                    referenced_factors = self._extract_factor_names(condition)
                    
                    for ref_factor in referenced_factors:
                        if ref_factor not in factor_names and ref_factor != 'composite_score':
                            errors.append(f"信号条件引用了不存在的因子: {ref_factor}")
            
            # 验证风险控制
            risk_controls = strategy.get('risk_controls', [])
            for risk_control in risk_controls:
                risk_type = risk_control.get('type')
                if risk_type == 'position':
                    max_position = risk_control.get('max_position', 0)
                    if max_position > 1.0:
                        errors.append(f"最大仓位不能超过100%: {max_position}")
                elif risk_type == 'stop_loss':
                    stop_loss = risk_control.get('stop_loss', 0)
                    if stop_loss <= 0 or stop_loss >= 1.0:
                        errors.append(f"止损比例应在0-1之间: {stop_loss}")
            
            is_valid = len(errors) == 0
            
            if is_valid:
                logger.info("策略验证通过")
            else:
                logger.warning(f"策略验证失败，发现{len(errors)}个错误")
            
            return is_valid, errors
            
        except Exception as e:
            logger.error(f"策略验证过程中发生错误: {e}")
            errors.append(f"验证过程中发生错误: {e}")
            return False, errors
    
    def _extract_factor_names(self, condition: str) -> List[str]:
        """
        从条件表达式中提取因子名称。
        
        Args:
            condition: 条件表达式
            
        Returns:
            因子名称列表
        """
        try:
            # 使用正则表达式提取可能的因子名称
            # 匹配字母开头，包含字母、数字、下划线的标识符
            pattern = r'\b[a-zA-Z][a-zA-Z0-9_]*\b'
            matches = re.findall(pattern, condition)
            
            # 过滤掉常见的操作符和关键字
            keywords = {'and', 'or', 'not', 'in', 'is', 'if', 'else', 'True', 'False', 'None'}
            factor_names = [match for match in matches if match not in keywords]
            
            return factor_names
            
        except Exception as e:
            logger.error(f"提取因子名称失败: {e}")
            return []
    
    def get_strategy_summary(self, strategy: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取策略摘要信息。
        
        Args:
            strategy: 策略配置
            
        Returns:
            策略摘要
        """
        try:
            factors = strategy.get('factors', [])
            signals = strategy.get('signals', [])
            risk_controls = strategy.get('risk_controls', [])
            
            summary = {
                'name': strategy.get('name', ''),
                'description': strategy.get('description', ''),
                'factor_count': len(factors),
                'signal_count': len(signals),
                'risk_control_count': len(risk_controls),
                'factor_types': list(set(factor.get('type') for factor in factors)),
                'signal_actions': list(set(signal.get('action') for signal in signals)),
                'created_at': strategy.get('created_at'),
                'version': strategy.get('version', '1.0')
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取策略摘要失败: {e}")
            return {}
    
    def export_strategy(self, strategy: Dict[str, Any], format: str = 'json') -> str:
        """
        导出策略配置。
        
        Args:
            strategy: 策略配置
            format: 导出格式 ('json', 'yaml')
            
        Returns:
            导出的字符串
        """
        try:
            if format == 'json':
                import json
                return json.dumps(strategy, indent=2, default=str, ensure_ascii=False)
            elif format == 'yaml':
                import yaml
                return yaml.dump(strategy, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的导出格式: {format}")
                
        except Exception as e:
            logger.error(f"导出策略失败: {e}")
            return ""
    
    def import_strategy(self, strategy_str: str, format: str = 'json') -> Dict[str, Any]:
        """
        导入策略配置。
        
        Args:
            strategy_str: 策略配置字符串
            format: 导入格式 ('json', 'yaml')
            
        Returns:
            策略配置字典
        """
        try:
            if format == 'json':
                import json
                strategy = json.loads(strategy_str)
            elif format == 'yaml':
                import yaml
                strategy = yaml.safe_load(strategy_str)
            else:
                raise ValueError(f"不支持的导入格式: {format}")
            
            # 验证导入的策略
            is_valid, errors = self.validate_strategy(strategy)
            if not is_valid:
                logger.warning(f"导入的策略验证失败: {errors}")
            
            return strategy
            
        except Exception as e:
            logger.error(f"导入策略失败: {e}")
            return {}
