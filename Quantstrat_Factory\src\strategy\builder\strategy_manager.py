"""
策略管理器模块。

提供策略的保存、加载、管理功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import json
from pathlib import Path
import shutil

logger = logging.getLogger(__name__)


class StrategyManager:
    """策略管理器。"""
    
    def __init__(self, strategy_dir: Optional[str] = None):
        """
        初始化策略管理器。
        
        Args:
            strategy_dir: 策略存储目录
        """
        self.strategy_dir = Path(strategy_dir) if strategy_dir else Path("strategies")
        self.strategy_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存中的策略缓存
        self.strategies = {}
        
        # 加载已有策略
        self._load_strategies()
    
    def _load_strategies(self):
        """加载所有策略。"""
        try:
            for strategy_file in self.strategy_dir.glob("*.json"):
                try:
                    with open(strategy_file, 'r', encoding='utf-8') as f:
                        strategy = json.load(f)
                    
                    strategy_name = strategy.get('name', strategy_file.stem)
                    self.strategies[strategy_name] = strategy
                    
                except Exception as e:
                    logger.warning(f"加载策略失败 {strategy_file}: {e}")
        
        except Exception as e:
            logger.error(f"扫描策略目录失败: {e}")
        
        logger.info(f"加载了 {len(self.strategies)} 个策略")
    
    def save_strategy(self, strategy: Dict[str, Any]) -> bool:
        """
        保存策略。
        
        Args:
            strategy: 策略配置
            
        Returns:
            是否保存成功
        """
        try:
            strategy_name = strategy.get('name')
            if not strategy_name:
                logger.error("策略名称不能为空")
                return False
            
            # 添加保存时间
            strategy['saved_at'] = datetime.now().isoformat()
            
            # 如果是更新现有策略，增加版本号
            if strategy_name in self.strategies:
                old_version = self.strategies[strategy_name].get('version', '1.0')
                try:
                    version_parts = old_version.split('.')
                    major, minor = int(version_parts[0]), int(version_parts[1])
                    new_version = f"{major}.{minor + 1}"
                    strategy['version'] = new_version
                except:
                    strategy['version'] = '1.1'
            else:
                strategy['version'] = strategy.get('version', '1.0')
            
            # 保存到文件
            strategy_file = self.strategy_dir / f"{strategy_name}.json"
            with open(strategy_file, 'w', encoding='utf-8') as f:
                json.dump(strategy, f, indent=2, ensure_ascii=False, default=str)
            
            # 更新内存缓存
            self.strategies[strategy_name] = strategy.copy()
            
            logger.info(f"成功保存策略: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"保存策略失败: {e}")
            return False
    
    def load_strategy(self, strategy_name: str) -> Optional[Dict[str, Any]]:
        """
        加载策略。
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            策略配置，如果不存在则返回None
        """
        try:
            if strategy_name in self.strategies:
                return self.strategies[strategy_name].copy()
            
            # 尝试从文件加载
            strategy_file = self.strategy_dir / f"{strategy_name}.json"
            if strategy_file.exists():
                with open(strategy_file, 'r', encoding='utf-8') as f:
                    strategy = json.load(f)
                
                # 更新缓存
                self.strategies[strategy_name] = strategy
                return strategy.copy()
            
            logger.warning(f"策略不存在: {strategy_name}")
            return None
            
        except Exception as e:
            logger.error(f"加载策略失败: {e}")
            return None
    
    def list_strategies(self) -> List[Dict[str, Any]]:
        """
        列出所有策略。
        
        Returns:
            策略信息列表
        """
        strategy_list = []
        
        for strategy_name, strategy in self.strategies.items():
            strategy_info = {
                'name': strategy_name,
                'description': strategy.get('description', ''),
                'category': strategy.get('category', 'custom'),
                'factor_count': len(strategy.get('factors', [])),
                'signal_count': len(strategy.get('signals', [])),
                'created_at': strategy.get('created_at'),
                'saved_at': strategy.get('saved_at'),
                'version': strategy.get('version', '1.0')
            }
            strategy_list.append(strategy_info)
        
        # 按保存时间排序
        strategy_list.sort(key=lambda x: x.get('saved_at', ''), reverse=True)
        
        return strategy_list
    
    def delete_strategy(self, strategy_name: str) -> bool:
        """
        删除策略。
        
        Args:
            strategy_name: 策略名称
            
        Returns:
            是否删除成功
        """
        try:
            # 检查策略是否存在
            if strategy_name not in self.strategies:
                logger.warning(f"策略不存在: {strategy_name}")
                return False
            
            # 删除文件
            strategy_file = self.strategy_dir / f"{strategy_name}.json"
            if strategy_file.exists():
                strategy_file.unlink()
            
            # 从缓存中删除
            del self.strategies[strategy_name]
            
            logger.info(f"成功删除策略: {strategy_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除策略失败: {e}")
            return False
    
    def update_strategy(self, strategy: Dict[str, Any]) -> bool:
        """
        更新策略。
        
        Args:
            strategy: 更新后的策略配置
            
        Returns:
            是否更新成功
        """
        strategy_name = strategy.get('name')
        if not strategy_name:
            logger.error("策略名称不能为空")
            return False
        
        if strategy_name not in self.strategies:
            logger.error(f"策略不存在: {strategy_name}")
            return False
        
        # 保留原始创建时间
        original_strategy = self.strategies[strategy_name]
        strategy['created_at'] = original_strategy.get('created_at')
        
        # 更新修改时间
        strategy['updated_at'] = datetime.now().isoformat()
        
        return self.save_strategy(strategy)
    
    def clone_strategy(self, source_name: str, target_name: str) -> Optional[Dict[str, Any]]:
        """
        克隆策略。
        
        Args:
            source_name: 源策略名称
            target_name: 目标策略名称
            
        Returns:
            克隆的策略配置，如果失败则返回None
        """
        try:
            # 加载源策略
            source_strategy = self.load_strategy(source_name)
            if not source_strategy:
                logger.error(f"源策略不存在: {source_name}")
                return None
            
            # 检查目标名称是否已存在
            if target_name in self.strategies:
                logger.error(f"目标策略名称已存在: {target_name}")
                return None
            
            # 创建克隆策略
            cloned_strategy = source_strategy.copy()
            cloned_strategy['name'] = target_name
            cloned_strategy['description'] = f"{source_strategy.get('description', '')} (副本)"
            cloned_strategy['created_at'] = datetime.now().isoformat()
            cloned_strategy['version'] = '1.0'
            
            # 移除保存时间和更新时间
            cloned_strategy.pop('saved_at', None)
            cloned_strategy.pop('updated_at', None)
            
            # 保存克隆的策略
            if self.save_strategy(cloned_strategy):
                logger.info(f"成功克隆策略: {source_name} -> {target_name}")
                return cloned_strategy
            else:
                return None
                
        except Exception as e:
            logger.error(f"克隆策略失败: {e}")
            return None
    
    def export_strategy(self, strategy_name: str, export_path: str) -> bool:
        """
        导出策略到指定路径。
        
        Args:
            strategy_name: 策略名称
            export_path: 导出路径
            
        Returns:
            是否导出成功
        """
        try:
            strategy = self.load_strategy(strategy_name)
            if not strategy:
                logger.error(f"策略不存在: {strategy_name}")
                return False
            
            export_file = Path(export_path)
            export_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(strategy, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"成功导出策略到: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出策略失败: {e}")
            return False
    
    def import_strategy(self, import_path: str, strategy_name: Optional[str] = None) -> bool:
        """
        从指定路径导入策略。
        
        Args:
            import_path: 导入路径
            strategy_name: 新策略名称（可选）
            
        Returns:
            是否导入成功
        """
        try:
            import_file = Path(import_path)
            if not import_file.exists():
                logger.error(f"导入文件不存在: {import_path}")
                return False
            
            with open(import_file, 'r', encoding='utf-8') as f:
                strategy = json.load(f)
            
            # 如果指定了新名称，则使用新名称
            if strategy_name:
                strategy['name'] = strategy_name
            
            # 检查策略名称是否已存在
            current_name = strategy.get('name')
            if current_name in self.strategies:
                logger.error(f"策略名称已存在: {current_name}")
                return False
            
            # 更新导入时间
            strategy['imported_at'] = datetime.now().isoformat()
            
            # 保存策略
            if self.save_strategy(strategy):
                logger.info(f"成功导入策略: {current_name}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"导入策略失败: {e}")
            return False
    
    def backup_strategies(self, backup_path: str) -> bool:
        """
        备份所有策略。
        
        Args:
            backup_path: 备份路径
            
        Returns:
            是否备份成功
        """
        try:
            backup_dir = Path(backup_path)
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建时间戳目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_subdir = backup_dir / f"strategies_backup_{timestamp}"
            backup_subdir.mkdir()
            
            # 复制所有策略文件
            for strategy_file in self.strategy_dir.glob("*.json"):
                shutil.copy2(strategy_file, backup_subdir)
            
            logger.info(f"成功备份策略到: {backup_subdir}")
            return True
            
        except Exception as e:
            logger.error(f"备份策略失败: {e}")
            return False
    
    def search_strategies(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索策略。
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的策略列表
        """
        all_strategies = self.list_strategies()
        keyword_lower = keyword.lower()
        
        matching_strategies = []
        for strategy in all_strategies:
            # 在名称和描述中搜索
            if (keyword_lower in strategy.get('name', '').lower() or
                keyword_lower in strategy.get('description', '').lower()):
                matching_strategies.append(strategy)
        
        return matching_strategies
    
    def get_strategies_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        按类别获取策略。
        
        Args:
            category: 策略类别
            
        Returns:
            策略列表
        """
        all_strategies = self.list_strategies()
        return [strategy for strategy in all_strategies if strategy.get('category') == category]
