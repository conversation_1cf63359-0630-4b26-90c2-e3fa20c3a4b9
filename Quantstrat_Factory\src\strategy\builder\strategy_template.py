"""
策略模板模块。

提供预定义的策略模板和自定义模板功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class StrategyTemplate:
    """策略模板管理器。"""
    
    def __init__(self, template_dir: Optional[str] = None):
        """
        初始化策略模板管理器。
        
        Args:
            template_dir: 模板存储目录
        """
        self.template_dir = Path(template_dir) if template_dir else Path("templates/strategies")
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        # 内置模板
        self.builtin_templates = self._load_builtin_templates()
        
        # 自定义模板
        self.custom_templates = self._load_custom_templates()
    
    def _load_builtin_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载内置模板。"""
        templates = {}
        
        # 动量策略模板
        templates['momentum_strategy'] = {
            'name': 'momentum_strategy',
            'display_name': '动量策略',
            'description': '基于价格动量的策略，买入上涨趋势强劲的股票',
            'category': 'trend_following',
            'factors': [
                {
                    'name': 'momentum_20',
                    'type': 'technical',
                    'weight': 0.6,
                    'parameters': {'period': 20}
                },
                {
                    'name': 'momentum_60',
                    'type': 'technical',
                    'weight': 0.4,
                    'parameters': {'period': 60}
                }
            ],
            'signals': [
                {
                    'name': 'long_signal',
                    'condition': 'composite_score > 0.5',
                    'action': 'buy',
                    'weight': 1.0
                },
                {
                    'name': 'short_signal',
                    'condition': 'composite_score < -0.5',
                    'action': 'sell',
                    'weight': 1.0
                }
            ],
            'risk_controls': [
                {
                    'name': 'position_limit',
                    'type': 'position',
                    'max_position': 0.1,
                    'max_total_position': 1.0
                },
                {
                    'name': 'stop_loss',
                    'type': 'stop_loss',
                    'stop_loss': 0.05
                }
            ]
        }
        
        # 均值回归策略模板
        templates['mean_reversion_strategy'] = {
            'name': 'mean_reversion_strategy',
            'display_name': '均值回归策略',
            'description': '基于价格均值回归的策略，买入超跌股票',
            'category': 'mean_reversion',
            'factors': [
                {
                    'name': 'reversal_5',
                    'type': 'technical',
                    'weight': 0.5,
                    'parameters': {'period': 5}
                },
                {
                    'name': 'rsi_14',
                    'type': 'technical',
                    'weight': 0.3,
                    'parameters': {'period': 14}
                },
                {
                    'name': 'bollinger_position_20',
                    'type': 'technical',
                    'weight': 0.2,
                    'parameters': {'period': 20, 'std_dev': 2.0}
                }
            ],
            'signals': [
                {
                    'name': 'oversold_signal',
                    'condition': 'rsi_14 < 30 and bollinger_position_20 < 0.2',
                    'action': 'buy',
                    'weight': 1.0
                },
                {
                    'name': 'overbought_signal',
                    'condition': 'rsi_14 > 70 and bollinger_position_20 > 0.8',
                    'action': 'sell',
                    'weight': 1.0
                }
            ],
            'risk_controls': [
                {
                    'name': 'position_limit',
                    'type': 'position',
                    'max_position': 0.08
                }
            ]
        }
        
        # 多因子策略模板
        templates['multi_factor_strategy'] = {
            'name': 'multi_factor_strategy',
            'display_name': '多因子策略',
            'description': '综合多个因子的策略，平衡不同类型的因子',
            'category': 'multi_factor',
            'factors': [
                {
                    'name': 'momentum_20',
                    'type': 'technical',
                    'weight': 0.3,
                    'parameters': {'period': 20}
                },
                {
                    'name': 'volatility_20',
                    'type': 'technical',
                    'weight': 0.2,
                    'parameters': {'period': 20}
                },
                {
                    'name': 'volume_factor_20',
                    'type': 'technical',
                    'weight': 0.2,
                    'parameters': {'period': 20}
                },
                {
                    'name': 'money_flow_20',
                    'type': 'sentiment',
                    'weight': 0.3,
                    'parameters': {'period': 20}
                }
            ],
            'signals': [
                {
                    'name': 'composite_long',
                    'condition': 'composite_score > 0.6',
                    'action': 'buy',
                    'weight': 1.0
                },
                {
                    'name': 'composite_short',
                    'condition': 'composite_score < -0.6',
                    'action': 'sell',
                    'weight': 1.0
                }
            ],
            'risk_controls': [
                {
                    'name': 'position_limit',
                    'type': 'position',
                    'max_position': 0.05
                },
                {
                    'name': 'volatility_control',
                    'type': 'volatility',
                    'max_volatility': 0.02
                }
            ]
        }
        
        # 价值策略模板
        templates['value_strategy'] = {
            'name': 'value_strategy',
            'display_name': '价值策略',
            'description': '基于基本面价值的策略，买入低估值股票',
            'category': 'value',
            'factors': [
                {
                    'name': 'pe_ratio',
                    'type': 'fundamental',
                    'weight': 0.4,
                    'parameters': {}
                },
                {
                    'name': 'pb_ratio',
                    'type': 'fundamental',
                    'weight': 0.3,
                    'parameters': {}
                },
                {
                    'name': 'roe',
                    'type': 'fundamental',
                    'weight': 0.3,
                    'parameters': {}
                }
            ],
            'signals': [
                {
                    'name': 'value_buy',
                    'condition': 'pe_ratio < 15 and pb_ratio < 2 and roe > 0.1',
                    'action': 'buy',
                    'weight': 1.0
                }
            ],
            'risk_controls': [
                {
                    'name': 'position_limit',
                    'type': 'position',
                    'max_position': 0.1
                }
            ]
        }
        
        return templates
    
    def _load_custom_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载自定义模板。"""
        custom_templates = {}
        
        try:
            # 扫描模板目录
            for template_file in self.template_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template = json.load(f)
                    
                    template_name = template.get('name', template_file.stem)
                    custom_templates[template_name] = template
                    
                except Exception as e:
                    logger.warning(f"加载自定义模板失败 {template_file}: {e}")
        
        except Exception as e:
            logger.error(f"扫描模板目录失败: {e}")
        
        return custom_templates
    
    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有可用的模板列表。
        
        Returns:
            模板信息列表
        """
        templates = []
        
        # 添加内置模板
        for template_name, template in self.builtin_templates.items():
            template_info = {
                'name': template_name,
                'display_name': template.get('display_name', template_name),
                'description': template.get('description', ''),
                'category': template.get('category', 'other'),
                'type': 'builtin'
            }
            templates.append(template_info)
        
        # 添加自定义模板
        for template_name, template in self.custom_templates.items():
            template_info = {
                'name': template_name,
                'display_name': template.get('display_name', template_name),
                'description': template.get('description', ''),
                'category': template.get('category', 'custom'),
                'type': 'custom'
            }
            templates.append(template_info)
        
        return templates
    
    def get_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取指定的模板。
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板配置字典，如果不存在则返回None
        """
        # 先查找内置模板
        if template_name in self.builtin_templates:
            return self.builtin_templates[template_name].copy()
        
        # 再查找自定义模板
        if template_name in self.custom_templates:
            return self.custom_templates[template_name].copy()
        
        logger.warning(f"模板不存在: {template_name}")
        return None
    
    def create_template(self, template_config: Dict[str, Any]) -> bool:
        """
        创建自定义模板。
        
        Args:
            template_config: 模板配置
            
        Returns:
            是否创建成功
        """
        try:
            template_name = template_config.get('name')
            if not template_name:
                logger.error("模板名称不能为空")
                return False
            
            # 验证模板配置
            required_fields = ['name', 'description', 'factors', 'signals']
            for field in required_fields:
                if field not in template_config:
                    logger.error(f"模板配置缺少必要字段: {field}")
                    return False
            
            # 添加创建时间
            template_config['created_at'] = datetime.now().isoformat()
            template_config['type'] = 'custom'
            
            # 保存到文件
            template_file = self.template_dir / f"{template_name}.json"
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template_config, f, indent=2, ensure_ascii=False, default=str)
            
            # 添加到自定义模板字典
            self.custom_templates[template_name] = template_config.copy()
            
            logger.info(f"成功创建自定义模板: {template_name}")
            return True
            
        except Exception as e:
            logger.error(f"创建模板失败: {e}")
            return False
    
    def delete_template(self, template_name: str) -> bool:
        """
        删除自定义模板。
        
        Args:
            template_name: 模板名称
            
        Returns:
            是否删除成功
        """
        try:
            # 不能删除内置模板
            if template_name in self.builtin_templates:
                logger.error("不能删除内置模板")
                return False
            
            # 检查模板是否存在
            if template_name not in self.custom_templates:
                logger.warning(f"模板不存在: {template_name}")
                return False
            
            # 删除文件
            template_file = self.template_dir / f"{template_name}.json"
            if template_file.exists():
                template_file.unlink()
            
            # 从字典中删除
            del self.custom_templates[template_name]
            
            logger.info(f"成功删除模板: {template_name}")
            return True
            
        except Exception as e:
            logger.error(f"删除模板失败: {e}")
            return False
    
    def apply_template(self, builder, template: Dict[str, Any]) -> bool:
        """
        将模板应用到策略构建器。
        
        Args:
            builder: 策略构建器实例
            template: 模板配置
            
        Returns:
            是否应用成功
        """
        try:
            # 重置构建器
            builder.reset()
            
            # 应用因子
            factors = template.get('factors', [])
            for factor in factors:
                if not builder.add_factor(factor):
                    logger.warning(f"应用因子失败: {factor.get('name')}")
            
            # 应用信号规则
            signals = template.get('signals', [])
            for signal in signals:
                if not builder.add_signal_rule(signal):
                    logger.warning(f"应用信号规则失败: {signal.get('name')}")
            
            # 应用风险控制
            risk_controls = template.get('risk_controls', [])
            for risk_control in risk_controls:
                if not builder.add_risk_control(risk_control):
                    logger.warning(f"应用风险控制失败: {risk_control.get('name')}")
            
            # 更新策略配置
            builder.strategy_config.update({
                'description': template.get('description', ''),
                'category': template.get('category', 'custom')
            })
            
            logger.info(f"成功应用模板: {template.get('name')}")
            return True
            
        except Exception as e:
            logger.error(f"应用模板失败: {e}")
            return False
    
    def get_templates_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        按类别获取模板。
        
        Args:
            category: 模板类别
            
        Returns:
            模板列表
        """
        all_templates = self.get_available_templates()
        return [template for template in all_templates if template.get('category') == category]
    
    def search_templates(self, keyword: str) -> List[Dict[str, Any]]:
        """
        搜索模板。
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的模板列表
        """
        all_templates = self.get_available_templates()
        keyword_lower = keyword.lower()
        
        matching_templates = []
        for template in all_templates:
            # 在名称和描述中搜索
            if (keyword_lower in template.get('name', '').lower() or
                keyword_lower in template.get('display_name', '').lower() or
                keyword_lower in template.get('description', '').lower()):
                matching_templates.append(template)
        
        return matching_templates
