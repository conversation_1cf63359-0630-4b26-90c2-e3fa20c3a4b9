# 模块 E: 回测器 (Backtester)

## 核心职责

本模块提供一个灵活且高效的回测框架，用于评估量化策略在历史数据上的表现。它支持多种运行模式，包括单次回测、参数优化和模拟盘信号生成。

## 功能

1.  **多模式支持**:
    *   **回测 (Backtest)**: 对单个策略在指定历史周期内进行性能评估。
    *   **参数调优 (Sweep)**: 通过遍历不同的参数组合，寻找最优策略参数。
    *   **模拟盘信号 (Signal)**: 根据实时或模拟数据生成交易信号，用于模拟交易或进一步分析。
2.  **性能评估**: 提供详细的回测报告，包括收益率、最大回撤、夏普比率等关键指标。
3.  **数据集成**: 与特征存储模块无缝集成，加载所需的历史数据和特征。
4.  **策略管理**: 支持加载和运行不同的策略定义。

## 如何运行

使用以下命令运行回测器：

```bash
python run.py --mode <运行模式>
```

**参数说明：**

*   `--mode`: **必需**。指定回测器的运行模式。
    *   可选值:
        *   `backtest`: 执行单次回测。
        *   `sweep`: 执行参数调优（参数范围需在相应配置文件中定义）。
        *   `signal`: 生成模拟盘交易信号。

**示例：**

执行单次回测：
```bash
python run.py --mode backtest
```

执行参数调优：
```bash
python run.py --mode sweep
```

生成模拟盘交易信号：
```bash
python run.py --mode signal
```

## 配置

回测器的具体行为（如回测时间范围、策略选择、参数调优范围等）通常通过 `configs/` 目录下的配置文件进行管理。请根据您的需求修改相应的配置文件。
