# main_controller.py
# 统一策略运行控制器：支持回测模式与实盘模式切换

import argparse
from run import run_strategy_backtest  # 你需要在 run.py 中定义这个函数作为接口
from realtime_signal_runner import RealTimeRunner


def main():
    parser = argparse.ArgumentParser(description="策略运行控制器")
    parser.add_argument("--mode", type=str, choices=["backtest", "live"], default="backtest", help="运行模式")
    args = parser.parse_args()

    if args.mode == "backtest":
        print("🚀 启动回测模式...")
        run_strategy_backtest()

    elif args.mode == "live":
        print("⚡ 启动实盘信号调度模式...")
        param = {
            "initial_capital": 1_000_000,
            "entry_momentum": 0.05,
            "entry_break_ratio": 1.01,
            "entry_mfratio": 0.5,
            "min_up_streak": 2,
            "max_position_num": 4
        }
        runner = RealTimeRunner(symbol_list=["000001.SZ", "000002.SZ"], strategy_param=param, interval=5)
        runner.loop()


if __name__ == "__main__":
    main()
