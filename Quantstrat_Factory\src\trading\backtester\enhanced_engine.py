"""
增强回测引擎

提供高精度回测、风险管理和性能优化功能
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from enum import Enum
import warnings

logger = logging.getLogger(__name__)
warnings.filterwarnings('ignore')


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


@dataclass
class Order:
    """订单类"""
    symbol: str
    quantity: float
    order_type: OrderType
    price: Optional[float] = None
    stop_price: Optional[float] = None
    timestamp: Optional[pd.Timestamp] = None
    status: OrderStatus = OrderStatus.PENDING
    order_id: Optional[str] = None
    commission: float = 0.0
    slippage: float = 0.0


@dataclass
class Position:
    """持仓类"""
    symbol: str
    quantity: float
    avg_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    entry_time: pd.Timestamp
    last_update: pd.Timestamp


@dataclass
class RiskMetrics:
    """风险指标类"""
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    calmar_ratio: float
    var_95: float
    cvar_95: float
    beta: float
    alpha: float
    tracking_error: float


class EnhancedBacktestEngine:
    """增强回测引擎"""
    
    def __init__(self, initial_capital: float = 1000000, commission_rate: float = 0.001):
        """
        初始化增强回测引擎
        
        Args:
            initial_capital: 初始资金
            commission_rate: 佣金费率
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        
        # 账户状态
        self.cash = initial_capital
        self.positions: Dict[str, Position] = {}
        self.orders: List[Order] = []
        self.trades: List[Dict] = []
        
        # 历史记录
        self.portfolio_history: List[Dict] = []
        self.performance_metrics: Dict[str, Any] = {}
        
        # 风险管理
        self.risk_manager = RiskManager()
        
        # 市场数据
        self.market_data: Dict[str, pd.DataFrame] = {}
        self.current_time: Optional[pd.Timestamp] = None
        
        logger.info(f"增强回测引擎初始化完成，初始资金: {initial_capital:,.2f}")
    
    def add_market_data(self, symbol: str, data: pd.DataFrame):
        """
        添加市场数据
        
        Args:
            symbol: 股票代码
            data: 市场数据
        """
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            raise ValueError(f"缺少必要列: {missing_columns}")
        
        # 确保数据按时间排序
        if 'datetime' in data.columns:
            data = data.sort_values('datetime')
            data.set_index('datetime', inplace=True)
        
        self.market_data[symbol] = data
        logger.info(f"添加市场数据: {symbol}, 数据量: {len(data)}")
    
    def submit_order(self, order: Order) -> str:
        """
        提交订单
        
        Args:
            order: 订单对象
            
        Returns:
            订单ID
        """
        # 生成订单ID
        order.order_id = f"order_{len(self.orders) + 1}_{order.symbol}"
        order.timestamp = self.current_time
        
        # 风险检查
        if not self.risk_manager.check_order_risk(order, self.positions, self.cash):
            order.status = OrderStatus.REJECTED
            logger.warning(f"订单被风险管理拒绝: {order.order_id}")
            return order.order_id
        
        # 添加到订单列表
        self.orders.append(order)
        logger.debug(f"订单提交成功: {order.order_id}")
        
        return order.order_id
    
    def process_orders(self):
        """处理待执行订单"""
        for order in self.orders:
            if order.status == OrderStatus.PENDING:
                self._execute_order(order)
    
    def _execute_order(self, order: Order):
        """
        执行订单
        
        Args:
            order: 待执行订单
        """
        if order.symbol not in self.market_data:
            order.status = OrderStatus.REJECTED
            logger.warning(f"无市场数据，订单拒绝: {order.order_id}")
            return
        
        current_data = self._get_current_market_data(order.symbol)
        if current_data is None:
            return
        
        # 根据订单类型确定执行价格
        execution_price = self._get_execution_price(order, current_data)
        if execution_price is None:
            return
        
        # 计算佣金和滑点
        commission = abs(order.quantity * execution_price * self.commission_rate)
        slippage_cost = abs(order.quantity * execution_price * order.slippage)
        total_cost = commission + slippage_cost
        
        # 检查资金充足性
        if order.quantity > 0:  # 买入
            required_cash = order.quantity * execution_price + total_cost
            if required_cash > self.cash:
                order.status = OrderStatus.REJECTED
                logger.warning(f"资金不足，订单拒绝: {order.order_id}")
                return
        
        # 执行交易
        self._execute_trade(order, execution_price, commission, slippage_cost)
        order.status = OrderStatus.FILLED
        
        logger.debug(f"订单执行成功: {order.order_id}, 价格: {execution_price:.4f}")
    
    def _get_execution_price(self, order: Order, market_data: pd.Series) -> Optional[float]:
        """
        获取执行价格
        
        Args:
            order: 订单
            market_data: 当前市场数据
            
        Returns:
            执行价格
        """
        if order.order_type == OrderType.MARKET:
            # 市价单使用开盘价
            return market_data['open']
        
        elif order.order_type == OrderType.LIMIT:
            # 限价单检查是否能成交
            if order.quantity > 0:  # 买入限价单
                if market_data['low'] <= order.price:
                    return min(order.price, market_data['open'])
            else:  # 卖出限价单
                if market_data['high'] >= order.price:
                    return max(order.price, market_data['open'])
            return None
        
        elif order.order_type == OrderType.STOP:
            # 止损单检查是否触发
            if order.quantity > 0:  # 买入止损单
                if market_data['high'] >= order.stop_price:
                    return market_data['open']
            else:  # 卖出止损单
                if market_data['low'] <= order.stop_price:
                    return market_data['open']
            return None
        
        elif order.order_type == OrderType.STOP_LIMIT:
            # 止损限价单
            if order.quantity > 0:  # 买入
                if market_data['high'] >= order.stop_price and market_data['low'] <= order.price:
                    return min(order.price, market_data['open'])
            else:  # 卖出
                if market_data['low'] <= order.stop_price and market_data['high'] >= order.price:
                    return max(order.price, market_data['open'])
            return None
        
        return None
    
    def _execute_trade(self, order: Order, price: float, commission: float, slippage: float):
        """
        执行交易
        
        Args:
            order: 订单
            price: 执行价格
            commission: 佣金
            slippage: 滑点成本
        """
        symbol = order.symbol
        quantity = order.quantity
        
        # 更新现金
        cash_change = -quantity * price - commission - slippage
        self.cash += cash_change
        
        # 更新持仓
        if symbol in self.positions:
            position = self.positions[symbol]
            
            if (position.quantity > 0 and quantity > 0) or (position.quantity < 0 and quantity < 0):
                # 同向加仓
                total_quantity = position.quantity + quantity
                total_cost = position.quantity * position.avg_price + quantity * price
                new_avg_price = total_cost / total_quantity if total_quantity != 0 else 0
                
                position.quantity = total_quantity
                position.avg_price = new_avg_price
                
            else:
                # 反向交易或平仓
                if abs(quantity) >= abs(position.quantity):
                    # 完全平仓或反向开仓
                    realized_pnl = -position.quantity * (price - position.avg_price)
                    position.realized_pnl += realized_pnl
                    
                    remaining_quantity = quantity + position.quantity
                    if remaining_quantity != 0:
                        position.quantity = remaining_quantity
                        position.avg_price = price
                    else:
                        # 完全平仓
                        del self.positions[symbol]
                        position = None
                else:
                    # 部分平仓
                    realized_pnl = -quantity * (price - position.avg_price)
                    position.realized_pnl += realized_pnl
                    position.quantity += quantity
        else:
            # 新开仓
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=quantity,
                avg_price=price,
                market_value=quantity * price,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                entry_time=self.current_time,
                last_update=self.current_time
            )
        
        # 记录交易
        trade_record = {
            'timestamp': self.current_time,
            'symbol': symbol,
            'quantity': quantity,
            'price': price,
            'commission': commission,
            'slippage': slippage,
            'cash_change': cash_change,
            'order_id': order.order_id
        }
        self.trades.append(trade_record)
    
    def update_portfolio(self):
        """更新投资组合状态"""
        total_market_value = self.cash
        total_unrealized_pnl = 0.0
        total_realized_pnl = sum(trade['cash_change'] for trade in self.trades)
        
        # 更新持仓市值和未实现盈亏
        for symbol, position in self.positions.items():
            current_data = self._get_current_market_data(symbol)
            if current_data is not None:
                current_price = current_data['close']
                position.market_value = position.quantity * current_price
                position.unrealized_pnl = position.quantity * (current_price - position.avg_price)
                position.last_update = self.current_time
                
                total_market_value += position.market_value
                total_unrealized_pnl += position.unrealized_pnl
        
        # 记录投资组合历史
        portfolio_record = {
            'timestamp': self.current_time,
            'cash': self.cash,
            'total_market_value': total_market_value,
            'total_unrealized_pnl': total_unrealized_pnl,
            'total_realized_pnl': total_realized_pnl,
            'total_pnl': total_unrealized_pnl + total_realized_pnl,
            'total_value': self.initial_capital + total_unrealized_pnl + total_realized_pnl,
            'positions_count': len(self.positions)
        }
        self.portfolio_history.append(portfolio_record)
    
    def _get_current_market_data(self, symbol: str) -> Optional[pd.Series]:
        """
        获取当前市场数据
        
        Args:
            symbol: 股票代码
            
        Returns:
            当前市场数据
        """
        if symbol not in self.market_data or self.current_time is None:
            return None
        
        data = self.market_data[symbol]
        
        # 查找最接近当前时间的数据
        if self.current_time in data.index:
            return data.loc[self.current_time]
        else:
            # 查找最近的历史数据
            available_times = data.index[data.index <= self.current_time]
            if len(available_times) > 0:
                return data.loc[available_times[-1]]
        
        return None
    
    def run_backtest(self, start_date: str, end_date: str, strategy_func: callable):
        """
        运行回测
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            strategy_func: 策略函数
        """
        logger.info(f"开始回测: {start_date} 至 {end_date}")
        
        # 获取所有交易日期
        all_dates = set()
        for data in self.market_data.values():
            all_dates.update(data.index)
        
        trading_dates = sorted([
            date for date in all_dates 
            if pd.Timestamp(start_date) <= date <= pd.Timestamp(end_date)
        ])
        
        # 逐日回测
        for date in trading_dates:
            self.current_time = date
            
            # 处理待执行订单
            self.process_orders()
            
            # 执行策略
            try:
                strategy_func(self, date)
            except Exception as e:
                logger.error(f"策略执行失败 {date}: {e}")
            
            # 更新投资组合
            self.update_portfolio()
        
        # 计算性能指标
        self._calculate_performance_metrics()
        
        logger.info("回测完成")
    
    def _calculate_performance_metrics(self):
        """计算性能指标"""
        if not self.portfolio_history:
            return
        
        df = pd.DataFrame(self.portfolio_history)
        df.set_index('timestamp', inplace=True)
        
        # 计算收益率
        df['returns'] = df['total_value'].pct_change()
        
        # 基本指标
        total_return = (df['total_value'].iloc[-1] / self.initial_capital) - 1
        annualized_return = (1 + total_return) ** (252 / len(df)) - 1
        
        # 风险指标
        returns = df['returns'].dropna()
        volatility = returns.std() * np.sqrt(252)
        
        # 夏普比率
        sharpe_ratio = annualized_return / volatility if volatility != 0 else 0
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns > 0).mean()
        
        self.performance_metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(self.trades),
            'final_value': df['total_value'].iloc[-1]
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return self.performance_metrics.copy()
    
    def get_portfolio_history(self) -> pd.DataFrame:
        """获取投资组合历史"""
        return pd.DataFrame(self.portfolio_history)
    
    def get_trades_history(self) -> pd.DataFrame:
        """获取交易历史"""
        return pd.DataFrame(self.trades)


class RiskManager:
    """风险管理器"""
    
    def __init__(self):
        """初始化风险管理器"""
        self.max_position_size = 0.1  # 单个持仓最大占比
        self.max_total_exposure = 1.0  # 最大总敞口
        self.max_daily_loss = 0.05  # 最大日损失
    
    def check_order_risk(
        self, 
        order: Order, 
        positions: Dict[str, Position], 
        cash: float
    ) -> bool:
        """
        检查订单风险
        
        Args:
            order: 订单
            positions: 当前持仓
            cash: 可用现金
            
        Returns:
            是否通过风险检查
        """
        # 检查资金充足性
        if order.quantity > 0 and order.price:
            required_cash = order.quantity * order.price
            if required_cash > cash:
                return False
        
        # 检查持仓集中度
        total_value = cash + sum(pos.market_value for pos in positions.values())
        if order.price:
            position_value = abs(order.quantity * order.price)
            if position_value / total_value > self.max_position_size:
                return False
        
        return True
