# strategy/data/data_loader.py

import pandas as pd
import os

DATA_PATH = "data"  # 默认本地数据目录


def load_ohlcv(symbol: str, start_date=None, end_date=None) -> pd.DataFrame:
    """
    加载本地 CSV 格式的 OHLCV K线数据（支持可选起止日期）
    文件命名规则：data/000001.SZ.csv
    返回：包含 ['date','open','high','low','close','volume'] 的 DataFrame
    """
    filename = os.path.join(DATA_PATH, f"{symbol}.csv")
    if not os.path.exists(filename):
        raise FileNotFoundError(f"❌ 未找到数据文件: {filename}")

    df = pd.read_csv(filename, parse_dates=['date'])
    df = df.sort_values("date").reset_index(drop=True)

    if start_date:
        df = df[df["date"] >= pd.to_datetime(start_date)]
    if end_date:
        df = df[df["date"] <= pd.to_datetime(end_date)]

    return df


def list_available_symbols() -> list:
    """
    列出当前数据目录下所有可用股票代码
    """
    if not os.path.exists(DATA_PATH):
        return []
    return [f.replace(".csv", "") for f in os.listdir(DATA_PATH) if f.endswith(".csv")]
