# strategy/data/data_utils.py
# 数据清洗工具模块：处理缺失值、涨跌停、复权等标准化问题

import pandas as pd


def fill_missing_ohlcv(df: pd.DataFrame) -> pd.DataFrame:
    """
    补全 OHLCV 缺失字段并用 0/前值填充
    """
    required_cols = ['open', 'high', 'low', 'close', 'volume']
    for col in required_cols:
        if col not in df.columns:
            df[col] = 0
    return df.fillna(method='ffill').fillna(0)


def detect_limit_up(row, limit_ratio=0.097) -> bool:
    """
    判断是否涨停（用于信号过滤）
    默认按收盘与开盘比较
    """
    return row['close'] >= row['open'] * (1 + limit_ratio)


def detect_limit_down(row, limit_ratio=0.097) -> bool:
    """
    判断是否跌停（用于信号过滤）
    """
    return row['close'] <= row['open'] * (1 - limit_ratio)


def apply_back_adjusted_factor(df: pd.DataFrame, factor_col='adj_factor') -> pd.DataFrame:
    """
    应用后复权因子，调整 open/high/low/close
    """
    if factor_col not in df.columns:
        return df  # 无复权因子，不处理

    for price_col in ['open', 'high', 'low', 'close']:
        df[price_col] = df[price_col] * df[factor_col]
    return df

# ✅ 预留字段说明（不影响当前使用）
# param["frequency"] = "1d" 或 "1min"，可用于切换数据粒度
# param["strategy_name"] = "trend" 或 "mean_revert"，用于多策略标识
