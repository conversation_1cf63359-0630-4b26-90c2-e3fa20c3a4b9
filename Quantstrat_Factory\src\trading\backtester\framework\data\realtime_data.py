# strategy/data/realtime_data.py
# 实时数据源接口抽象层（可接入 TuShare / 聚宽 / 掘金等）

from abc import ABC, abstractmethod
from typing import Dict
import datetime
import tushare as ts

class RealtimeDataSource(ABC):
    """
    抽象基类：定义所有实时行情数据接口的标准格式
    """
    @abstractmethod
    def get_current_price(self, symbol: str) -> float:
        pass

    @abstractmethod
    def get_batch_price(self, symbols: list) -> Dict[str, float]:
        pass

    @abstractmethod
    def get_timestamp(self) -> datetime.datetime:
        pass


class DummyDataSource(RealtimeDataSource):
    def get_current_price(self, symbol: str) -> float:
        return 10.0

    def get_batch_price(self, symbols: list) -> Dict[str, float]:
        return {s: 10.0 for s in symbols}

    def get_timestamp(self) -> datetime.datetime:
        return datetime.datetime.now()


class TushareDataSource(RealtimeDataSource):
    def __init__(self, token: str):
        ts.set_token(token)
        self.pro = ts.pro_api()

    def get_current_price(self, symbol: str) -> float:
        df = self.pro.stocks_basic(ts_code=symbol)
        return float(df['close'].values[0]) if not df.empty else 0.0

    def get_batch_price(self, symbols: list) -> Dict[str, float]:
        result = {}
        for symbol in symbols:
            try:
                df = self.pro.stocks_basic(ts_code=symbol)
                price = float(df['close'].values[0]) if not df.empty else 0.0
            except:
                price = 0.0
            result[symbol] = price
        return result

    def get_timestamp(self) -> datetime.datetime:
        return datetime.datetime.now()
