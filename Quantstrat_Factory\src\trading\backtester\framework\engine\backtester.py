# backtester.py
# 回测引擎模块：加载数据、执行事件驱动框架、生成交易信号、输出绩效指标

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../")))

from collections import deque, defaultdict
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import time
from pathlib import Path

from tqdm import tqdm

from framework.utils.logger import get_logger
from framework.events.__init__ import MarketEvent, FillEvent
from strategy.performance import PerformanceAnalyzer
from strategy.portfolio import Portfolio
from strategy.signals.trend_signal import SignalEvent # SignalEvent 仍然需要
# from strategy.signals.trend_signal import TrendSignalGenerator # 不再直接导入
from strategy.signals.trend_signal import compute_streaks
from strategy.signals.trend_signal import compute_streaks_np # 新增导入
from strategy.signal_utils import ParameterConfig 
from strategy.features.factor_pipeline import compute_features_batch
from strategy.base_strategy import BaseStrategy # 新增导入



logger = get_logger("backtest", to_file=False)

class Backtester:
    def __init__(self, data_dir, symbols, param: ParameterConfig, 
                 strategy_instance: BaseStrategy = None, # 修改：接收策略实例
                 force_refresh_cache=False,
                 signal_matrix_df: pd.DataFrame = None):
        self.data_dir = data_dir
        self.symbols = symbols
        self.param = param # param 仍然用于 Portfolio 和可能的其他配置
        # self.strategy_name = strategy_name # 移除 strategy_name
        self.strategy = strategy_instance # 存储策略实例
        self.force_refresh_cache = force_refresh_cache
        
        self.signal_matrix_df = signal_matrix_df # 存储外部信号矩阵
        self.external_signals_mode = self.signal_matrix_df is not None
        if self.external_signals_mode:
            logger.info("回测引擎以【外部信号模式】运行。")
            # 确保外部信号矩阵的索引是 datetime 类型
            if not isinstance(self.signal_matrix_df.index, pd.DatetimeIndex):
                try:
                    self.signal_matrix_df.index = pd.to_datetime(self.signal_matrix_df.index)
                except Exception as e:
                    logger.error(f"转换外部信号矩阵索引为 DatetimeIndex 失败: {e}")
                    raise ValueError("外部信号矩阵的索引必须是或可转换为 DatetimeIndex。")

        self.portfolio = Portfolio(param)
        self.event_queue = deque()
        self.daily_selected_count = defaultdict(int)
        self.all_data = self._load_all_data() # 必须在 portfolio 初始化之后，因为它可能需要 param.start_date
        logger.info(f"[调试] 实际传入 force_refresh_cache = {force_refresh_cache}")

        # 初始化策略组件 (仅在非外部信号模式且策略实例已提供时需要)
        if not self.external_signals_mode and self.strategy is None:
            # 如果不是外部信号模式，且没有传入策略实例，这是个问题
            # 之前的逻辑是硬编码 TrendSignalGenerator
            # 现在，如果 strategy_instance 为 None 且不是外部信号模式，应该报错或使用默认策略
            logger.error("内部信号模式下必须提供一个策略实例 (strategy_instance)。")
            raise ValueError("内部信号模式下必须提供一个策略实例。")
        
        if self.external_signals_mode:
             logger.info("外部信号模式：将忽略内部策略实例（如果提供）。")
             self.strategy = None # 外部信号模式下，不使用内部策略逻辑生成信号
        
        # self.param_obj = param # param 已经赋值给 self.param
        # self.generator = TrendSignalGenerator() # 移除，由 self.strategy 替代

    # ============================================================
    # 数据加载：增加 look‑back 缓冲区，确保特征可计算 (外部信号模式下可减少)
    # ============================================================
    def _load_all_data(self):
        raw_list = []
        # 如果是外部信号模式，self.symbols 可能需要从 signal_matrix_df 的列名获取
        # 但为了兼容性和价格查找，仍然从文件加载这些 symbols 的数据
        symbols_to_load = self.symbols
        if self.external_signals_mode and self.signal_matrix_df is not None:
            # 确保只加载信号矩阵中存在的股票数据，避免加载不必要的数据
            symbols_in_signal_matrix = [col + ".parquet" for col in self.signal_matrix_df.columns]
            symbols_to_load = [s for s in self.symbols if s in symbols_in_signal_matrix]
            if not symbols_to_load:
                 logger.warning("外部信号模式下，信号矩阵中的股票在提供的 symbols 列表中均未找到对应数据文件。")
                 # Fallback or raise error? For now, try to load based on original self.symbols if any.
                 symbols_to_load = self.symbols


        for file in symbols_to_load:
            path = os.path.join(self.data_dir, file)
            if not os.path.exists(path):
                logger.warning(f"数据文件不存在: {path}，跳过加载。")
                continue
            df = pd.read_parquet(path)
            df['symbol'] = file.replace(".parquet", "") # 保持 symbol 列为 XXXXXX.SZ 格式
            df['date'] = pd.to_datetime(df['datetime']) # 确保 'date' 列存在且为 datetime 类型
            raw_list.append(df)

        if not raw_list:
            logger.error("未能加载任何股票数据。")
            return pd.DataFrame()

        df_all = pd.concat(raw_list).sort_values('date').reset_index(drop=True)

        # 根据模式调整 lookback_days
        if self.external_signals_mode:
            lookback_days = 1 # 外部信号模式下，只需要当天价格数据用于执行和估值
            logger.info("外部信号模式：数据加载的回溯期设置为1天。")
        else:
            lookback_days = getattr(self.param, "lookback_days", 60)
        
        # 确保 self.param.start_date 和 self.param.end_date 是 datetime 对象
        # 这通常在 ParameterConfig 中处理，但这里再次确认
        try:
            param_start_date = pd.to_datetime(self.param.start_date)
            param_end_date = pd.to_datetime(self.param.end_date)
        except AttributeError:
            logger.error("param 对象中缺少 start_date 或 end_date。")
            raise # 或者返回空DataFrame

        start = param_start_date - pd.Timedelta(days=lookback_days)
        end = param_end_date

        logger.info(f"[数据加载] 实际加载区间: {start.date()} ~ {end.date()}（含回溯 {lookback_days} 天）")

        df_all = df_all[(df_all['date'] >= start) & (df_all['date'] <= end)].reset_index(drop=True)

        return df_all

    # ============================================================
    # 主回测循环
    # ============================================================
    def run_backtest(self):
        if not self.external_signals_mode:
            self._precompute_all_features() # 仅在内部信号模式下预计算特征
        else:
            # 外部信号模式下，确保 all_data 包含信号矩阵中所有股票的价格数据
            # 如果 self.all_data 为空，则无法进行回测
            if self.all_data.empty:
                logger.error("外部信号模式：未能加载任何价格数据，无法执行回测。")
                return
            logger.info("外部信号模式：跳过内部特征预计算。")
            # 外部信号模式也需要 history 和 history_ptr 来获取最新价格
            unique_symbols_from_data = self.all_data['symbol'].unique().tolist()
            self.history = {s: np.full((1000, 5), np.nan, dtype=np.float32) for s in unique_symbols_from_data}
            self.history_ptr = {s: 0 for s in unique_symbols_from_data}


        self._current_day_index = -1
        self._perf_log = []
        self.progress_bar = tqdm(total=len(self.all_data['date'].unique()), desc="回测进度", ncols=80)
        self._start_time = time.time()

        grouped = self.all_data.groupby('date')
        for date, daily_data in grouped:
            # ✅ 注入上一交易日延迟信号（如次日买入/卖出）
            for sig in self.portfolio.daily_signals:
                self.event_queue.append(sig)
            self.portfolio.daily_signals.clear()
            
            t0 = time.time()
            ...
        unique_symbols = self.all_data['symbol'].unique().tolist()
        self.history = {s: np.full((1000, 5), np.nan, dtype=np.float32) for s in unique_symbols}
        self.history_ptr = {s: 0 for s in unique_symbols}

        for date, daily_data in grouped:
            t0 = time.time()
            self._process_existing_positions(date)
            t1 = time.time()
            self._execute_pending_signals(date, daily_data)
            t2 = time.time()
            self._generate_market_event(date, daily_data)
            self._process_event_queue()
            t3 = time.time()

            logger.info(f"[profiling] {date.date()} 持仓更新耗时: {t1 - t0:.2f}s | 信号: {t2 - t1:.2f}s | 事件: {t3 - t2:.2f}s")
            self._perf_log.append((date, t3 - t0))
            self._current_day_index += 1
            self.progress_bar.update(1)

        self.progress_bar.close()
        logger.info(f"✅ 回测完成，总耗时 {time.time() - self._start_time:.2f}s")
        self._show_results()
        self.portfolio.export_trade_records()  # ✅ 保存交易明细


    # ============================================================
    # 持仓估值
    # ============================================================
    def _process_existing_positions(self, date):
        daily = self.all_data[self.all_data['date'] == date]
        self.portfolio.update_market(date, daily)

          # ✅ 新增：执行 entry 信号，建仓处理
        self.portfolio.process_signals(date, daily)

    # ============================================================
    # 信号执行逻辑（保持不变）
    # ============================================================
    def _execute_pending_signals(self, date, daily_data):
        daily_vals = daily_data[['symbol', 'open', 'high', 'low', 'close', 'volume']].values
        for row in daily_vals:
            sym_raw, o, h, l, c, v = row
            sym_id = sym_raw # sym_raw 已经是 XXXXXX.SZ 格式
            ptr = self.history_ptr.get(sym_id) # 使用 .get 以防万一
            if ptr is not None and ptr < self.history[sym_id].shape[0]:
                self.history[sym_id][ptr] = [o, h, l, c, v]
                self.history_ptr[sym_id] += 1
            elif ptr is None:
                logger.warning(f"在 history_ptr 中未找到股票 {sym_id}，可能未正确加载数据。")


        if self.external_signals_mode:
            # --- 外部信号模式 ---
            current_date_normalized = pd.to_datetime(date).normalize()
            if current_date_normalized in self.signal_matrix_df.index:
                daily_signals_from_matrix = self.signal_matrix_df.loc[current_date_normalized]
                for symbol_code, signal_value in daily_signals_from_matrix.items():
                    # symbol_code 应该是 XXXXXX.SZ 或 XXXXXX.SH 格式
                    if signal_value == 1: # 买入信号
                        event = SignalEvent(symbol=symbol_code, date=current_date_normalized, signal_type='LONG', metadata={'price_type': 'close'})
                        self.event_queue.append(event)
                    elif signal_value == -1: # 卖出信号
                        event = SignalEvent(symbol=symbol_code, date=current_date_normalized, signal_type='SHORT', metadata={'price_type': 'close'})
                        self.event_queue.append(event)
                    # signal_value == 0 表示无操作或平仓，平仓逻辑由 Portfolio 管理
            else:
                logger.debug(f"日期 {current_date_normalized} 在外部信号矩阵中没有对应的信号。")

        else:
            # --- 内部信号模式 (原有逻辑) ---
            if not hasattr(self, 'feature_cache') or not self.feature_cache:
                 logger.warning(f"日期 {date}: 内部信号模式下 feature_cache 为空，无法生成信号。")
                 return

            symbol_list, row_map = [], {}
            for sym_raw in daily_data['symbol'].unique(): # sym_raw 是 XXXXXX.SZ 格式
                if sym_raw not in self.feature_cache: # feature_cache 的 key 也是 XXXXXX.SZ
                    continue
                
                idx = self.history_ptr.get(sym_raw, 0) -1 # 使用 .get

                row_date = pd.to_datetime(daily_data[daily_data['symbol'] == sym_raw]['date'].values[0]).normalize()

                if row_date not in self.feature_cache[sym_raw]['date_index_map']:
                    logger.debug(f"[跳过信号] {sym_raw} {row_date} 不在特征日期索引中")
                    continue

                # 这里的 lookback 检查 (idx >= 30) 可能需要与 self.param.lookback_days 关联
                # 或者，TrendSignalGenerator 内部会处理历史数据不足的情况
                required_lookback = getattr(self.param, "signal_generation_lookback", 30) # 假设信号生成需要30天
                if idx >= required_lookback -1 : # 索引从0开始
                    symbol_df = daily_data[daily_data['symbol'] == sym_raw]
                    # row_date 已经获取并检查过
                    row_map[sym_raw] = symbol_df.iloc[0]
                    symbol_list.append(sym_raw)

            if not symbol_list:
                return

            index_map = {
                sym: min(self.history_ptr[sym] - 1, len(self.feature_cache[sym]["ma5"]) - 1)
                for sym in symbol_list
            }
            
            # 确保 strategy 实例存在 (在非外部信号模式下)
            if self.strategy is None:
                logger.error("内部信号模式下，策略实例 (self.strategy) 未初始化。")
                return

            # 调用策略实例的 generate_signals 方法
            # 注意：TrendFollowingStrategy.generate_signals 接受的参数与旧的 generate_signals_batch_vectorized 类似
            # 但 BaseStrategy 接口是 (daily_data, feature_cache)
            # 为了兼容 TrendFollowingStrategy，我们暂时传递它需要的额外参数
            # 理想情况下，这些额外参数应该被封装或通过其他方式传递给策略
            signals_df = self.strategy.generate_signals(
                daily_data=daily_data, # 传递整个当日数据，策略内部自行按需处理
                feature_cache=self.feature_cache,
                portfolio_positions=self.portfolio.positions, # 传递仓位信息
                current_day_index=self._current_day_index,   # 传递日期索引
                history_ptr=self.history_ptr                 # 传递历史数据指针
            )

            # 将返回的 DataFrame 转换为 SignalEvent 对象
            if not signals_df.empty:
                for _, row_sig in signals_df.iterrows():
                    signal_type_str = ""
                    if row_sig['signal'] == 1:
                        signal_type_str = 'LONG'
                    elif row_sig['signal'] == -1:
                        signal_type_str = 'EXIT' # 或者 SHORT，取决于策略定义
                    
                    if signal_type_str:
                        event = SignalEvent(
                            symbol=row_sig['symbol'],
                            date=pd.to_datetime(date).normalize(), # 使用当前回测日期
                            signal_type=signal_type_str,
                            metadata=row_sig.get('metadata', {})
                        )
                        self.event_queue.append(event)
            # for s in signals: # 旧的逻辑
            #     self.event_queue.append(s)

    # ============================================================
    # 市场事件占位
    # ============================================================
    def _generate_market_event(self, date, daily_data):
        pass

    # ============================================================
    # 事件处理（保持不变）
    # ============================================================
    # backtester.py
# 回测核心引擎模块（含成交逻辑：支持 price_type、涨停跌停处理、force_fill）

    def _process_event_queue(self):
        max_daily_trades = getattr(self.param, 'max_daily_trades', 20)
        max_positions = getattr(self.param, 'max_position_num', 4)
        ratio = getattr(self.param, 'position_ratio', 0.25)
        slippage = getattr(self.param, 'slippage', 0.0)
        fee_rate = getattr(self.param, 'fee_rate', 0.0)

        opened_today = 0
        next_day_orders = []

        while self.event_queue:
            event = self.event_queue.popleft()
            # logger.info(f"[信号处理] symbol={event.symbol}, signal={event.signal_type}, metadata={event.metadata}")

            if not isinstance(event, SignalEvent):
                continue

            sym = event.symbol
            typ = event.signal_type
            direction = 'BUY' if typ == 'LONG' else 'SELL'
            price_type = event.metadata.get("price_type", "close") if hasattr(event, 'metadata') else "close"

            if price_type == 'next_open':
                next_day_orders.append(event)
                continue

            price = self._get_latest_price(sym)
            if np.isnan(price):
                logger.info(f"[跳过成交] {sym} | 无可用价格数据")
                continue

            price *= (1 + slippage) if direction == 'BUY' else (1 - slippage)
            cash = self.portfolio.cash
            quantity = int((cash * ratio) // price) if direction == 'BUY' else self.portfolio.positions.get(sym, {}).get("quantity", 0)

            if direction == 'BUY':
                if quantity == 0:
                    logger.debug(f"[跳过成交] {sym} | 可买股数为 0 | cash={cash:.2f}, price={price:.2f}, ratio={ratio}")
                    continue
                if sym in self.portfolio.positions:
                    logger.debug(f"[跳过成交] {sym} | 已持仓，跳过重复买入")
                    continue
                if len(self.portfolio.positions) >= max_positions:
                    logger.debug(f"[跳过成交] {sym} | 已达最大持仓数 {max_positions}")
                    continue
                if opened_today >= max_daily_trades:
                    logger.debug(f"[跳过成交] {sym} | 当日成交数已满 {max_daily_trades}")
                    continue
                cost = price * quantity * (1 + fee_rate)
                if cost > cash:
                    logger.debug(f"[跳过成交] {sym} | 资金不足 | 需要成本={cost:.2f} > 现金={cash:.2f}")
                    continue
                opened_today += 1

            if direction == 'SELL':
                if sym not in self.portfolio.positions:
                    logger.debug(f"[跳过成交] {sym} | 无持仓无法卖出")
                    continue

            fill = FillEvent(symbol=sym, price=price, quantity=quantity, direction=direction, date=event.date)
            if hasattr(event, 'metadata'):
                if event.metadata.get('limit_up') or event.metadata.get('limit_down'):
                    fill.force_fill = True

            logger.info(f"[调试] 执行成交 fill: {sym} | {direction}, qty={quantity}, price={price:.2f}")
            self.portfolio.process_fill(fill)

        for evt in next_day_orders:
            self.portfolio.defer_signal(evt)



    # ============================================================
    # 工具函数
    # ============================================================
    def _get_latest_price(self, symbol_id): # symbol_id 应该是 XXXXXX.SZ 格式
        # 确保 symbol_id 在 history_ptr 和 history 中存在
        if symbol_id not in self.history_ptr or symbol_id not in self.history:
            logger.warning(f"尝试获取最新价格失败：股票 {symbol_id} 不在历史记录中。")
            return np.nan
        i = self.history_ptr[symbol_id]
        return self.history[symbol_id][i-1][3] if i > 0 else np.nan

    def _get_fixed_quantity(self, symbol):
        return 100

  # ============================================================
    # 特征预计算函数：一次性计算所有股票的所有特征并缓存
    # ============================================================
    def _precompute_all_features(self):
        if self.external_signals_mode: # 如果是外部信号模式，则不进行预计算
            logger.info("外部信号模式：跳过特征预计算。")
            return

        import pickle
        # 修正 cache_dir 的路径，使其在 Quantstrat_Factory/06_backtester/cache
        current_module_dir = Path(__file__).resolve().parent # .../framework/engine
        backtester_module_root = current_module_dir.parents[1] # .../06_backtester
        cache_dir = backtester_module_root / "cache"
        # root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
        # cache_dir = os.path.join(root_dir, "strategies", "cache") # 旧路径
        os.makedirs(cache_dir, exist_ok=True)
        cache_path = os.path.join(cache_dir, "features_cache.pkl")

        logger.info(f"[缓存] 使用缓存路径: {cache_path}")

        if os.path.exists(cache_path) and not self.force_refresh_cache:
            with open(cache_path, "rb") as f:
                cache = pickle.load(f)
                self.feature_cache = cache["feature_cache"]
                logger.info(f"[预处理] 读取缓存特征，共 {len(self.feature_cache)} 支股票")
                return

        logger.info("[预处理] 开始批量计算特征...")

        tensor, valid_symbols = self._build_feature_tensor()
        ma5, ma10, vol_ma5, momentum, recent_high, mfratio, turnover_rate, close_prices = compute_features_batch(tensor)

        # 新增：为每个股票的每个日期计算 up_streak 和 down_streak
        all_up_streaks_history = []
        all_down_streaks_history = []
        N = tensor.shape[0] # 股票数量
        L = tensor.shape[1] # 历史长度

        for i in range(N): # 遍历每支股票
            single_stock_close_history = close_prices[i] # 这是一个 (L,) 数组，L 是历史长度
            
            up_streaks_for_stock = np.zeros(L, dtype=np.int32)
            down_streaks_for_stock = np.zeros(L, dtype=np.int32)

            for j in range(1, L): # 从第二个元素开始，因为需要 diff
                # 每次计算到当前日期 j 的 up_streak 和 down_streak
                up_s, down_s = compute_streaks_np(single_stock_close_history[:j+1])
                up_streaks_for_stock[j] = up_s
                down_streaks_for_stock[j] = down_s
            
            all_up_streaks_history.append(up_streaks_for_stock)
            all_down_streaks_history.append(down_streaks_for_stock)

        features_dict = {
            "ma5": ma5,
            "ma10": ma10,
            "vol_ma5": vol_ma5,
            "momentum": momentum,
            "recent_high": recent_high,
            "mfratio": mfratio,
            "turnover_rate": turnover_rate,
            "close": close_prices,
            "up_streak_history": np.array(all_up_streaks_history), # 存储整个历史序列
            "down_streak_history": np.array(all_down_streaks_history) # 存储整个历史序列
        }

        any_key = next(iter(features_dict))
        if not isinstance(features_dict[any_key], (np.ndarray, list)):
            raise TypeError(f"features_dict[{any_key}] 应为数组，但得到 {type(features_dict[any_key])}")

        n = len(features_dict[any_key])
        logger.info(f"[预处理] 有效股票数 = {n} 条")

        self.feature_cache = {}
        progress = tqdm(total=n, desc="构建特征缓存", ncols=80)

        for i, symbol in enumerate(valid_symbols):
            symbol_id = symbol.replace(".parquet", "")

            try:
                # ✅ 校验所有字段都是数组（不是 float）
                for k in features_dict:
                    if not isinstance(features_dict[k][i], (np.ndarray, list)):
                        raise ValueError(f"字段 {k} 是无效结构: {type(features_dict[k][i])}")

                self.feature_cache[symbol_id] = {
                    "ma5": ma5[i],
                    "ma10": ma10[i],
                    "vol_ma5": vol_ma5[i],
                    "momentum": momentum[i],
                    "recent_high": recent_high[i],
                    "mfratio": mfratio[i],
                    "turnover_rate": turnover_rate[i],
                    "close": close_prices[i],
                    "up_streak": all_up_streaks_history[i], # 存储整个历史序列
                    "down_streak": all_down_streaks_history[i], # 存储整个历史序列
                    "date_index_map": self.date_index_map_dict[symbol_id]
                }
            except Exception as e:
                logger.warning(f"[跳过构建] {symbol_id} 特征异常跳过: {e}")
                continue

            progress.update(1)

        progress.close()
        logger.info("[预处理] 特征缓存完成，正在保存至本地...")

        with open(cache_path, "wb") as f:
            pickle.dump({
                "feature_cache": self.feature_cache,
                "valid_symbols": valid_symbols
            }, f)

        logger.info(f"[缓存] 已保存特征缓存至 {cache_path}")

    def _build_feature_tensor(self):
        all_tensor = []
        valid_symbols = []
        self.date_index_map_dict = {}

        lookback_days = getattr(self.param, "lookback_days", 60)

        for symbol_file_name in self.symbols: # self.symbols 包含 .parquet 后缀
            # symbol_id = symbol.replace(".parquet", "") # 旧的 symbol_id
            # df = self.all_data[self.all_data["symbol"] == symbol_id] # 旧的 df 筛选
            # all_data 中的 'symbol' 列现在是 XXXXXX.SZ 格式
            df = self.all_data[self.all_data["symbol"] == symbol_file_name.replace(".parquet", "")]
            if df.empty:
                logger.warning(f"[跳过] {symbol_file_name} 在 all_data 中未找到或数据为空。")
                continue
            df = df.sort_values("date").reset_index(drop=True)

            ohlcv = df[["open", "high", "low", "close", "volume"]].values

            if len(ohlcv) < lookback_days:
                logger.warning(f"[跳过] {symbol_file_name} 数据不足 {lookback_days} 天")
                continue

            recent_df = df.reset_index(drop=True) # df 已经是单个股票的数据
            all_tensor.append(ohlcv)

            valid_symbols.append(symbol_file_name.replace(".parquet", "")) # 存储不带后缀的 symbol_id

            # ✅ 构建该 symbol 的 date → idx 映射（idx 对应 0~lookback_days-1）
            self.date_index_map_dict[symbol_file_name.replace(".parquet", "")] = {
                pd.to_datetime(row["date"]).normalize(): i for i, row in recent_df.iterrows()
            }

        if not all_tensor:
            raise ValueError("无可用数据构建特征张量")

        # 找出所有 symbol 的最小 K 线长度
        min_len = min(arr.shape[0] for arr in all_tensor)
        logger.info(f"[预处理] 所有 symbol 张量将裁剪至统一长度: {min_len} 天")

        # 裁剪为统一长度
        all_tensor = [arr[-min_len:] for arr in all_tensor]

        return np.stack(all_tensor), valid_symbols

    def _generate_date_index_map(self, symbol):
        df = self.all_data[self.all_data["symbol"] == symbol.replace(".parquet", "")]
        df = df.sort_values("date").reset_index(drop=True)

        lookback_days = getattr(self.param, "lookback_days", 60)
        df_recent = df[-lookback_days:].reset_index(drop=True)  


        return {pd.to_datetime(row["date"]).normalize(): i for i, row in df_recent.iterrows()}

    
    # 输出绩效指标
    def _show_results(self):
        benchmark_path = os.path.join(self.data_dir, "benchmark.csv")
        benchmark_df = None
        if os.path.exists(benchmark_path):
            benchmark_df = pd.read_csv(benchmark_path)
            benchmark_df['date'] = pd.to_datetime(benchmark_df['datetime'])
            benchmark_df = benchmark_df[['date', 'close']]
            benchmark_df = benchmark_df.sort_values('date').reset_index(drop=True)
            benchmark_df = benchmark_df[
                (benchmark_df['date'] >= self.param.start_date) & (benchmark_df['date'] <= self.param.end_date)
            ]
            benchmark_df['daily_return'] = benchmark_df['close'].pct_change().fillna(0)
            benchmark_df['cum_return'] = (1 + benchmark_df['daily_return']).cumprod()

        self._log_timing_breakdown()
        self._save_additional_analysis()
        self._save_trade_records()

        df = pd.DataFrame(self.portfolio.holdings)
        trades = pd.DataFrame(self.portfolio.trades, columns=['date', 'symbol', 'direction', 'price', 'quantity'])
        if df.empty:
            logger.warning("⚠️ 没有生成任何持仓记录，回测无效。")
            return

        analyzer = PerformanceAnalyzer(df, self.portfolio.trades)
        result = analyzer.calculate()

        logger.info("\n===== 回测绩效摘要 =====")
        initial_value = df['value'].iloc[0]
        final_value = df['value'].iloc[-1]
        max_drawdown_pct = result.get('最大回撤', 0)
        max_drawdown_amt = initial_value * max_drawdown_pct

        logger.info(f"初始金额: {initial_value:,.2f}")
        logger.info(f"最终金额: {final_value:,.2f}")

        # 指定顺序输出核心指标
        for key in ['累计收益率', '年化收益率', '最大回撤']:
            if key in result:
                val = result[key]
                if key == '最大回撤':
                    logger.info(f"最大回撤金额: {max_drawdown_amt:,.2f}")
                if isinstance(val, float):
                    logger.info(f"{key}: {val * 100:.2f}%")
                else:
                    logger.info(f"{key}: {val}")

        # 输出剩余所有绩效指标（不重复）
        for k, v in result.items():
            if k in ['累计收益率', '年化收益率', '最大回撤']:
                continue
            if isinstance(v, float):
                if k in ['波动率', '交易胜率', '平均盈亏比']:
                    logger.info(f"{k}: {v * 100:.2f}%")
                else:
                    logger.info(f"{k}: {v:.4f}")
            else:
                logger.info(f"{k}: {v}")

        # ✅ 替代原“换手率”输出
        logger.info(f"总成交笔数: {len(self.portfolio.trades)}")

        df['cum_return'] = df['value'] / df['value'].iloc[0]
        plt.figure(figsize=(10, 4))
        plt.plot(df['date'], df['cum_return'], label="策略净值")
        if benchmark_df is not None:
            plt.plot(benchmark_df['date'], benchmark_df['cum_return'], label="基准指数")
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False
        plt.title("策略累计收益曲线")
        plt.xlabel("日期")
        plt.ylabel("净值")
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        output_path = "output/nav_curve.png"
        os.makedirs("output", exist_ok=True)
        plt.savefig(output_path)
        plt.close()
        logger.info(f"已保存净值曲线图至 {output_path}")

        nav_df = df[['date', 'cum_return']].copy()
        if benchmark_df is not None:
            nav_df = nav_df.merge(benchmark_df[['date', 'cum_return']], on='date', how='left', suffixes=('', '_benchmark'))
        nav_csv_path = "output/nav_curve.csv"
        nav_df.to_csv(nav_csv_path, index=False, encoding="utf-8-sig")
        logger.info(f"已保存净值数据到 {nav_csv_path}")


    def _log_timing_breakdown(self):
        df = pd.DataFrame(self._perf_log, columns=['date', 'seconds'])
        if not df.empty:
            logger.info("===== 回测单日耗时统计（前10慢） =====")
            top = df.sort_values('seconds', ascending=False).head(10)
            for _, row in top.iterrows():
                logger.info(f"{row['date'].date()} 用时: {row['seconds']:.2f}s")

    def _save_additional_analysis(self):
        df = pd.DataFrame(self.portfolio.holdings)
        trades = pd.DataFrame(self.portfolio.trades, columns=['date', 'symbol', 'direction', 'price', 'quantity'])

        if not trades.empty:
            trades['date'] = pd.to_datetime(trades['date'])
            trades['symbol'] = trades['symbol'].astype(str)
            trades['abs_qty'] = trades['quantity'].abs()

            unique_holding_days = trades['date'].nunique()
            logger.info(f"平均持仓活跃天数（粗估）: {unique_holding_days} 天")

            trades['net'] = trades['direction'].map({'BUY': 1, 'SELL': -1}) * trades['quantity']
            trades['cum_qty'] = trades.groupby('symbol')['net'].cumsum()
            active_positions = trades[trades['cum_qty'] > 0].groupby('date').size()
            max_holding = active_positions.max() if not active_positions.empty else 0
            logger.info(f"最大同时持有股票数: {max_holding} 只")

            signal_counts = trades.groupby('date').size()
            if not signal_counts.empty:
                logger.info(f"平均每日交易次数: {signal_counts.mean():.2f} 次")
                logger.info(f"最大单日交易数: {signal_counts.max()} 次")
                buy_count = trades[trades['direction'] == 'BUY'].shape[0]
                sell_count = trades[trades['direction'] == 'SELL'].shape[0]
                logger.info(f"总成交笔数: {trades.shape[0]}，买入: {buy_count}，卖出: {sell_count}")


    def _save_trade_records(self):
        if not self.portfolio.trades:
            logger.info("无交易记录可导出。")
            return

        os.makedirs("output", exist_ok=True)
        trade_df = pd.DataFrame(
            self.portfolio.trades, columns=['date', 'symbol', 'direction', 'price', 'quantity']
        )
        trade_df['amount'] = trade_df['price'] * trade_df['quantity']

        # ✅ 只保存 detail 文件
        trade_df.to_csv("output/trades_detail.csv", index=False, encoding="utf-8-sig")
        logger.info("✅ 已保存交易明细到 output/trades_detail.csv")
