"""
增强版回测引擎。

在原有回测引擎基础上添加以下功能：
1. 更精确的滑点和交易成本模型
2. 风险管理和止损机制
3. 多策略组合回测
4. 实时性能监控
5. 更详细的分析报告
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import logging
from pathlib import Path
import time
import warnings


@dataclass
class TradingCosts:
    """交易成本配置。"""
    commission_rate: float = 0.0003  # 佣金费率
    stamp_tax_rate: float = 0.001    # 印花税（仅卖出）
    transfer_fee_rate: float = 0.00002  # 过户费
    min_commission: float = 5.0      # 最小佣金
    slippage_bps: float = 5.0        # 滑点（基点）


@dataclass
class RiskLimits:
    """风险限制配置。"""
    max_position_size: float = 0.1   # 单只股票最大仓位
    max_sector_exposure: float = 0.3  # 单个行业最大敞口
    max_drawdown: float = 0.15       # 最大回撤限制
    stop_loss_pct: float = 0.05      # 止损比例
    max_leverage: float = 1.0        # 最大杠杆


class EnhancedPortfolio:
    """增强版投资组合管理器。"""
    
    def __init__(self, 
                 initial_capital: float,
                 trading_costs: TradingCosts,
                 risk_limits: RiskLimits):
        """
        初始化投资组合。
        
        Args:
            initial_capital: 初始资金
            trading_costs: 交易成本配置
            risk_limits: 风险限制配置
        """
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trading_costs = trading_costs
        self.risk_limits = risk_limits
        
        # 持仓信息
        self.positions = {}  # {symbol: quantity}
        self.avg_costs = {}  # {symbol: avg_cost}
        
        # 交易记录
        self.trades = []
        self.daily_nav = []
        
        # 风险监控
        self.daily_drawdown = 0.0
        self.max_drawdown_reached = 0.0
        self.peak_nav = initial_capital
        
        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_commission = 0.0
        self.total_slippage = 0.0
        
        self.logger = logging.getLogger(__name__)
    
    def calculate_trading_cost(self, symbol: str, quantity: int, price: float, side: str) -> float:
        """
        计算交易成本。
        
        Args:
            symbol: 股票代码
            quantity: 交易数量
            price: 交易价格
            side: 交易方向 ('buy' 或 'sell')
            
        Returns:
            总交易成本
        """
        notional = abs(quantity) * price
        
        # 佣金
        commission = max(notional * self.trading_costs.commission_rate, 
                        self.trading_costs.min_commission)
        
        # 印花税（仅卖出）
        stamp_tax = notional * self.trading_costs.stamp_tax_rate if side == 'sell' else 0
        
        # 过户费
        transfer_fee = notional * self.trading_costs.transfer_fee_rate
        
        # 滑点
        slippage = notional * self.trading_costs.slippage_bps / 10000
        
        total_cost = commission + stamp_tax + transfer_fee + slippage
        
        # 记录成本
        self.total_commission += commission
        self.total_slippage += slippage
        
        return total_cost
    
    def check_risk_limits(self, symbol: str, quantity: int, price: float) -> bool:
        """
        检查风险限制。
        
        Args:
            symbol: 股票代码
            quantity: 交易数量
            price: 交易价格
            
        Returns:
            是否通过风险检查
        """
        # 计算交易后的仓位
        current_pos = self.positions.get(symbol, 0)
        new_pos = current_pos + quantity
        new_notional = abs(new_pos) * price
        
        # 检查单只股票仓位限制
        position_ratio = new_notional / self.current_capital
        if position_ratio > self.risk_limits.max_position_size:
            self.logger.warning(f"股票 {symbol} 仓位超限: {position_ratio:.2%} > {self.risk_limits.max_position_size:.2%}")
            return False
        
        # 检查最大回撤限制
        if self.daily_drawdown > self.risk_limits.max_drawdown:
            self.logger.warning(f"回撤超限: {self.daily_drawdown:.2%} > {self.risk_limits.max_drawdown:.2%}")
            return False
        
        return True
    
    def execute_trade(self, symbol: str, quantity: int, price: float, timestamp: pd.Timestamp) -> bool:
        """
        执行交易。
        
        Args:
            symbol: 股票代码
            quantity: 交易数量（正数买入，负数卖出）
            price: 交易价格
            timestamp: 交易时间
            
        Returns:
            是否成功执行
        """
        if quantity == 0:
            return True
        
        side = 'buy' if quantity > 0 else 'sell'
        
        # 风险检查
        if not self.check_risk_limits(symbol, quantity, price):
            return False
        
        # 计算交易成本
        trading_cost = self.calculate_trading_cost(symbol, quantity, price, side)
        
        # 检查资金充足性
        if side == 'buy':
            required_capital = quantity * price + trading_cost
            if required_capital > self.current_capital:
                self.logger.warning(f"资金不足: 需要 {required_capital:.2f}, 可用 {self.current_capital:.2f}")
                return False
        
        # 执行交易
        current_pos = self.positions.get(symbol, 0)
        new_pos = current_pos + quantity
        
        # 更新持仓
        if new_pos == 0:
            self.positions.pop(symbol, None)
            self.avg_costs.pop(symbol, None)
        else:
            self.positions[symbol] = new_pos
            
            # 更新平均成本
            if side == 'buy':
                if symbol in self.avg_costs:
                    total_cost = self.avg_costs[symbol] * current_pos + price * quantity
                    self.avg_costs[symbol] = total_cost / new_pos
                else:
                    self.avg_costs[symbol] = price
        
        # 更新资金
        self.current_capital -= quantity * price + trading_cost
        
        # 记录交易
        trade_record = {
            'timestamp': timestamp,
            'symbol': symbol,
            'side': side,
            'quantity': abs(quantity),
            'price': price,
            'trading_cost': trading_cost,
            'position_after': new_pos
        }
        self.trades.append(trade_record)
        self.total_trades += 1
        
        # 计算盈亏（仅卖出时）
        if side == 'sell' and symbol in self.avg_costs:
            pnl = (price - self.avg_costs[symbol]) * abs(quantity) - trading_cost
            if pnl > 0:
                self.winning_trades += 1
        
        self.logger.info(f"交易执行: {side} {abs(quantity)} {symbol} @ {price:.2f}, 成本: {trading_cost:.2f}")
        
        return True
    
    def update_nav(self, market_data: pd.DataFrame, timestamp: pd.Timestamp):
        """
        更新净值。
        
        Args:
            market_data: 市场数据
            timestamp: 时间戳
        """
        # 计算持仓市值
        position_value = 0.0
        for symbol, quantity in self.positions.items():
            symbol_data = market_data[market_data['symbol'] == symbol]
            if not symbol_data.empty:
                current_price = symbol_data['close'].iloc[0]
                position_value += quantity * current_price
        
        # 计算总净值
        total_nav = self.current_capital + position_value
        
        # 更新回撤统计
        if total_nav > self.peak_nav:
            self.peak_nav = total_nav
            self.daily_drawdown = 0.0
        else:
            self.daily_drawdown = (self.peak_nav - total_nav) / self.peak_nav
            self.max_drawdown_reached = max(self.max_drawdown_reached, self.daily_drawdown)
        
        # 记录净值
        nav_record = {
            'timestamp': timestamp,
            'total_nav': total_nav,
            'cash': self.current_capital,
            'position_value': position_value,
            'drawdown': self.daily_drawdown
        }
        self.daily_nav.append(nav_record)
    
    def get_performance_stats(self) -> Dict[str, float]:
        """获取性能统计。"""
        if not self.daily_nav:
            return {}
        
        nav_df = pd.DataFrame(self.daily_nav)
        returns = nav_df['total_nav'].pct_change().dropna()
        
        stats = {
            'total_return': (nav_df['total_nav'].iloc[-1] / self.initial_capital - 1),
            'annualized_return': returns.mean() * 252,
            'volatility': returns.std() * np.sqrt(252),
            'sharpe_ratio': (returns.mean() / returns.std() * np.sqrt(252)) if returns.std() > 0 else 0,
            'max_drawdown': self.max_drawdown_reached,
            'win_rate': self.winning_trades / self.total_trades if self.total_trades > 0 else 0,
            'total_trades': self.total_trades,
            'total_commission': self.total_commission,
            'total_slippage': self.total_slippage
        }
        
        return stats


class EnhancedBacktester:
    """
    增强版回测引擎。
    """
    
    def __init__(self,
                 initial_capital: float = 1000000,
                 trading_costs: Optional[TradingCosts] = None,
                 risk_limits: Optional[RiskLimits] = None):
        """
        初始化增强版回测引擎。
        
        Args:
            initial_capital: 初始资金
            trading_costs: 交易成本配置
            risk_limits: 风险限制配置
        """
        self.initial_capital = initial_capital
        self.trading_costs = trading_costs or TradingCosts()
        self.risk_limits = risk_limits or RiskLimits()
        
        self.portfolio = EnhancedPortfolio(
            initial_capital, self.trading_costs, self.risk_limits
        )
        
        self.logger = logging.getLogger(__name__)
    
    def run_backtest(self,
                    market_data: pd.DataFrame,
                    signals: pd.DataFrame,
                    start_date: Optional[str] = None,
                    end_date: Optional[str] = None) -> Dict[str, Any]:
        """
        运行回测。
        
        Args:
            market_data: 市场数据，包含 datetime, symbol, open, high, low, close, volume
            signals: 信号数据，包含 datetime, symbol, signal
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            回测结果字典
        """
        self.logger.info("开始增强版回测...")
        
        # 数据预处理
        market_data = market_data.copy()
        signals = signals.copy()
        
        # 确保日期列为datetime类型
        market_data['datetime'] = pd.to_datetime(market_data['datetime'])
        signals['datetime'] = pd.to_datetime(signals['datetime'])
        
        # 过滤日期范围
        if start_date:
            start_date = pd.to_datetime(start_date)
            market_data = market_data[market_data['datetime'] >= start_date]
            signals = signals[signals['datetime'] >= start_date]
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            market_data = market_data[market_data['datetime'] <= end_date]
            signals = signals[signals['datetime'] <= end_date]
        
        # 合并数据
        combined_data = pd.merge(
            market_data, signals, 
            on=['datetime', 'symbol'], 
            how='left'
        ).fillna({'signal': 0})
        
        # 按日期分组回测
        start_time = time.time()
        
        for date, daily_data in combined_data.groupby('datetime'):
            # 更新净值
            self.portfolio.update_nav(daily_data, date)
            
            # 处理信号
            for _, row in daily_data.iterrows():
                if row['signal'] != 0:
                    # 简化的信号处理：信号值直接作为目标仓位
                    symbol = row['symbol']
                    target_position = int(row['signal'] * 1000)  # 假设每个信号单位对应1000股
                    current_position = self.portfolio.positions.get(symbol, 0)
                    
                    trade_quantity = target_position - current_position
                    
                    if trade_quantity != 0:
                        self.portfolio.execute_trade(
                            symbol, trade_quantity, row['close'], date
                        )
        
        execution_time = time.time() - start_time
        
        # 生成回测结果
        results = {
            'performance_stats': self.portfolio.get_performance_stats(),
            'trades': pd.DataFrame(self.portfolio.trades),
            'nav_curve': pd.DataFrame(self.portfolio.daily_nav),
            'execution_time': execution_time
        }
        
        self.logger.info(f"回测完成，耗时 {execution_time:.2f} 秒")
        
        return results
    
    def generate_report(self, results: Dict[str, Any]) -> str:
        """
        生成回测报告。
        
        Args:
            results: 回测结果
            
        Returns:
            报告字符串
        """
        stats = results['performance_stats']
        
        report = f"""
=== 增强版回测报告 ===

基本信息:
- 初始资金: {self.initial_capital:,.2f}
- 回测期间: {len(results['nav_curve'])} 天
- 执行时间: {results['execution_time']:.2f} 秒

收益指标:
- 总收益率: {stats.get('total_return', 0):.2%}
- 年化收益率: {stats.get('annualized_return', 0):.2%}
- 年化波动率: {stats.get('volatility', 0):.2%}
- 夏普比率: {stats.get('sharpe_ratio', 0):.2f}

风险指标:
- 最大回撤: {stats.get('max_drawdown', 0):.2%}

交易统计:
- 总交易次数: {stats.get('total_trades', 0)}
- 胜率: {stats.get('win_rate', 0):.2%}
- 总佣金: {stats.get('total_commission', 0):.2f}
- 总滑点: {stats.get('total_slippage', 0):.2f}

=== 报告结束 ===
        """
        
        return report.strip()


# 使用示例和测试函数
def test_enhanced_backtester():
    """测试增强版回测引擎。"""
    print("=== 测试增强版回测引擎 ===")
    
    # 创建模拟数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    symbols = ['STOCK_001', 'STOCK_002', 'STOCK_003']
    
    # 市场数据
    market_data = []
    for date in dates:
        for symbol in symbols:
            price = 100 + np.random.normal(0, 5)
            market_data.append({
                'datetime': date,
                'symbol': symbol,
                'open': price,
                'high': price * 1.02,
                'low': price * 0.98,
                'close': price,
                'volume': np.random.randint(1000, 10000)
            })
    
    market_df = pd.DataFrame(market_data)
    
    # 信号数据
    signals_data = []
    for date in dates[::5]:  # 每5天一个信号
        for symbol in symbols:
            signals_data.append({
                'datetime': date,
                'symbol': symbol,
                'signal': np.random.choice([-1, 0, 1])
            })
    
    signals_df = pd.DataFrame(signals_data)
    
    # 运行回测
    backtester = EnhancedBacktester(initial_capital=1000000)
    results = backtester.run_backtest(market_df, signals_df)
    
    # 生成报告
    report = backtester.generate_report(results)
    print(report)
    
    print("✅ 增强版回测引擎测试完成")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    test_enhanced_backtester()
