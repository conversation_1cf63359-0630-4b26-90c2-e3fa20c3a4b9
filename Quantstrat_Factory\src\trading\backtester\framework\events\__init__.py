# events.py
# 定义事件系统中的基础事件类型：市场事件、信号事件、订单事件、成交事件

class Event:
    """基础事件类，所有事件类型的父类"""
    pass

class MarketEvent(Event):
    """市场数据事件：每日行情触发"""
    def __init__(self, date, data):
        self.type = "MARKET"
        self.date = date
        self.data = data

class OrderEvent(Event):
    """订单事件：策略生成买卖意图（当前未使用）"""
    def __init__(self, symbol, order_type, quantity, direction):
        self.type = "ORDER"
        self.symbol = symbol
        self.order_type = order_type
        self.quantity = quantity
        self.direction = direction

class FillEvent(Event):
    """成交事件：用于更新组合中的持仓与资金"""
    def __init__(self, symbol, price, quantity, direction, date):
        self.type = "FILL"
        self.symbol = symbol
        self.price = price
        self.quantity = quantity
        self.direction = direction
        self.date = date

class SignalEvent(Event):
    """信号事件：由信号函数生成的买入/卖出信号"""
    def __init__(self, symbol, date, signal_type):
        self.type = "SIGNAL"
        self.symbol = symbol
        self.date = date
        self.signal_type = signal_type
