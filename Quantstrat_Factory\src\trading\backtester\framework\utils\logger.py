import logging
import os

_loggers = {}

def get_logger(name="run", log_dir="logs", to_file=True, level=logging.INFO, minimal=False):
    log_file_name = "run.log"  # ✅ 所有模块都输出到这个文件
    if name in _loggers:
        return _loggers[name]

    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger.propagate = False

    # ✅ 清除旧 Handler（关键）
    if logger.hasHandlers():
        logger.handlers.clear()

    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')

    # 控制台输出
    ch = logging.StreamHandler()
    ch.setFormatter(formatter)
    logger.addHandler(ch)

    if to_file:
        os.makedirs(log_dir, exist_ok=True)
        file_path = os.path.join(log_dir, log_file_name)
        fh = logging.FileHandler(file_path, mode="w", encoding="utf-8-sig")
        fh.setFormatter(formatter)

        if minimal:
            fh.setLevel(logging.INFO)  # ✅ 保留 INFO 摘要，不写 DEBUG


        logger.addHandler(fh)

    _loggers[name] = logger
    return logger
