# strategy/live/live_account.py
# 策略中用到的账户信息
# 资金与持仓管理逻辑


from typing import Dict, List
from datetime import datetime

class LiveAccount:
    def __init__(self, initial_cash: float = 1_000_000):
        self.cash = initial_cash
        self.positions: Dict[str, Dict] = {}  # symbol -> {quantity, avg_price}
        self.trades: List[Dict] = []  # 实时成交记录

    def buy(self, symbol: str, price: float, quantity: int, timestamp: datetime):
        cost = price * quantity
        if cost > self.cash:
            return False  # 资金不足

        self.cash -= cost

        if symbol not in self.positions:
            self.positions[symbol] = {
                'quantity': quantity,
                'avg_price': price
            }
        else:
            pos = self.positions[symbol]
            total_qty = pos['quantity'] + quantity
            pos['avg_price'] = (pos['avg_price'] * pos['quantity'] + price * quantity) / total_qty
            pos['quantity'] = total_qty

        self.trades.append({
            'symbol': symbol,
            'action': 'BUY',
            'price': price,
            'quantity': quantity,
            'timestamp': timestamp
        })
        return True

    def sell(self, symbol: str, price: float, quantity: int, timestamp: datetime):
        if symbol not in self.positions or self.positions[symbol]['quantity'] < quantity:
            return False  # 无足够持仓

        proceeds = price * quantity
        self.cash += proceeds
        self.positions[symbol]['quantity'] -= quantity

        if self.positions[symbol]['quantity'] == 0:
            del self.positions[symbol]

        self.trades.append({
            'symbol': symbol,
            'action': 'SELL',
            'price': price,
            'quantity': quantity,
            'timestamp': timestamp
        })
        return True

    def get_summary(self):
        return {
            'cash': self.cash,
            'positions': self.positions,
            'trades': self.trades[-10:]  # 最近10笔交易
        }

    def get_position_map(self):
        """
        获取当前持仓映射：symbol → 持仓字典（简化读取用）
        """
        return self.positions.copy()
