# strategy/live/live_data_builder.py

import os

import pandas as pd
from datetime import datetime

def build_row_map(price_map: dict) -> dict:
    """
    构建 row_map：symbol → 最新价格行（仿 row_map 格式）
    """
    now = datetime.now()
    row_map = {}
    for symbol, price in price_map.items():
        row_map[symbol] = {
            "symbol": symbol,
            "date": now,
            "open": price,
            "high": price,
            "low": price,
            "close": price,
            "volume": 1_000_000  # 默认估值，或后续接入真实成交量
        }
    return row_map

def build_feature_cache(recent_df_map: dict) -> dict:
    """
    构建 feature_cache：symbol → { 'df', 'date_index_map' }
    recent_df_map: symbol → 最近历史 DataFrame（用于因子计算）
    """
    cache = {}
    for symbol, df in recent_df_map.items():
        df = df.copy()
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date').reset_index(drop=True)

        index_map = {d.normalize(): i for i, d in enumerate(df['date'])}
        cache[symbol] = {
            "df": df,
            "date_index_map": index_map
        }
    return cache


def load_recent_data(symbol: str, data_dir="data", lookback_days=30) -> pd.DataFrame:
    """
    加载指定 symbol 最近 N 天的历史 K线（默认从本地CSV）
    - symbol: 例如 "000001.SZ"
    - data_dir: 存放 CSV 文件的目录
    - lookback_days: 回溯天数（默认取近30日）
    """
    path = os.path.join(data_dir, f"{symbol}.csv")
    if not os.path.exists(path):
        raise FileNotFoundError(f"❌ 找不到本地数据文件: {path}")

    df = pd.read_csv(path)
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date').reset_index(drop=True)

    if len(df) < lookback_days:
        return df  # 不足则全返回

    return df.iloc[-lookback_days:].copy()
