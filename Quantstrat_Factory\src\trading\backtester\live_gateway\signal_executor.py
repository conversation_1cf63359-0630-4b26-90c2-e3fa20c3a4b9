# strategy/live/signal_executor.py

from strategy.live.live_account import LiveAccount
from strategy.core import SignalEvent
from datetime import datetime
import pandas as pd
import os

class SignalExecutor:
    def __init__(self, account: LiveAccount, log_dir: str = "output"):
        self.account = account
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)

    def handle_signal(self, signal: dict):
        """
        单条信号处理（适合外部调用或 REST API）
        """
        symbol = signal['symbol']
        action = signal['action'].upper()
        price = signal['price']
        quantity = signal['quantity']
        timestamp = signal.get('timestamp', datetime.now())

        # ✅ 若已连接实盘网关（VeighNa Gateway），则实盘下单
        if hasattr(self, "gateway"):
            return self.send_order_via_gateway(symbol, price, quantity, action)

        # 否则默认使用模拟账户
        if action == 'BUY':
            return self.account.buy(symbol, price, quantity, timestamp)
        elif action == 'SELL':
            return self.account.sell(symbol, price, quantity, timestamp)
        else:
            return False

    def execute_signals(self, signal_list: list, price_data: dict, timestamp: datetime):
        """
        批量信号执行接口（适合策略层调用）
        参数：
            signal_list: List[SignalEvent]（内部标准信号对象）
            price_data: dict[symbol] = 当前价格（收盘或开盘）
            timestamp: 当前时间（datetime）
        返回：
            List[(symbol, action, True/False or 'SKIPPED_NO_PRICE')]
        """
        results = []

        for signal in signal_list:
            symbol = signal.symbol
            price = price_data.get(symbol)
            if price is None:
                results.append((symbol, signal.signal_type, 'SKIPPED_NO_PRICE'))
                continue

            qty = 100  # 默认固定下单手数，可改为 signal.metadata 控制

            if signal.signal_type == 'LONG':
                success = self.account.buy(symbol, price, qty, timestamp)
                results.append((symbol, 'BUY', success))
            elif signal.signal_type == 'EXIT':
                success = self.account.sell(symbol, price, qty, timestamp)
                results.append((symbol, 'SELL', success))
            else:
                results.append((symbol, 'UNKNOWN_SIGNAL', False))

        return results

    def sync_summary(self):
        """
        获取账户当前状态（资金、持仓、交易记录）
        """
        return self.account.get_summary()

    def export_trade_log(self, filename: str = "live_trades.csv"):
        """
        导出当前账户的交易记录为 CSV 文件
        """
        df = pd.DataFrame(self.account.trades)
        path = os.path.join(self.log_dir, filename)
        df.to_csv(path, index=False)
        print(f"✅ 实盘成交记录已保存至 {path}")

    def connect_gateway(self, gateway):
        """
        预留：对接 VeighNa Gateway 实盘接口（如掘金、CTP、IB）
        gateway: 实盘下单接口（需实现 send_order(symbol, price, qty, side)）
        """
        self.gateway = gateway

    def send_order_via_gateway(self, symbol: str, price: float, quantity: int, side: str):
        """
        使用实盘网关下单（兼容 VeighNa 接口风格）
        side: 'BUY' or 'SELL'
        """
        if not hasattr(self, "gateway"):
            raise RuntimeError("❌ 未连接任何实盘网关接口！请先调用 connect_gateway().")

        return self.gateway.send_order(symbol=symbol, price=price, volume=quantity, direction=side)