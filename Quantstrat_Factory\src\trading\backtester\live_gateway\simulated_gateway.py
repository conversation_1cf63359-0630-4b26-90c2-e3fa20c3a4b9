# simulated_gateway.py
# 位于文件：strategy/sim/simulated_gateway.py
# 用于自动生成行情数据，替代 VeighNa Gateway 实现行情模拟

import random
from datetime import datetime

class SimulatedGateway:
    def __init__(self, symbols):
        self.symbols = symbols
        self.price_map = {s: 10.0 + random.random() for s in symbols}  # 初始价格

    def get_batch_price(self, symbols):
        price_data = {}
        for s in symbols:
            last = self.price_map[s]
            pct_change = random.uniform(-0.01, 0.01)  # 随机滚动 1%
            new_price = round(last * (1 + pct_change), 2)
            self.price_map[s] = new_price

            price_data[s] = {
                "open": round(new_price * random.uniform(0.99, 1.01), 2),
                "high": round(new_price * 1.01, 2),
                "low": round(new_price * 0.99, 2),
                "close": new_price,
                "volume": random.randint(50000, 200000)
            }
        return price_data

    def on_tick(self, tick):
        pass  # 模拟模式不处理 tick 事件
