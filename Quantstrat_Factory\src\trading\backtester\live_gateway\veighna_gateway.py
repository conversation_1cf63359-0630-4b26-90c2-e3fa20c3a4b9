# strategy/live/veighna_gateway.py
# 掘金 TraderGateway 实盘行情封装器

from vnpy.trader.engine import MainEngine
from vnpy.trader.object import SubscribeRequest
from vnpy.trader.constant import Exchange
from typing import Dict
import datetime


class VeighnaGateway:
    def __init__(self, main_engine: MainEngine, gateway_name: str):
        self.main_engine = main_engine
        self.gateway_name = gateway_name
        self.latest_prices: Dict[str, float] = {}

    def subscribe_symbols(self, symbols: list):
        """
        批量订阅行情（需要在 gateway connect 后立即调用）
        """
        for symbol in symbols:
            req = self._build_subscribe_request(symbol)
            self.main_engine.subscribe(req, self.gateway_name)

    def _build_subscribe_request(self, symbol: str):
        """
        例如 000001.SZ → SubscribeRequest(symbol=000001, exchange=SZSE)
        """
        code, suffix = symbol.split(".")
        exchange = Exchange.SZSE if suffix.upper() == "SZ" else Exchange.SSE
        return SubscribeRequest(
            symbol=code,
            exchange=exchange
        )

    def on_tick(self, tick):
        """
        由 VeighNa 系统外部 TickHandler 注入，保存最新 tick
        """
        self.latest_prices[tick.vt_symbol] = tick.last_price

    def get_current_price(self, symbol: str) -> float:
        vt_symbol = self._to_vt_symbol(symbol)
        return self.latest_prices.get(vt_symbol, 0.0)

    def get_batch_price(self, symbols: list) -> Dict[str, float]:
        return {s: self.get_current_price(s) for s in symbols}

    def get_timestamp(self) -> datetime.datetime:
        return datetime.datetime.now()

    def _to_vt_symbol(self, symbol: str):
        code, suffix = symbol.split(".")
        exchange = "SZSE" if suffix.upper() == "SZ" else "SSE"
        return f"{code}.{exchange}"
