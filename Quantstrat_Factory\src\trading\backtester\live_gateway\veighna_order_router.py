# strategy/live/veighna_order_router.py
# VeighNa 实盘下单封装模块（接入 MainEngine.send_order），新增 dry_run 模式

from vnpy.trader.engine import MainEngine
from vnpy.trader.object import OrderRequest
from vnpy.trader.constant import Direction, Offset, Exchange, OrderType
from typing import Literal

class VeighnaOrderRouter:
    def __init__(self, main_engine: MainEngine, gateway_name: str, dry_run: bool = False):
        """
        初始化下单路由器
        :param main_engine: VeighNa 主引擎
        :param gateway_name: VeighNa gateway 名称（如 "TRADER"）
        :param dry_run: True 为模拟模式，不发送实际委托
        """
        self.main_engine = main_engine
        self.gateway_name = gateway_name
        self.dry_run = dry_run

    def send_order(self, symbol: str, price: float, volume: int, side: Literal["BUY", "SELL"]):
        """
        发送一笔委托，支持 dry_run 模式
        :param symbol: 如 "000001.SZ"
        :param price: 价格
        :param volume: 数量
        :param side: "BUY" 或 "SELL"
        """
        vt_symbol, exchange = self._parse_symbol(symbol)

        if self.dry_run:
            print(f"[DRY RUN] 模拟下单: {symbol} {side} {volume}@{price}")
            return None

        direction = Direction.LONG if side.upper() == "BUY" else Direction.SHORT
        req = OrderRequest(
            symbol=vt_symbol,
            exchange=exchange,
            price=price,
            volume=volume,
            direction=direction,
            offset=Offset.OPEN,
            type=OrderType.LIMIT
        )

        order_id = self.main_engine.send_order(req, self.gateway_name)
        print(f"实际下单: {order_id} -> {symbol} {side} {volume}@{price}")
        return order_id

    def _parse_symbol(self, symbol: str):
        """
        解析 symbol 格式，如 "000001.SZ" 转换为 VeighNa 样式
        :return: (票码, 交易所)
        """
        code, suffix = symbol.split(".")
        exchange = Exchange.SZSE if suffix.upper() == "SZ" else Exchange.SSE
        return code, exchange
