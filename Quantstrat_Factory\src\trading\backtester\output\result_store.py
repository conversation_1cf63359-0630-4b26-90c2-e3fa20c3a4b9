# strategy/storage/result_store.py
# 回测与实盘结果统一存储模块（支持 SQLite）

import sqlite3
import json
import os
import pandas as pd

DB_PATH = "output/strategy_results.db"


def init_db():
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute("""
    CREATE TABLE IF NOT EXISTS results (
        run_id TEXT PRIMARY KEY,
        date TEXT,
        strategy_name TEXT,
        metrics TEXT,
        parameters TEXT
    )
    """)
    conn.commit()
    conn.close()


def save_result(run_id: str, metrics: dict, parameters: dict, strategy_name="default"):
    init_db()
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    c.execute(
        "REPLACE INTO results (run_id, date, strategy_name, metrics, parameters) VALUES (?, DATE('now'), ?, ?, ?)",
        (run_id, strategy_name, json.dumps(metrics, ensure_ascii=False), json.dumps(parameters, ensure_ascii=False))
    )
    conn.commit()
    conn.close()


def query_results(sort_by="年化收益率", top_n=20):
    conn = sqlite3.connect(DB_PATH)
    df = pd.read_sql("SELECT * FROM results", conn)
    conn.close()
    df["metrics"] = df["metrics"].apply(json.loads)
    df["parameters"] = df["parameters"].apply(json.loads)
    df = pd.concat([df.drop(columns=["metrics"]), df["metrics"].apply(pd.Series)], axis=1)
    return df.sort_values(by=sort_by, ascending=False).head(top_n)
