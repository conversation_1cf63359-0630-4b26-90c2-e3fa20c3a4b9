# score_results.py
# 评分器：从批量回测结果中提取关键绩效指标并排序输出

import json
import pandas as pd
import os


def load_summary(summary_path):
    with open(summary_path, 'r') as f:
        data = json.load(f)
    return data


def score_and_rank(summary, top_n=10, sort_by="年化收益率"):
    records = []
    for r in summary:
        records.append({
            "run_id": r["run_id"],
            "收益率": r.get("累计收益率", 0),
            "年化收益率": r.get("年化收益率", 0),
            "最大回撤": r.get("最大回撤", 0),
            "夏普比率": r.get("夏普比率", 0),
            "参数": json.dumps(r.get("config", {}), ensure_ascii=False)
        })

    df = pd.DataFrame(records)
    df_sorted = df.sort_values(by=sort_by, ascending=False).head(top_n)
    print("\n📊 策略评分结果（按" + sort_by + "排序）")
    print(df_sorted.to_markdown(index=False))

    return df_sorted


if __name__ == "__main__":
    summary_path = "output/batch/summary.json"
    if not os.path.exists(summary_path):
        print("❌ 未找到 summary.json，请先运行 run_param_sweeper.py")
    else:
        summary = load_summary(summary_path)
        score_and_rank(summary, top_n=10, sort_by="年化收益率")
