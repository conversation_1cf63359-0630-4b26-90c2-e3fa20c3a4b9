# visualize_results.py
# 可视化模块：生成回测结果的图表与 HTML 排名

import json
import os
import pandas as pd
import matplotlib.pyplot as plt


def load_summary(summary_path):
    with open(summary_path, 'r') as f:
        return json.load(f)


def plot_top_n(summary, top_n=10, metric="年化收益率"):
    df = pd.DataFrame([{
        "run_id": r["run_id"],
        "收益率": r.get("累计收益率", 0),
        "年化收益率": r.get("年化收益率", 0),
        "最大回撤": r.get("最大回撤", 0),
        "夏普比率": r.get("夏普比率", 0),
    } for r in summary])

    df = df.sort_values(by=metric, ascending=False).head(top_n)
    df.plot(kind="bar", x="run_id", y=metric, legend=False)
    plt.title(f"Top {top_n} 策略按 {metric} 排名")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    plt.savefig("output/batch/top_strategies.png")
    plt.close()
    print("📊 已保存图表至 output/batch/top_strategies.png")


def export_html_table(summary, output_path="output/batch/top_strategies.html", top_n=20):
    df = pd.DataFrame([{
        "run_id": r["run_id"],
        "收益率": r.get("累计收益率", 0),
        "年化收益率": r.get("年化收益率", 0),
        "最大回撤": r.get("最大回撤", 0),
        "夏普比率": r.get("夏普比率", 0),
        "参数": json.dumps(r.get("config", {}), ensure_ascii=False)
    } for r in summary])

    df = df.sort_values(by="年化收益率", ascending=False).head(top_n)
    df.to_html(output_path, index=False)
    print(f"📄 已保存 HTML 排名表至 {output_path}")


if __name__ == "__main__":
    summary_path = "output/batch/summary.json"
    if not os.path.exists(summary_path):
        print("❌ 请先运行 run_param_sweeper.py 生成 summary.json")
    else:
        summary = load_summary(summary_path)
        plot_top_n(summary, top_n=10, metric="年化收益率")
        export_html_table(summary)
