# 文件：run.py
# 非实盘入口：统一支持回测、参数调优、模拟盘信号调度

import argparse
import sys
from pathlib import Path

# 动态添加策略路径
strategies_path = Path(__file__).resolve().parents[4] / "strategies"
if strategies_path.exists():
    sys.path.append(str(strategies_path))



def main():
    parser = argparse.ArgumentParser(description="策略运行入口（非实盘）")
    parser.add_argument('--mode', type=str, required=True,
                        choices=['backtest', 'sweep', 'signal'],
                        help="运行模式：backtest（回测）/ sweep（参数调优）/ signal（模拟盘）")
    args = parser.parse_args()

    if args.mode == 'backtest':
        from runner.backtest_runner import run as run_backtest
        run_backtest()
    elif args.mode == 'sweep':
        from runner.param_sweeper import run as run_sweep
        run_sweep()
    elif args.mode == 'signal':
        from runner.signal_runner import run as run_signal
        run_signal()
    else:
        print("❌ 未知模式")
        sys.exit(1)


if __name__ == '__main__':
    main()
