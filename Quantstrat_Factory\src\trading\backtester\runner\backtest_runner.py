# run_manager.py
# 回测运行与项目管理优化工具：多任务调度、日志归档、结果快照

import os
import shutil
import json
from datetime import datetime
from multiprocessing import Pool
from runner.param_sweeper import load_param_grid, run_batch_backtests


OUTPUT_BASE = "output/experiments"


def archive_run(tag=None):
    """
    将当前 output 目录打包存档，带时间戳与可选标签
    """
    time_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    tag = tag or "batch"
    target_dir = os.path.join(OUTPUT_BASE, f"{time_str}_{tag}")
    shutil.copytree("output/batch", target_dir)
    print(f"📦 已归档运行结果至 {target_dir}")


def parallel_run(param_chunks):
    """
    并行执行多个参数组合列表
    """
    with Pool(processes=os.cpu_count()) as pool:
        pool.map(run_batch_backtests, param_chunks)


def split_params(param_list, n_chunks):
    chunk_size = len(param_list) // n_chunks + 1
    return [param_list[i:i+chunk_size] for i in range(0, len(param_list), chunk_size)]


if __name__ == "__main__":
    os.makedirs(OUTPUT_BASE, exist_ok=True)
    all_params = load_param_grid()

    # 串行运行
    run_batch_backtests(all_params)

    # ✅ 可选：归档结果
    archive_run(tag="gridsearch")

    # ✅ 可选：并行执行（视机器性能）
    # chunks = split_params(all_params, os.cpu_count())
    # parallel_run(chunks)

def run():
    all_params = load_param_grid()
    run_batch_backtests(all_params)

