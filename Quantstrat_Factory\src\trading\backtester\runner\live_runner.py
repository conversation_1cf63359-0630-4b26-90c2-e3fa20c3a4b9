# realtime_live_runner.py
# 实盘联调主控脚本（提供 VeighNa Gateway + 策略信号 + 实盘下单 + 日志记录）

from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy_trader_gw import TraderGateway  # 提供给 VeighNa 的提供商接口（提供接入握里）

from strategy.live.veighna_gateway import VeighnaGateway  # 行情接入软维
from strategy.live.veighna_order_router import VeighnaOrderRouter  # 订单路由器（连接 VeighNa 和策略应用）
from strategy.live.signal_executor import SignalExecutor  # 执行器：根据策略信号进行下单
from strategy.live.live_account import LiveAccount  # 本地实盘账户结构
from strategy.signals.trend_signal import TrendSignalGenerator  # 趋势策略信号生成器
from strategy.live.live_data_builder import (
    load_recent_data,
    build_row_map,
    build_feature_cache
)

from datetime import datetime
import time
import json
import os
import csv

# === 实盘运行状态日志 ===
def log_runtime_state(account: LiveAccount, signals: list):
    os.makedirs("output", exist_ok=True)
    os.makedirs("logs", exist_ok=True)

    snapshot = {
        "time": datetime.now().isoformat(),
        "cash": account.cash,
        "positions": account.positions,
        "pending_signals": [s.__dict__ for s in signals]
    }

    with open("output/live_state.json", "w", encoding="utf-8") as f:
        json.dump(snapshot, f, indent=2, ensure_ascii=False)

    csv_path = "output/live_history.csv"
    is_new = not os.path.exists(csv_path)
    with open(csv_path, "a", newline="", encoding="utf-8-sig") as f:
        writer = csv.writer(f)
        if is_new:
            writer.writerow(["time", "cash", "position_count", "signal_count"])
        writer.writerow([
            snapshot["time"],
            snapshot["cash"],
            len(snapshot["positions"]),
            len(snapshot["pending_signals"])
        ])


# === 初始化 VeighNa 主引擎 + TraderGateway ===
event_engine = EventEngine()
main_engine = MainEngine(event_engine)
main_engine.add_gateway(TraderGateway)

gateway_name = "TRADER"

# === 加载接入配置 ===
with open("configs/jq_connect.json", "r", encoding="utf-8") as f:
    connect_config = json.load(f)

main_engine.connect(connect_config, gateway_name)

# === 实时行情接入器 ===
gateway = VeighnaGateway(main_engine, gateway_name)

# === 注册 tick 事件处理 ===
def handle_tick(tick):
    gateway.on_tick(tick)

event_engine.register("eTick", handle_tick)

# === 目标系列 ===
symbols = ["000001.SZ", "600000.SH"]
gateway.subscribe_symbols(symbols)

# === 初始化组件 ===
order_router = VeighnaOrderRouter(main_engine, gateway_name)
account = LiveAccount(initial_cash=1_000_000)
executor = SignalExecutor(account)
executor.connect_gateway(order_router)
signal_generator = TrendSignalGenerator()

# 读取策略参数
with open("configs/config_template.json", "r") as f:
    param = json.load(f)

# 读取历史 K 线，构造特征 cache
recent_df_map = {s: load_recent_data(s) for s in symbols}
feature_cache = build_feature_cache(recent_df_map)

# === 主运行循环 ===
while True:
    print(f"📡 [{datetime.now()}] 实盘行情转换中...")

    # 得到最新行情
    price_map = gateway.get_batch_price(symbols)
    row_map = build_row_map(price_map)
    current_positions = account.get_position_map()

    # 生成策略信号
    signals = signal_generator.generate_signals_batch_vectorized(
        symbol_list=symbols,
        row_map=row_map,
        param=param,
        current_positions=current_positions,
        feature_cache=feature_cache,
        day_index=0
    )

    # 执行信号（实际下单）
    executor.execute_signals(signals, price_map, datetime.now())

    # 记录实时运行状态
    log_runtime_state(account, signals)

    time.sleep(5)  # 间隔 5 秒进行一次运行
