# 文件路径：runner/param_sweeper.py
# 批量参数回测执行器

import json
import os
import itertools
import uuid
from datetime import datetime
import sys
from pathlib import Path # 新增
import pandas as pd # 新增
import configparser # 新增

# 将项目根目录添加到 Python 路径
ROOT_DIR = Path(__file__).resolve().parents[2] # Quantstrat_Factory
sys.path.append(str(ROOT_DIR))
# 将 06_backtester 目录（原 Strategies）添加到 Python 路径
BACKTESTER_ROOT_DIR = Path(__file__).resolve().parents[1] # 06_backtester
sys.path.append(str(BACKTESTER_ROOT_DIR))


from framework.engine.backtester import Backtester
from strategy.signal_utils import ParameterConfig
from strategy.base_strategy import BaseStrategy # 新增导入
import importlib # 新增导入


def load_param_grid(config_path_relative_to_strategies_root="configs/config_template.json"):
    """
    读取模板配置并构造参数网格（如值为列表则组合）
    """
    # 获取当前脚本（param_sweeper.py）的目录
    current_script_dir = os.path.dirname(os.path.abspath(__file__))
    # 计算 Strategies 根目录的路径 (param_sweeper.py 在 runner 目录下，runner 在 Strategies 目录下)
    strategies_root_dir = os.path.abspath(os.path.join(current_script_dir, '..'))
    # 拼接完整的配置文件路径
    full_config_path = os.path.join(strategies_root_dir, config_path_relative_to_strategies_root)

    with open(full_config_path, 'r') as f:
        base_config = json.load(f)

    grid_keys = []
    grid_values = []

    for k, v in base_config.items():
        if isinstance(v, list):
            grid_keys.append(k)
            grid_values.append(v)

    param_list = []
    for combo in itertools.product(*grid_values):
        new_param = base_config.copy()
        for i, val in enumerate(combo):
            new_param[grid_keys[i]] = val
        param_list.append(new_param)

    return param_list


def run_batch_backtests(param_list, output_dir="output/batch", signal_matrix_df: pd.DataFrame = None):
    # output_dir 现在是相对于 06_backtester 模块的路径
    # 例如: Quantstrat_Factory/06_backtester/output/batch
    full_output_dir = BACKTESTER_ROOT_DIR / output_dir 
    os.makedirs(full_output_dir, exist_ok=True)
    summary = []

    # 从 config.ini 读取数据目录
    config = configparser.ConfigParser()
    config_file_path = ROOT_DIR / 'config.ini'
    if not config_file_path.exists():
        raise FileNotFoundError(f"主配置文件 config.ini 未找到于: {config_file_path}")
    config.read(config_file_path, encoding='utf-8')
    
    # 使用日线数据作为回测的价格数据源
    # 使用统一的日K数据路径
    DATA_DIR = Path("D:/PY/Data/cleaned/daily")
    if not DATA_DIR.exists():
        raise FileNotFoundError(f"日线数据目录未找到: {DATA_DIR}")

    symbols = [f for f in os.listdir(DATA_DIR) if f.endswith(".parquet")]
    if not symbols:
        raise FileNotFoundError(f"在 {DATA_DIR} 中没有找到任何 .parquet 数据文件。")

    for i, param_dict in enumerate(param_list): # param 改名为 param_dict 以清晰
        run_id = f"run_{i}_{uuid.uuid4().hex[:6]}"
        print(f"🚀 正在执行: {run_id}")

        # 确保 param_dict 是字典类型，然后解包给 ParameterConfig
        if not isinstance(param_dict, dict):
            print(f"警告: 参数配置不是字典类型，跳过 run_id {run_id}。参数: {param_dict}")
            continue
        
        current_param_config = ParameterConfig(**param_dict)
        
        strategy_instance = None
        if not signal_matrix_df: # 仅在内部信号模式下创建策略实例
            # 动态加载策略类 - 这里我们硬编码使用 TrendFollowingStrategy 作为示例
            # 实际应用中，策略名称可以来自参数或配置文件
            try:
                strategy_module_path = "strategy.trend_following_strategy" # 相对于 06_backtester 目录
                StrategyClass = getattr(importlib.import_module(strategy_module_path), "TrendFollowingStrategy")
                strategy_instance = StrategyClass(params=current_param_config)
            except Exception as e:
                print(f"错误: 无法加载或实例化策略 TrendFollowingStrategy: {e}")
                continue # 跳过此次回测

        bt = Backtester(
            data_dir=str(DATA_DIR), 
            symbols=symbols, 
            param=current_param_config,
            strategy_instance=strategy_instance, # 传递策略实例
            signal_matrix_df=signal_matrix_df 
        )
        bt.run_backtest()
        
        result = {}
        result["run_id"] = run_id
        result["config"] = param_dict
        # 可以从 bt.portfolio 或 bt.analyzer 获取更多结果指标
        # 例如: result["final_value"] = bt.portfolio.current_holdings_value
        summary.append(result)

        with open(full_output_dir / f"{run_id}_result.json", "w", encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)

    with open(full_output_dir / "summary.json", "w", encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    print(f"✅ 所有回测已完成，共 {len(summary)} 组，结果保存在: {full_output_dir}")


def run():
    grid = load_param_grid()
    run_batch_backtests(grid)


if __name__ == "__main__":
    # --- 测试内部信号模式 (现在使用动态加载的 TrendFollowingStrategy) ---
    print("--- 测试内部信号模式 (使用 TrendFollowingStrategy) ---")
    internal_params_grid = load_param_grid()
    if internal_params_grid:
        # 对于内部信号模式，不传递 signal_matrix_df
        run_batch_backtests(internal_params_grid, output_dir="output/internal_trend_strategy_batch")
    else:
        print("未能加载内部信号模式的参数网格。")

    # --- 测试外部信号模式 ---
    print("\n--- 测试外部信号模式 (不使用内部策略实例) ---")
    # 构造一个简单的外部信号矩阵用于测试
    # 在实际应用中，这个 signal_matrix_df 会由 05_signal_generator 模块生成
    dates = pd.to_datetime(['2023-01-03', '2023-01-04', '2023-01-05', '2023-01-06'])
    # 假设我们有两只股票的信号，股票代码需要与数据文件匹配 (不含.parquet)
    # 例如，如果数据文件是 000001.SZ.parquet, 600000.SH.parquet
    # 注意：确保这些股票的数据文件存在于 config.ini 指定的 day_data_path 中
    example_symbols_in_data = ["000001.SZ", "600000.SH"] # 假设这些股票数据存在
    
    # 检查符号是否在可用符号列表中
    config_main = configparser.ConfigParser()
    config_main.read(ROOT_DIR / 'config.ini', encoding='utf-8')
    day_data_p = Path(config_main['Paths']['day_data_path'])
    available_symbols_files = [f for f in os.listdir(day_data_p) if f.endswith(".parquet")]
    available_symbols = [s.replace(".parquet","") for s in available_symbols_files]
    
    test_symbols = [s for s in example_symbols_in_data if s in available_symbols]

    if not test_symbols:
        print(f"警告: 示例股票 {example_symbols_in_data} 在数据目录 {day_data_p} 中均未找到对应数据文件。外部信号模式测试可能无法正确运行。")
        print(f"可用股票文件示例: {available_symbols_files[:5]}")
    else:
        print(f"将使用以下股票进行外部信号测试: {test_symbols}")

        data_for_signal_matrix = {
            test_symbols[0]: [1, 0, -1, 0], # 买入, 持有, 卖出, 持有
        }
        if len(test_symbols) > 1:
            data_for_signal_matrix[test_symbols[1]] = [0, 1, 0, -1] # 持有, 买入, 持有, 卖出
        
        sample_signal_matrix = pd.DataFrame(data_for_signal_matrix, index=dates)

        # 外部信号模式通常只需要一个基础参数配置
        # 可以从 config_template.json 加载一个，或者手动定义
        try:
            base_params_list = load_param_grid() # 这会返回一个列表
            if not base_params_list:
                raise ValueError("load_param_grid 返回空列表")
            external_mode_params = [base_params_list[0]] # 取第一个作为基础配置
            print(f"外部信号模式使用的基础参数: {external_mode_params[0]}")
            run_batch_backtests(external_mode_params, 
                                output_dir="output/external_signal_batch", 
                                signal_matrix_df=sample_signal_matrix)
        except FileNotFoundError as e:
            print(f"错误: 加载参数配置文件失败: {e}")
            print("请确保 configs/config_template.json 文件存在于 06_backtester 模块下。")
        except Exception as e:
            print(f"运行外部信号模式测试时发生错误: {e}")
            import traceback
            traceback.print_exc()
