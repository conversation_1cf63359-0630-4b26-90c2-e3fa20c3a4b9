# realtime_signal_runner.py
# 实盘信号调度测试器：周期性轮询实时数据并触发策略信号与账户执行

import time
from datetime import datetime
from strategy.live.live_account import LiveAccount
from strategy.live.signal_executor import SignalExecutor
from strategy.data.realtime_data import DummyDataSource
from strategy.signals.trend_signal import TrendSignalGenerator
from strategy.core import SignalEvent


class RealTimeRunner:
    def __init__(self, symbol_list, strategy_param, interval=60):
        self.symbols = symbol_list
        self.param = strategy_param
        self.interval = interval
        self.account = LiveAccount(initial_cash=strategy_param.get("initial_capital", 1_000_000))
        self.executor = SignalExecutor(self.account)
        self.datasource = DummyDataSource()  # TODO: 替换为实时行情源
        self.signal_gen = TrendSignalGenerator()
        self.row_map = {}  # 简化模拟当前行情封装结构

    def run_once(self):
        price_data = self.datasource.get_batch_price(self.symbols)
        timestamp = self.datasource.get_timestamp()

        for symbol in self.symbols:
            self.row_map[symbol] = {
                "symbol": symbol,
                "date": timestamp,
                "open": price_data[symbol],
                "close": price_data[symbol],
                "high": price_data[symbol],
                "low": price_data[symbol],
                "volume": 1000000  # 模拟量
            }

        signals = self.signal_gen.generate_signals_batch_vectorized(
            self.symbols,
            self.row_map,
            self.param,
            self.account.positions,
            feature_cache={},
            day_index=-1
        )

        result = self.executor.execute_signals(signals, price_data, timestamp)
        print(f"[{timestamp.strftime('%H:%M:%S')}] 信号执行完成: {result}")

    def loop(self):
        while True:
            self.run_once()
            time.sleep(self.interval)


if __name__ == "__main__":
    param = {
        "initial_capital": 1_000_000,
        "entry_momentum": 0.05,
        "entry_break_ratio": 1.01,
        "entry_mfratio": 0.5,
        "min_up_streak": 2,
        "max_position_num": 4
    }
    runner = RealTimeRunner(symbol_list=["000001.SZ", "000002.SZ"], strategy_param=param, interval=5)
    runner.loop()

def run():
    import json
    with open("configs/config_template.json", "r", encoding="utf-8") as f:
        param = json.load(f)

    runner = RealTimeRunner(symbol_list=["000001.SZ", "000002.SZ"], strategy_param=param, interval=5)
    runner.loop()
