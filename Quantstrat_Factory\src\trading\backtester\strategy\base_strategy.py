# 文件路径: 06_backtester/strategy/base_strategy.py
# 作用: 定义所有策略类的抽象基类，实现策略与回测引擎的解耦。

from abc import ABC, abstractmethod
import pandas as pd
from strategy.signal_utils import ParameterConfig

class BaseStrategy(ABC):
    """
    所有策略类的抽象基类。
    
    每个具体的策略都需要继承这个类，并实现 generate_signals 方法。
    """

    def __init__(self, params: ParameterConfig):
        """
        初始化策略。
        
        :param params: 一个 ParameterConfig 对象，包含策略所需的所有参数。
        """
        self.params = params
        super().__init__()

    @abstractmethod
    def generate_signals(self, daily_data: pd.DataFrame, feature_cache: dict) -> pd.DataFrame:
        """
        生成交易信号的核心方法。这是每个子策略必须实现的方法。

        :param daily_data: 当前交易日的市场行情数据 (DataFrame)，包含多支股票。
        :param feature_cache: 包含预计算特征的字典，可以通过股票代码访问。
                              例如: feature_cache['000001.SZ']['ma5']
        :return: 一个包含信号的 DataFrame。
                 - Index: 无 (或从0开始的整数索引)
                 - Columns: 必须包含 'symbol' (股票代码) 和 'signal' (信号值，如 1, -1, 0)。
                            可以包含其他元数据，如 'price_type'。
                 示例:
                   symbol      signal
                 0  000001.SZ       1
                 1  600000.SH      -1
        """
        raise NotImplementedError("必须在子类中实现 generate_signals() 方法！")
