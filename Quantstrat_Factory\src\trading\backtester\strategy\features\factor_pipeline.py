# strategy/features/factor_pipeline.py

import pandas as pd
from typing import List

def compute_streaks(close_list: List[float]) -> (int, int):
    """
    计算连续上涨天数与下跌天数
    """
    up_streak = 0
    down_streak = 0
    for i in range(len(close_list) - 1, 0, -1):
        if close_list[i] > close_list[i - 1]:
            if down_streak > 0:
                break
            up_streak += 1
        elif close_list[i] < close_list[i - 1]:
            if up_streak > 0:
                break
            down_streak += 1
        else:
            break
    return up_streak, down_streak

def compute_factors(df_symbol: pd.DataFrame, idx: int = -1) -> dict:
    """
    输入单股票的K线数据，输出指定位置的一组标准化因子字典
    参数：
        df_symbol: 单只股票的历史K线数据（含momentum, ma5, ma10等字段）
        idx: 计算因子的目标位置索引，默认最后一行
    返回：
        dict: 包含所有所需因子的键值对
    """
    if idx >= len(df_symbol):
        return {}

    row = df_symbol.iloc[idx]
    closes = df_symbol['close'].iloc[max(0, idx - 9):idx + 1].tolist()
    up_streak, down_streak = compute_streaks(closes)

    factors = {
        "momentum": row.get("momentum", 0),
        "ma5": row.get("ma5", 0),
        "ma10": row.get("ma10", 0),
        "close": row.get("close", 0),
        "recent_high": df_symbol['high'].iloc[max(0, idx - 20):idx + 1].max(),
        "mfratio": row.get("mfratio", 0),
        "up_streak": up_streak,
        "down_streak": down_streak,
        "pct_change_5": compute_pct_change(df_symbol, idx, n=5),  # 5日涨跌幅
        "pct_change_10": compute_pct_change(df_symbol, idx, n=10),  # 10日涨跌幅

    }
    return factors

def compute_pct_change(df: pd.DataFrame, idx: int, n: int = 5) -> float:
    """
    计算 N 日涨跌幅 = 当前收盘 / N日前收盘 - 1
    """
    if idx < n or idx >= len(df):
        return 0.0
    return df['close'].iloc[idx] / df['close'].iloc[idx - n] - 1

# ==========================================
# 以下为 NumPy 特征计算函数（来自 core/__init__.py）
# ==========================================

import numpy as np

def compute_features(data):
    """
    高性能 NumPy 特征提取函数（输入为 (30,5) 数组，列为 OHLCV）
    返回：
        - ma5, ma10, vol_ma5, price_momentum, recent_high, mfratio
    """
    ohlcv = data
    close = ohlcv[:, 3]
    high = ohlcv[:, 1]
    low = ohlcv[:, 2]
    volume = ohlcv[:, 4]

    def moving_avg(arr, window):
        if len(arr) < window:
            return np.full_like(arr, np.nan)
        kernel = np.ones(window) / window
        conv = np.convolve(arr, kernel, mode='valid')
        return np.concatenate([np.full(window - 1, np.nan), conv])

    def recent_max(arr, window):
        result = np.full_like(arr, np.nan)
        for i in range(window - 1, len(arr)):
            result[i] = np.max(arr[i - window + 1:i + 1])
        return result

    ma5 = moving_avg(close, 5)
    ma10 = moving_avg(close, 10)
    vol_ma5 = moving_avg(volume, 5)

    price_momentum = np.full_like(close, np.nan)
    price_momentum[5:] = close[5:] / close[:-5]
    recent_high = recent_max(high, 20)
    mfratio = (close - low) / (high - low + 1e-6)

    return ma5, ma10, vol_ma5, price_momentum, recent_high, mfratio


def compute_features_batch(arr: np.ndarray):
    """
    批量计算特征：输入为 (N, 30, 5) 的 OHLCV 张量
    返回每支股票各自的特征，shape = (N, 30)
    """
    N = arr.shape[0]
    open_ = arr[:, :, 0]
    high = arr[:, :, 1]
    low = arr[:, :, 2]
    close = arr[:, :, 3]
    volume = arr[:, :, 4]
    # 计算移动平均线
    def moving_avg(x: np.ndarray, window: int) -> np.ndarray:
        res = np.full_like(x, np.nan)
        for i in range(window - 1, x.shape[1]):
            res[:, i] = np.nanmean(x[:, i - window + 1:i + 1], axis=1)
        return res
    # 计算最近20日最高价
    def recent_max(x: np.ndarray, window: int) -> np.ndarray:
        res = np.full_like(x, np.nan)
        for i in range(window - 1, x.shape[1]):
            res[:, i] = np.nanmax(x[:, i - window + 1:i + 1], axis=1)
        return res

    ma5 = moving_avg(close, 5)
    ma10 = moving_avg(close, 10)
    vol_ma5 = moving_avg(volume, 5)

    # 计算动量
    momentum = np.full_like(close, np.nan)
    for i in range(5, close.shape[1]):
        momentum[:, i] = (close[:, i] / close[:, i - 5]) - 1  # 滚动5日动量，可调

    # 计算换手率
    # 计算每一天的换手率 = 当日成交量 / 过去20日均量
    turnover_rate = np.full_like(volume, np.nan)
    for i in range(19, volume.shape[1]):
        avg_vol = np.mean(volume[:, i - 19:i + 1], axis=1)
        turnover_rate[:, i] = volume[:, i] / (avg_vol + 1e-6)

    mfratio = (close - low) / (high - low + 1e-6)
    recent_high = recent_max(high, 20)

    return ma5, ma10, vol_ma5, momentum, recent_high, mfratio, turnover_rate, close
