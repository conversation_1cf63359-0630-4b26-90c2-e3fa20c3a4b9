# 文件路径：strategy/performance.py
# 策略绩效分析模块：统计各类回测指标，如收益率、回撤、夏普比率等

import numpy as np
import pandas as pd
from framework.utils.logger import get_logger
from strategy.signal_utils import ParameterConfig, SignalEvent

logger = get_logger("performance")

class PerformanceAnalyzer:
    """绩效分析器：计算策略回测结果的关键指标"""
    def __init__(self, df, trades=None, benchmark_df=None):
        self.df = df.copy()
        self.trades = trades or []
        self.benchmark_df = benchmark_df
        self.df['date'] = pd.to_datetime(self.df['date'])
        self.df = self.df.sort_values('date').reset_index(drop=True)
        self.df['daily_return'] = self.df['value'].pct_change().fillna(0)
        self.df['cum_return'] = (1 + self.df['daily_return']).cumprod()
        self.returns = self.df['daily_return']
        self.cum_return = self.df['cum_return']

    def calculate(self):
        result = {}
        result['累计收益率'] = self.cum_return.iloc[-1] - 1
        result['年化收益率'] = (self.cum_return.iloc[-1]) ** (252 / len(self.df)) - 1

        # 最大回撤
        peak = self.cum_return.cummax()
        drawdown = (self.cum_return - peak) / peak
        result['最大回撤'] = drawdown.min()

        # 年化波动率
        annual_vol = self.returns.std() * np.sqrt(252)
        result['年化波动率'] = annual_vol

        # 夏普比率
        result['夏普比率'] = result['年化收益率'] / annual_vol if annual_vol != 0 else 0

        # 胜率与盈亏比（基于交易记录）
        if self.trades:
            df = pd.DataFrame(self.trades, columns=['date', 'symbol', 'direction', 'price', 'quantity'])
            df['ret'] = 0.0
            positions = {}
            for _, row in df.iterrows():
                key = row['symbol']
                if row['direction'] == 'BUY':
                    positions[key] = row['price']
                elif row['direction'] == 'SELL' and key in positions:
                    buy_price = positions.pop(key)
                    df.loc[df['symbol'] == key, 'ret'] = (row['price'] - buy_price) / buy_price

            ret_series = df['ret'][df['ret'] != 0]
            wins = ret_series[ret_series > 0]
            losses = ret_series[ret_series < 0]

            result['交易胜率'] = round(len(wins) / len(ret_series), 2) if len(ret_series) > 0 else 0.0
            result['平均盈亏比'] = wins.mean() / -losses.mean() if len(wins) > 0 and len(losses) > 0 else 0

        return result
