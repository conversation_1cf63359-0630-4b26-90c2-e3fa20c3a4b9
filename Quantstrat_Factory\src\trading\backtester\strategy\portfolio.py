# portfolio.py
# 资金账户管理模块：处理仓位、资金变化、成交记录、持仓估值等

from typing import Dict, List
from strategy.signal_utils import ParameterConfig  # ✅正确路径
from framework.events import FillEvent
from strategy.signal_utils import SignalEvent
from framework.utils.logger import get_logger
from strategy.signals.trend_signal import check_exit_by_stop  # 文件顶部添加

logger = get_logger("portfolio")

class Portfolio:
    def __init__(self, param: ParameterConfig):
        """初始化资金账户状态"""
        self.initial_capital = param.initial_capital
        self.max_holdings = getattr(param, "max_position_num", 4)
        self.param = param
        self.cash = param.initial_capital
        self.positions: Dict[str, Dict] = {}  # 当前持仓
        self.holdings = []  # 每日估值记录
        self.trades = []  # 所有交易记录
        self.daily_signals = []  # 所有信号记录
        self.stop_loss_pct = getattr(param, "stop_loss_pct", 0.05)
        self.take_profit_pct = getattr(param, "take_profit_pct", 0.10)



    def update_market(self, date, data):
        """根据每日市场数据更新当前持仓的市值估算"""
        data_map = data.set_index('symbol')
        held_symbols = list(self.positions.keys())

        # ✅ 只保留当天有数据的持仓股票
        available_symbols = [s for s in held_symbols if s in data_map.index]

        if available_symbols:
            held_data = data_map.loc[available_symbols].copy()
            held_data['quantity'] = [self.positions[s]['quantity'] for s in available_symbols]
            held_data['value'] = held_data['close'] * held_data['quantity']
            total_value = held_data['value'].sum() + self.cash

            for symbol in available_symbols:
                price = held_data.loc[symbol, 'close']
                self.positions[symbol]['highest_price'] = max(
                    self.positions[symbol].get('highest_price', price), price
                )
                self.positions[symbol]['lowest_price'] = min(
                    self.positions[symbol].get('lowest_price', price), price
                )

                # 止盈止损检测
                if check_exit_by_stop(
                    self.positions[symbol],
                    current_price=price,
                    stop_loss_pct=self.stop_loss_pct,
                    take_profit_pct=self.take_profit_pct,

                ):
                    # 构造卖出信号对象（伪代码，需与你的事件系统集成）
                    signal_event = SignalEvent(symbol=symbol, signal_type='EXIT', date=date, metadata={'reason': 'stop'})
                    self.defer_signal(signal_event)

        else:
            total_value = self.cash

        self.holdings.append({'date': date, 'value': total_value})

    def process_fill(self, fill: FillEvent):
        
        logger.info(f"[调试] 执行成交 fill: {fill.symbol}, {fill.direction}, qty={fill.quantity}, price={fill.price}")

        """处理每一笔成交事件，包括资金与持仓变动"""
        symbol = fill.symbol
        direction = fill.direction
        qty = fill.quantity
        price = fill.price
        cost = price * qty

        slip_cost = cost * self.param.slippage
        fee_cost = cost * self.param.commission
        total_cost = slip_cost + fee_cost

        if direction == 'BUY':
            if symbol in self.positions:
                return  # 忽略重复买入
            self.cash -= (cost + total_cost)
            self.positions[symbol] = {
                'entry_price': price,
                'entry_date': fill.date,
                'quantity': qty,
                'highest_price': price,
                'lowest_price': price
            }

        elif direction == 'SELL':
            if symbol in self.positions:
                proceeds = qty * price
                self.cash += proceeds - (proceeds * (self.param.slippage + self.param.commission))
                del self.positions[symbol]

        self.trades.append((fill.date, symbol, direction, price, qty))

        if getattr(fill, "force_fill", False):
            logger.info(
                f"[强制成交] {fill.date.date()} - {direction} {symbol} @ {price:.2f} x {qty}"
            )

        if self.param.verbose:
            logger.info(
                f"{fill.date.date()} - {direction} {symbol} @ {price:.2f} x {qty}, 手续费: {fee_cost:.2f}, 滑点: {slip_cost:.2f}"
            )

    def defer_signal(self, signal_event: SignalEvent):
        """延迟信号缓存接口：用于次日处理涨停/跌停信号"""
        self.daily_signals.append(signal_event)
    
    def export_trade_records(self, output_path="output/trades_detail.csv"):
        """导出所有成交记录到 CSV"""
        import pandas as pd
        df = pd.DataFrame(self.trades, columns=["date", "symbol", "direction", "price", "quantity"])
        df.to_csv(output_path, index=False)
        logger.info(f"✅ 已保存交易明细到 {output_path}")

    def process_signals(self, date, data):
        logger.info(f"🚨 正在执行 process_signals | 信号数: {len(self.daily_signals)}")

        """
        根据 daily_signals 列表，执行 entry 下单逻辑
        :param date: 当前交易日
        :param data: 当前行情数据字典（symbol -> row）
        """
        for signal in self.daily_signals:
            logger.info("✅ 已进入 for signal in daily_signals 循环")
            symbol = signal.symbol
            signal_type = signal.signal_type

            # 检查是否需要延迟买入（涨停当天不能买）
            if signal.metadata.get("delay_buy", False):
                logger.info(f"[延迟买入登记] {symbol} | 涨停收盘，次日开盘买入")
                self.defer_signal(signal)
                continue

            # 忽略非买入信号
            if signal_type != 'LONG':
                logger.info(f"[跳过建仓] {symbol} | 原因: 非买入信号 ({signal_type})")
                continue

            # 已持仓，跳过
            if symbol in self.positions:
                logger.info(f"[跳过建仓] {symbol} | 原因: 已持仓")
                continue

            # 持仓数已满，跳过
            if len(self.positions) >= self.max_holdings:
                logger.info(f"[跳过建仓] {symbol} | 原因: 已满持仓上限 ({self.max_holdings})")
                continue

            # 无当日行情数据
            if symbol not in data:
                logger.warning(f"[跳过建仓] {symbol} | 原因: 缺少当日数据")
                continue

            row = data[symbol]
            price = row.get("close")
            if not price or price <= 0:
                logger.warning(f"[跳过建仓] {symbol} | 原因: 无效价格 | price={price}")
                continue

            # 计算买入股数
            capital_per_stock = self.initial_capital / self.max_holdings
            size = capital_per_stock // price
            logger.info(f"[调试] {symbol} | size={size}, capital={capital_per_stock:.2f}, price={price:.2f}")

            if size <= 0:
                logger.info(f"[跳过建仓] {symbol} | 原因: 可买股数为 0 | price={price:.2f}, capital={capital_per_stock:.2f}")
                continue

            # 创建成交事件
            fill_event = FillEvent(symbol, price, size, 'BUY', date)

            # 执行成交处理
            self.process_fill(fill_event)
            logger.info(f"[建仓成功] {symbol} | 数量={size}, 价格={price:.2f}, 时间={date}")

        # 处理完所有信号后清空
        self.daily_signals.clear()



