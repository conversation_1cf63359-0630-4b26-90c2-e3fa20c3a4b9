# signal_utils.py
# 高性能 NumPy 批量信号调度器模块

from typing import Dict, List
import numpy as np
import pandas as pd
from dataclasses import dataclass, field
from typing import Dict
from datetime import datetime


# ✅ 定义参数配置类

class ParameterConfig:
    """
    回测参数配置类：用于统一存储回测所需的所有参数（包括已有与新增字段）
    """
    def __init__(self, **kwargs):
        self.start_date = kwargs.get("start_date")
        self.end_date = kwargs.get("end_date")
        self.initial_capital = kwargs.get("initial_capital")
        self.use_capital_factors = kwargs.get("use_capital_factors", False)
        self.verbose = kwargs.get("verbose", False)

        # 并行相关
        self.n_jobs = kwargs.get("n_jobs", -1)

        # 成交成本相关
        self.slippage = kwargs.get("slippage", 0.0)
        self.fee_rate = kwargs.get("fee_rate", 0.001)

        # 持仓控制
        self.max_position_num = kwargs.get("max_position_num", 30)
        self.max_daily_trades = kwargs.get("max_daily_trades", 20)

        # 策略逻辑控制参数
        self.stop_loss_pct = kwargs.get("stop_loss_pct", 0.05)
        self.take_profit_pct = kwargs.get("take_profit_pct", 0.1)
        self.max_holding_days = kwargs.get("max_holding_days", 5)
        self.position_ratio = kwargs.get("position_ratio", 0.5)
        self.max_positions = kwargs.get("max_positions", 2)
        self.exclude_st = kwargs.get("exclude_st", True)
        self.exclude_new = kwargs.get("exclude_new", True)
        self.listing_days_threshold = kwargs.get("listing_days_threshold", 60)
        self.commission = kwargs.get("commission", 0.001)
        self.force_reload = kwargs.get("force_reload", False)
        self.enable_signal_filter = kwargs.get("enable_signal_filter", True)
        self.min_amount = kwargs.get("min_amount", 2_000_000)
        self.min_intraday_volatility = kwargs.get("min_intraday_volatility", 0.008)
        self.entry_momentum = kwargs.get("entry_momentum", 0.005)
        self.entry_break_ratio = kwargs.get("entry_break_ratio", 0.95)
        self.entry_mfratio = kwargs.get("entry_mfratio", 1.0)
    
    @classmethod
    def from_dict(cls, config_dict):
        """
        从字典构造参数对象，自动注入所有键值
        """
        obj = cls()
        for k, v in config_dict.items():
            setattr(obj, k, v)
        return obj


# ✅ 定义信号事件结构（与回测框架兼容）
@dataclass
class SignalEvent:
    """
    策略信号事件

    参数：
    - symbol: 股票代码
    - signal_type: 'LONG'（买入）或 'EXIT'（卖出）
    - date: 触发该信号的交易日
    - metadata: 附加信息（如是否涨停、止盈止损等）
    """
    symbol: str
    signal_type: str
    date: datetime
    metadata: Dict = field(default_factory=dict)

    def __post_init__(self):
        self.type = "SIGNAL"

# ✅ NumPy 向量化信号调度接口（留空）

def generate_signals_batch_numpy(
    daily_df: pd.DataFrame,
    history_dict: Dict[str, np.ndarray],
    param: ParameterConfig,
    generator
) -> List[SignalEvent]:
    """
    高性能版本：一次性批处理整日所有股票的信号（基于 NumPy，避免多余构造）
    """
    signals = []

    for symbol in daily_df['symbol'].unique():
        row = daily_df[daily_df['symbol'] == symbol].iloc[0]
        arr = history_dict[symbol]
        i = np.count_nonzero(~np.isnan(arr[:, 0]))
        if i < 30:
            continue  # 不足30日历史，跳过
        hist_arr = arr[i-30:i]

        s = generator.generate_signals(
            symbol=symbol,
            date=row['date'],
            open_=row['open'],
            high=row['high'],
            low=row['low'],
            close=row['close'],
            volume=row['volume'],
            arr=hist_arr,
            param=param
        )
        signals.extend(s)

    return signals
