# __init__.py (in strategy/signals/)
# 信号模块统一注册入口，便于未来扩展多种策略信号生成器

from strategy.signal_utils import ParameterConfig, SignalEvent  # ✅正确
from .trend_signal import TrendSignalGenerator

# 信号生成器注册表，可扩展为多策略支持
generators = {
    "trend": TrendSignalGenerator,
    # "mean_reversion": MeanReversionSignalGenerator,
}

def get_signal_generator(name="trend"):
    """
    获取指定策略名称对应的信号生成器类实例
    :param name: 策略名称，例如 'trend'
    :return: 信号生成器实例
    """
    cls = generators.get(name)
    if cls is None:
        raise ValueError(f"未注册的信号生成器: {name}")
    return cls()
