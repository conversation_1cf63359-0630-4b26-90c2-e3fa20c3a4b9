# 文件路径：strategy/signals/trend_signal.py

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
from collections import defaultdict
from numba import jit
from datetime import datetime, timedelta
from strategy.signal_utils import SignalEvent
from framework.utils.logger import get_logger
from strategy.features.factor_pipeline import compute_factors

@jit(nopython=True) # 使用 Numba 加速
def compute_streaks_np(close_array: np.ndarray) -> (int, int):
    """
    计算连续上涨天数与下跌天数 (NumPy 向量化版本)
    输入: NumPy 数组
    """
    if len(close_array) < 2:
        return 0, 0

    diff = np.diff(close_array)
    direction = np.sign(diff)

    up_streak = 0
    down_streak = 0

    # 计算 up_streak
    if direction[-1] > 0: # 如果最新一天是上涨
        # 找到最后一个非上涨的索引
        non_up_indices = np.where(direction != 1)[0]
        
        # 过滤掉在当前连续序列之后的索引
        relevant_non_up_indices = non_up_indices[non_up_indices < len(direction) - 1]
        
        if len(relevant_non_up_indices) == 0: # 全部上涨
            up_streak = len(direction)
        else:
            last_non_up_idx = relevant_non_up_indices[-1]
            up_streak = len(direction) - 1 - last_non_up_idx
    
    # 计算 down_streak
    if direction[-1] < 0: # 如果最新一天是下跌
        # 找到最后一个非下跌的索引
        non_down_indices = np.where(direction != -1)[0]

        # 过滤掉在当前连续序列之后的索引
        relevant_non_down_indices = non_down_indices[non_down_indices < len(direction) - 1]

        if len(relevant_non_down_indices) == 0: # 全部下跌
            down_streak = len(direction)
        else:
            last_non_down_idx = relevant_non_down_indices[-1]
            down_streak = len(direction) - 1 - last_non_down_idx
            
    return up_streak, down_streak

def compute_streaks(close_list: List[float]) -> (int, int):
    return compute_streaks_np(np.array(close_list))

def is_limit_up(open_price: float, close_price: float, prev_close: float) -> bool:
    """
    判断是否涨停
    参数:
        open_price: 开盘价
        close_price: 收盘价
        prev_close: 前一日收盘价
    返回:
        是否涨停
    """
    # 涨停通常是收盘价比前一日收盘价上涨接近10%
    # 这里使用9.7%作为阈值，考虑到可能的小数点误差
    return close_price >= prev_close * 1.097 and abs(close_price - open_price) / prev_close < 0.02

def is_limit_down(open_price: float, close_price: float, prev_close: float) -> bool:
    """
    判断是否跌停
    参数:
        open_price: 开盘价
        close_price: 收盘价
        prev_close: 前一日收盘价
    返回:
        是否跌停
    """
    # 跌停通常是收盘价比前一日收盘价下跌接近10%
    # 这里使用9.7%作为阈值，考虑到可能的小数点误差
    return close_price <= prev_close * 0.903 and abs(close_price - open_price) / prev_close < 0.02

def check_exit_by_stop(position, current_price, stop_loss_pct, take_profit_pct):
    entry_price = position.get("entry_price")
    if entry_price is None:
        return False
    if take_profit_pct > 0 and current_price >= entry_price * (1 + take_profit_pct):
        return True
    if stop_loss_pct > 0 and current_price <= entry_price * (1 - stop_loss_pct):
        return True
    return False

class TrendSignalGenerator:
    def __init__(self):
        self.limit_up_history = defaultdict(dict)  # 存储每个股票的涨停历史
        self.limit_down_history = defaultdict(dict)  # 存储每个股票的跌停历史
        self.entry_dates = {}  # 存储每个股票的买入日期
        self.limit_up_after_entry = defaultdict(int)  # 存储买入后的涨停次数
    
    def generate_signals_batch_vectorized(self, symbol_list, row_map, param, current_positions, feature_cache, day_index, index_map):
        logger = get_logger("signal_debug", to_file=False)

        entry_count, exit_count, skipped = 0, 0, 0
        signals = []
        
        # 收集所有股票的当前日期的因子数据和原始数据
        data_for_vectorization = defaultdict(list)
        valid_symbols_for_vectorization = []
        
        # 当前日期
        current_date = pd.to_datetime(row_map[symbol_list[0]]['date']).normalize() if symbol_list else None
        if current_date is None:
            return []
        
        for symbol in symbol_list:
            row = row_map[symbol]
            idx = index_map[symbol]

            # 如果K线数量太少，跳过
            if idx < 20:
                skipped += 1
                continue
            
            required_keys = ["ma5", "ma10", "vol_ma5", "momentum", "recent_high", "mfratio"]
            missing = [
                k for k in required_keys
                if k not in feature_cache[symbol]
                or not isinstance(feature_cache[symbol][k], (np.ndarray, list))
                or idx >= len(feature_cache[symbol][k])
            ]
            if missing:
                skipped += 1
                continue

            # 提取因子和原始数据
            for k in required_keys:
                data_for_vectorization[k].append(feature_cache[symbol][k][idx])
            data_for_vectorization["close"].append(row["close"])
            data_for_vectorization["open"].append(row["open"])
            data_for_vectorization["high"].append(row["high"])
            data_for_vectorization["low"].append(row["low"])
            data_for_vectorization["turnover"].append(row.get("turnover", 0))
            
            # 获取历史价格数据用于检测涨跌停
            try:
                # 获取最近5天的数据
                recent_idx = max(0, idx-5)
                close_history = feature_cache[symbol]["close"][recent_idx:idx+1]
                
                # 确保有足够的历史数据
                if len(close_history) >= 4:
                    # 检测当前日期是否涨停
                    if idx > 0 and idx < len(feature_cache[symbol]["close"]):
                        prev_close = feature_cache[symbol]["close"][idx-1]
                        curr_open = row["open"]
                        curr_close = row["close"]
                        
                        is_today_limit_up = is_limit_up(curr_open, curr_close, prev_close)
                        is_today_limit_down = is_limit_down(curr_open, curr_close, prev_close)
                        
                        self.limit_up_history[symbol][current_date] = is_today_limit_up
                        self.limit_down_history[symbol][current_date] = is_today_limit_down
                        
                        data_for_vectorization["is_limit_up_today"].append(is_today_limit_up)
                        data_for_vectorization["is_limit_down_today"].append(is_today_limit_down)
                    else:
                        data_for_vectorization["is_limit_up_today"].append(False)
                        data_for_vectorization["is_limit_down_today"].append(False)
                    
                    # 检查前三天的涨跌停情况
                    prev_dates = [current_date - timedelta(days=i) for i in range(1, 4)]
                    
                    # 初始化前三天的涨跌停状态
                    prev_limit_ups = [False, False, False]
                    prev_limit_downs = [False, False, False]
                    
                    # 检查前三天的历史数据
                    for i, prev_date in enumerate(prev_dates):
                        if idx-i-1 >= 0 and idx-i < len(feature_cache[symbol]["close"]):
                            prev_prev_close = feature_cache[symbol]["close"][idx-i-2] if idx-i-2 >= 0 else 0
                            prev_open = feature_cache[symbol]["close"][idx-i-1]  # 使用收盘价近似开盘价
                            prev_close = feature_cache[symbol]["close"][idx-i-1]
                            
                            if prev_prev_close > 0:
                                prev_limit_ups[i] = is_limit_up(prev_open, prev_close, prev_prev_close)
                                prev_limit_downs[i] = is_limit_down(prev_open, prev_close, prev_prev_close)
                                
                                self.limit_up_history[symbol][prev_date] = prev_limit_ups[i]
                                self.limit_down_history[symbol][prev_date] = prev_limit_downs[i]
                    
                    # 添加前三天的涨跌停状态
                    data_for_vectorization["prev1_limit_up"].append(prev_limit_ups[0])
                    data_for_vectorization["prev2_limit_up"].append(prev_limit_ups[1])
                    data_for_vectorization["prev3_limit_up"].append(prev_limit_ups[2])
                    data_for_vectorization["prev1_limit_down"].append(prev_limit_downs[0])
                    
            # 检查是否满足"两连板涨停，第三天跌停"的条件
                    two_ups_one_down = (
                        prev_limit_ups[2] and  # 三天前涨停
                        prev_limit_ups[1] and  # 两天前涨停
                        prev_limit_downs[0]    # 一天前跌停
                    )
                    
                    # 放宽条件：检查是否满足"一个涨停，第二天跌停"的条件
                    one_up_one_down = (
                        prev_limit_ups[1] and  # 两天前涨停
                        prev_limit_downs[0]    # 一天前跌停
                    )
                    
                    data_for_vectorization["one_up_one_down"].append(one_up_one_down)
                    
                    # 增加一个条件：检查股票是否处于低位
                    # 使用20日均线作为参考
                    if idx >= 20:
                        ma20 = np.mean(feature_cache[symbol]["close"][idx-20:idx])
                        data_for_vectorization["is_low_price"].append(row["close"] < ma20 * 0.9)  # 当前价格低于20日均线的90%
                    else:
                        data_for_vectorization["is_low_price"].append(False)
                    data_for_vectorization["two_ups_one_down"].append(two_ups_one_down)
                else:
                    # 历史数据不足
                    data_for_vectorization["is_limit_up_today"].append(False)
                    data_for_vectorization["is_limit_down_today"].append(False)
                    data_for_vectorization["prev1_limit_up"].append(False)
                    data_for_vectorization["prev2_limit_up"].append(False)
                    data_for_vectorization["prev3_limit_up"].append(False)
                    data_for_vectorization["prev1_limit_down"].append(False)
                    data_for_vectorization["two_ups_one_down"].append(False)
                    data_for_vectorization["one_up_one_down"].append(False)
                    data_for_vectorization["is_low_price"].append(False)
            except Exception as e:
                logger.debug(f"[跳过] {symbol} 处理涨跌停历史出错: {e}")
                data_for_vectorization["is_limit_up_today"].append(False)
                data_for_vectorization["is_limit_down_today"].append(False)
                data_for_vectorization["prev1_limit_up"].append(False)
                data_for_vectorization["prev2_limit_up"].append(False)
                data_for_vectorization["prev3_limit_up"].append(False)
                data_for_vectorization["prev1_limit_down"].append(False)
                data_for_vectorization["two_ups_one_down"].append(False)
                data_for_vectorization["one_up_one_down"].append(False)
                data_for_vectorization["is_low_price"].append(False)
            
            # 尝试获取 turnover_rate，如果不存在或不是数值，则跳过该股票
            try:
                tr = feature_cache[symbol]["turnover_rate"][idx]
                if not isinstance(tr, (int, float)):
                    raise ValueError("turnover_rate is not numeric")
                data_for_vectorization["turnover_rate"].append(tr)
            except (KeyError, IndexError, ValueError):
                skipped += 1
                # 移除之前添加的该股票的数据，因为要跳过它
                for k in required_keys:
                    data_for_vectorization[k].pop()
                data_for_vectorization["close"].pop()
                data_for_vectorization["open"].pop()
                data_for_vectorization["turnover"].pop()
                # data_for_vectorization["turnover_rate"].pop() # 这一行不需要，因为如果异常，turnover_rate 根本没被 append
                continue

            # 从 feature_cache 中直接获取预计算的 up_streak 和 down_streak
            try:
                data_for_vectorization["up_streak"].append(feature_cache[symbol]["up_streak"][idx])
                data_for_vectorization["down_streak"].append(feature_cache[symbol]["down_streak"][idx])
            except (KeyError, IndexError):
                skipped += 1
                # 移除之前添加的该股票的数据，因为要跳过它
                for k in required_keys:
                    data_for_vectorization[k].pop()
                data_for_vectorization["close"].pop()
                data_for_vectorization["open"].pop()
                data_for_vectorization["turnover"].pop()
                data_for_vectorization["turnover_rate"].pop() # 也要移除 turnover_rate
                continue

            valid_symbols_for_vectorization.append(symbol)

        if not valid_symbols_for_vectorization:
            logger.debug(f"[调试] 第 {day_index} 日无有效股票进行信号生成，skipped: {skipped}")
            return []

        # 将收集到的数据转换为DataFrame
        df = pd.DataFrame(data_for_vectorization, index=valid_symbols_for_vectorization)
        df.index.name = 'symbol'

        # 添加持仓信息
        df['is_holding'] = df.index.map(lambda s: s in current_positions)
        df['entry_price'] = df.index.map(lambda s: current_positions[s].get("entry_price") if s in current_positions else np.nan)
        df['entry_date'] = df.index.map(lambda s: pd.to_datetime(current_positions[s].get("entry_date")) if s in current_positions else pd.NaT)
        
        # 批量计算 holding_days
        df['holding_days'] = (pd.to_datetime(row_map[df.index[0]]['date']).normalize() - df['entry_date']).dt.days
        df.loc[~df['is_holding'], 'holding_days'] = np.nan # 非持仓股票的持仓天数设为NaN

        # 获取参数
        stop_loss_pct = getattr(param, "stop_loss_pct", 0.05)  # 止损比例
        take_profit_pct = getattr(param, "take_profit_pct", 0.1)  # 止盈比例
        max_holding_days = getattr(param, "max_holding_days", 10)  # 最大持仓天数
        min_turnover = getattr(param, "min_turnover", 1000000)  # 最小成交额
        min_turnover_rate = getattr(param, "min_turnover_rate", 0.5)  # 最小换手率

        # 向量化买入条件：放宽为"两连板涨停，第三天跌停"或"一个涨停，第二天跌停"
        df['entry_cond'] = (
            (df['two_ups_one_down'] | df['one_up_one_down'])  # 满足"两连板涨停，第三天跌停"或"一个涨停，第二天跌停"的条件
            & (~df['is_limit_up_today'])  # 今天不是涨停（因为要在开盘价买入）
            & (~df['is_limit_down_today'])  # 今天不是跌停
            & (df['turnover'] >= min_turnover * 0.5)  # 成交额要足够大，但放宽条件
            & (df['turnover_rate'] >= min_turnover_rate * 0.5)  # 换手率要足够高，但放宽条件
            & (~df['is_holding'])  # 当前没有持仓
        )

        # 更新买入后的涨停次数
        for symbol in df.index[df['is_holding']]:
            if symbol not in self.entry_dates:
                # 如果没有记录买入日期，记录当前日期为买入日期
                self.entry_dates[symbol] = current_date
                self.limit_up_after_entry[symbol] = 0
            
            # 检查今天是否涨停
            if df.loc[symbol, 'is_limit_up_today']:
                self.limit_up_after_entry[symbol] += 1
        
        # 向量化卖出条件
        # 止损条件
        stop_loss_triggered = (df['close'] <= df['entry_price'] * (1 - stop_loss_pct)) if stop_loss_pct > 0 else pd.Series(False, index=df.index)
        
        # 止盈条件
        take_profit_triggered = (df['close'] >= df['entry_price'] * (1 + take_profit_pct)) if take_profit_pct > 0 else pd.Series(False, index=df.index)
        
        # 基本止损止盈条件
        df['exit_by_stop_cond'] = (
            df['is_holding']
            & (df['entry_price'].notna())
            & (take_profit_triggered | stop_loss_triggered)
        )
        
        # 最大持仓天数条件
        df['max_holding_days_cond'] = (df['is_holding']) & (df['holding_days'] >= max_holding_days)
        
        # 自定义卖出条件：买入后三天内没有涨停则卖出
        df['no_limit_up_cond'] = df.index.map(
            lambda s: s in current_positions 
                    and s in self.entry_dates 
                    and (current_date - self.entry_dates[s]).days >= 3  # 已持有至少3天
                    and self.limit_up_after_entry[s] == 0  # 买入后没有涨停
        )
        
        # 增加一个止损条件：如果价格跌破5日均线，则卖出
        df['ma5_cross_cond'] = (
            df['is_holding']
            & (df['close'] < df['ma5'])
            & (df['holding_days'] >= 2)  # 至少持有2天
        )
        
        # 组合所有卖出条件
        df['exit_cond'] = (
            df['exit_by_stop_cond']
            | df['max_holding_days_cond']
            | df['no_limit_up_cond']
            | df['ma5_cross_cond']
        )

        # 批量生成信号
        signals = []

        # 识别买入信号
        buy_signals_df = df[df['entry_cond']].copy()
        if not buy_signals_df.empty:
            buy_signals_df['signal_type'] = 'LONG'
            buy_signals_df['date'] = pd.to_datetime(row_map[buy_signals_df.index[0]]['date']).normalize() # 所有信号日期相同
            buy_signals_df['limit_up'] = buy_signals_df['close'] >= buy_signals_df['open'] * 1.097
            
            # 构建 metadata
            buy_signals_df['metadata'] = [{} for _ in range(len(buy_signals_df))] # 初始化空字典列表
            buy_signals_df.loc[buy_signals_df['limit_up'], 'metadata'] = buy_signals_df.loc[buy_signals_df['limit_up'], 'metadata'].apply(lambda x: {**x, 'limit_up': True, 'delay_buy': True})
            buy_signals_df.loc[~buy_signals_df['limit_up'], 'metadata'] = buy_signals_df.loc[~buy_signals_df['limit_up'], 'metadata'].apply(lambda x: {**x, 'limit_up': False})

            for _, row_sig in buy_signals_df.iterrows():
                signals.append(SignalEvent(
                    symbol=row_sig.name, # symbol 是索引
                    signal_type=row_sig['signal_type'],
                    date=row_sig['date'],
                    metadata=row_sig['metadata']
                ))
            entry_count = len(buy_signals_df)

        # 识别卖出信号
        sell_signals_df = df[df['exit_cond']].copy()
        if not sell_signals_df.empty:
            sell_signals_df['signal_type'] = 'EXIT'
            sell_signals_df['date'] = pd.to_datetime(row_map[sell_signals_df.index[0]]['date']).normalize() # 所有信号日期相同
            sell_signals_df['limit_down'] = sell_signals_df['close'] <= sell_signals_df['open'] * 0.903

            # 初始化 metadata 列为字典列表
            sell_signals_df['metadata'] = [{} for _ in range(len(sell_signals_df))]

            # 根据条件设置 reason (优先级：止损止盈 > 最大持仓天数 > 无涨停 > 跌破均线)
            sell_signals_df.loc[sell_signals_df['exit_by_stop_cond'], 'metadata'] = \
                sell_signals_df.loc[sell_signals_df['exit_by_stop_cond'], 'metadata'].apply(lambda x: {**x, 'reason': 'stop'})
            
            sell_signals_df.loc[sell_signals_df['max_holding_days_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'] = \
                sell_signals_df.loc[sell_signals_df['max_holding_days_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'].apply(lambda x: {**x, 'reason': 'max_holding_days'})
            
            sell_signals_df.loc[sell_signals_df['no_limit_up_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'] = \
                sell_signals_df.loc[sell_signals_df['no_limit_up_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'].apply(lambda x: {**x, 'reason': 'no_limit_up'})
                
            sell_signals_df.loc[sell_signals_df['ma5_cross_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'] = \
                sell_signals_df.loc[sell_signals_df['ma5_cross_cond'] & sell_signals_df['metadata'].apply(lambda x: 'reason' not in x), 'metadata'].apply(lambda x: {**x, 'reason': 'ma5_cross'})

            # 设置 limit_down
            sell_signals_df.loc[sell_signals_df['limit_down'], 'metadata'] = \
                sell_signals_df.loc[sell_signals_df['limit_down'], 'metadata'].apply(lambda x: {**x, 'limit_down': True})
            sell_signals_df.loc[~sell_signals_df['limit_down'], 'metadata'] = \
                sell_signals_df.loc[~sell_signals_df['limit_down'], 'metadata'].apply(lambda x: {**x, 'limit_down': False})

            for _, row_sig in sell_signals_df.iterrows():
                signals.append(SignalEvent(
                    symbol=row_sig.name,
                    signal_type=row_sig['signal_type'],
                    date=row_sig['date'],
                    metadata=row_sig['metadata']
                ))
            exit_count = len(sell_signals_df)

        logger.debug(f"[调试] 第 {day_index} 日生成信号数: {len(signals)}")
        logger.debug(f"[诊断] entry: {entry_count}，exit: {exit_count}，skipped: {skipped}")
        return signals
