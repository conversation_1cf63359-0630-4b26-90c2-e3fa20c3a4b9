# 文件路径: 06_backtester/strategy/trend_following_strategy.py
# 作用: 将旧的 TrendSignalGenerator 封装成符合新策略接口的策略类。

import pandas as pd
import numpy as np

# 路径修正，确保能正确导入
import sys
from pathlib import Path
BACKTESTER_ROOT_DIR = Path(__file__).resolve().parents[1]
sys.path.append(str(BACKTESTER_ROOT_DIR))

from strategy.base_strategy import BaseStrategy
from strategy.signals.trend_signal import TrendSignalGenerator, SignalEvent
from strategy.signal_utils import ParameterConfig

class TrendFollowingStrategy(BaseStrategy):
    """
    一个具体的趋势跟踪策略实现。
    它封装了原有的 TrendSignalGenerator 的逻辑。
    """

    def __init__(self, params: ParameterConfig):
        """
        初始化策略。
        
        :param params: 一个 ParameterConfig 对象，包含策略所需的所有参数。
        """
        super().__init__(params)
        # 在策略内部实例化具体的信号生成器
        self.signal_generator = TrendSignalGenerator()
        # 注意：TrendSignalGenerator 内部有状态（如涨跌停历史），
        # 因此每个策略实例应该拥有自己的 signal_generator 实例。

    def generate_signals(self, daily_data: pd.DataFrame, feature_cache: dict, portfolio_positions: dict, current_day_index: int, history_ptr: dict) -> pd.DataFrame:
        """
        生成交易信号。
        此方法适配了 BaseStrategy 接口，并调用内部的 TrendSignalGenerator。
        
        注意：为了调用旧的 generate_signals_batch_vectorized，我们需要比 BaseStrategy 接口更多的参数，
        这表明旧接口与回测引擎耦合较深。这是一个过渡性实现。
        """
        if daily_data.empty:
            return pd.DataFrame(columns=['symbol', 'signal', 'metadata'])

        # 1. 从 daily_data 和 feature_cache 构造 TrendSignalGenerator 所需的参数
        symbol_list = daily_data['symbol'].unique().tolist()
        
        # 创建 row_map 和 index_map
        row_map = {row['symbol']: row for index, row in daily_data.iterrows()}
        
        # 这个 index_map 的逻辑比较复杂，它依赖于 history_ptr 和 feature_cache 的长度
        # 这里我们简化模拟，实际需要从 Backtester 传递更完整的状态
        index_map = {}
        valid_symbol_list = []
        for sym in symbol_list:
            if sym in feature_cache and sym in history_ptr:
                 # 确保索引不越界
                max_feature_len = len(feature_cache[sym].get("ma5", []))
                if max_feature_len > 0:
                    index_map[sym] = min(history_ptr[sym] - 1, max_feature_len - 1)
                    valid_symbol_list.append(sym)

        if not valid_symbol_list:
            return pd.DataFrame(columns=['symbol', 'signal', 'metadata'])

        # 2. 调用旧的信号生成方法
        signal_events: list[SignalEvent] = self.signal_generator.generate_signals_batch_vectorized(
            symbol_list=valid_symbol_list,
            row_map=row_map,
            param=self.params,
            current_positions=portfolio_positions,
            feature_cache=feature_cache,
            day_index=current_day_index,
            index_map=index_map
        )

        # 3. 将 SignalEvent 列表转换为约定的 DataFrame
        if not signal_events:
            return pd.DataFrame(columns=['symbol', 'signal', 'metadata'])

        signals_data = []
        for event in signal_events:
            signal_value = 0
            if event.signal_type == 'LONG':
                signal_value = 1
            elif event.signal_type == 'EXIT': # EXIT 映射为卖出信号
                signal_value = -1
            # SHORT 也可以映射为 -1
            elif event.signal_type == 'SHORT':
                signal_value = -1

            signals_data.append({
                "symbol": event.symbol,
                "signal": signal_value,
                "metadata": event.metadata
            })
            
        return pd.DataFrame(signals_data)
