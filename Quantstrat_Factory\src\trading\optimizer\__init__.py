"""
优化器模块。

提供参数优化和策略搜索功能，包括：
- 网格搜索优化
- 贝叶斯优化
- 遗传算法优化
- 粒子群优化
- 多目标优化
"""

from .base_optimizer import BaseOptimizer, ParameterSpace, ParameterType, OptimizationResult
from .grid_optimizer import GridOptimizer
from .bayesian_optimizer import BayesianOptimizer

__version__ = "1.0.0"
__author__ = "Quantstrat Factory Team"

__all__ = [
    "GridOptimizer",
    "BayesianOptimizer", 
    "GeneticOptimizer",
    "MultiObjectiveOptimizer",
    "OptimizerFactory"
]
