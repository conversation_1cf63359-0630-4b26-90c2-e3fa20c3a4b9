"""
优化器基类。

定义优化器的通用接口和基础功能。
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Callable, Optional, Tuple, Union
from dataclasses import dataclass, field
import numpy as np
import pandas as pd
import time
import logging
from enum import Enum


class OptimizationStatus(Enum):
    """优化状态枚举。"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ParameterType(Enum):
    """参数类型枚举。"""
    INTEGER = "integer"
    FLOAT = "float"
    CATEGORICAL = "categorical"
    BOOLEAN = "boolean"


@dataclass
class ParameterSpace:
    """参数空间定义。"""
    name: str
    param_type: ParameterType
    low: Optional[Union[int, float]] = None
    high: Optional[Union[int, float]] = None
    choices: Optional[List[Any]] = None
    default: Optional[Any] = None
    
    def __post_init__(self):
        """后处理验证。"""
        if self.param_type in [ParameterType.INTEGER, ParameterType.FLOAT]:
            if self.low is None or self.high is None:
                raise ValueError(f"数值参数 {self.name} 必须指定 low 和 high")
        elif self.param_type == ParameterType.CATEGORICAL:
            if not self.choices:
                raise ValueError(f"分类参数 {self.name} 必须指定 choices")
    
    def sample(self) -> Any:
        """随机采样参数值。"""
        if self.param_type == ParameterType.INTEGER:
            return np.random.randint(self.low, self.high + 1)
        elif self.param_type == ParameterType.FLOAT:
            return np.random.uniform(self.low, self.high)
        elif self.param_type == ParameterType.CATEGORICAL:
            return np.random.choice(self.choices)
        elif self.param_type == ParameterType.BOOLEAN:
            return np.random.choice([True, False])
        else:
            raise ValueError(f"不支持的参数类型: {self.param_type}")
    
    def validate(self, value: Any) -> bool:
        """验证参数值是否有效。"""
        if self.param_type == ParameterType.INTEGER:
            return isinstance(value, int) and self.low <= value <= self.high
        elif self.param_type == ParameterType.FLOAT:
            return isinstance(value, (int, float)) and self.low <= value <= self.high
        elif self.param_type == ParameterType.CATEGORICAL:
            return value in self.choices
        elif self.param_type == ParameterType.BOOLEAN:
            return isinstance(value, bool)
        else:
            return False


@dataclass
class OptimizationResult:
    """优化结果。"""
    best_params: Dict[str, Any]
    best_score: float
    optimization_history: List[Dict[str, Any]] = field(default_factory=list)
    status: OptimizationStatus = OptimizationStatus.NOT_STARTED
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    total_evaluations: int = 0
    
    @property
    def duration(self) -> Optional[float]:
        """优化耗时。"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class BaseOptimizer(ABC):
    """优化器基类。"""
    
    def __init__(self, 
                 parameter_space: Dict[str, ParameterSpace],
                 objective_function: Callable[[Dict[str, Any]], float],
                 maximize: bool = True,
                 random_state: Optional[int] = None):
        """
        初始化优化器。
        
        Args:
            parameter_space: 参数空间定义
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
        """
        self.parameter_space = parameter_space
        self.objective_function = objective_function
        self.maximize = maximize
        self.random_state = random_state
        
        if random_state is not None:
            np.random.seed(random_state)
        
        self.result = OptimizationResult(
            best_params={},
            best_score=float('-inf') if maximize else float('inf')
        )
        
        self.logger = logging.getLogger(self.__class__.__name__)
        self._evaluation_count = 0
    
    @abstractmethod
    def optimize(self, 
                n_trials: int = 100,
                timeout: Optional[float] = None,
                **kwargs) -> OptimizationResult:
        """
        执行优化。
        
        Args:
            n_trials: 试验次数
            timeout: 超时时间（秒）
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        pass
    
    def evaluate(self, params: Dict[str, Any]) -> float:
        """
        评估参数组合。
        
        Args:
            params: 参数字典
            
        Returns:
            目标函数值
        """
        # 验证参数
        for name, value in params.items():
            if name not in self.parameter_space:
                raise ValueError(f"未知参数: {name}")
            
            if not self.parameter_space[name].validate(value):
                raise ValueError(f"参数 {name} 的值 {value} 无效")
        
        try:
            # 调用目标函数
            score = self.objective_function(params)
            
            # 记录评估历史
            self._evaluation_count += 1
            evaluation_record = {
                'evaluation_id': self._evaluation_count,
                'params': params.copy(),
                'score': score,
                'timestamp': time.time()
            }
            self.result.optimization_history.append(evaluation_record)
            
            # 更新最佳结果
            is_better = (self.maximize and score > self.result.best_score) or \
                       (not self.maximize and score < self.result.best_score)
            
            if is_better:
                self.result.best_params = params.copy()
                self.result.best_score = score
                self.logger.info(f"发现更好的参数组合: {params}, 得分: {score:.6f}")
            
            return score
            
        except Exception as e:
            self.logger.error(f"评估参数时发生错误 {params}: {e}")
            # 返回最差分数
            return float('-inf') if self.maximize else float('inf')
    
    def sample_random_params(self) -> Dict[str, Any]:
        """随机采样参数组合。"""
        params = {}
        for name, space in self.parameter_space.items():
            params[name] = space.sample()
        return params
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化摘要。"""
        return {
            'best_params': self.result.best_params,
            'best_score': self.result.best_score,
            'total_evaluations': self.result.total_evaluations,
            'status': self.result.status.value,
            'duration': self.result.duration,
            'parameter_space_size': len(self.parameter_space)
        }
    
    def save_results(self, filepath: str) -> None:
        """保存优化结果。"""
        import json
        
        # 准备可序列化的数据
        data = {
            'best_params': self.result.best_params,
            'best_score': self.result.best_score,
            'optimization_history': self.result.optimization_history,
            'status': self.result.status.value,
            'start_time': self.result.start_time,
            'end_time': self.result.end_time,
            'total_evaluations': self.result.total_evaluations,
            'parameter_space': {
                name: {
                    'name': space.name,
                    'param_type': space.param_type.value,
                    'low': space.low,
                    'high': space.high,
                    'choices': space.choices,
                    'default': space.default
                }
                for name, space in self.parameter_space.items()
            }
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"优化结果已保存到: {filepath}")
    
    def load_results(self, filepath: str) -> None:
        """加载优化结果。"""
        import json
        
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.result.best_params = data['best_params']
        self.result.best_score = data['best_score']
        self.result.optimization_history = data['optimization_history']
        self.result.status = OptimizationStatus(data['status'])
        self.result.start_time = data['start_time']
        self.result.end_time = data['end_time']
        self.result.total_evaluations = data['total_evaluations']
        
        self.logger.info(f"优化结果已从 {filepath} 加载")


class ObjectiveFunction:
    """目标函数包装器。"""
    
    def __init__(self, 
                 strategy_function: Callable,
                 data: pd.DataFrame,
                 metric: str = 'sharpe_ratio'):
        """
        初始化目标函数。
        
        Args:
            strategy_function: 策略函数
            data: 数据
            metric: 评估指标
        """
        self.strategy_function = strategy_function
        self.data = data
        self.metric = metric
    
    def __call__(self, params: Dict[str, Any]) -> float:
        """
        调用目标函数。
        
        Args:
            params: 参数字典
            
        Returns:
            目标函数值
        """
        try:
            # 使用参数运行策略
            result = self.strategy_function(self.data, **params)
            
            # 提取指定指标
            if isinstance(result, dict):
                return result.get(self.metric, 0.0)
            elif hasattr(result, self.metric):
                return getattr(result, self.metric)
            else:
                return float(result)
                
        except Exception as e:
            logging.getLogger(__name__).error(f"目标函数执行失败: {e}")
            return float('-inf')


def create_parameter_space(config: Dict[str, Dict[str, Any]]) -> Dict[str, ParameterSpace]:
    """
    从配置创建参数空间。
    
    Args:
        config: 参数配置字典
        
    Returns:
        参数空间字典
    """
    parameter_space = {}
    
    for name, param_config in config.items():
        param_type = ParameterType(param_config['type'])
        
        space = ParameterSpace(
            name=name,
            param_type=param_type,
            low=param_config.get('low'),
            high=param_config.get('high'),
            choices=param_config.get('choices'),
            default=param_config.get('default')
        )
        
        parameter_space[name] = space
    
    return parameter_space
