"""
贝叶斯优化器。

实现基于高斯过程的贝叶斯优化算法。
"""

import time
import warnings
from typing import Dict, List, Any, Optional, Callable
import numpy as np
from scipy.optimize import minimize
from scipy.stats import norm
from .base_optimizer import BaseOptimizer, ParameterSpace, ParameterType, OptimizationStatus

# 抑制警告
warnings.filterwarnings('ignore')


class GaussianProcess:
    """简化的高斯过程实现。"""
    
    def __init__(self, kernel_type: str = 'rbf', noise: float = 1e-6):
        """
        初始化高斯过程。
        
        Args:
            kernel_type: 核函数类型
            noise: 噪声水平
        """
        self.kernel_type = kernel_type
        self.noise = noise
        self.X_train = None
        self.y_train = None
        self.alpha = None
        self.length_scale = 1.0
        self.signal_variance = 1.0
    
    def rbf_kernel(self, X1: np.ndarray, X2: np.ndarray) -> np.ndarray:
        """RBF核函数。"""
        sqdist = np.sum(X1**2, 1).reshape(-1, 1) + np.sum(X2**2, 1) - 2 * np.dot(X1, X2.T)
        return self.signal_variance * np.exp(-0.5 / self.length_scale**2 * sqdist)
    
    def fit(self, X: np.ndarray, y: np.ndarray):
        """拟合高斯过程。"""
        self.X_train = X.copy()
        self.y_train = y.copy()
        
        # 计算核矩阵
        K = self.rbf_kernel(X, X)
        K += self.noise * np.eye(len(X))
        
        # 求解线性系统
        try:
            self.alpha = np.linalg.solve(K, y)
        except np.linalg.LinAlgError:
            # 如果矩阵奇异，使用伪逆
            self.alpha = np.linalg.pinv(K) @ y
    
    def predict(self, X: np.ndarray) -> tuple:
        """预测均值和方差。"""
        if self.X_train is None:
            raise ValueError("模型未训练")
        
        # 计算核矩阵
        K_s = self.rbf_kernel(self.X_train, X)
        K_ss = self.rbf_kernel(X, X)
        K = self.rbf_kernel(self.X_train, self.X_train)
        K += self.noise * np.eye(len(self.X_train))
        
        # 预测均值
        mu = K_s.T @ self.alpha
        
        # 预测方差
        try:
            K_inv = np.linalg.inv(K)
            var = np.diag(K_ss) - np.diag(K_s.T @ K_inv @ K_s)
        except np.linalg.LinAlgError:
            var = np.diag(K_ss)
        
        # 确保方差为正
        var = np.maximum(var, 1e-8)
        
        return mu, var


class AcquisitionFunction:
    """采集函数。"""
    
    @staticmethod
    def expected_improvement(mu: np.ndarray, sigma: np.ndarray, 
                           f_best: float, xi: float = 0.01) -> np.ndarray:
        """期望改进采集函数。"""
        sigma = np.maximum(sigma, 1e-8)
        improvement = mu - f_best - xi
        Z = improvement / sigma
        ei = improvement * norm.cdf(Z) + sigma * norm.pdf(Z)
        return ei
    
    @staticmethod
    def upper_confidence_bound(mu: np.ndarray, sigma: np.ndarray, 
                             kappa: float = 2.576) -> np.ndarray:
        """置信上界采集函数。"""
        return mu + kappa * sigma
    
    @staticmethod
    def probability_of_improvement(mu: np.ndarray, sigma: np.ndarray,
                                 f_best: float, xi: float = 0.01) -> np.ndarray:
        """改进概率采集函数。"""
        sigma = np.maximum(sigma, 1e-8)
        Z = (mu - f_best - xi) / sigma
        return norm.cdf(Z)


class BayesianOptimizer(BaseOptimizer):
    """贝叶斯优化器。"""
    
    def __init__(self, 
                 parameter_space: Dict[str, ParameterSpace],
                 objective_function,
                 maximize: bool = True,
                 random_state: Optional[int] = None,
                 acquisition_function: str = 'ei',
                 n_initial_points: int = 5):
        """
        初始化贝叶斯优化器。
        
        Args:
            parameter_space: 参数空间定义
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
            acquisition_function: 采集函数类型 ('ei', 'ucb', 'pi')
            n_initial_points: 初始随机点数量
        """
        super().__init__(parameter_space, objective_function, maximize, random_state)
        self.acquisition_function = acquisition_function
        self.n_initial_points = n_initial_points
        self.gp = GaussianProcess()
        self.X_observed = []
        self.y_observed = []
    
    def _params_to_array(self, params: Dict[str, Any]) -> np.ndarray:
        """将参数字典转换为数组。"""
        array = []
        for name in sorted(self.parameter_space.keys()):
            value = params[name]
            space = self.parameter_space[name]
            
            if space.param_type == ParameterType.INTEGER:
                # 归一化到 [0, 1]
                normalized = (value - space.low) / (space.high - space.low)
            elif space.param_type == ParameterType.FLOAT:
                # 归一化到 [0, 1]
                normalized = (value - space.low) / (space.high - space.low)
            elif space.param_type == ParameterType.CATEGORICAL:
                # 独热编码
                normalized = space.choices.index(value) / (len(space.choices) - 1)
            elif space.param_type == ParameterType.BOOLEAN:
                # 布尔值转换为 0 或 1
                normalized = float(value)
            else:
                normalized = 0.0
            
            array.append(normalized)
        
        return np.array(array)
    
    def _array_to_params(self, array: np.ndarray) -> Dict[str, Any]:
        """将数组转换为参数字典。"""
        params = {}
        param_names = sorted(self.parameter_space.keys())
        
        for i, name in enumerate(param_names):
            space = self.parameter_space[name]
            normalized_value = np.clip(array[i], 0, 1)
            
            if space.param_type == ParameterType.INTEGER:
                # 反归一化并取整
                value = int(round(normalized_value * (space.high - space.low) + space.low))
                value = np.clip(value, space.low, space.high)
            elif space.param_type == ParameterType.FLOAT:
                # 反归一化
                value = normalized_value * (space.high - space.low) + space.low
            elif space.param_type == ParameterType.CATEGORICAL:
                # 从独热编码恢复
                index = int(round(normalized_value * (len(space.choices) - 1)))
                index = np.clip(index, 0, len(space.choices) - 1)
                value = space.choices[index]
            elif space.param_type == ParameterType.BOOLEAN:
                # 转换为布尔值
                value = normalized_value > 0.5
            else:
                value = normalized_value
            
            params[name] = value
        
        return params
    
    def _acquisition_function(self, X: np.ndarray) -> np.ndarray:
        """计算采集函数值。"""
        if len(self.y_observed) == 0:
            return np.random.random(len(X))
        
        # 预测
        mu, var = self.gp.predict(X)
        sigma = np.sqrt(var)
        
        # 当前最佳值
        f_best = max(self.y_observed) if self.maximize else min(self.y_observed)
        
        # 如果是最小化问题，转换为最大化
        if not self.maximize:
            mu = -mu
            f_best = -f_best
        
        # 计算采集函数
        if self.acquisition_function == 'ei':
            return AcquisitionFunction.expected_improvement(mu, sigma, f_best)
        elif self.acquisition_function == 'ucb':
            return AcquisitionFunction.upper_confidence_bound(mu, sigma)
        elif self.acquisition_function == 'pi':
            return AcquisitionFunction.probability_of_improvement(mu, sigma, f_best)
        else:
            raise ValueError(f"不支持的采集函数: {self.acquisition_function}")
    
    def _suggest_next_point(self) -> Dict[str, Any]:
        """建议下一个评估点。"""
        # 生成候选点
        n_candidates = 1000
        candidates = np.random.random((n_candidates, len(self.parameter_space)))
        
        # 计算采集函数值
        acquisition_values = self._acquisition_function(candidates)
        
        # 选择最佳候选点
        best_idx = np.argmax(acquisition_values)
        best_candidate = candidates[best_idx]
        
        return self._array_to_params(best_candidate)
    
    def optimize(self, 
                n_trials: int = 50,
                timeout: Optional[float] = None,
                **kwargs) -> 'OptimizationResult':
        """
        执行贝叶斯优化。
        
        Args:
            n_trials: 试验次数
            timeout: 超时时间（秒）
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        self.result.status = OptimizationStatus.RUNNING
        self.result.start_time = time.time()
        
        try:
            self.logger.info(f"开始贝叶斯优化，试验次数: {n_trials}")
            
            # 初始随机采样
            for i in range(min(self.n_initial_points, n_trials)):
                if timeout and (time.time() - self.result.start_time) > timeout:
                    break
                
                params = self.sample_random_params()
                score = self.evaluate(params)
                
                # 记录观测数据
                X = self._params_to_array(params)
                self.X_observed.append(X)
                self.y_observed.append(score if self.maximize else -score)
                
                self.logger.info(f"初始采样 {i+1}/{self.n_initial_points}: {score:.6f}")
            
            # 贝叶斯优化主循环
            for i in range(self.n_initial_points, n_trials):
                if timeout and (time.time() - self.result.start_time) > timeout:
                    self.logger.warning("贝叶斯优化超时")
                    break
                
                # 训练高斯过程
                if len(self.X_observed) > 0:
                    X_train = np.array(self.X_observed)
                    y_train = np.array(self.y_observed)
                    self.gp.fit(X_train, y_train)
                
                # 建议下一个点
                params = self._suggest_next_point()
                score = self.evaluate(params)
                
                # 记录观测数据
                X = self._params_to_array(params)
                self.X_observed.append(X)
                self.y_observed.append(score if self.maximize else -score)
                
                # 记录进度
                if (i + 1) % max(1, n_trials // 10) == 0:
                    progress = (i + 1) / n_trials * 100
                    self.logger.info(f"贝叶斯优化进度: {progress:.1f}% ({i + 1}/{n_trials})")
            
            self.result.status = OptimizationStatus.COMPLETED
            self.result.total_evaluations = self._evaluation_count
            
        except Exception as e:
            self.logger.error(f"贝叶斯优化失败: {e}")
            self.result.status = OptimizationStatus.FAILED
            raise
        
        finally:
            self.result.end_time = time.time()
        
        self.logger.info(f"贝叶斯优化完成，最佳参数: {self.result.best_params}, "
                        f"最佳得分: {self.result.best_score:.6f}")
        
        return self.result


# 示例使用
if __name__ == "__main__":
    # 示例目标函数
    def example_objective(params):
        x = params['x']
        y = params['y']
        return -(x - 2)**2 - (y - 3)**2  # 最大值在 (2, 3)
    
    # 参数配置
    from .base_optimizer import create_parameter_space, ParameterSpace, ParameterType
    
    parameter_space = {
        'x': ParameterSpace('x', ParameterType.FLOAT, 0, 5),
        'y': ParameterSpace('y', ParameterType.FLOAT, 0, 5)
    }
    
    # 创建优化器
    optimizer = BayesianOptimizer(
        parameter_space, 
        example_objective, 
        maximize=True,
        acquisition_function='ei',
        n_initial_points=5
    )
    
    # 执行优化
    result = optimizer.optimize(n_trials=30)
    
    print(f"最佳参数: {result.best_params}")
    print(f"最佳得分: {result.best_score}")
    print(f"总评估次数: {result.total_evaluations}")
    print(f"优化耗时: {result.duration:.2f}秒")
