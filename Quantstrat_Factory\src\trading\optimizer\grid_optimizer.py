"""
网格搜索优化器。

实现网格搜索算法，系统性地搜索参数空间。
"""

import itertools
import time
from typing import Dict, List, Any, Optional, Union
import numpy as np
from .base_optimizer import BaseOptimizer, ParameterSpace, ParameterType, OptimizationStatus


class GridOptimizer(BaseOptimizer):
    """网格搜索优化器。"""
    
    def __init__(self, 
                 parameter_space: Dict[str, ParameterSpace],
                 objective_function,
                 maximize: bool = True,
                 random_state: Optional[int] = None):
        """
        初始化网格搜索优化器。
        
        Args:
            parameter_space: 参数空间定义
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
        """
        super().__init__(parameter_space, objective_function, maximize, random_state)
        self.grid_points = self._generate_grid_points()
    
    def _generate_grid_points(self, n_points_per_dim: int = 10) -> List[Dict[str, Any]]:
        """
        生成网格点。
        
        Args:
            n_points_per_dim: 每个维度的点数
            
        Returns:
            网格点列表
        """
        param_grids = {}
        
        for name, space in self.parameter_space.items():
            if space.param_type == ParameterType.INTEGER:
                # 整数参数：生成等间隔的整数序列
                if space.high - space.low + 1 <= n_points_per_dim:
                    param_grids[name] = list(range(space.low, space.high + 1))
                else:
                    param_grids[name] = np.linspace(
                        space.low, space.high, n_points_per_dim, dtype=int
                    ).tolist()
            
            elif space.param_type == ParameterType.FLOAT:
                # 浮点参数：生成等间隔的浮点序列
                param_grids[name] = np.linspace(
                    space.low, space.high, n_points_per_dim
                ).tolist()
            
            elif space.param_type == ParameterType.CATEGORICAL:
                # 分类参数：使用所有可能的选择
                param_grids[name] = space.choices
            
            elif space.param_type == ParameterType.BOOLEAN:
                # 布尔参数：使用True和False
                param_grids[name] = [True, False]
        
        # 生成笛卡尔积
        param_names = list(param_grids.keys())
        param_values = list(param_grids.values())
        
        grid_points = []
        for combination in itertools.product(*param_values):
            point = dict(zip(param_names, combination))
            grid_points.append(point)
        
        self.logger.info(f"生成了 {len(grid_points)} 个网格点")
        return grid_points
    
    def optimize(self, 
                n_trials: Optional[int] = None,
                timeout: Optional[float] = None,
                n_points_per_dim: int = 10,
                **kwargs) -> 'OptimizationResult':
        """
        执行网格搜索优化。
        
        Args:
            n_trials: 试验次数（对于网格搜索，这个参数会被忽略）
            timeout: 超时时间（秒）
            n_points_per_dim: 每个维度的网格点数
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        self.result.status = OptimizationStatus.RUNNING
        self.result.start_time = time.time()
        
        try:
            # 重新生成网格点（如果指定了不同的点数）
            if n_points_per_dim != 10:
                self.grid_points = self._generate_grid_points(n_points_per_dim)
            
            total_points = len(self.grid_points)
            self.logger.info(f"开始网格搜索，共 {total_points} 个参数组合")
            
            for i, params in enumerate(self.grid_points):
                # 检查超时
                if timeout and (time.time() - self.result.start_time) > timeout:
                    self.logger.warning("网格搜索超时")
                    break
                
                # 评估参数组合
                score = self.evaluate(params)
                
                # 记录进度
                if (i + 1) % max(1, total_points // 10) == 0:
                    progress = (i + 1) / total_points * 100
                    self.logger.info(f"网格搜索进度: {progress:.1f}% ({i + 1}/{total_points})")
            
            self.result.status = OptimizationStatus.COMPLETED
            self.result.total_evaluations = self._evaluation_count
            
        except Exception as e:
            self.logger.error(f"网格搜索失败: {e}")
            self.result.status = OptimizationStatus.FAILED
            raise
        
        finally:
            self.result.end_time = time.time()
        
        self.logger.info(f"网格搜索完成，最佳参数: {self.result.best_params}, "
                        f"最佳得分: {self.result.best_score:.6f}")
        
        return self.result
    
    def get_grid_size(self) -> int:
        """获取网格大小。"""
        return len(self.grid_points)
    
    def get_remaining_points(self) -> List[Dict[str, Any]]:
        """获取剩余未评估的网格点。"""
        evaluated_params = [record['params'] for record in self.result.optimization_history]
        remaining = []
        
        for point in self.grid_points:
            if point not in evaluated_params:
                remaining.append(point)
        
        return remaining


class RandomGridOptimizer(BaseOptimizer):
    """随机网格搜索优化器。"""
    
    def __init__(self, 
                 parameter_space: Dict[str, ParameterSpace],
                 objective_function,
                 maximize: bool = True,
                 random_state: Optional[int] = None):
        """
        初始化随机网格搜索优化器。
        
        Args:
            parameter_space: 参数空间定义
            objective_function: 目标函数
            maximize: 是否最大化目标函数
            random_state: 随机种子
        """
        super().__init__(parameter_space, objective_function, maximize, random_state)
    
    def optimize(self, 
                n_trials: int = 100,
                timeout: Optional[float] = None,
                **kwargs) -> 'OptimizationResult':
        """
        执行随机网格搜索优化。
        
        Args:
            n_trials: 试验次数
            timeout: 超时时间（秒）
            **kwargs: 其他参数
            
        Returns:
            优化结果
        """
        self.result.status = OptimizationStatus.RUNNING
        self.result.start_time = time.time()
        
        try:
            self.logger.info(f"开始随机网格搜索，试验次数: {n_trials}")
            
            for i in range(n_trials):
                # 检查超时
                if timeout and (time.time() - self.result.start_time) > timeout:
                    self.logger.warning("随机网格搜索超时")
                    break
                
                # 随机采样参数
                params = self.sample_random_params()
                
                # 评估参数组合
                score = self.evaluate(params)
                
                # 记录进度
                if (i + 1) % max(1, n_trials // 10) == 0:
                    progress = (i + 1) / n_trials * 100
                    self.logger.info(f"随机网格搜索进度: {progress:.1f}% ({i + 1}/{n_trials})")
            
            self.result.status = OptimizationStatus.COMPLETED
            self.result.total_evaluations = self._evaluation_count
            
        except Exception as e:
            self.logger.error(f"随机网格搜索失败: {e}")
            self.result.status = OptimizationStatus.FAILED
            raise
        
        finally:
            self.result.end_time = time.time()
        
        self.logger.info(f"随机网格搜索完成，最佳参数: {self.result.best_params}, "
                        f"最佳得分: {self.result.best_score:.6f}")
        
        return self.result


def create_grid_optimizer(parameter_config: Dict[str, Dict[str, Any]],
                         objective_function,
                         optimizer_type: str = "grid",
                         maximize: bool = True,
                         random_state: Optional[int] = None) -> BaseOptimizer:
    """
    创建网格优化器的便捷函数。
    
    Args:
        parameter_config: 参数配置
        objective_function: 目标函数
        optimizer_type: 优化器类型 ("grid" 或 "random_grid")
        maximize: 是否最大化目标函数
        random_state: 随机种子
        
    Returns:
        优化器实例
    """
    from .base_optimizer import create_parameter_space
    
    parameter_space = create_parameter_space(parameter_config)
    
    if optimizer_type == "grid":
        return GridOptimizer(parameter_space, objective_function, maximize, random_state)
    elif optimizer_type == "random_grid":
        return RandomGridOptimizer(parameter_space, objective_function, maximize, random_state)
    else:
        raise ValueError(f"不支持的优化器类型: {optimizer_type}")


# 示例使用
if __name__ == "__main__":
    # 示例目标函数
    def example_objective(params):
        x = params['x']
        y = params['y']
        return -(x - 2)**2 - (y - 3)**2  # 最大值在 (2, 3)
    
    # 参数配置
    param_config = {
        'x': {'type': 'float', 'low': 0, 'high': 5},
        'y': {'type': 'float', 'low': 0, 'high': 5}
    }
    
    # 创建优化器
    optimizer = create_grid_optimizer(
        param_config, 
        example_objective, 
        optimizer_type="grid",
        maximize=True
    )
    
    # 执行优化
    result = optimizer.optimize(n_points_per_dim=11)
    
    print(f"最佳参数: {result.best_params}")
    print(f"最佳得分: {result.best_score}")
    print(f"总评估次数: {result.total_evaluations}")
    print(f"优化耗时: {result.duration:.2f}秒")
