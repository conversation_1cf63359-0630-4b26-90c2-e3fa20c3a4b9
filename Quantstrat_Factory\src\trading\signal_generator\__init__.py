"""
信号生成器模块 (Signal Generator)

该模块负责将有效的因子转换为可执行的交易信号。

主要功能:
- 因子信号转换
- 信号过滤和优化
- 多因子信号合成
- 信号强度评估
- 交易时机判断

作者: Quantstrat Factory Team
版本: 1.0.0
"""

from .signal_generator import SignalGenerator
from .signal_combiner import SignalCombiner
from .signal_filters import SignalFilter, VolatilityFilter, LiquidityFilter
from .signal_evaluator import SignalEvaluator

__version__ = "1.0.0"
__author__ = "Quantstrat Factory Team"

__all__ = [
    "SignalGenerator",
    "SignalCombiner", 
    "SignalFilter",
    "VolatilityFilter",
    "LiquidityFilter",
    "SignalEvaluator"
]
