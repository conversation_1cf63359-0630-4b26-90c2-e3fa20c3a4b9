"""
信号合成器模块。

该模块实现多因子信号的合成和优化功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Literal, Tuple
from abc import ABC, abstractmethod
import warnings
from scipy.optimize import minimize
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.ensemble import RandomForestRegressor


class BaseSignalCombiner(ABC):
    """信号合成器基类。"""
    
    @abstractmethod
    def combine_signals(self, signals_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        合成多个信号。
        
        Args:
            signals_dict: 信号字典，键为信号名称，值为信号DataFrame
            
        Returns:
            合成后的信号DataFrame
        """
        pass


class WeightedSignalCombiner(BaseSignalCombiner):
    """
    加权信号合成器。
    
    使用预设权重对多个信号进行线性组合。
    """
    
    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        初始化加权信号合成器。
        
        Args:
            weights: 信号权重字典，如果为None则使用等权重
        """
        self.weights = weights
    
    def combine_signals(self, signals_dict: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        使用权重合成信号。
        
        Args:
            signals_dict: 信号字典
            
        Returns:
            合成后的信号DataFrame
        """
        if not signals_dict:
            raise ValueError("signals_dict不能为空")
        
        # 获取第一个信号作为基础框架
        base_signal = list(signals_dict.values())[0].copy()
        
        # 验证所有信号的结构一致性
        self._validate_signal_consistency(signals_dict)
        
        # 设置权重
        if self.weights is None:
            # 等权重
            weights = {name: 1.0 / len(signals_dict) for name in signals_dict.keys()}
        else:
            weights = self.weights.copy()
            # 归一化权重
            total_weight = sum(weights.values())
            weights = {k: v / total_weight for k, v in weights.items()}
        
        # 合成信号
        combined_signal = pd.Series(0.0, index=base_signal.index)
        
        for signal_name, signal_df in signals_dict.items():
            weight = weights.get(signal_name, 0.0)
            combined_signal += weight * signal_df['signal']
        
        # 将合成信号添加到结果中
        result = base_signal.copy()
        result['signal'] = combined_signal
        result['combined_signal_raw'] = combined_signal  # 保留原始合成值
        
        return result
    
    def _validate_signal_consistency(self, signals_dict: Dict[str, pd.DataFrame]):
        """验证信号一致性。"""
        if len(signals_dict) < 2:
            return
        
        base_columns = set(['datetime', 'symbol'])
        base_shape = None
        
        for name, signal_df in signals_dict.items():
            if not base_columns.issubset(signal_df.columns):
                raise ValueError(f"信号 {name} 缺少必需的列: {base_columns}")
            
            if 'signal' not in signal_df.columns:
                raise ValueError(f"信号 {name} 缺少'signal'列")
            
            if base_shape is None:
                base_shape = signal_df.shape[0]
            elif signal_df.shape[0] != base_shape:
                warnings.warn(f"信号 {name} 的行数与其他信号不一致")


class OptimizedSignalCombiner(BaseSignalCombiner):
    """
    优化信号合成器。
    
    使用历史数据优化信号权重。
    """
    
    def __init__(self, 
                 optimization_method: Literal['sharpe', 'return', 'ic'] = 'sharpe',
                 lookback_periods: int = 252,
                 rebalance_frequency: int = 63):
        """
        初始化优化信号合成器。
        
        Args:
            optimization_method: 优化目标
            lookback_periods: 回看期数
            rebalance_frequency: 再平衡频率
        """
        self.optimization_method = optimization_method
        self.lookback_periods = lookback_periods
        self.rebalance_frequency = rebalance_frequency
        self.optimal_weights_history = []
    
    def combine_signals(self, 
                       signals_dict: Dict[str, pd.DataFrame],
                       return_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        使用优化权重合成信号。
        
        Args:
            signals_dict: 信号字典
            return_data: 收益率数据，用于权重优化
            
        Returns:
            合成后的信号DataFrame
        """
        if return_data is None:
            warnings.warn("未提供收益率数据，使用等权重合成")
            combiner = WeightedSignalCombiner()
            return combiner.combine_signals(signals_dict)
        
        # 获取基础框架
        base_signal = list(signals_dict.values())[0].copy()
        dates = sorted(base_signal['datetime'].unique())
        
        # 初始化结果
        result = base_signal.copy()
        combined_signals = []
        
        # 滚动优化权重
        for i, current_date in enumerate(dates):
            if i < self.lookback_periods:
                # 初期使用等权重
                weights = {name: 1.0 / len(signals_dict) for name in signals_dict.keys()}
            elif i % self.rebalance_frequency == 0:
                # 重新优化权重
                start_idx = max(0, i - self.lookback_periods)
                optimization_dates = dates[start_idx:i]
                weights = self._optimize_weights(signals_dict, return_data, optimization_dates)
                self.optimal_weights_history.append({
                    'date': current_date,
                    'weights': weights.copy()
                })
            # 否则使用上一期权重
            
            # 计算当期合成信号
            current_data = {name: df[df['datetime'] == current_date] 
                           for name, df in signals_dict.items()}
            
            if all(len(data) > 0 for data in current_data.values()):
                combined_value = sum(weights[name] * data['signal'].values[0] 
                                   for name, data in current_data.items() 
                                   if len(data) > 0)
                combined_signals.extend([combined_value] * len(current_data[list(current_data.keys())[0]]))
            else:
                combined_signals.extend([0.0] * len(base_signal[base_signal['datetime'] == current_date]))
        
        result['signal'] = combined_signals[:len(result)]
        return result
    
    def _optimize_weights(self, 
                         signals_dict: Dict[str, pd.DataFrame],
                         return_data: pd.DataFrame,
                         optimization_dates: List) -> Dict[str, float]:
        """
        优化信号权重。
        
        Args:
            signals_dict: 信号字典
            return_data: 收益率数据
            optimization_dates: 优化期间的日期列表
            
        Returns:
            优化后的权重字典
        """
        # 准备优化数据
        signal_names = list(signals_dict.keys())
        n_signals = len(signal_names)
        
        # 提取优化期间的信号和收益率
        opt_signals = []
        opt_returns = []
        
        for date in optimization_dates:
            date_signals = []
            date_returns = []
            
            for name in signal_names:
                signal_data = signals_dict[name][signals_dict[name]['datetime'] == date]
                return_subset = return_data[return_data['datetime'] == date]
                
                if len(signal_data) > 0 and len(return_subset) > 0:
                    # 合并信号和收益率数据
                    merged = pd.merge(signal_data[['symbol', 'signal']], 
                                    return_subset[['symbol', 'fwd_return_1d']], 
                                    on='symbol', how='inner')
                    
                    if len(merged) > 0:
                        date_signals.append(merged['signal'].values)
                        if len(date_returns) == 0:
                            date_returns = merged['fwd_return_1d'].values
            
            if date_signals and len(date_returns) > 0:
                opt_signals.append(np.column_stack(date_signals))
                opt_returns.append(date_returns)
        
        if not opt_signals:
            # 如果没有有效数据，返回等权重
            return {name: 1.0 / n_signals for name in signal_names}
        
        # 合并所有期间的数据
        X = np.vstack(opt_signals)  # 信号矩阵
        y = np.concatenate(opt_returns)  # 收益率向量
        
        # 定义优化目标函数
        def objective(weights):
            portfolio_signals = X @ weights
            portfolio_returns = portfolio_signals * y
            
            if self.optimization_method == 'return':
                return -np.mean(portfolio_returns)
            elif self.optimization_method == 'sharpe':
                mean_ret = np.mean(portfolio_returns)
                std_ret = np.std(portfolio_returns)
                return -(mean_ret / std_ret) if std_ret > 0 else -mean_ret
            elif self.optimization_method == 'ic':
                ic = np.corrcoef(portfolio_signals, y)[0, 1]
                return -ic if not np.isnan(ic) else 0
        
        # 约束条件：权重和为1，权重非负
        constraints = [{'type': 'eq', 'fun': lambda w: np.sum(w) - 1}]
        bounds = [(0, 1) for _ in range(n_signals)]
        
        # 初始权重
        initial_weights = np.ones(n_signals) / n_signals
        
        # 执行优化
        try:
            result = minimize(objective, initial_weights, 
                            method='SLSQP', bounds=bounds, constraints=constraints)
            
            if result.success:
                optimal_weights = result.x
            else:
                optimal_weights = initial_weights
        except:
            optimal_weights = initial_weights
        
        return {name: weight for name, weight in zip(signal_names, optimal_weights)}


class MLSignalCombiner(BaseSignalCombiner):
    """
    机器学习信号合成器。
    
    使用机器学习模型合成信号。
    """
    
    def __init__(self, 
                 model_type: Literal['linear', 'ridge', 'lasso', 'rf'] = 'ridge',
                 model_params: Optional[Dict] = None):
        """
        初始化ML信号合成器。
        
        Args:
            model_type: 模型类型
            model_params: 模型参数
        """
        self.model_type = model_type
        self.model_params = model_params or {}
        self.model = None
        self._create_model()
    
    def _create_model(self):
        """创建机器学习模型。"""
        if self.model_type == 'linear':
            self.model = LinearRegression(**self.model_params)
        elif self.model_type == 'ridge':
            self.model = Ridge(**self.model_params)
        elif self.model_type == 'lasso':
            self.model = Lasso(**self.model_params)
        elif self.model_type == 'rf':
            self.model = RandomForestRegressor(**self.model_params)
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def combine_signals(self, 
                       signals_dict: Dict[str, pd.DataFrame],
                       return_data: Optional[pd.DataFrame] = None,
                       train_ratio: float = 0.7) -> pd.DataFrame:
        """
        使用ML模型合成信号。
        
        Args:
            signals_dict: 信号字典
            return_data: 收益率数据
            train_ratio: 训练集比例
            
        Returns:
            合成后的信号DataFrame
        """
        if return_data is None:
            raise ValueError("ML信号合成器需要收益率数据进行训练")
        
        # 准备训练数据
        X, y, base_data = self._prepare_training_data(signals_dict, return_data)
        
        if len(X) == 0:
            warnings.warn("无有效训练数据，使用等权重合成")
            combiner = WeightedSignalCombiner()
            return combiner.combine_signals(signals_dict)
        
        # 分割训练和测试集
        n_train = int(len(X) * train_ratio)
        X_train, X_test = X[:n_train], X[n_train:]
        y_train, y_test = y[:n_train], y[n_train:]
        
        # 训练模型
        self.model.fit(X_train, y_train)
        
        # 预测
        if len(X_test) > 0:
            y_pred = self.model.predict(X_test)
        else:
            y_pred = []
        
        # 构建结果
        result = base_data.copy()
        
        # 为训练期使用实际收益率，为测试期使用预测值
        combined_signals = np.concatenate([y_train, y_pred])[:len(result)]
        result['signal'] = combined_signals
        
        return result
    
    def _prepare_training_data(self, 
                              signals_dict: Dict[str, pd.DataFrame],
                              return_data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, pd.DataFrame]:
        """
        准备训练数据。
        
        Returns:
            (特征矩阵, 目标向量, 基础数据)
        """
        # 获取所有日期
        all_dates = set()
        for signal_df in signals_dict.values():
            all_dates.update(signal_df['datetime'].unique())
        all_dates = sorted(all_dates)
        
        X_list = []
        y_list = []
        base_rows = []
        
        for date in all_dates:
            # 获取当日所有信号
            date_signals = {}
            for name, signal_df in signals_dict.items():
                date_data = signal_df[signal_df['datetime'] == date]
                if len(date_data) > 0:
                    date_signals[name] = date_data
            
            # 获取当日收益率
            date_returns = return_data[return_data['datetime'] == date]
            
            if date_signals and len(date_returns) > 0:
                # 找到共同的股票
                common_symbols = set(date_returns['symbol'])
                for signal_data in date_signals.values():
                    common_symbols &= set(signal_data['symbol'])
                
                if common_symbols:
                    for symbol in common_symbols:
                        # 提取该股票的所有信号
                        signal_values = []
                        for name in sorted(date_signals.keys()):
                            signal_row = date_signals[name][date_signals[name]['symbol'] == symbol]
                            if len(signal_row) > 0:
                                signal_values.append(signal_row['signal'].iloc[0])
                            else:
                                signal_values.append(0.0)
                        
                        # 提取收益率
                        return_row = date_returns[date_returns['symbol'] == symbol]
                        if len(return_row) > 0:
                            X_list.append(signal_values)
                            y_list.append(return_row['fwd_return_1d'].iloc[0])
                            base_rows.append({
                                'datetime': date,
                                'symbol': symbol
                            })
        
        X = np.array(X_list) if X_list else np.array([]).reshape(0, len(signals_dict))
        y = np.array(y_list) if y_list else np.array([])
        base_data = pd.DataFrame(base_rows) if base_rows else pd.DataFrame()
        
        return X, y, base_data


class SignalCombiner:
    """
    主信号合成器类，整合多种合成方法。
    """
    
    def __init__(self):
        """初始化信号合成器。"""
        self.combiners = {
            'weighted': WeightedSignalCombiner,
            'optimized': OptimizedSignalCombiner,
            'ml': MLSignalCombiner
        }
    
    def combine_signals(self,
                       signals_dict: Dict[str, pd.DataFrame],
                       method: str = 'weighted',
                       **kwargs) -> pd.DataFrame:
        """
        合成信号。
        
        Args:
            signals_dict: 信号字典
            method: 合成方法
            **kwargs: 传递给具体合成器的参数
            
        Returns:
            合成后的信号DataFrame
        """
        if method not in self.combiners:
            raise ValueError(f"未知的合成方法: {method}")
        
        combiner_class = self.combiners[method]
        combiner = combiner_class(**kwargs)
        
        return combiner.combine_signals(signals_dict, **kwargs)
