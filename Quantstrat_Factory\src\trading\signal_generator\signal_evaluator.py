"""
信号评估器模块。

该模块实现信号质量评估和性能分析功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import warnings
from scipy import stats


class SignalEvaluator:
    """
    信号评估器，用于评估交易信号的质量和性能。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化信号评估器。
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
    
    def evaluate_signal_performance(self,
                                  signal_data: pd.DataFrame,
                                  return_col: str = 'fwd_return_1d',
                                  benchmark_return: Optional[pd.Series] = None) -> Dict[str, any]:
        """
        评估信号性能。
        
        Args:
            signal_data: 包含信号和收益率的DataFrame
            return_col: 收益率列名
            benchmark_return: 基准收益率序列
            
        Returns:
            信号性能评估结果字典
        """
        if 'signal' not in signal_data.columns:
            raise ValueError("signal_data必须包含'signal'列")
        
        if return_col not in signal_data.columns:
            raise ValueError(f"signal_data必须包含'{return_col}'列")
        
        results = {}
        
        # 1. 基础统计
        results['basic_stats'] = self._calculate_basic_stats(signal_data, return_col)
        
        # 2. 收益率分析
        results['return_analysis'] = self._analyze_returns(signal_data, return_col)
        
        # 3. 信号质量分析
        results['signal_quality'] = self._analyze_signal_quality(signal_data, return_col)
        
        # 4. 风险分析
        results['risk_analysis'] = self._analyze_risk(signal_data, return_col)
        
        # 5. 交易成本分析
        results['trading_cost_analysis'] = self._analyze_trading_costs(signal_data)
        
        # 6. 基准比较（如果提供）
        if benchmark_return is not None:
            results['benchmark_comparison'] = self._compare_with_benchmark(
                signal_data, return_col, benchmark_return
            )
        
        # 7. 综合评分
        results['overall_score'] = self._calculate_overall_score(results)
        
        return results
    
    def _calculate_basic_stats(self, signal_data: pd.DataFrame, return_col: str) -> Dict[str, float]:
        """计算基础统计指标。"""
        stats = {}
        
        # 信号覆盖率
        stats['signal_coverage'] = (signal_data['signal'] != 0).mean()
        stats['long_coverage'] = (signal_data['signal'] > 0).mean()
        stats['short_coverage'] = (signal_data['signal'] < 0).mean()
        
        # 信号强度分布
        non_zero_signals = signal_data[signal_data['signal'] != 0]['signal']
        if len(non_zero_signals) > 0:
            stats['signal_mean'] = non_zero_signals.mean()
            stats['signal_std'] = non_zero_signals.std()
            stats['signal_skew'] = non_zero_signals.skew()
            stats['signal_kurt'] = non_zero_signals.kurtosis()
        else:
            stats.update({
                'signal_mean': 0, 'signal_std': 0, 
                'signal_skew': 0, 'signal_kurt': 0
            })
        
        # 时间分布
        dates = signal_data['datetime'].nunique()
        symbols = signal_data['symbol'].nunique()
        stats['total_observations'] = len(signal_data)
        stats['unique_dates'] = dates
        stats['unique_symbols'] = symbols
        stats['avg_signals_per_date'] = len(signal_data) / dates if dates > 0 else 0
        
        return stats
    
    def _analyze_returns(self, signal_data: pd.DataFrame, return_col: str) -> Dict[str, float]:
        """分析收益率表现。"""
        # 计算信号加权收益率
        signal_returns = signal_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).sum() / x['signal'].abs().sum() 
            if x['signal'].abs().sum() > 0 else 0,
            include_groups=False
        )
        
        # 分别计算多空收益率
        long_data = signal_data[signal_data['signal'] > 0]
        short_data = signal_data[signal_data['signal'] < 0]
        
        long_returns = long_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).mean(),
            include_groups=False
        ) if len(long_data) > 0 else pd.Series([])
        
        short_returns = short_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).mean(),
            include_groups=False
        ) if len(short_data) > 0 else pd.Series([])
        
        analysis = {}
        
        # 总体收益率分析
        if len(signal_returns) > 0:
            analysis['total_return'] = signal_returns.sum()
            analysis['mean_daily_return'] = signal_returns.mean()
            analysis['return_volatility'] = signal_returns.std()
            analysis['sharpe_ratio'] = (signal_returns.mean() / signal_returns.std() 
                                      if signal_returns.std() > 0 else 0)
            analysis['max_drawdown'] = self._calculate_max_drawdown(signal_returns)
            analysis['win_rate'] = (signal_returns > 0).mean()
            analysis['profit_loss_ratio'] = self._calculate_profit_loss_ratio(signal_returns)
        
        # 多头收益率分析
        if len(long_returns) > 0:
            analysis['long_mean_return'] = long_returns.mean()
            analysis['long_volatility'] = long_returns.std()
            analysis['long_sharpe'] = (long_returns.mean() / long_returns.std() 
                                     if long_returns.std() > 0 else 0)
            analysis['long_win_rate'] = (long_returns > 0).mean()
        
        # 空头收益率分析
        if len(short_returns) > 0:
            analysis['short_mean_return'] = short_returns.mean()
            analysis['short_volatility'] = short_returns.std()
            analysis['short_sharpe'] = (short_returns.mean() / short_returns.std() 
                                      if short_returns.std() > 0 else 0)
            analysis['short_win_rate'] = (short_returns > 0).mean()
        
        return analysis
    
    def _analyze_signal_quality(self, signal_data: pd.DataFrame, return_col: str) -> Dict[str, float]:
        """分析信号质量。"""
        quality = {}
        
        # 信号与收益率的相关性
        non_zero_data = signal_data[signal_data['signal'] != 0]
        if len(non_zero_data) > 0:
            correlation = non_zero_data['signal'].corr(non_zero_data[return_col])
            quality['signal_return_correlation'] = correlation if not np.isnan(correlation) else 0
        else:
            quality['signal_return_correlation'] = 0
        
        # 按日期计算IC
        ic_series = signal_data.groupby('datetime').apply(
            lambda x: x['signal'].corr(x[return_col]) if len(x) > 1 else np.nan,
            include_groups=False
        ).dropna()
        
        if len(ic_series) > 0:
            quality['ic_mean'] = ic_series.mean()
            quality['ic_std'] = ic_series.std()
            quality['icir'] = ic_series.mean() / ic_series.std() if ic_series.std() > 0 else 0
            quality['ic_win_rate'] = (ic_series > 0).mean()
            
            # IC的t检验
            if len(ic_series) > 1:
                t_stat, p_value = stats.ttest_1samp(ic_series, 0)
                quality['ic_t_stat'] = t_stat
                quality['ic_p_value'] = p_value
                quality['ic_significant'] = p_value < 0.05
        
        # 信号稳定性
        signal_by_date = signal_data.groupby('datetime')['signal'].apply(
            lambda x: x.abs().mean()
        )
        quality['signal_stability'] = 1 - (signal_by_date.std() / signal_by_date.mean() 
                                          if signal_by_date.mean() > 0 else 0)
        
        return quality
    
    def _analyze_risk(self, signal_data: pd.DataFrame, return_col: str) -> Dict[str, float]:
        """分析风险指标。"""
        # 计算组合收益率
        portfolio_returns = signal_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).sum() / x['signal'].abs().sum() 
            if x['signal'].abs().sum() > 0 else 0,
            include_groups=False
        )
        
        risk = {}
        
        if len(portfolio_returns) > 0:
            # VaR计算
            risk['var_95'] = portfolio_returns.quantile(0.05)
            risk['var_99'] = portfolio_returns.quantile(0.01)
            
            # CVaR计算
            var_95 = risk['var_95']
            var_99 = risk['var_99']
            risk['cvar_95'] = portfolio_returns[portfolio_returns <= var_95].mean()
            risk['cvar_99'] = portfolio_returns[portfolio_returns <= var_99].mean()
            
            # 下行风险
            negative_returns = portfolio_returns[portfolio_returns < 0]
            risk['downside_deviation'] = negative_returns.std() if len(negative_returns) > 0 else 0
            
            # Calmar比率
            max_dd = self._calculate_max_drawdown(portfolio_returns)
            annual_return = portfolio_returns.mean() * 252  # 假设252个交易日
            risk['calmar_ratio'] = annual_return / abs(max_dd) if max_dd != 0 else 0
        
        return risk
    
    def _analyze_trading_costs(self, signal_data: pd.DataFrame) -> Dict[str, float]:
        """分析交易成本。"""
        costs = {}
        
        # 计算换手率
        def calculate_turnover(group):
            signals = group.sort_values('datetime')['signal']
            changes = (signals != signals.shift(1)).sum()
            return changes / len(signals) if len(signals) > 1 else 0
        
        turnover_by_symbol = signal_data.groupby('symbol').apply(
            calculate_turnover, include_groups=False
        )
        costs['average_turnover'] = turnover_by_symbol.mean()
        costs['max_turnover'] = turnover_by_symbol.max()
        costs['min_turnover'] = turnover_by_symbol.min()
        
        # 估算交易成本影响（假设单边成本0.1%）
        trading_cost_rate = 0.001
        costs['estimated_trading_cost'] = costs['average_turnover'] * trading_cost_rate
        
        return costs
    
    def _compare_with_benchmark(self, 
                               signal_data: pd.DataFrame,
                               return_col: str,
                               benchmark_return: pd.Series) -> Dict[str, float]:
        """与基准比较。"""
        # 计算策略收益率
        strategy_returns = signal_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).sum() / x['signal'].abs().sum() 
            if x['signal'].abs().sum() > 0 else 0,
            include_groups=False
        )
        
        # 对齐时间序列
        common_dates = strategy_returns.index.intersection(benchmark_return.index)
        strategy_aligned = strategy_returns.loc[common_dates]
        benchmark_aligned = benchmark_return.loc[common_dates]
        
        comparison = {}
        
        if len(strategy_aligned) > 0:
            # 超额收益
            excess_returns = strategy_aligned - benchmark_aligned
            comparison['excess_return_mean'] = excess_returns.mean()
            comparison['excess_return_std'] = excess_returns.std()
            comparison['information_ratio'] = (excess_returns.mean() / excess_returns.std() 
                                             if excess_returns.std() > 0 else 0)
            
            # Beta和Alpha
            if benchmark_aligned.std() > 0:
                beta = strategy_aligned.cov(benchmark_aligned) / benchmark_aligned.var()
                alpha = strategy_aligned.mean() - beta * benchmark_aligned.mean()
                comparison['beta'] = beta
                comparison['alpha'] = alpha
            
            # 胜率
            comparison['outperformance_rate'] = (excess_returns > 0).mean()
        
        return comparison
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """计算最大回撤。"""
        if len(returns) == 0:
            return 0
        
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def _calculate_profit_loss_ratio(self, returns: pd.Series) -> float:
        """计算盈亏比。"""
        positive_returns = returns[returns > 0]
        negative_returns = returns[returns < 0]
        
        if len(positive_returns) == 0 or len(negative_returns) == 0:
            return 0
        
        avg_profit = positive_returns.mean()
        avg_loss = abs(negative_returns.mean())
        
        return avg_profit / avg_loss if avg_loss > 0 else 0
    
    def _calculate_overall_score(self, results: Dict) -> Dict[str, float]:
        """计算综合评分。"""
        score = {}
        
        # 收益率评分 (0-30分)
        return_analysis = results.get('return_analysis', {})
        sharpe_ratio = return_analysis.get('sharpe_ratio', 0)
        return_score = min(30, max(0, sharpe_ratio * 15 + 15))
        
        # 信号质量评分 (0-30分)
        signal_quality = results.get('signal_quality', {})
        icir = signal_quality.get('icir', 0)
        ic_win_rate = signal_quality.get('ic_win_rate', 0.5)
        quality_score = min(30, abs(icir) * 10 + (ic_win_rate - 0.5) * 40)
        
        # 风险控制评分 (0-25分)
        risk_analysis = results.get('risk_analysis', {})
        max_dd = abs(return_analysis.get('max_drawdown', 0))
        risk_score = min(25, max(0, 25 - max_dd * 500))
        
        # 交易成本评分 (0-15分)
        trading_costs = results.get('trading_cost_analysis', {})
        turnover = trading_costs.get('average_turnover', 1)
        cost_score = min(15, max(0, 15 - turnover * 15))
        
        # 总分
        total_score = return_score + quality_score + risk_score + cost_score
        
        score.update({
            'return_score': return_score,
            'quality_score': quality_score,
            'risk_score': risk_score,
            'cost_score': cost_score,
            'total_score': total_score,
            'max_score': 100
        })
        
        return score
