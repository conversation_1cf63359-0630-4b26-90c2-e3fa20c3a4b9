"""
信号过滤器模块。

该模块实现各种信号过滤和优化功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Literal, Tuple
from abc import ABC, abstractmethod
import warnings


class BaseSignalFilter(ABC):
    """信号过滤器基类。"""
    
    @abstractmethod
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        过滤信号。
        
        Args:
            signal_data: 包含信号的DataFrame
            
        Returns:
            过滤后的信号DataFrame
        """
        pass


class VolatilityFilter(BaseSignalFilter):
    """
    波动率过滤器。
    
    根据股票的历史波动率过滤信号。
    """
    
    def __init__(self, 
                 volatility_col: str = 'volatility',
                 min_volatility: Optional[float] = None,
                 max_volatility: Optional[float] = None,
                 volatility_percentile_range: Optional[Tuple[float, float]] = None):
        """
        初始化波动率过滤器。
        
        Args:
            volatility_col: 波动率列名
            min_volatility: 最小波动率阈值
            max_volatility: 最大波动率阈值
            volatility_percentile_range: 波动率百分位数范围，如(10, 90)
        """
        self.volatility_col = volatility_col
        self.min_volatility = min_volatility
        self.max_volatility = max_volatility
        self.volatility_percentile_range = volatility_percentile_range
    
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        根据波动率过滤信号。
        
        Args:
            signal_data: 包含信号和波动率的DataFrame
            
        Returns:
            过滤后的信号DataFrame
        """
        if self.volatility_col not in signal_data.columns:
            warnings.warn(f"未找到波动率列 {self.volatility_col}，跳过波动率过滤")
            return signal_data.copy()
        
        result = signal_data.copy()
        
        # 计算过滤条件
        filter_condition = pd.Series(True, index=result.index)
        
        if self.min_volatility is not None:
            filter_condition &= (result[self.volatility_col] >= self.min_volatility)
        
        if self.max_volatility is not None:
            filter_condition &= (result[self.volatility_col] <= self.max_volatility)
        
        if self.volatility_percentile_range is not None:
            low_pct, high_pct = self.volatility_percentile_range
            
            # 按日期分组计算百分位数
            def calc_percentile_filter(group):
                vol_values = group[self.volatility_col]
                low_thresh = vol_values.quantile(low_pct / 100)
                high_thresh = vol_values.quantile(high_pct / 100)
                return (vol_values >= low_thresh) & (vol_values <= high_thresh)
            
            percentile_filter = result.groupby('datetime').apply(
                calc_percentile_filter, include_groups=False
            ).reset_index(level=0, drop=True)
            
            filter_condition &= percentile_filter
        
        # 应用过滤条件
        result.loc[~filter_condition, 'signal'] = 0
        
        return result


class LiquidityFilter(BaseSignalFilter):
    """
    流动性过滤器。
    
    根据股票的流动性指标过滤信号。
    """
    
    def __init__(self,
                 volume_col: str = 'volume',
                 turnover_col: Optional[str] = None,
                 min_volume: Optional[float] = None,
                 min_turnover: Optional[float] = None,
                 liquidity_percentile: Optional[float] = None):
        """
        初始化流动性过滤器。
        
        Args:
            volume_col: 成交量列名
            turnover_col: 换手率列名
            min_volume: 最小成交量阈值
            min_turnover: 最小换手率阈值
            liquidity_percentile: 流动性百分位数阈值，如20表示只保留前80%流动性的股票
        """
        self.volume_col = volume_col
        self.turnover_col = turnover_col
        self.min_volume = min_volume
        self.min_turnover = min_turnover
        self.liquidity_percentile = liquidity_percentile
    
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        根据流动性过滤信号。
        
        Args:
            signal_data: 包含信号和流动性指标的DataFrame
            
        Returns:
            过滤后的信号DataFrame
        """
        result = signal_data.copy()
        filter_condition = pd.Series(True, index=result.index)
        
        # 成交量过滤
        if self.volume_col in result.columns and self.min_volume is not None:
            filter_condition &= (result[self.volume_col] >= self.min_volume)
        
        # 换手率过滤
        if (self.turnover_col and self.turnover_col in result.columns 
            and self.min_turnover is not None):
            filter_condition &= (result[self.turnover_col] >= self.min_turnover)
        
        # 百分位数过滤
        if self.liquidity_percentile is not None:
            if self.volume_col in result.columns:
                def calc_volume_filter(group):
                    volume_thresh = group[self.volume_col].quantile(
                        (100 - self.liquidity_percentile) / 100
                    )
                    return group[self.volume_col] >= volume_thresh
                
                volume_filter = result.groupby('datetime').apply(
                    calc_volume_filter, include_groups=False
                ).reset_index(level=0, drop=True)
                
                filter_condition &= volume_filter
        
        # 应用过滤条件
        result.loc[~filter_condition, 'signal'] = 0
        
        return result


class UniverseFilter(BaseSignalFilter):
    """
    股票池过滤器。
    
    根据预定义的股票池过滤信号。
    """
    
    def __init__(self, 
                 universe: Optional[List[str]] = None,
                 exclude_symbols: Optional[List[str]] = None,
                 market_cap_col: Optional[str] = None,
                 min_market_cap: Optional[float] = None,
                 top_n_by_market_cap: Optional[int] = None):
        """
        初始化股票池过滤器。
        
        Args:
            universe: 允许的股票列表
            exclude_symbols: 排除的股票列表
            market_cap_col: 市值列名
            min_market_cap: 最小市值阈值
            top_n_by_market_cap: 按市值选择前N只股票
        """
        self.universe = set(universe) if universe else None
        self.exclude_symbols = set(exclude_symbols) if exclude_symbols else set()
        self.market_cap_col = market_cap_col
        self.min_market_cap = min_market_cap
        self.top_n_by_market_cap = top_n_by_market_cap
    
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        根据股票池过滤信号。
        
        Args:
            signal_data: 包含信号的DataFrame
            
        Returns:
            过滤后的信号DataFrame
        """
        result = signal_data.copy()
        filter_condition = pd.Series(True, index=result.index)
        
        # 股票池过滤
        if self.universe is not None:
            filter_condition &= result['symbol'].isin(self.universe)
        
        # 排除股票
        if self.exclude_symbols:
            filter_condition &= ~result['symbol'].isin(self.exclude_symbols)
        
        # 市值过滤
        if (self.market_cap_col and self.market_cap_col in result.columns 
            and self.min_market_cap is not None):
            filter_condition &= (result[self.market_cap_col] >= self.min_market_cap)
        
        # 按市值选择前N只股票
        if (self.top_n_by_market_cap and self.market_cap_col 
            and self.market_cap_col in result.columns):
            
            def select_top_n_by_market_cap(group):
                top_symbols = group.nlargest(self.top_n_by_market_cap, self.market_cap_col)['symbol']
                return group['symbol'].isin(top_symbols)
            
            top_n_filter = result.groupby('datetime').apply(
                select_top_n_by_market_cap, include_groups=False
            ).reset_index(level=0, drop=True)
            
            filter_condition &= top_n_filter
        
        # 应用过滤条件
        result.loc[~filter_condition, 'signal'] = 0
        
        return result


class TurnoverFilter(BaseSignalFilter):
    """
    换手率过滤器。
    
    控制信号的换手率，避免过度交易。
    """
    
    def __init__(self, 
                 max_turnover_rate: float = 0.5,
                 lookback_periods: int = 20):
        """
        初始化换手率过滤器。
        
        Args:
            max_turnover_rate: 最大允许换手率
            lookback_periods: 计算换手率的回看期数
        """
        self.max_turnover_rate = max_turnover_rate
        self.lookback_periods = lookback_periods
    
    def filter_signals(self, signal_data: pd.DataFrame) -> pd.DataFrame:
        """
        根据换手率过滤信号。
        
        Args:
            signal_data: 包含信号的DataFrame
            
        Returns:
            过滤后的信号DataFrame
        """
        result = signal_data.copy()
        
        # 按股票分组处理
        def filter_symbol_turnover(group):
            group = group.sort_values('datetime').copy()
            signals = group['signal'].copy()
            
            # 计算滚动换手率
            for i in range(len(signals)):
                if i >= self.lookback_periods:
                    # 计算过去N期的换手率
                    past_signals = signals.iloc[i-self.lookback_periods:i]
                    changes = (past_signals != past_signals.shift(1)).sum()
                    turnover_rate = changes / self.lookback_periods
                    
                    # 如果换手率过高，保持上一期信号
                    if turnover_rate > self.max_turnover_rate:
                        signals.iloc[i] = signals.iloc[i-1]
            
            group['signal'] = signals
            return group
        
        result = result.groupby('symbol').apply(
            filter_symbol_turnover, include_groups=False
        ).reset_index(drop=True)
        
        return result


class SignalFilter:
    """
    主信号过滤器类，整合多种过滤方法。
    """
    
    def __init__(self):
        """初始化信号过滤器。"""
        self.filters = {
            'volatility': VolatilityFilter,
            'liquidity': LiquidityFilter,
            'universe': UniverseFilter,
            'turnover': TurnoverFilter
        }
    
    def apply_filters(self,
                     signal_data: pd.DataFrame,
                     filter_configs: List[Dict]) -> pd.DataFrame:
        """
        应用多个过滤器。
        
        Args:
            signal_data: 信号数据
            filter_configs: 过滤器配置列表
            
        Returns:
            过滤后的信号DataFrame
        """
        result = signal_data.copy()
        
        for config in filter_configs:
            filter_type = config.get('type')
            if filter_type not in self.filters:
                warnings.warn(f"未知的过滤器类型: {filter_type}")
                continue
            
            # 提取过滤器参数
            params = {k: v for k, v in config.items() if k != 'type'}
            
            # 创建并应用过滤器
            filter_class = self.filters[filter_type]
            filter_instance = filter_class(**params)
            result = filter_instance.filter_signals(result)
        
        return result
    
    def register_filter(self, name: str, filter_class: type):
        """
        注册新的过滤器。
        
        Args:
            name: 过滤器名称
            filter_class: 过滤器类
        """
        if not issubclass(filter_class, BaseSignalFilter):
            raise ValueError("过滤器类必须继承自BaseSignalFilter")
        
        self.filters[name] = filter_class
