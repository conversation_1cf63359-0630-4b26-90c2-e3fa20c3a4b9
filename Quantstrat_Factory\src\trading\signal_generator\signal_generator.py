"""
信号生成器核心模块。

该模块实现了将因子值转换为交易信号的核心功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Literal, Tuple, Union
from abc import ABC, abstractmethod
import warnings
from scipy import stats


class BaseSignalGenerator(ABC):
    """信号生成器基类。"""
    
    @abstractmethod
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            
        Returns:
            包含信号的DataFrame
        """
        pass


class QuantileSignalGenerator(BaseSignalGenerator):
    """
    基于分位数的信号生成器。
    
    将因子值按分位数分组，生成多空信号。
    """
    
    def __init__(self, 
                 quantiles: int = 5,
                 long_quantile: int = 1,
                 short_quantile: int = 5,
                 neutral_quantiles: Optional[List[int]] = None):
        """
        初始化分位数信号生成器。
        
        Args:
            quantiles: 分位数数量
            long_quantile: 做多分位数（1为最小值分位数）
            short_quantile: 做空分位数
            neutral_quantiles: 中性分位数列表
        """
        self.quantiles = quantiles
        self.long_quantile = long_quantile
        self.short_quantile = short_quantile
        self.neutral_quantiles = neutral_quantiles or []
        
        # 验证参数
        if not (1 <= long_quantile <= quantiles):
            raise ValueError(f"long_quantile必须在1到{quantiles}之间")
        if not (1 <= short_quantile <= quantiles):
            raise ValueError(f"short_quantile必须在1到{quantiles}之间")
    
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        基于分位数生成信号。
        
        Args:
            factor_data: 包含'datetime', 'symbol', 'factor'列的DataFrame
            
        Returns:
            包含信号的DataFrame，新增'signal'列
        """
        if not all(col in factor_data.columns for col in ['datetime', 'symbol']):
            raise ValueError("factor_data必须包含'datetime'和'symbol'列")
        
        # 找到因子列（除了datetime和symbol之外的数值列）
        factor_cols = [col for col in factor_data.columns 
                      if col not in ['datetime', 'symbol'] 
                      and pd.api.types.is_numeric_dtype(factor_data[col])]
        
        if len(factor_cols) == 0:
            raise ValueError("未找到有效的因子列")
        
        # 如果有多个因子列，使用第一个
        factor_col = factor_cols[0]
        if len(factor_cols) > 1:
            warnings.warn(f"发现多个因子列，使用第一个: {factor_col}")
        
        result_data = factor_data.copy()
        
        # 按日期分组计算分位数
        def assign_quantile_signals(group):
            # 计算分位数
            quantile_labels = pd.qcut(
                group[factor_col], 
                q=self.quantiles, 
                labels=range(1, self.quantiles + 1),
                duplicates='drop'
            )
            
            # 生成信号
            signals = pd.Series(0, index=group.index)  # 默认为0（中性）
            
            # 做多信号
            signals[quantile_labels == self.long_quantile] = 1
            
            # 做空信号
            signals[quantile_labels == self.short_quantile] = -1
            
            # 中性信号
            for neutral_q in self.neutral_quantiles:
                signals[quantile_labels == neutral_q] = 0
            
            return signals
        
        # 按日期分组生成信号
        result_data['signal'] = result_data.groupby('datetime').apply(
            assign_quantile_signals, include_groups=False
        ).reset_index(level=0, drop=True)
        
        return result_data


class ThresholdSignalGenerator(BaseSignalGenerator):
    """
    基于阈值的信号生成器。
    
    根据因子值与设定阈值的比较生成信号。
    """
    
    def __init__(self,
                 long_threshold: float,
                 short_threshold: float,
                 method: Literal['absolute', 'percentile', 'zscore'] = 'absolute'):
        """
        初始化阈值信号生成器。
        
        Args:
            long_threshold: 做多阈值
            short_threshold: 做空阈值
            method: 阈值方法
                - 'absolute': 绝对值阈值
                - 'percentile': 百分位数阈值
                - 'zscore': Z分数阈值
        """
        self.long_threshold = long_threshold
        self.short_threshold = short_threshold
        self.method = method
        
        if method == 'absolute' and long_threshold <= short_threshold:
            raise ValueError("绝对值方法中，long_threshold应大于short_threshold")
    
    def generate_signals(self, factor_data: pd.DataFrame) -> pd.DataFrame:
        """
        基于阈值生成信号。
        
        Args:
            factor_data: 包含因子数据的DataFrame
            
        Returns:
            包含信号的DataFrame
        """
        # 找到因子列
        factor_cols = [col for col in factor_data.columns 
                      if col not in ['datetime', 'symbol'] 
                      and pd.api.types.is_numeric_dtype(factor_data[col])]
        
        if len(factor_cols) == 0:
            raise ValueError("未找到有效的因子列")
        
        factor_col = factor_cols[0]
        result_data = factor_data.copy()
        
        def generate_threshold_signals(group):
            factor_values = group[factor_col]
            
            if self.method == 'absolute':
                long_condition = factor_values >= self.long_threshold
                short_condition = factor_values <= self.short_threshold
                
            elif self.method == 'percentile':
                long_thresh = factor_values.quantile(self.long_threshold / 100)
                short_thresh = factor_values.quantile(self.short_threshold / 100)
                long_condition = factor_values >= long_thresh
                short_condition = factor_values <= short_thresh
                
            elif self.method == 'zscore':
                z_scores = stats.zscore(factor_values, nan_policy='omit')
                long_condition = z_scores >= self.long_threshold
                short_condition = z_scores <= self.short_threshold
            
            # 生成信号
            signals = pd.Series(0, index=group.index)
            signals[long_condition] = 1
            signals[short_condition] = -1
            
            return signals
        
        # 按日期分组生成信号
        result_data['signal'] = result_data.groupby('datetime').apply(
            generate_threshold_signals, include_groups=False
        ).reset_index(level=0, drop=True)
        
        return result_data


class SignalGenerator:
    """
    主信号生成器类，整合多种信号生成方法。
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化信号生成器。
        
        Args:
            config: 配置字典
        """
        self.config = config or {}
        self.generators = {}
        
        # 注册默认生成器
        self._register_default_generators()
    
    def _register_default_generators(self):
        """注册默认的信号生成器。"""
        self.generators['quantile'] = QuantileSignalGenerator
        self.generators['threshold'] = ThresholdSignalGenerator
    
    def register_generator(self, name: str, generator_class: type):
        """
        注册新的信号生成器。
        
        Args:
            name: 生成器名称
            generator_class: 生成器类
        """
        if not issubclass(generator_class, BaseSignalGenerator):
            raise ValueError("生成器类必须继承自BaseSignalGenerator")
        
        self.generators[name] = generator_class
    
    def generate_signals(self,
                        factor_data: pd.DataFrame,
                        method: str = 'quantile',
                        **kwargs) -> pd.DataFrame:
        """
        生成交易信号。
        
        Args:
            factor_data: 因子数据
            method: 信号生成方法
            **kwargs: 传递给具体生成器的参数
            
        Returns:
            包含信号的DataFrame
        """
        if method not in self.generators:
            raise ValueError(f"未知的信号生成方法: {method}")
        
        generator_class = self.generators[method]
        generator = generator_class(**kwargs)
        
        return generator.generate_signals(factor_data)
    
    def batch_generate_signals(self,
                              factor_data: pd.DataFrame,
                              methods: List[Dict]) -> Dict[str, pd.DataFrame]:
        """
        批量生成多种信号。
        
        Args:
            factor_data: 因子数据
            methods: 方法配置列表，每个元素包含'name', 'method'和参数
            
        Returns:
            信号结果字典
        """
        results = {}
        
        for method_config in methods:
            name = method_config['name']
            method = method_config['method']
            params = {k: v for k, v in method_config.items() 
                     if k not in ['name', 'method']}
            
            try:
                signals = self.generate_signals(factor_data, method, **params)
                results[name] = signals
            except Exception as e:
                warnings.warn(f"生成信号 {name} 失败: {e}")
                results[name] = None
        
        return results
    
    def evaluate_signal_quality(self, 
                               signal_data: pd.DataFrame,
                               return_col: str = 'fwd_return_1d') -> Dict[str, float]:
        """
        评估信号质量。
        
        Args:
            signal_data: 包含信号和收益率的数据
            return_col: 收益率列名
            
        Returns:
            信号质量指标字典
        """
        if 'signal' not in signal_data.columns:
            raise ValueError("signal_data必须包含'signal'列")
        
        if return_col not in signal_data.columns:
            raise ValueError(f"signal_data必须包含'{return_col}'列")
        
        # 计算信号收益率
        signal_returns = signal_data.groupby('datetime').apply(
            lambda x: (x['signal'] * x[return_col]).mean(),
            include_groups=False
        )
        
        # 计算质量指标
        metrics = {
            'signal_return_mean': signal_returns.mean(),
            'signal_return_std': signal_returns.std(),
            'signal_sharpe': signal_returns.mean() / signal_returns.std() if signal_returns.std() > 0 else 0,
            'signal_coverage': (signal_data['signal'] != 0).mean(),
            'long_ratio': (signal_data['signal'] > 0).mean(),
            'short_ratio': (signal_data['signal'] < 0).mean(),
            'turnover': self._calculate_turnover(signal_data)
        }
        
        return metrics
    
    def _calculate_turnover(self, signal_data: pd.DataFrame) -> float:
        """
        计算信号换手率。
        
        Args:
            signal_data: 信号数据
            
        Returns:
            换手率
        """
        # 按股票分组计算信号变化
        def calc_symbol_turnover(group):
            signals = group.sort_values('datetime')['signal']
            changes = (signals != signals.shift(1)).sum()
            return changes / len(signals) if len(signals) > 1 else 0
        
        turnovers = signal_data.groupby('symbol').apply(
            calc_symbol_turnover, include_groups=False
        )
        
        return turnovers.mean()
