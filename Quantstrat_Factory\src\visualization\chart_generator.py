"""
图表生成器模块。

提供各种类型图表的生成功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Union
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

# 延迟导入绘图库
def _import_plotly():
    try:
        import plotly.graph_objects as go
        import plotly.express as px
        from plotly.subplots import make_subplots
        return go, px, make_subplots
    except ImportError:
        logger.warning("Plotly未安装，某些图表功能将不可用")
        return None, None, None


class ChartGenerator:
    """图表生成器。"""
    
    def __init__(self):
        """初始化图表生成器。"""
        self.supported_chart_types = [
            'line', 'bar', 'scatter', 'candlestick', 'heatmap',
            'histogram', 'box', 'performance', 'drawdown'
        ]
        
        # 导入绘图库
        self.go, self.px, self.make_subplots = _import_plotly()
        
        if not self.go:
            logger.error("无法导入Plotly，图表功能将不可用")
    
    def create_line_chart(
        self, 
        data: pd.DataFrame, 
        x_col: str, 
        y_col: str, 
        title: str = '',
        **kwargs
    ):
        """
        创建线图。
        
        Args:
            data: 数据DataFrame
            x_col: X轴列名
            y_col: Y轴列名
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            logger.error("Plotly未安装，无法创建图表")
            return None
        
        try:
            fig = self.go.Figure()
            
            fig.add_trace(self.go.Scatter(
                x=data[x_col],
                y=data[y_col],
                mode='lines',
                name=y_col,
                line=dict(color=kwargs.get('color', 'blue'))
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title=x_col,
                yaxis_title=y_col,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建线图失败: {e}")
            return None
    
    def create_candlestick_chart(
        self, 
        data: pd.DataFrame, 
        title: str = '',
        **kwargs
    ):
        """
        创建K线图。
        
        Args:
            data: OHLCV数据
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            # 检查必要的列
            required_cols = ['open', 'high', 'low', 'close']
            if not all(col in data.columns for col in required_cols):
                logger.error(f"数据缺少必要的列: {required_cols}")
                return None
            
            # 确定日期列
            date_col = 'date'
            if 'datetime' in data.columns:
                date_col = 'datetime'
            elif 'Date' in data.columns:
                date_col = 'Date'
            
            fig = self.go.Figure(data=self.go.Candlestick(
                x=data[date_col] if date_col in data.columns else data.index,
                open=data['open'],
                high=data['high'],
                low=data['low'],
                close=data['close']
            ))
            
            fig.update_layout(
                title=title,
                yaxis_title='价格',
                template='plotly_white',
                xaxis_rangeslider_visible=False
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建K线图失败: {e}")
            return None
    
    def create_bar_chart(
        self, 
        data: pd.DataFrame, 
        x_col: str, 
        y_col: str, 
        title: str = '',
        **kwargs
    ):
        """
        创建柱状图。
        
        Args:
            data: 数据DataFrame
            x_col: X轴列名
            y_col: Y轴列名
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            fig = self.go.Figure(data=self.go.Bar(
                x=data[x_col],
                y=data[y_col],
                marker_color=kwargs.get('color', 'lightblue')
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title=x_col,
                yaxis_title=y_col,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建柱状图失败: {e}")
            return None
    
    def create_scatter_plot(
        self, 
        data: pd.DataFrame, 
        x_col: str, 
        y_col: str, 
        title: str = '',
        **kwargs
    ):
        """
        创建散点图。
        
        Args:
            data: 数据DataFrame
            x_col: X轴列名
            y_col: Y轴列名
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            fig = self.go.Figure(data=self.go.Scatter(
                x=data[x_col],
                y=data[y_col],
                mode='markers',
                marker=dict(
                    color=kwargs.get('color', 'blue'),
                    size=kwargs.get('size', 6)
                )
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title=x_col,
                yaxis_title=y_col,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建散点图失败: {e}")
            return None
    
    def create_heatmap(
        self, 
        data: pd.DataFrame, 
        title: str = '',
        **kwargs
    ):
        """
        创建热力图。
        
        Args:
            data: 数据DataFrame（通常是相关性矩阵）
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            fig = self.go.Figure(data=self.go.Heatmap(
                z=data.values,
                x=data.columns,
                y=data.index,
                colorscale=kwargs.get('colorscale', 'RdBu'),
                zmid=0
            ))
            
            fig.update_layout(
                title=title,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建热力图失败: {e}")
            return None
    
    def create_histogram(
        self, 
        data: pd.DataFrame, 
        column: str, 
        title: str = '',
        **kwargs
    ):
        """
        创建直方图。
        
        Args:
            data: 数据DataFrame
            column: 列名
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            fig = self.go.Figure(data=self.go.Histogram(
                x=data[column],
                nbinsx=kwargs.get('bins', 30),
                marker_color=kwargs.get('color', 'lightblue')
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title=column,
                yaxis_title='频数',
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建直方图失败: {e}")
            return None
    
    def create_box_plot(
        self, 
        data: pd.DataFrame, 
        y_col: str, 
        title: str = '',
        **kwargs
    ):
        """
        创建箱线图。
        
        Args:
            data: 数据DataFrame
            y_col: Y轴列名
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            fig = self.go.Figure(data=self.go.Box(
                y=data[y_col],
                name=y_col,
                marker_color=kwargs.get('color', 'lightblue')
            ))
            
            fig.update_layout(
                title=title,
                yaxis_title=y_col,
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建箱线图失败: {e}")
            return None
    
    def create_performance_chart(
        self, 
        data: pd.DataFrame, 
        title: str = '业绩表现',
        **kwargs
    ):
        """
        创建业绩图表。
        
        Args:
            data: 业绩数据，包含cumulative_return列
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            # 确定日期列
            date_col = 'date'
            if 'datetime' in data.columns:
                date_col = 'datetime'
            
            fig = self.go.Figure()
            
            # 添加累积收益曲线
            fig.add_trace(self.go.Scatter(
                x=data[date_col] if date_col in data.columns else data.index,
                y=data['cumulative_return'],
                mode='lines',
                name='累积收益',
                line=dict(color='blue')
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title='日期',
                yaxis_title='累积收益',
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建业绩图表失败: {e}")
            return None
    
    def create_drawdown_chart(
        self, 
        data: pd.DataFrame, 
        title: str = '回撤分析',
        **kwargs
    ):
        """
        创建回撤图表。
        
        Args:
            data: 业绩数据
            title: 图表标题
            **kwargs: 其他参数
            
        Returns:
            Plotly图表对象
        """
        if not self.go:
            return None
        
        try:
            # 计算回撤
            cumulative_return = data['cumulative_return']
            running_max = cumulative_return.expanding().max()
            drawdown = (cumulative_return - running_max) / running_max
            
            # 确定日期列
            date_col = 'date'
            if 'datetime' in data.columns:
                date_col = 'datetime'
            
            fig = self.go.Figure()
            
            # 添加回撤曲线
            fig.add_trace(self.go.Scatter(
                x=data[date_col] if date_col in data.columns else data.index,
                y=drawdown,
                mode='lines',
                name='回撤',
                line=dict(color='red'),
                fill='tonexty'
            ))
            
            fig.update_layout(
                title=title,
                xaxis_title='日期',
                yaxis_title='回撤',
                template='plotly_white'
            )
            
            return fig
            
        except Exception as e:
            logger.error(f"创建回撤图表失败: {e}")
            return None
    
    def save_chart(self, chart, output_path: str, format: str = 'html') -> bool:
        """
        保存图表。
        
        Args:
            chart: 图表对象
            output_path: 输出路径
            format: 输出格式 ('html', 'png', 'pdf')
            
        Returns:
            是否保存成功
        """
        if not chart:
            logger.error("图表对象为空")
            return False
        
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if format == 'html':
                chart.write_html(str(output_file))
            elif format == 'png':
                chart.write_image(str(output_file))
            elif format == 'pdf':
                chart.write_image(str(output_file))
            else:
                logger.error(f"不支持的格式: {format}")
                return False
            
            logger.info(f"图表已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存图表失败: {e}")
            return False
    
    def batch_create_charts(self, chart_configs: List[Dict[str, Any]]) -> List:
        """
        批量创建图表。
        
        Args:
            chart_configs: 图表配置列表
            
        Returns:
            图表对象列表
        """
        charts = []
        
        for config in chart_configs:
            chart_type = config.get('type')
            
            try:
                if chart_type == 'line':
                    chart = self.create_line_chart(**config)
                elif chart_type == 'bar':
                    chart = self.create_bar_chart(**config)
                elif chart_type == 'scatter':
                    chart = self.create_scatter_plot(**config)
                elif chart_type == 'candlestick':
                    chart = self.create_candlestick_chart(**config)
                elif chart_type == 'heatmap':
                    chart = self.create_heatmap(**config)
                elif chart_type == 'histogram':
                    chart = self.create_histogram(**config)
                elif chart_type == 'box':
                    chart = self.create_box_plot(**config)
                elif chart_type == 'performance':
                    chart = self.create_performance_chart(**config)
                elif chart_type == 'drawdown':
                    chart = self.create_drawdown_chart(**config)
                else:
                    logger.warning(f"不支持的图表类型: {chart_type}")
                    chart = None
                
                if chart:
                    charts.append(chart)
                    
            except Exception as e:
                logger.error(f"创建图表失败 {chart_type}: {e}")
        
        return charts
