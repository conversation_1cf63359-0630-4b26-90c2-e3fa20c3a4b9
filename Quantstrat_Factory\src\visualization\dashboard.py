"""
仪表板模块。

提供交互式仪表板功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
import json
from pathlib import Path
import uuid

from .chart_generator import ChartGenerator

logger = logging.getLogger(__name__)


class Dashboard:
    """仪表板管理器。"""
    
    def __init__(self, dashboard_id: Optional[str] = None):
        """
        初始化仪表板。
        
        Args:
            dashboard_id: 仪表板ID
        """
        self.dashboard_id = dashboard_id or str(uuid.uuid4())
        self.widgets = []
        self.layout = {
            'title': '量化策略仪表板',
            'description': '',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        self.chart_generator = ChartGenerator()
    
    def add_widget(self, widget_config: Dict[str, Any]) -> bool:
        """
        添加组件到仪表板。
        
        Args:
            widget_config: 组件配置
                - type: 组件类型 (chart, metric, table, text)
                - title: 组件标题
                - data_source: 数据源
                - chart_type: 图表类型（如果是图表组件）
                - position: 位置信息
                
        Returns:
            是否添加成功
        """
        try:
            # 验证必要字段
            required_fields = ['type', 'title']
            for field in required_fields:
                if field not in widget_config:
                    logger.error(f"组件配置缺少必要字段: {field}")
                    return False
            
            # 生成唯一ID
            widget_id = str(uuid.uuid4())
            
            # 创建组件
            widget = {
                'id': widget_id,
                'type': widget_config['type'],
                'title': widget_config['title'],
                'data_source': widget_config.get('data_source', ''),
                'chart_type': widget_config.get('chart_type', 'line'),
                'position': widget_config.get('position', {'x': 0, 'y': 0, 'w': 6, 'h': 4}),
                'config': widget_config.get('config', {}),
                'created_at': datetime.now().isoformat()
            }
            
            # 添加到组件列表
            self.widgets.append(widget)
            
            # 更新仪表板时间
            self.layout['updated_at'] = datetime.now().isoformat()
            
            logger.info(f"成功添加组件: {widget_config['title']}")
            return True
            
        except Exception as e:
            logger.error(f"添加组件失败: {e}")
            return False
    
    def remove_widget(self, widget_id: str) -> bool:
        """
        移除组件。
        
        Args:
            widget_id: 组件ID
            
        Returns:
            是否移除成功
        """
        try:
            # 查找组件
            widget_index = None
            for i, widget in enumerate(self.widgets):
                if widget['id'] == widget_id:
                    widget_index = i
                    break
            
            if widget_index is None:
                logger.warning(f"组件不存在: {widget_id}")
                return False
            
            # 移除组件
            removed_widget = self.widgets.pop(widget_index)
            
            # 更新仪表板时间
            self.layout['updated_at'] = datetime.now().isoformat()
            
            logger.info(f"成功移除组件: {removed_widget['title']}")
            return True
            
        except Exception as e:
            logger.error(f"移除组件失败: {e}")
            return False
    
    def update_widget(self, widget_id: str, updates: Dict[str, Any]) -> bool:
        """
        更新组件。
        
        Args:
            widget_id: 组件ID
            updates: 更新内容
            
        Returns:
            是否更新成功
        """
        try:
            # 查找组件
            widget = None
            for w in self.widgets:
                if w['id'] == widget_id:
                    widget = w
                    break
            
            if widget is None:
                logger.warning(f"组件不存在: {widget_id}")
                return False
            
            # 更新组件
            for key, value in updates.items():
                if key in widget:
                    widget[key] = value
            
            widget['updated_at'] = datetime.now().isoformat()
            
            # 更新仪表板时间
            self.layout['updated_at'] = datetime.now().isoformat()
            
            logger.info(f"成功更新组件: {widget['title']}")
            return True
            
        except Exception as e:
            logger.error(f"更新组件失败: {e}")
            return False
    
    def get_widget(self, widget_id: str) -> Optional[Dict[str, Any]]:
        """
        获取组件。
        
        Args:
            widget_id: 组件ID
            
        Returns:
            组件配置，如果不存在则返回None
        """
        for widget in self.widgets:
            if widget['id'] == widget_id:
                return widget.copy()
        
        return None
    
    def get_layout(self) -> Dict[str, Any]:
        """
        获取仪表板布局。
        
        Returns:
            布局配置
        """
        layout = self.layout.copy()
        layout['widgets'] = self.widgets.copy()
        layout['dashboard_id'] = self.dashboard_id
        
        return layout
    
    def set_layout(self, layout_config: Dict[str, Any]) -> bool:
        """
        设置仪表板布局。
        
        Args:
            layout_config: 布局配置
            
        Returns:
            是否设置成功
        """
        try:
            # 更新基本信息
            if 'title' in layout_config:
                self.layout['title'] = layout_config['title']
            
            if 'description' in layout_config:
                self.layout['description'] = layout_config['description']
            
            # 更新组件
            if 'widgets' in layout_config:
                self.widgets = layout_config['widgets']
            
            self.layout['updated_at'] = datetime.now().isoformat()
            
            logger.info("仪表板布局设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置仪表板布局失败: {e}")
            return False
    
    def render_widget(self, widget_id: str, data: pd.DataFrame) -> Any:
        """
        渲染组件。
        
        Args:
            widget_id: 组件ID
            data: 数据
            
        Returns:
            渲染后的组件对象
        """
        widget = self.get_widget(widget_id)
        if not widget:
            logger.error(f"组件不存在: {widget_id}")
            return None
        
        try:
            widget_type = widget['type']
            
            if widget_type == 'chart':
                return self._render_chart_widget(widget, data)
            elif widget_type == 'metric':
                return self._render_metric_widget(widget, data)
            elif widget_type == 'table':
                return self._render_table_widget(widget, data)
            elif widget_type == 'text':
                return self._render_text_widget(widget, data)
            else:
                logger.warning(f"不支持的组件类型: {widget_type}")
                return None
                
        except Exception as e:
            logger.error(f"渲染组件失败: {e}")
            return None
    
    def _render_chart_widget(self, widget: Dict[str, Any], data: pd.DataFrame) -> Any:
        """渲染图表组件。"""
        chart_type = widget.get('chart_type', 'line')
        title = widget.get('title', '')
        config = widget.get('config', {})
        
        if chart_type == 'line':
            x_col = config.get('x_col', data.columns[0])
            y_col = config.get('y_col', data.columns[1] if len(data.columns) > 1 else data.columns[0])
            return self.chart_generator.create_line_chart(data, x_col, y_col, title)
        
        elif chart_type == 'bar':
            x_col = config.get('x_col', data.columns[0])
            y_col = config.get('y_col', data.columns[1] if len(data.columns) > 1 else data.columns[0])
            return self.chart_generator.create_bar_chart(data, x_col, y_col, title)
        
        elif chart_type == 'candlestick':
            return self.chart_generator.create_candlestick_chart(data, title)
        
        elif chart_type == 'heatmap':
            return self.chart_generator.create_heatmap(data, title)
        
        else:
            logger.warning(f"不支持的图表类型: {chart_type}")
            return None
    
    def _render_metric_widget(self, widget: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """渲染指标组件。"""
        config = widget.get('config', {})
        metric_type = config.get('metric_type', 'mean')
        column = config.get('column', data.columns[0] if len(data.columns) > 0 else None)
        
        if not column or column not in data.columns:
            return {'value': 'N/A', 'error': '列不存在'}
        
        try:
            if metric_type == 'mean':
                value = data[column].mean()
            elif metric_type == 'sum':
                value = data[column].sum()
            elif metric_type == 'count':
                value = data[column].count()
            elif metric_type == 'max':
                value = data[column].max()
            elif metric_type == 'min':
                value = data[column].min()
            else:
                value = 'N/A'
            
            return {
                'value': value,
                'title': widget.get('title', ''),
                'format': config.get('format', '.2f')
            }
            
        except Exception as e:
            return {'value': 'Error', 'error': str(e)}
    
    def _render_table_widget(self, widget: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """渲染表格组件。"""
        config = widget.get('config', {})
        max_rows = config.get('max_rows', 10)
        columns = config.get('columns', data.columns.tolist())
        
        # 过滤列
        available_columns = [col for col in columns if col in data.columns]
        if not available_columns:
            available_columns = data.columns.tolist()
        
        # 限制行数
        display_data = data[available_columns].head(max_rows)
        
        return {
            'data': display_data.to_dict('records'),
            'columns': available_columns,
            'title': widget.get('title', '')
        }
    
    def _render_text_widget(self, widget: Dict[str, Any], data: pd.DataFrame) -> Dict[str, Any]:
        """渲染文本组件。"""
        config = widget.get('config', {})
        text_content = config.get('content', '默认文本内容')
        
        return {
            'content': text_content,
            'title': widget.get('title', '')
        }
    
    def save_dashboard(self, file_path: str) -> bool:
        """
        保存仪表板到文件。
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            layout = self.get_layout()
            
            output_file = Path(file_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(layout, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"仪表板已保存到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存仪表板失败: {e}")
            return False
    
    def load_dashboard(self, file_path: str) -> bool:
        """
        从文件加载仪表板。
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否加载成功
        """
        try:
            input_file = Path(file_path)
            if not input_file.exists():
                logger.error(f"文件不存在: {file_path}")
                return False
            
            with open(input_file, 'r', encoding='utf-8') as f:
                layout = json.load(f)
            
            # 设置布局
            self.set_layout(layout)
            
            if 'dashboard_id' in layout:
                self.dashboard_id = layout['dashboard_id']
            
            logger.info(f"仪表板已从文件加载: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载仪表板失败: {e}")
            return False
    
    def export_dashboard(self, export_path: str, format: str = 'html') -> bool:
        """
        导出仪表板。
        
        Args:
            export_path: 导出路径
            format: 导出格式 ('html', 'json')
            
        Returns:
            是否导出成功
        """
        try:
            if format == 'json':
                return self.save_dashboard(export_path)
            elif format == 'html':
                return self._export_html_dashboard(export_path)
            else:
                logger.error(f"不支持的导出格式: {format}")
                return False
                
        except Exception as e:
            logger.error(f"导出仪表板失败: {e}")
            return False
    
    def _export_html_dashboard(self, export_path: str) -> bool:
        """导出HTML格式的仪表板。"""
        try:
            layout = self.get_layout()
            
            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{layout.get('title', '仪表板')}</title>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 20px; }}
                    .dashboard-header {{ text-align: center; margin-bottom: 30px; }}
                    .widget {{ border: 1px solid #ddd; margin: 10px; padding: 15px; border-radius: 5px; }}
                    .widget-title {{ font-weight: bold; margin-bottom: 10px; }}
                </style>
            </head>
            <body>
                <div class="dashboard-header">
                    <h1>{layout.get('title', '仪表板')}</h1>
                    <p>{layout.get('description', '')}</p>
                </div>
                <div class="dashboard-content">
            """
            
            for widget in layout.get('widgets', []):
                html_content += f"""
                <div class="widget">
                    <div class="widget-title">{widget.get('title', '')}</div>
                    <div class="widget-content">
                        <p>组件类型: {widget.get('type', '')}</p>
                        <p>数据源: {widget.get('data_source', '')}</p>
                    </div>
                </div>
                """
            
            html_content += """
                </div>
            </body>
            </html>
            """
            
            output_file = Path(export_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML仪表板已导出到: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出HTML仪表板失败: {e}")
            return False
