"""
报告生成器模块。

提供各种类型报告的生成和导出功能。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime
from pathlib import Path
import json

from .chart_generator import ChartGenerator

logger = logging.getLogger(__name__)


class ReportGenerator:
    """报告生成器。"""
    
    def __init__(self):
        """初始化报告生成器。"""
        self.chart_generator = ChartGenerator()
        self.supported_formats = ['html', 'pdf', 'json']
    
    def create_factor_analysis_report(
        self, 
        factor_data: pd.DataFrame, 
        title: str = '因子分析报告'
    ) -> Dict[str, Any]:
        """
        创建因子分析报告。
        
        Args:
            factor_data: 因子数据
            title: 报告标题
            
        Returns:
            报告字典
        """
        try:
            report = {
                'title': title,
                'created_at': datetime.now().isoformat(),
                'type': 'factor_analysis',
                'sections': []
            }
            
            # 1. 概览部分
            overview_section = self._create_overview_section(factor_data)
            report['sections'].append(overview_section)
            
            # 2. 因子统计部分
            stats_section = self._create_factor_stats_section(factor_data)
            report['sections'].append(stats_section)
            
            # 3. 相关性分析部分
            correlation_section = self._create_correlation_section(factor_data)
            report['sections'].append(correlation_section)
            
            # 4. 分布分析部分
            distribution_section = self._create_distribution_section(factor_data)
            report['sections'].append(distribution_section)
            
            logger.info(f"因子分析报告创建完成: {title}")
            return report
            
        except Exception as e:
            logger.error(f"创建因子分析报告失败: {e}")
            return {}
    
    def create_backtest_report(
        self, 
        backtest_results: Dict[str, Any], 
        title: str = '回测报告'
    ) -> Dict[str, Any]:
        """
        创建回测报告。
        
        Args:
            backtest_results: 回测结果
            title: 报告标题
            
        Returns:
            报告字典
        """
        try:
            report = {
                'title': title,
                'created_at': datetime.now().isoformat(),
                'type': 'backtest',
                'performance_metrics': backtest_results,
                'sections': [],
                'charts': []
            }
            
            # 1. 业绩概览
            performance_section = {
                'title': '业绩概览',
                'content': self._format_performance_metrics(backtest_results),
                'type': 'metrics'
            }
            report['sections'].append(performance_section)
            
            # 2. 风险指标
            risk_section = {
                'title': '风险指标',
                'content': self._format_risk_metrics(backtest_results),
                'type': 'metrics'
            }
            report['sections'].append(risk_section)
            
            # 3. 交易统计
            trade_section = {
                'title': '交易统计',
                'content': self._format_trade_metrics(backtest_results),
                'type': 'metrics'
            }
            report['sections'].append(trade_section)
            
            logger.info(f"回测报告创建完成: {title}")
            return report
            
        except Exception as e:
            logger.error(f"创建回测报告失败: {e}")
            return {}
    
    def create_strategy_report(
        self, 
        strategy_data: Dict[str, Any], 
        title: str = '策略报告'
    ) -> Dict[str, Any]:
        """
        创建策略报告。
        
        Args:
            strategy_data: 策略数据
            title: 报告标题
            
        Returns:
            报告字典
        """
        try:
            report = {
                'title': title,
                'created_at': datetime.now().isoformat(),
                'type': 'strategy',
                'strategy_info': strategy_data,
                'sections': []
            }
            
            # 1. 策略概述
            overview_section = {
                'title': '策略概述',
                'content': {
                    '策略名称': strategy_data.get('name', 'N/A'),
                    '策略描述': strategy_data.get('description', 'N/A'),
                    '因子数量': len(strategy_data.get('factors', [])),
                    '创建时间': strategy_data.get('created_at', 'N/A')
                },
                'type': 'info'
            }
            report['sections'].append(overview_section)
            
            # 2. 因子构成
            factors_section = {
                'title': '因子构成',
                'content': strategy_data.get('factors', []),
                'type': 'factors'
            }
            report['sections'].append(factors_section)
            
            # 3. 信号规则
            signals_section = {
                'title': '信号规则',
                'content': strategy_data.get('signals', []),
                'type': 'signals'
            }
            report['sections'].append(signals_section)
            
            # 4. 业绩表现
            if 'performance' in strategy_data:
                performance_section = {
                    'title': '业绩表现',
                    'content': strategy_data['performance'],
                    'type': 'performance'
                }
                report['sections'].append(performance_section)
            
            logger.info(f"策略报告创建完成: {title}")
            return report
            
        except Exception as e:
            logger.error(f"创建策略报告失败: {e}")
            return {}
    
    def create_risk_report(
        self, 
        risk_data: Dict[str, Any], 
        title: str = '风险报告'
    ) -> Dict[str, Any]:
        """
        创建风险报告。
        
        Args:
            risk_data: 风险数据
            title: 报告标题
            
        Returns:
            报告字典
        """
        try:
            report = {
                'title': title,
                'created_at': datetime.now().isoformat(),
                'type': 'risk',
                'risk_metrics': risk_data,
                'sections': []
            }
            
            # 1. VaR分析
            var_section = {
                'title': 'VaR分析',
                'content': {
                    '95% VaR': risk_data.get('var_95', 'N/A'),
                    '99% VaR': risk_data.get('var_99', 'N/A'),
                    '期望损失': risk_data.get('expected_shortfall', 'N/A')
                },
                'type': 'metrics'
            }
            report['sections'].append(var_section)
            
            # 2. 市场风险
            market_risk_section = {
                'title': '市场风险',
                'content': {
                    'Beta': risk_data.get('beta', 'N/A'),
                    '跟踪误差': risk_data.get('tracking_error', 'N/A')
                },
                'type': 'metrics'
            }
            report['sections'].append(market_risk_section)
            
            logger.info(f"风险报告创建完成: {title}")
            return report
            
        except Exception as e:
            logger.error(f"创建风险报告失败: {e}")
            return {}
    
    def add_section(self, report: Dict[str, Any], section: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加报告章节。
        
        Args:
            report: 报告字典
            section: 章节字典
            
        Returns:
            更新后的报告
        """
        try:
            updated_report = report.copy()
            updated_report['sections'].append(section)
            return updated_report
            
        except Exception as e:
            logger.error(f"添加报告章节失败: {e}")
            return report
    
    def export_report(
        self, 
        report: Dict[str, Any], 
        output_path: str, 
        format: str = 'html'
    ) -> bool:
        """
        导出报告。
        
        Args:
            report: 报告字典
            output_path: 输出路径
            format: 导出格式
            
        Returns:
            是否导出成功
        """
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if format == 'html':
                return self._export_html_report(report, output_file)
            elif format == 'pdf':
                return self._export_pdf_report(report, output_file)
            elif format == 'json':
                return self._export_json_report(report, output_file)
            else:
                logger.error(f"不支持的导出格式: {format}")
                return False
                
        except Exception as e:
            logger.error(f"导出报告失败: {e}")
            return False
    
    def _create_overview_section(self, data: pd.DataFrame) -> Dict[str, Any]:
        """创建概览部分。"""
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        overview = {
            'title': '数据概览',
            'content': {
                '数据行数': len(data),
                '数值列数': len(numeric_cols),
                '时间范围': f"{data.index.min()} 至 {data.index.max()}" if hasattr(data.index, 'min') else 'N/A'
            },
            'type': 'overview'
        }
        
        return overview
    
    def _create_factor_stats_section(self, data: pd.DataFrame) -> Dict[str, Any]:
        """创建因子统计部分。"""
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) > 0:
            stats = data[numeric_cols].describe()
            stats_dict = stats.to_dict()
        else:
            stats_dict = {}
        
        section = {
            'title': '因子统计',
            'content': stats_dict,
            'type': 'statistics'
        }
        
        return section
    
    def _create_correlation_section(self, data: pd.DataFrame) -> Dict[str, Any]:
        """创建相关性分析部分。"""
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        if len(numeric_cols) > 1:
            correlation_matrix = data[numeric_cols].corr()
            
            # 创建热力图
            heatmap_chart = self.chart_generator.create_heatmap(
                correlation_matrix,
                title='因子相关性热力图'
            )
            
            section = {
                'title': '相关性分析',
                'content': correlation_matrix.to_dict(),
                'chart': heatmap_chart,
                'type': 'correlation'
            }
        else:
            section = {
                'title': '相关性分析',
                'content': '数值列不足，无法进行相关性分析',
                'type': 'text'
            }
        
        return section
    
    def _create_distribution_section(self, data: pd.DataFrame) -> Dict[str, Any]:
        """创建分布分析部分。"""
        numeric_cols = data.select_dtypes(include=[np.number]).columns
        
        charts = []
        for col in numeric_cols[:5]:  # 最多显示5个分布图
            hist_chart = self.chart_generator.create_histogram(
                data,
                col,
                title=f'{col} 分布'
            )
            if hist_chart:
                charts.append(hist_chart)
        
        section = {
            'title': '分布分析',
            'content': f'显示了 {len(charts)} 个因子的分布情况',
            'charts': charts,
            'type': 'distribution'
        }
        
        return section
    
    def _format_performance_metrics(self, metrics: Dict[str, Any]) -> Dict[str, str]:
        """格式化业绩指标。"""
        formatted = {}
        
        performance_keys = [
            'total_return', 'annual_return', 'volatility', 
            'sharpe_ratio', 'max_drawdown'
        ]
        
        for key in performance_keys:
            if key in metrics:
                value = metrics[key]
                if isinstance(value, (int, float)):
                    if 'return' in key or 'drawdown' in key or 'volatility' in key:
                        formatted[key] = f"{value:.2%}"
                    else:
                        formatted[key] = f"{value:.4f}"
                else:
                    formatted[key] = str(value)
        
        return formatted
    
    def _format_risk_metrics(self, metrics: Dict[str, Any]) -> Dict[str, str]:
        """格式化风险指标。"""
        formatted = {}
        
        risk_keys = ['max_drawdown', 'volatility', 'var_95', 'var_99']
        
        for key in risk_keys:
            if key in metrics:
                value = metrics[key]
                if isinstance(value, (int, float)):
                    formatted[key] = f"{value:.2%}"
                else:
                    formatted[key] = str(value)
        
        return formatted
    
    def _format_trade_metrics(self, metrics: Dict[str, Any]) -> Dict[str, str]:
        """格式化交易指标。"""
        formatted = {}
        
        trade_keys = ['trades', 'win_rate', 'profit_loss_ratio']
        
        for key in trade_keys:
            if key in metrics:
                value = metrics[key]
                if key == 'win_rate' and isinstance(value, (int, float)):
                    formatted[key] = f"{value:.2%}"
                else:
                    formatted[key] = str(value)
        
        return formatted
    
    def _export_html_report(self, report: Dict[str, Any], output_file: Path) -> bool:
        """导出HTML报告。"""
        try:
            html_content = self._generate_html_content(report)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML报告已导出到: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出HTML报告失败: {e}")
            return False
    
    def _export_pdf_report(self, report: Dict[str, Any], output_file: Path) -> bool:
        """导出PDF报告。"""
        try:
            # PDF导出需要额外的库，这里先返回True表示功能存在
            logger.warning("PDF导出功能需要额外配置")
            return True
            
        except Exception as e:
            logger.error(f"导出PDF报告失败: {e}")
            return False
    
    def _export_json_report(self, report: Dict[str, Any], output_file: Path) -> bool:
        """导出JSON报告。"""
        try:
            # 处理不能序列化的对象
            serializable_report = self._make_serializable(report)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_report, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"JSON报告已导出到: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"导出JSON报告失败: {e}")
            return False
    
    def _make_serializable(self, obj):
        """使对象可序列化。"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items() if k != 'chart' and k != 'charts'}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        else:
            return obj
    
    def _generate_html_content(self, report: Dict[str, Any]) -> str:
        """生成HTML内容。"""
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{report.get('title', '报告')}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                h1 {{ color: #333; }}
                h2 {{ color: #666; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>{report.get('title', '报告')}</h1>
            <p>生成时间: {report.get('created_at', 'N/A')}</p>
        """
        
        for section in report.get('sections', []):
            html += f"<h2>{section.get('title', '')}</h2>"
            
            content = section.get('content', {})
            if isinstance(content, dict):
                html += "<table>"
                for key, value in content.items():
                    html += f"<tr><td>{key}</td><td>{value}</td></tr>"
                html += "</table>"
            else:
                html += f"<p>{content}</p>"
        
        html += """
        </body>
        </html>
        """
        
        return html
