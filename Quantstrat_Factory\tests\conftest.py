"""
pytest配置文件，定义全局fixtures和测试配置。
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
import tempfile
import shutil
from configparser import ConfigParser
import sys
import os

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def project_root_path():
    """返回项目根目录路径。"""
    return Path(__file__).parent.parent


@pytest.fixture(scope="session")
def test_config():
    """创建测试用的配置对象。"""
    config = ConfigParser()
    
    # 创建临时目录用于测试
    temp_dir = tempfile.mkdtemp(prefix="quantstrat_test_")
    
    config.add_section('Paths')
    config.set('Paths', 'data_root', temp_dir)
    config.set('Paths', 'feature_store_path', f"{temp_dir}/features")
    config.set('Paths', 'cleaned_data_path', f"{temp_dir}/cleaned")
    config.set('Paths', 'factor_lab_output', f"{temp_dir}/factor_lab")
    
    # 创建必要的目录
    for path_key in ['feature_store_path', 'cleaned_data_path', 'factor_lab_output']:
        path = config.get('Paths', path_key)
        os.makedirs(path, exist_ok=True)
    
    yield config
    
    # 清理临时目录
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def sample_market_data():
    """生成样本市场数据。"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    symbols = ['STOCK_001', 'STOCK_002', 'STOCK_003', 'STOCK_004', 'STOCK_005']
    
    data_list = []
    for date in dates:
        for symbol in symbols:
            # 生成模拟的OHLCV数据
            base_price = 100 + np.random.normal(0, 10)
            volatility = 0.02
            
            open_price = base_price * (1 + np.random.normal(0, volatility))
            close_price = open_price * (1 + np.random.normal(0, volatility))
            high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, volatility/2)))
            low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, volatility/2)))
            volume = np.random.randint(1000, 10000)
            
            data_list.append({
                'datetime': date,
                'symbol': symbol,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume
            })
    
    return pd.DataFrame(data_list)


@pytest.fixture
def sample_factor_data():
    """生成样本因子数据。"""
    dates = pd.date_range('2023-01-01', periods=50, freq='D')
    symbols = ['STOCK_001', 'STOCK_002', 'STOCK_003', 'STOCK_004', 'STOCK_005']
    
    data_list = []
    for date in dates:
        for symbol in symbols:
            # 生成有相关性的因子和收益率数据
            momentum = np.random.normal(0, 0.1)
            volatility = abs(np.random.normal(0.02, 0.01))
            
            # 未来收益率与动量因子有正相关关系
            future_return = momentum * 0.3 + np.random.normal(0, 0.01)
            
            data_list.append({
                'datetime': date,
                'symbol': symbol,
                'MOM': momentum,
                'VOL': volatility,
                'fwd_return_1d': future_return,
                'fwd_return_5d': future_return * 5 + np.random.normal(0, 0.02)
            })
    
    return pd.DataFrame(data_list)


@pytest.fixture
def performance_benchmark():
    """性能测试基准配置。"""
    return {
        'max_execution_time': {
            'ic_calculation': 5.0,  # 秒
            'quantile_analysis': 10.0,  # 秒
            'comprehensive_evaluation': 15.0  # 秒
        },
        'max_memory_usage': {
            'small_dataset': 100,  # MB
            'medium_dataset': 500,  # MB
            'large_dataset': 1000  # MB
        }
    }


class TestDataGenerator:
    """测试数据生成器类。"""
    
    @staticmethod
    def generate_correlated_data(n_samples: int = 1000, 
                               n_factors: int = 5,
                               correlation: float = 0.3) -> pd.DataFrame:
        """
        生成具有指定相关性的因子和收益率数据。
        
        参数:
        - n_samples: 样本数量
        - n_factors: 因子数量
        - correlation: 因子与收益率的相关系数
        """
        dates = pd.date_range('2023-01-01', periods=n_samples//10, freq='D')
        symbols = [f'STOCK_{i:03d}' for i in range(10)]
        
        data_list = []
        for date in dates:
            for symbol in symbols:
                # 生成因子值
                factors = {}
                for i in range(n_factors):
                    factors[f'factor_{i}'] = np.random.normal(0, 1)
                
                # 生成与因子相关的收益率
                factor_sum = sum(factors.values())
                return_1d = factor_sum * correlation + np.random.normal(0, 0.02)
                
                row = {
                    'datetime': date,
                    'symbol': symbol,
                    'fwd_return_1d': return_1d,
                    **factors
                }
                data_list.append(row)
        
        return pd.DataFrame(data_list)


@pytest.fixture
def test_data_generator():
    """返回测试数据生成器实例。"""
    return TestDataGenerator()


# 性能测试装饰器
def performance_test(max_time: float = None, max_memory: float = None):
    """
    性能测试装饰器。
    
    参数:
    - max_time: 最大执行时间（秒）
    - max_memory: 最大内存使用（MB）
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            import psutil
            import os
            
            # 记录开始时间和内存
            start_time = time.time()
            process = psutil.Process(os.getpid())
            start_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            execution_time = end_time - start_time
            memory_used = end_memory - start_memory
            
            # 检查性能指标
            if max_time and execution_time > max_time:
                pytest.fail(f"执行时间超限: {execution_time:.2f}s > {max_time}s")
            
            if max_memory and memory_used > max_memory:
                pytest.fail(f"内存使用超限: {memory_used:.2f}MB > {max_memory}MB")
            
            print(f"性能指标 - 执行时间: {execution_time:.2f}s, 内存使用: {memory_used:.2f}MB")
            
            return result
        return wrapper
    return decorator


# 测试标记
pytest.mark.unit = pytest.mark.unit
pytest.mark.integration = pytest.mark.integration
pytest.mark.performance = pytest.mark.performance
pytest.mark.slow = pytest.mark.slow
