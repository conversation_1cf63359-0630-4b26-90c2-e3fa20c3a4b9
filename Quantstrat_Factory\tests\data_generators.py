"""
测试数据生成器模块。

提供各种类型的模拟数据生成功能，用于测试不同的场景。
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json
from pathlib import Path


class MarketDataGenerator:
    """市场数据生成器。"""
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化生成器。
        
        参数:
        - seed: 随机种子，用于可重复的测试
        """
        if seed is not None:
            np.random.seed(seed)
        
    def generate_ohlcv_data(self, 
                           symbols: List[str],
                           start_date: str,
                           end_date: str,
                           freq: str = 'D',
                           base_price: float = 100.0,
                           volatility: float = 0.02) -> pd.DataFrame:
        """
        生成OHLCV数据。
        
        参数:
        - symbols: 股票代码列表
        - start_date: 开始日期
        - end_date: 结束日期
        - freq: 频率 ('D'=日, 'H'=小时, 'T'=分钟)
        - base_price: 基础价格
        - volatility: 波动率
        
        返回:
        - pd.DataFrame: OHLCV数据
        """
        dates = pd.date_range(start_date, end_date, freq=freq)
        data_list = []
        
        for symbol in symbols:
            # 为每个股票生成价格序列
            returns = np.random.normal(0, volatility, len(dates))
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            for i, date in enumerate(dates):
                price = prices[i]
                daily_vol = volatility * np.random.uniform(0.5, 2.0)
                
                # 生成OHLC
                open_price = price * (1 + np.random.normal(0, daily_vol/4))
                close_price = open_price * (1 + np.random.normal(0, daily_vol))
                high_price = max(open_price, close_price) * (1 + abs(np.random.normal(0, daily_vol/2)))
                low_price = min(open_price, close_price) * (1 - abs(np.random.normal(0, daily_vol/2)))
                
                # 生成成交量
                volume = int(np.random.lognormal(10, 1))
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })
        
        return pd.DataFrame(data_list)


class FactorDataGenerator:
    """因子数据生成器。"""
    
    def __init__(self, seed: Optional[int] = None):
        if seed is not None:
            np.random.seed(seed)
    
    def generate_factor_data(self,
                           symbols: List[str],
                           start_date: str,
                           end_date: str,
                           factor_configs: Dict[str, Dict]) -> pd.DataFrame:
        """
        生成因子数据。
        
        参数:
        - symbols: 股票代码列表
        - start_date: 开始日期
        - end_date: 结束日期
        - factor_configs: 因子配置字典
          格式: {
              'factor_name': {
                  'mean': 0.0,
                  'std': 1.0,
                  'correlation_with_return': 0.3
              }
          }
        
        返回:
        - pd.DataFrame: 因子数据
        """
        dates = pd.date_range(start_date, end_date, freq='D')
        data_list = []
        
        for date in dates:
            for symbol in symbols:
                row = {
                    'datetime': date,
                    'symbol': symbol
                }
                
                # 生成因子值
                factor_values = {}
                for factor_name, config in factor_configs.items():
                    mean = config.get('mean', 0.0)
                    std = config.get('std', 1.0)
                    factor_values[factor_name] = np.random.normal(mean, std)
                    row[factor_name] = factor_values[factor_name]
                
                # 生成相关的收益率
                total_factor_effect = 0
                for factor_name, config in factor_configs.items():
                    correlation = config.get('correlation_with_return', 0.0)
                    total_factor_effect += factor_values[factor_name] * correlation
                
                # 添加噪声
                noise = np.random.normal(0, 0.01)
                fwd_return_1d = total_factor_effect + noise
                fwd_return_5d = total_factor_effect * 5 + np.random.normal(0, 0.02)
                
                row['fwd_return_1d'] = fwd_return_1d
                row['fwd_return_5d'] = fwd_return_5d
                
                data_list.append(row)
        
        return pd.DataFrame(data_list)
    
    def generate_realistic_factors(self,
                                 symbols: List[str],
                                 start_date: str,
                                 end_date: str) -> pd.DataFrame:
        """
        生成现实的因子数据，模拟真实市场中的因子特征。
        """
        # 定义现实的因子配置
        factor_configs = {
            'momentum': {
                'mean': 0.0,
                'std': 0.15,
                'correlation_with_return': 0.25
            },
            'value': {
                'mean': 0.0,
                'std': 1.2,
                'correlation_with_return': 0.15
            },
            'quality': {
                'mean': 0.0,
                'std': 0.8,
                'correlation_with_return': 0.20
            },
            'volatility': {
                'mean': 0.02,
                'std': 0.01,
                'correlation_with_return': -0.10
            },
            'size': {
                'mean': 10.0,
                'std': 2.0,
                'correlation_with_return': -0.05
            }
        }
        
        return self.generate_factor_data(symbols, start_date, end_date, factor_configs)


class ScenarioDataGenerator:
    """场景数据生成器，用于特定测试场景。"""
    
    def __init__(self, seed: Optional[int] = None):
        if seed is not None:
            np.random.seed(seed)
    
    def generate_high_correlation_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """生成高相关性的因子和收益率数据。"""
        dates = pd.date_range('2023-01-01', periods=n_samples//10, freq='D')
        symbols = [f'STOCK_{i:03d}' for i in range(10)]
        
        data_list = []
        for date in dates:
            for symbol in symbols:
                factor = np.random.normal(0, 1)
                # 高相关性：收益率 = 0.8 * 因子 + 噪声
                fwd_return = factor * 0.8 + np.random.normal(0, 0.1)
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'factor': factor,
                    'fwd_return_1d': fwd_return
                })
        
        return pd.DataFrame(data_list)
    
    def generate_no_correlation_data(self, n_samples: int = 1000) -> pd.DataFrame:
        """生成无相关性的因子和收益率数据。"""
        dates = pd.date_range('2023-01-01', periods=n_samples//10, freq='D')
        symbols = [f'STOCK_{i:03d}' for i in range(10)]
        
        data_list = []
        for date in dates:
            for symbol in symbols:
                factor = np.random.normal(0, 1)
                fwd_return = np.random.normal(0, 0.02)  # 完全独立的收益率
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'factor': factor,
                    'fwd_return_1d': fwd_return
                })
        
        return pd.DataFrame(data_list)
    
    def generate_outlier_data(self, n_samples: int = 1000, outlier_ratio: float = 0.05) -> pd.DataFrame:
        """生成包含异常值的数据。"""
        dates = pd.date_range('2023-01-01', periods=n_samples//10, freq='D')
        symbols = [f'STOCK_{i:03d}' for i in range(10)]
        
        data_list = []
        for date in dates:
            for symbol in symbols:
                factor = np.random.normal(0, 1)
                fwd_return = factor * 0.3 + np.random.normal(0, 0.01)
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'factor': factor,
                    'fwd_return_1d': fwd_return
                })
        
        df = pd.DataFrame(data_list)
        
        # 添加异常值
        n_outliers = int(len(df) * outlier_ratio)
        outlier_indices = np.random.choice(len(df), n_outliers, replace=False)
        
        # 因子异常值
        df.loc[outlier_indices[:n_outliers//2], 'factor'] = np.random.normal(0, 10, n_outliers//2)
        # 收益率异常值
        df.loc[outlier_indices[n_outliers//2:], 'fwd_return_1d'] = np.random.normal(0, 0.1, n_outliers - n_outliers//2)
        
        return df


class TestDataManager:
    """测试数据管理器。"""
    
    def __init__(self, data_dir: str = "tests/data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.market_generator = MarketDataGenerator(seed=42)
        self.factor_generator = FactorDataGenerator(seed=42)
        self.scenario_generator = ScenarioDataGenerator(seed=42)
    
    def create_standard_test_datasets(self):
        """创建标准测试数据集。"""
        symbols = ['STOCK_001', 'STOCK_002', 'STOCK_003', 'STOCK_004', 'STOCK_005']
        
        datasets = {
            'small_market_data': self.market_generator.generate_ohlcv_data(
                symbols, '2023-01-01', '2023-01-31'
            ),
            'medium_market_data': self.market_generator.generate_ohlcv_data(
                symbols, '2023-01-01', '2023-06-30'
            ),
            'realistic_factors': self.factor_generator.generate_realistic_factors(
                symbols, '2023-01-01', '2023-03-31'
            ),
            'high_correlation_scenario': self.scenario_generator.generate_high_correlation_data(500),
            'no_correlation_scenario': self.scenario_generator.generate_no_correlation_data(500),
            'outlier_scenario': self.scenario_generator.generate_outlier_data(500, 0.1)
        }
        
        # 保存数据集
        for name, data in datasets.items():
            file_path = self.data_dir / f"{name}.parquet"
            data.to_parquet(file_path, index=False)
            print(f"保存测试数据集: {file_path}")
        
        # 保存数据集元信息
        metadata = {
            name: {
                'rows': len(data),
                'columns': list(data.columns),
                'date_range': [
                    data['datetime'].min().strftime('%Y-%m-%d'),
                    data['datetime'].max().strftime('%Y-%m-%d')
                ] if 'datetime' in data.columns else None
            }
            for name, data in datasets.items()
        }
        
        metadata_path = self.data_dir / "metadata.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        print(f"保存元数据: {metadata_path}")
        return datasets
    
    def load_test_dataset(self, name: str) -> pd.DataFrame:
        """加载测试数据集。"""
        file_path = self.data_dir / f"{name}.parquet"
        if not file_path.exists():
            raise FileNotFoundError(f"测试数据集不存在: {file_path}")
        
        return pd.read_parquet(file_path)
    
    def list_available_datasets(self) -> List[str]:
        """列出可用的测试数据集。"""
        parquet_files = list(self.data_dir.glob("*.parquet"))
        return [f.stem for f in parquet_files]
