#!/usr/bin/env python3
"""
简单的回测测试脚本

用于验证回测系统配置是否正确
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

def test_config_loading():
    """测试配置文件加载"""
    print("🔧 测试配置文件加载...")
    
    try:
        import configparser
        
        config_path = project_root / "src" / "trading" / "config.ini"
        if not config_path.exists():
            print(f"❌ 配置文件不存在: {config_path}")
            return False
        
        config = configparser.ConfigParser()
        config.read(config_path)
        
        print(f"✅ 配置文件加载成功: {config_path}")
        print(f"   包含节: {list(config.sections())}")
        
        # 检查关键配置
        data_root = config.get('DATA', 'data_root', fallback='')
        if data_root and Path(data_root).exists():
            print(f"✅ 数据根目录存在: {data_root}")
        else:
            print(f"⚠️ 数据根目录不存在: {data_root}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def test_data_availability():
    """测试数据可用性"""
    print("\n📊 测试数据可用性...")
    
    try:
        # 检查股票列表
        # 使用相对路径，从Data目录查找
        stock_list_path = Path(__file__).resolve().parent.parent.parent / "Data" / "stock_list.txt"
        if stock_list_path.exists():
            with open(stock_list_path, 'r') as f:
                stocks = [line.strip() for line in f.readlines()]
            print(f"✅ 股票列表文件存在，包含 {len(stocks)} 只股票")
            print(f"   前5只股票: {stocks[:5]}")
        else:
            print(f"❌ 股票列表文件不存在: {stock_list_path}")
            return False
        
        # 检查特征数据 - 使用新的统一路径
        features_path = Path("D:/PY/Data/cleaned/daily/daily_basics.parquet")
        if features_path.exists():
            print(f"✅ 日K数据文件存在: {features_path}")

            # 尝试读取数据文件
            try:
                df = pd.read_parquet(features_path)
                print(f"✅ 成功读取日K数据: {len(df)} 行, {len(df.columns)} 列")
                print(f"   列名: {list(df.columns)[:10]}...")
                print(f"   日期范围: {df['datetime'].min()} 到 {df['datetime'].max()}")
                return True
            except Exception as e:
                print(f"❌ 读取数据文件失败: {e}")
                return False
        else:
            print(f"❌ 日K数据文件不存在: {features_path}")
            return False
            
    except Exception as e:
        print(f"❌ 数据可用性检查失败: {e}")
        return False

def test_simple_backtest():
    """测试简单回测"""
    print("\n🎯 测试简单回测...")
    
    try:
        # 创建简单的模拟数据
        dates = pd.date_range('2020-10-09', '2020-12-31', freq='D')
        stocks = ['sz000001', 'sz000002', 'sz000004', 'sz000006', 'sz000007']
        
        # 生成模拟价格数据
        np.random.seed(42)
        data_list = []
        
        for stock in stocks:
            for date in dates:
                if date.weekday() < 5:  # 只包含工作日
                    price = 10 + np.random.normal(0, 0.5)
                    volume = np.random.randint(1000000, 10000000)
                    
                    data_list.append({
                        'date': date,
                        'stock_code': stock,
                        'close': max(price, 1),  # 确保价格为正
                        'volume': volume,
                        'amount': price * volume,
                        'momentum_20': np.random.normal(0, 1),
                        'volatility_20': abs(np.random.normal(0.02, 0.01))
                    })
        
        df = pd.DataFrame(data_list)
        print(f"✅ 生成模拟数据: {len(df)} 行")
        
        # 简单的策略逻辑
        df['signal'] = 0
        df.loc[df['momentum_20'] > 0.5, 'signal'] = 1  # 买入信号
        df.loc[df['momentum_20'] < -0.5, 'signal'] = -1  # 卖出信号
        
        buy_signals = len(df[df['signal'] == 1])
        sell_signals = len(df[df['signal'] == -1])
        
        print(f"✅ 策略信号生成完成:")
        print(f"   买入信号: {buy_signals}")
        print(f"   卖出信号: {sell_signals}")
        print(f"   信号比例: {(buy_signals + sell_signals) / len(df):.2%}")
        
        # 简单的收益计算
        df['returns'] = df.groupby('stock_code')['close'].pct_change()
        df['strategy_returns'] = df['signal'].shift(1) * df['returns']
        
        total_return = df['strategy_returns'].sum()
        print(f"✅ 简单回测完成:")
        print(f"   总收益: {total_return:.4f}")
        print(f"   年化收益: {total_return * 252 / len(dates):.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单回测失败: {e}")
        return False

def test_output_directories():
    """测试输出目录"""
    print("\n📁 测试输出目录...")
    
    try:
        output_dirs = [
            project_root / "output",
            project_root / "output" / "results",
            project_root / "output" / "charts", 
            project_root / "output" / "reports"
        ]
        
        for output_dir in output_dirs:
            output_dir.mkdir(parents=True, exist_ok=True)
            print(f"✅ 输出目录创建/确认: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 输出目录创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Quantstrat Factory 回测系统测试")
    print("=" * 60)
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("数据可用性", test_data_availability),
        ("输出目录", test_output_directories),
        ("简单回测", test_simple_backtest)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！回测系统配置正确。")
        print("\n💡 现在可以尝试运行:")
        print("   cd D:/PY/Quantstrat_Factory")
        print("   python src/trading/backtester/run.py --mode backtest")
    else:
        print("\n⚠️ 部分测试失败，请检查配置和数据。")

if __name__ == "__main__":
    main()
