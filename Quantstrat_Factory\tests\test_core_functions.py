#!/usr/bin/env python3
"""
测试核心功能是否正常工作。
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent  # 回到项目根目录
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_import():
    """测试模块导入。"""
    print("🔍 测试模块导入...")
    
    try:
        import src
        print(f"  ✅ 主模块导入成功")
        print(f"  ✅ 版本: {src.__version__}")
        return True
    except Exception as e:
        print(f"  ❌ 主模块导入失败: {e}")
        return False

def test_factory_creation():
    """测试工厂创建。"""
    print("\n🔍 测试工厂创建...")
    
    try:
        import src
        factory = src.QuantstratFactory()
        print(f"  ✅ 工厂创建成功")
        
        status = factory.get_status()
        print(f"  ✅ 状态获取成功: {status}")
        return True
    except Exception as e:
        print(f"  ❌ 工厂创建失败: {e}")
        return False

def test_data_auditor():
    """测试数据审计器。"""
    print("\n🔍 测试数据审计器...")
    
    try:
        import src
        factory = src.QuantstratFactory()
        auditor = factory.get_data_auditor()
        
        if auditor:
            print(f"  ✅ 数据审计器获取成功")
            return True
        else:
            print(f"  ⚠️  数据审计器不可用（模块可能缺失）")
            return True  # 不算失败，只是模块不可用
    except Exception as e:
        print(f"  ❌ 数据审计器测试失败: {e}")
        return False

def test_feature_profiler():
    """测试特征分析器。"""
    print("\n🔍 测试特征分析器...")
    
    try:
        import src
        factory = src.QuantstratFactory()
        profiler = factory.get_feature_profiler()
        
        if profiler:
            print(f"  ✅ 特征分析器获取成功")
            return True
        else:
            print(f"  ⚠️  特征分析器不可用（模块可能缺失）")
            return True  # 不算失败，只是模块不可用
    except Exception as e:
        print(f"  ❌ 特征分析器测试失败: {e}")
        return False

def test_factor_lab():
    """测试因子实验室。"""
    print("\n🔍 测试因子实验室...")
    
    try:
        import src
        factory = src.QuantstratFactory()
        factor_lab = factory.get_factor_lab()
        
        if factor_lab:
            print(f"  ✅ 因子实验室获取成功")
            return True
        else:
            print(f"  ⚠️  因子实验室不可用（模块可能缺失）")
            return True  # 不算失败，只是模块不可用
    except Exception as e:
        print(f"  ❌ 因子实验室测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构。"""
    print("\n🔍 测试目录结构...")
    
    required_dirs = [
        "src",
        "src/data",
        "src/data/auditor",
        "src/data/profiler", 
        "src/research",
        "src/research/factor_lab",
        "config"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if not full_path.exists():
            missing_dirs.append(dir_path)
        else:
            print(f"  ✅ {dir_path}")
    
    if missing_dirs:
        print(f"  ❌ 缺少目录: {missing_dirs}")
        return False
    
    print("  ✅ 目录结构检查通过")
    return True

def test_config_files():
    """测试配置文件。"""
    print("\n🔍 测试配置文件...")
    
    config_files = [
        "config/app.yaml",
        "config/environments/development.yaml"
    ]
    
    missing_files = []
    for file_path in config_files:
        full_path = project_root / file_path
        if not full_path.exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ {file_path}")
    
    if missing_files:
        print(f"  ⚠️  缺少配置文件: {missing_files}")
        return True  # 配置文件缺失不算失败
    
    print("  ✅ 配置文件检查通过")
    return True

def main():
    """主测试函数。"""
    print("🚀 Quantstrat Factory 核心功能测试")
    print("=" * 50)
    
    tests = [
        test_directory_structure,
        test_config_files,
        test_import,
        test_factory_creation,
        test_data_auditor,
        test_feature_profiler,
        test_factor_lab
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 项测试通过")
    
    if passed >= total - 1:  # 允许1个测试失败
        print("🎉 核心功能测试基本通过！")
        print("\n📝 使用指南:")
        print("  1. 导入: from src import QuantstratFactory")
        print("  2. 创建: factory = QuantstratFactory()")
        print("  3. 使用: factory.run_data_auditor(), factory.run_feature_profiler(), factory.run_factor_lab()")
        return True
    else:
        print("⚠️  部分核心功能测试未通过，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
