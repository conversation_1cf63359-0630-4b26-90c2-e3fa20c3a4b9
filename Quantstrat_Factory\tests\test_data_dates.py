#!/usr/bin/env python3
"""
检查清洗后数据的日期完整性

检查指定股票的2023年和2024年数据清洗到哪一天了
"""

import pandas as pd
from pathlib import Path
import sys

def check_data_dates(stock_code='sh600507'):
    """
    检查指定股票的数据日期范围
    
    Args:
        stock_code: 股票代码
    """
    print(f"🔍 检查股票 {stock_code} 的数据日期完整性")
    print("=" * 60)
    
    # 数据文件路径 - 使用新的统一路径
    data_base_path = Path("D:/PY/Data/cleaned/minute")
    
    years = [2023, 2024]
    results = {}
    
    for year in years:
        file_path = data_base_path / stock_code / f"year={year}" / "cleaned_data.parquet"
        
        print(f"\n📅 检查 {year} 年数据...")
        print(f"文件路径: {file_path}")
        
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            results[year] = None
            continue
        
        try:
            # 读取parquet文件
            df = pd.read_parquet(file_path)
            
            print(f"✅ 文件读取成功")
            print(f"   数据行数: {len(df):,}")
            print(f"   数据列数: {len(df.columns)}")
            
            # 检查列名
            print(f"   列名: {list(df.columns)}")
            
            # 查找日期列
            date_columns = []
            for col in df.columns:
                if 'date' in col.lower() or 'time' in col.lower():
                    date_columns.append(col)
            
            if not date_columns:
                print("⚠️ 未找到日期列")
                results[year] = {'error': '未找到日期列'}
                continue
            
            # 使用第一个日期列
            date_col = date_columns[0]
            print(f"   使用日期列: {date_col}")
            
            # 转换为日期类型
            if df[date_col].dtype == 'object':
                df[date_col] = pd.to_datetime(df[date_col])
            
            # 获取日期范围
            min_date = df[date_col].min()
            max_date = df[date_col].max()
            
            print(f"   📊 日期范围:")
            print(f"      开始日期: {min_date.strftime('%Y-%m-%d')}")
            print(f"      结束日期: {max_date.strftime('%Y-%m-%d')}")
            print(f"      数据天数: {(max_date - min_date).days + 1} 天")
            
            # 检查数据完整性
            date_range = pd.date_range(start=min_date.date(), end=max_date.date(), freq='D')
            unique_dates = df[date_col].dt.date.unique()
            
            missing_dates = set(date_range.date) - set(unique_dates)
            
            if missing_dates:
                print(f"   ⚠️ 缺失日期数量: {len(missing_dates)}")
                if len(missing_dates) <= 10:
                    print(f"      缺失日期: {sorted(missing_dates)}")
                else:
                    sorted_missing = sorted(missing_dates)
                    print(f"      前5个缺失日期: {sorted_missing[:5]}")
                    print(f"      后5个缺失日期: {sorted_missing[-5:]}")
            else:
                print(f"   ✅ 日期连续，无缺失")
            
            # 检查是否包含周末
            weekdays = df[date_col].dt.dayofweek.unique()
            weekend_days = [day for day in weekdays if day >= 5]  # 5=Saturday, 6=Sunday
            
            if weekend_days:
                print(f"   📅 包含周末数据: {len(weekend_days)} 种周末日")
            else:
                print(f"   📅 仅包含工作日数据")
            
            # 检查每日数据量
            daily_counts = df.groupby(df[date_col].dt.date).size()
            
            print(f"   📈 每日数据量统计:")
            print(f"      平均每日: {daily_counts.mean():.1f} 条")
            print(f"      最少一日: {daily_counts.min()} 条")
            print(f"      最多一日: {daily_counts.max()} 条")
            
            # 保存结果
            results[year] = {
                'file_exists': True,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'date_column': date_col,
                'start_date': min_date.strftime('%Y-%m-%d'),
                'end_date': max_date.strftime('%Y-%m-%d'),
                'total_days': (max_date - min_date).days + 1,
                'unique_dates': len(unique_dates),
                'missing_dates': len(missing_dates),
                'has_weekends': len(weekend_days) > 0,
                'avg_daily_records': daily_counts.mean(),
                'min_daily_records': daily_counts.min(),
                'max_daily_records': daily_counts.max()
            }
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            results[year] = {'error': str(e)}
    
    # 总结报告
    print("\n" + "=" * 60)
    print("📋 数据完整性总结报告")
    print("=" * 60)
    
    for year in years:
        result = results.get(year)
        if result is None:
            print(f"\n{year}年: ❌ 文件不存在")
        elif 'error' in result:
            print(f"\n{year}年: ❌ 错误 - {result['error']}")
        else:
            print(f"\n{year}年: ✅ 数据正常")
            print(f"  📅 数据范围: {result['start_date']} 至 {result['end_date']}")
            print(f"  📊 数据量: {result['total_rows']:,} 条记录")
            print(f"  📈 覆盖天数: {result['unique_dates']} 天")
            if result['missing_dates'] > 0:
                print(f"  ⚠️ 缺失天数: {result['missing_dates']} 天")
            print(f"  📱 平均每日: {result['avg_daily_records']:.0f} 条记录")
    
    # 对比分析
    if results.get(2023) and results.get(2024) and 'error' not in results[2023] and 'error' not in results[2024]:
        print(f"\n🔄 年度对比:")
        
        # 数据量对比
        rows_2023 = results[2023]['total_rows']
        rows_2024 = results[2024]['total_rows']
        print(f"  数据量变化: {rows_2023:,} → {rows_2024:,} ({((rows_2024-rows_2023)/rows_2023*100):+.1f}%)")
        
        # 日期范围对比
        print(f"  2023年最后日期: {results[2023]['end_date']}")
        print(f"  2024年最后日期: {results[2024]['end_date']}")
        
        # 判断2024年数据是否更新到最新
        from datetime import datetime
        today = datetime.now().date()
        end_2024 = pd.to_datetime(results[2024]['end_date']).date()
        days_behind = (today - end_2024).days
        
        if days_behind <= 1:
            print(f"  ✅ 2024年数据基本是最新的（落后 {days_behind} 天）")
        elif days_behind <= 7:
            print(f"  ⚠️ 2024年数据稍有滞后（落后 {days_behind} 天）")
        else:
            print(f"  ❌ 2024年数据明显滞后（落后 {days_behind} 天）")
    
    return results

def main():
    """主函数"""
    print("🔍 数据日期完整性检查工具")
    print("检查 sh600507 股票的2023年和2024年清洗后数据")
    
    try:
        results = check_data_dates('sh600507')
        
        print(f"\n✅ 检查完成！")
        
        # 简要结论
        print(f"\n📝 简要结论:")
        for year in [2023, 2024]:
            result = results.get(year)
            if result and 'error' not in result:
                print(f"  {year}年: 数据清洗到 {result['end_date']}")
            else:
                print(f"  {year}年: 数据不可用")
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
