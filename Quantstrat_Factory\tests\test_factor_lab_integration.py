"""
因子实验室集成测试。
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 添加因子实验室模块到路径
factor_lab_path = Path(__file__).parent.parent / "04_factor_lab" / "web_app"
sys.path.insert(0, str(factor_lab_path))

from factor_evaluator import (
    calculate_ic, 
    perform_quantile_analysis,
    comprehensive_factor_evaluation,
    calculate_ic_statistics,
    validate_input_data,
    optimize_dataframe_memory
)


@pytest.mark.integration
@pytest.mark.factor_lab
class TestFactorLabIntegration:
    """因子实验室集成测试类。"""
    
    def test_complete_factor_evaluation_workflow(self, sample_factor_data):
        """测试完整的因子评估工作流程。"""
        # 1. 数据验证
        is_valid, msg = validate_input_data(
            sample_factor_data, 'MOM', 'fwd_return_1d', min_samples=10
        )
        assert is_valid, f"数据验证失败: {msg}"
        
        # 2. IC计算
        ic_series = calculate_ic(
            sample_factor_data, 'MOM', 'fwd_return_1d', 
            min_periods=5
        )
        assert isinstance(ic_series, pd.Series)
        assert len(ic_series) > 0
        
        # 3. IC统计分析
        ic_stats = calculate_ic_statistics(ic_series)
        assert 'IC均值' in ic_stats
        assert 'ICIR' in ic_stats
        assert 'IC胜率' in ic_stats
        
        # 4. 分层回测
        net_value_df, stats_df = perform_quantile_analysis(
            sample_factor_data, 'MOM', 'fwd_return_1d', quantiles=3
        )
        assert isinstance(net_value_df, pd.DataFrame)
        assert isinstance(stats_df, pd.DataFrame)
        assert len(stats_df) >= 3  # 至少3个分层
        
        # 5. 综合评估
        comprehensive_results = comprehensive_factor_evaluation(
            sample_factor_data, 'MOM', 'fwd_return_1d', 
            quantiles=3, min_periods=5
        )
        
        required_keys = ['ic_series', 'ic_statistics', 'net_value', 
                        'quantile_stats', 'factor_effectiveness']
        for key in required_keys:
            assert key in comprehensive_results
        
        print("✅ 完整因子评估工作流程测试通过")
    
    def test_multiple_factors_evaluation(self, sample_factor_data):
        """测试多因子评估。"""
        factors = ['MOM', 'VOL']
        results = {}
        
        for factor in factors:
            try:
                result = comprehensive_factor_evaluation(
                    sample_factor_data, factor, 'fwd_return_1d',
                    quantiles=3, min_periods=5
                )
                results[factor] = result
            except Exception as e:
                pytest.fail(f"因子 {factor} 评估失败: {e}")
        
        # 验证所有因子都有结果
        assert len(results) == len(factors)
        
        # 比较因子效果
        for factor, result in results.items():
            effectiveness = result.get('factor_effectiveness', {})
            score = effectiveness.get('overall_score', 0)
            print(f"因子 {factor} 综合评分: {score}")
        
        print("✅ 多因子评估测试通过")
    
    def test_data_quality_handling(self, test_data_generator):
        """测试数据质量处理能力。"""
        # 生成包含异常值的数据
        data = test_data_generator.generate_correlated_data(
            n_samples=500, n_factors=1, correlation=0.5
        )
        
        # 添加一些异常值
        outlier_indices = np.random.choice(len(data), size=20, replace=False)
        data.loc[outlier_indices, 'factor_0'] = np.random.normal(0, 10, 20)  # 极值
        
        # 添加一些缺失值
        missing_indices = np.random.choice(len(data), size=30, replace=False)
        data.loc[missing_indices, 'fwd_return_1d'] = np.nan
        
        # 测试数据验证
        is_valid, msg = validate_input_data(
            data, 'factor_0', 'fwd_return_1d', min_samples=100
        )
        assert is_valid, f"数据验证应该通过: {msg}"
        
        # 测试IC计算（应该能处理异常值和缺失值）
        ic_series = calculate_ic(
            data, 'factor_0', 'fwd_return_1d',
            min_periods=10, clean_outliers_flag=True
        )
        
        # 验证结果合理性
        valid_ic = ic_series.dropna()
        assert len(valid_ic) > 0, "应该有有效的IC值"
        
        # IC值应该在合理范围内
        assert valid_ic.abs().max() <= 1.0, "IC值应该在[-1, 1]范围内"
        
        print("✅ 数据质量处理测试通过")
    
    @pytest.mark.performance
    def test_large_dataset_performance(self, test_data_generator, performance_benchmark):
        """测试大数据集性能。"""
        # 生成大数据集
        large_data = test_data_generator.generate_correlated_data(
            n_samples=10000, n_factors=3, correlation=0.3
        )
        
        print(f"测试数据集大小: {len(large_data)} 行")
        
        # 内存优化测试
        original_memory = large_data.memory_usage(deep=True).sum() / 1024**2
        optimized_data = optimize_dataframe_memory(large_data)
        optimized_memory = optimized_data.memory_usage(deep=True).sum() / 1024**2
        
        memory_reduction = (original_memory - optimized_memory) / original_memory
        assert memory_reduction > 0, "内存优化应该减少内存使用"
        
        # 性能测试
        import time
        
        start_time = time.time()
        ic_series = calculate_ic(
            optimized_data, 'factor_0', 'fwd_return_1d',
            min_periods=10
        )
        ic_time = time.time() - start_time
        
        start_time = time.time()
        net_value_df, stats_df = perform_quantile_analysis(
            optimized_data, 'factor_0', 'fwd_return_1d', quantiles=5
        )
        quantile_time = time.time() - start_time
        
        # 检查性能基准
        max_times = performance_benchmark['max_execution_time']
        assert ic_time <= max_times['ic_calculation'], \
            f"IC计算时间超限: {ic_time:.2f}s > {max_times['ic_calculation']}s"
        
        assert quantile_time <= max_times['quantile_analysis'], \
            f"分层分析时间超限: {quantile_time:.2f}s > {max_times['quantile_analysis']}s"
        
        print(f"✅ 大数据集性能测试通过")
        print(f"   - 内存优化: {memory_reduction:.1%}")
        print(f"   - IC计算时间: {ic_time:.2f}s")
        print(f"   - 分层分析时间: {quantile_time:.2f}s")
    
    def test_edge_cases(self):
        """测试边界情况。"""
        # 测试空数据
        empty_data = pd.DataFrame(columns=['datetime', 'symbol', 'factor', 'return'])
        
        is_valid, msg = validate_input_data(empty_data, 'factor', 'return')
        assert not is_valid, "空数据应该验证失败"
        
        # 测试单一值数据
        single_value_data = pd.DataFrame({
            'datetime': pd.date_range('2023-01-01', periods=20),
            'symbol': ['A'] * 20,
            'factor': [1.0] * 20,  # 所有值相同
            'return': np.random.normal(0, 0.01, 20)
        })
        
        ic_series = calculate_ic(
            single_value_data, 'factor', 'return', min_periods=5
        )
        # 所有因子值相同时，IC应该是NaN
        assert ic_series.isna().all(), "因子值全相同时IC应该为NaN"
        
        # 测试极小数据集
        tiny_data = pd.DataFrame({
            'datetime': pd.date_range('2023-01-01', periods=3),
            'symbol': ['A'] * 3,
            'factor': [1, 2, 3],
            'return': [0.01, 0.02, 0.03]
        })
        
        is_valid, msg = validate_input_data(tiny_data, 'factor', 'return', min_samples=10)
        assert not is_valid, "数据量不足应该验证失败"
        
        print("✅ 边界情况测试通过")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
