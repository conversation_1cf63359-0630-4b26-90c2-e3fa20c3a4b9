"""
信号生成器模块测试。
"""

import pytest
import pandas as pd
import numpy as np
import sys
from pathlib import Path

# 添加信号生成器模块到路径
signal_generator_path = Path(__file__).parent.parent / "05_signal_generator"
sys.path.insert(0, str(signal_generator_path))

from signal_generator import SignalGenerator, QuantileSignalGenerator, ThresholdSignalGenerator
from signal_combiner import SignalCombiner, WeightedSignalCombiner
from signal_filters import SignalFilter, VolatilityFilter, LiquidityFilter
from signal_evaluator import SignalEvaluator


@pytest.mark.unit
@pytest.mark.signal_generator
class TestSignalGenerator:
    """信号生成器测试类。"""
    
    @pytest.fixture
    def sample_factor_data(self):
        """创建样本因子数据。"""
        dates = pd.date_range('2023-01-01', periods=20)
        symbols = ['A', 'B', 'C', 'D', 'E']
        
        data_list = []
        for date in dates:
            for i, symbol in enumerate(symbols):
                # 创建有趋势的因子值
                factor_value = (i - 2) * 0.1 + np.random.normal(0, 0.05)
                fwd_return = factor_value * 0.3 + np.random.normal(0, 0.01)
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'momentum': factor_value,
                    'fwd_return_1d': fwd_return,
                    'volume': np.random.randint(1000, 10000),
                    'volatility': abs(np.random.normal(0.02, 0.005))
                })
        
        return pd.DataFrame(data_list)
    
    def test_quantile_signal_generator(self, sample_factor_data):
        """测试分位数信号生成器。"""
        generator = QuantileSignalGenerator(quantiles=5, long_quantile=1, short_quantile=5)
        
        # 生成信号
        result = generator.generate_signals(sample_factor_data)
        
        # 验证结果
        assert 'signal' in result.columns
        assert len(result) == len(sample_factor_data)
        
        # 验证信号值
        unique_signals = result['signal'].unique()
        assert set(unique_signals).issubset({-1, 0, 1})
        
        # 验证每个日期都有信号
        signals_per_date = result.groupby('datetime')['signal'].apply(lambda x: (x != 0).sum())
        assert all(signals_per_date > 0)
        
        print("✅ 分位数信号生成器测试通过")
    
    def test_threshold_signal_generator(self, sample_factor_data):
        """测试阈值信号生成器。"""
        generator = ThresholdSignalGenerator(
            long_threshold=0.1, 
            short_threshold=-0.1, 
            method='absolute'
        )
        
        # 生成信号
        result = generator.generate_signals(sample_factor_data)
        
        # 验证结果
        assert 'signal' in result.columns
        assert len(result) == len(sample_factor_data)
        
        # 验证信号逻辑
        long_signals = result[result['signal'] == 1]
        short_signals = result[result['signal'] == -1]
        
        if len(long_signals) > 0:
            assert all(long_signals['momentum'] >= 0.1)
        
        if len(short_signals) > 0:
            assert all(short_signals['momentum'] <= -0.1)
        
        print("✅ 阈值信号生成器测试通过")
    
    def test_signal_generator_main_class(self, sample_factor_data):
        """测试主信号生成器类。"""
        generator = SignalGenerator()
        
        # 测试分位数方法
        result1 = generator.generate_signals(
            sample_factor_data, 
            method='quantile',
            quantiles=3
        )
        assert 'signal' in result1.columns
        
        # 测试阈值方法
        result2 = generator.generate_signals(
            sample_factor_data,
            method='threshold',
            long_threshold=0.05,
            short_threshold=-0.05
        )
        assert 'signal' in result2.columns
        
        # 测试批量生成
        methods = [
            {'name': 'quantile_3', 'method': 'quantile', 'quantiles': 3},
            {'name': 'threshold_abs', 'method': 'threshold', 'long_threshold': 0.1, 'short_threshold': -0.1}
        ]
        
        batch_results = generator.batch_generate_signals(sample_factor_data, methods)
        assert len(batch_results) == 2
        assert 'quantile_3' in batch_results
        assert 'threshold_abs' in batch_results
        
        print("✅ 主信号生成器类测试通过")
    
    def test_signal_quality_evaluation(self, sample_factor_data):
        """测试信号质量评估。"""
        generator = SignalGenerator()
        
        # 生成信号
        signal_data = generator.generate_signals(
            sample_factor_data,
            method='quantile',
            quantiles=5
        )
        
        # 评估信号质量
        metrics = generator.evaluate_signal_quality(signal_data, 'fwd_return_1d')
        
        # 验证指标
        expected_metrics = [
            'signal_return_mean', 'signal_return_std', 'signal_sharpe',
            'signal_coverage', 'long_ratio', 'short_ratio', 'turnover'
        ]
        
        for metric in expected_metrics:
            assert metric in metrics
            assert isinstance(metrics[metric], (int, float))
        
        print("✅ 信号质量评估测试通过")


@pytest.mark.unit
@pytest.mark.signal_generator
class TestSignalCombiner:
    """信号合成器测试类。"""
    
    @pytest.fixture
    def sample_signals(self, sample_factor_data):
        """创建样本信号数据。"""
        generator = SignalGenerator()
        
        # 生成多个信号
        signal1 = generator.generate_signals(
            sample_factor_data, method='quantile', quantiles=3
        )
        signal1['signal'] = signal1['signal'] * 0.8  # 调整信号强度
        
        signal2 = generator.generate_signals(
            sample_factor_data, method='threshold', 
            long_threshold=0.05, short_threshold=-0.05
        )
        signal2['signal'] = signal2['signal'] * 1.2
        
        return {'momentum_quantile': signal1, 'momentum_threshold': signal2}
    
    def test_weighted_signal_combiner(self, sample_signals):
        """测试加权信号合成器。"""
        # 等权重合成
        combiner = WeightedSignalCombiner()
        result = combiner.combine_signals(sample_signals)
        
        assert 'signal' in result.columns
        assert 'combined_signal_raw' in result.columns
        assert len(result) == len(list(sample_signals.values())[0])
        
        # 自定义权重合成
        weights = {'momentum_quantile': 0.7, 'momentum_threshold': 0.3}
        combiner_weighted = WeightedSignalCombiner(weights)
        result_weighted = combiner_weighted.combine_signals(sample_signals)
        
        assert 'signal' in result_weighted.columns
        
        print("✅ 加权信号合成器测试通过")
    
    def test_signal_combiner_main_class(self, sample_signals):
        """测试主信号合成器类。"""
        combiner = SignalCombiner()
        
        # 测试加权合成
        result = combiner.combine_signals(sample_signals, method='weighted')
        assert 'signal' in result.columns
        
        print("✅ 主信号合成器类测试通过")


@pytest.mark.unit
@pytest.mark.signal_generator
class TestSignalFilters:
    """信号过滤器测试类。"""
    
    @pytest.fixture
    def sample_signal_data(self, sample_factor_data):
        """创建样本信号数据。"""
        generator = SignalGenerator()
        return generator.generate_signals(sample_factor_data, method='quantile', quantiles=5)
    
    def test_volatility_filter(self, sample_signal_data):
        """测试波动率过滤器。"""
        filter_obj = VolatilityFilter(
            volatility_col='volatility',
            min_volatility=0.015,
            max_volatility=0.025
        )
        
        filtered_data = filter_obj.filter_signals(sample_signal_data)
        
        # 验证过滤效果
        assert len(filtered_data) == len(sample_signal_data)
        
        # 检查被过滤的信号
        original_signals = (sample_signal_data['signal'] != 0).sum()
        filtered_signals = (filtered_data['signal'] != 0).sum()
        
        # 过滤后信号数量应该减少或保持不变
        assert filtered_signals <= original_signals
        
        print("✅ 波动率过滤器测试通过")
    
    def test_liquidity_filter(self, sample_signal_data):
        """测试流动性过滤器。"""
        filter_obj = LiquidityFilter(
            volume_col='volume',
            min_volume=5000,
            liquidity_percentile=20
        )
        
        filtered_data = filter_obj.filter_signals(sample_signal_data)
        
        assert len(filtered_data) == len(sample_signal_data)
        
        print("✅ 流动性过滤器测试通过")
    
    def test_signal_filter_main_class(self, sample_signal_data):
        """测试主信号过滤器类。"""
        filter_obj = SignalFilter()
        
        # 配置多个过滤器
        filter_configs = [
            {
                'type': 'volatility',
                'volatility_col': 'volatility',
                'min_volatility': 0.01,
                'max_volatility': 0.03
            },
            {
                'type': 'liquidity',
                'volume_col': 'volume',
                'min_volume': 3000
            }
        ]
        
        filtered_data = filter_obj.apply_filters(sample_signal_data, filter_configs)
        
        assert len(filtered_data) == len(sample_signal_data)
        assert 'signal' in filtered_data.columns
        
        print("✅ 主信号过滤器类测试通过")


@pytest.mark.unit
@pytest.mark.signal_generator
class TestSignalEvaluator:
    """信号评估器测试类。"""
    
    @pytest.fixture
    def sample_signal_data(self, sample_factor_data):
        """创建样本信号数据。"""
        generator = SignalGenerator()
        return generator.generate_signals(sample_factor_data, method='quantile', quantiles=5)
    
    def test_signal_performance_evaluation(self, sample_signal_data):
        """测试信号性能评估。"""
        evaluator = SignalEvaluator()
        
        # 评估信号性能
        results = evaluator.evaluate_signal_performance(
            sample_signal_data, 
            return_col='fwd_return_1d'
        )
        
        # 验证结果结构
        expected_sections = [
            'basic_stats', 'return_analysis', 'signal_quality',
            'risk_analysis', 'trading_cost_analysis', 'overall_score'
        ]
        
        for section in expected_sections:
            assert section in results
            assert isinstance(results[section], dict)
        
        # 验证基础统计
        basic_stats = results['basic_stats']
        assert 'signal_coverage' in basic_stats
        assert 'long_coverage' in basic_stats
        assert 'short_coverage' in basic_stats
        
        # 验证收益率分析
        return_analysis = results['return_analysis']
        assert 'mean_daily_return' in return_analysis
        assert 'sharpe_ratio' in return_analysis
        
        # 验证信号质量
        signal_quality = results['signal_quality']
        assert 'ic_mean' in signal_quality
        assert 'icir' in signal_quality
        
        # 验证综合评分
        overall_score = results['overall_score']
        assert 'total_score' in overall_score
        assert 0 <= overall_score['total_score'] <= 100
        
        print("✅ 信号性能评估测试通过")


@pytest.mark.integration
@pytest.mark.signal_generator
class TestSignalGeneratorIntegration:
    """信号生成器集成测试。"""
    
    def test_complete_signal_workflow(self, sample_factor_data):
        """测试完整的信号生成工作流。"""
        # 1. 生成多个信号
        generator = SignalGenerator()
        
        signal1 = generator.generate_signals(
            sample_factor_data, method='quantile', quantiles=5
        )
        signal2 = generator.generate_signals(
            sample_factor_data, method='threshold',
            long_threshold=0.1, short_threshold=-0.1
        )
        
        # 2. 合成信号
        combiner = SignalCombiner()
        combined_signal = combiner.combine_signals(
            {'signal1': signal1, 'signal2': signal2},
            method='weighted'
        )
        
        # 3. 过滤信号
        filter_obj = SignalFilter()
        filter_configs = [
            {
                'type': 'volatility',
                'volatility_col': 'volatility',
                'min_volatility': 0.01
            }
        ]
        filtered_signal = filter_obj.apply_filters(combined_signal, filter_configs)
        
        # 4. 评估信号
        evaluator = SignalEvaluator()
        evaluation_results = evaluator.evaluate_signal_performance(
            filtered_signal, 'fwd_return_1d'
        )
        
        # 验证整个流程
        assert 'signal' in filtered_signal.columns
        assert 'overall_score' in evaluation_results
        assert len(filtered_signal) == len(sample_factor_data)
        
        print("✅ 完整信号生成工作流测试通过")
        print(f"   - 信号覆盖率: {evaluation_results['basic_stats']['signal_coverage']:.2%}")
        print(f"   - IC均值: {evaluation_results['signal_quality']['ic_mean']:.4f}")
        print(f"   - 综合评分: {evaluation_results['overall_score']['total_score']:.1f}/100")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
