"""
TDD实践示例：开发一个新的因子评分器。

这个示例展示了如何使用TDD方法开发一个新功能。
"""

import pytest
import pandas as pd
import numpy as np
from typing import Dict, List


class TestFactorScorer:
    """
    因子评分器测试类。
    
    这个类展示了TDD的完整流程：
    1. 先写测试（Red）
    2. 实现功能（Green）
    3. 重构优化（Refactor）
    """
    
    @pytest.fixture
    def sample_factor_data(self):
        """创建测试用的因子数据。"""
        np.random.seed(42)  # 确保测试结果可重复
        
        dates = pd.date_range('2023-01-01', periods=50)
        symbols = ['A', 'B', 'C', 'D', 'E']
        
        data_list = []
        for date in dates:
            for i, symbol in enumerate(symbols):
                # 创建有一定相关性的因子和收益率
                factor_value = (i - 2) * 0.1 + np.random.normal(0, 0.05)
                fwd_return = factor_value * 0.3 + np.random.normal(0, 0.01)
                
                data_list.append({
                    'datetime': date,
                    'symbol': symbol,
                    'momentum': factor_value,
                    'value': np.random.normal(0, 1),
                    'quality': np.random.normal(0, 0.8),
                    'fwd_return_1d': fwd_return,
                    'fwd_return_5d': fwd_return * 5 + np.random.normal(0, 0.02)
                })
        
        return pd.DataFrame(data_list)
    
    # ========== 第一轮 TDD：基础评分功能 ==========
    
    def test_should_return_score_between_0_and_100(self, sample_factor_data):
        """
        测试：因子评分应该在0到100之间。
        
        这是第一个测试（Red阶段），定义了基本的接口和期望。
        """
        scorer = FactorScorer()
        score = scorer.score_factor(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        assert isinstance(score, (int, float))
        assert 0 <= score <= 100
    
    def test_should_return_higher_score_for_better_factor(self, sample_factor_data):
        """
        测试：更好的因子应该得到更高的评分。
        
        这个测试定义了评分的相对性：好因子 > 差因子。
        """
        scorer = FactorScorer()
        
        # momentum因子有正相关性，应该得分较高
        momentum_score = scorer.score_factor(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        # value因子是随机的，应该得分较低
        value_score = scorer.score_factor(sample_factor_data, 'value', 'fwd_return_1d')
        
        assert momentum_score > value_score
    
    # ========== 第二轮 TDD：详细评分组件 ==========
    
    def test_should_calculate_ic_component_score(self, sample_factor_data):
        """
        测试：应该计算IC组件评分。
        
        扩展功能：评分应该包含IC分析组件。
        """
        scorer = FactorScorer()
        components = scorer.get_score_components(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        assert 'ic_score' in components
        assert isinstance(components['ic_score'], (int, float))
        assert 0 <= components['ic_score'] <= 40  # IC最大40分
    
    def test_should_calculate_return_component_score(self, sample_factor_data):
        """
        测试：应该计算收益率组件评分。
        """
        scorer = FactorScorer()
        components = scorer.get_score_components(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        assert 'return_score' in components
        assert isinstance(components['return_score'], (int, float))
        assert 0 <= components['return_score'] <= 30  # 收益率最大30分
    
    def test_should_calculate_stability_component_score(self, sample_factor_data):
        """
        测试：应该计算稳定性组件评分。
        """
        scorer = FactorScorer()
        components = scorer.get_score_components(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        assert 'stability_score' in components
        assert isinstance(components['stability_score'], (int, float))
        assert 0 <= components['stability_score'] <= 30  # 稳定性最大30分
    
    # ========== 第三轮 TDD：边界条件和异常处理 ==========
    
    def test_should_handle_empty_data(self):
        """
        测试：应该处理空数据。
        
        边界条件测试：确保函数能优雅地处理异常情况。
        """
        scorer = FactorScorer()
        empty_data = pd.DataFrame()
        
        with pytest.raises(ValueError, match="输入数据不能为空"):
            scorer.score_factor(empty_data, 'factor', 'return')
    
    def test_should_handle_missing_columns(self, sample_factor_data):
        """
        测试：应该处理缺失列。
        """
        scorer = FactorScorer()
        
        with pytest.raises(ValueError, match="未找到因子列"):
            scorer.score_factor(sample_factor_data, 'nonexistent_factor', 'fwd_return_1d')
    
    def test_should_handle_insufficient_data(self):
        """
        测试：应该处理数据量不足的情况。
        """
        scorer = FactorScorer()
        
        # 创建只有很少数据的DataFrame
        small_data = pd.DataFrame({
            'datetime': pd.date_range('2023-01-01', periods=2),
            'symbol': ['A', 'B'],
            'factor': [1, 2],
            'return': [0.01, 0.02]
        })
        
        score = scorer.score_factor(small_data, 'factor', 'return')
        
        # 数据不足时应该返回较低的评分
        assert score < 50
    
    # ========== 第四轮 TDD：性能测试 ==========
    
    @pytest.mark.performance
    def test_should_complete_scoring_within_time_limit(self):
        """
        测试：评分应该在时间限制内完成。
        
        性能测试：确保函数在大数据集上的性能。
        """
        import time
        
        # 创建大数据集
        large_data = self._create_large_dataset(10000)
        
        scorer = FactorScorer()
        
        start_time = time.time()
        score = scorer.score_factor(large_data, 'factor', 'return')
        execution_time = time.time() - start_time
        
        assert execution_time < 5.0  # 应在5秒内完成
        assert isinstance(score, (int, float))
    
    def _create_large_dataset(self, n_samples):
        """创建大数据集用于性能测试。"""
        np.random.seed(42)
        
        return pd.DataFrame({
            'datetime': pd.date_range('2023-01-01', periods=n_samples//100, freq='D').repeat(100),
            'symbol': [f'STOCK_{i:03d}' for i in range(100)] * (n_samples//100),
            'factor': np.random.normal(0, 1, n_samples),
            'return': np.random.normal(0, 0.02, n_samples)
        })
    
    # ========== 第五轮 TDD：集成测试 ==========
    
    @pytest.mark.integration
    def test_should_integrate_with_factor_evaluator(self, sample_factor_data):
        """
        测试：应该与因子评估器集成。
        
        集成测试：确保新功能与现有系统兼容。
        """
        # 这里假设我们要与现有的因子评估器集成
        scorer = FactorScorer()
        
        # 获取详细的评分组件
        components = scorer.get_score_components(sample_factor_data, 'momentum', 'fwd_return_1d')
        
        # 验证组件完整性
        expected_components = ['ic_score', 'return_score', 'stability_score']
        for component in expected_components:
            assert component in components
        
        # 验证总分等于各组件之和
        total_score = scorer.score_factor(sample_factor_data, 'momentum', 'fwd_return_1d')
        component_sum = sum(components.values())
        
        assert abs(total_score - component_sum) < 0.01  # 允许小的浮点误差


# ========== 实现部分（Green阶段）==========

class FactorScorer:
    """
    因子评分器实现。
    
    这个实现是在测试驱动下逐步完善的。
    """
    
    def __init__(self):
        """初始化因子评分器。"""
        self.max_ic_score = 40
        self.max_return_score = 30
        self.max_stability_score = 30
    
    def score_factor(self, data: pd.DataFrame, factor_col: str, return_col: str) -> float:
        """
        对因子进行评分。
        
        Args:
            data: 包含因子和收益率的数据
            factor_col: 因子列名
            return_col: 收益率列名
            
        Returns:
            因子评分（0-100）
        """
        # 数据验证
        self._validate_input(data, factor_col, return_col)
        
        # 获取评分组件
        components = self.get_score_components(data, factor_col, return_col)
        
        # 计算总分
        total_score = sum(components.values())
        
        return min(100, max(0, total_score))
    
    def get_score_components(self, data: pd.DataFrame, factor_col: str, return_col: str) -> Dict[str, float]:
        """
        获取评分组件。
        
        Returns:
            包含各组件评分的字典
        """
        components = {}
        
        # IC评分组件
        components['ic_score'] = self._calculate_ic_score(data, factor_col, return_col)
        
        # 收益率评分组件
        components['return_score'] = self._calculate_return_score(data, factor_col, return_col)
        
        # 稳定性评分组件
        components['stability_score'] = self._calculate_stability_score(data, factor_col, return_col)
        
        return components
    
    def _validate_input(self, data: pd.DataFrame, factor_col: str, return_col: str):
        """验证输入数据。"""
        if data.empty:
            raise ValueError("输入数据不能为空")
        
        if factor_col not in data.columns:
            raise ValueError(f"未找到因子列: {factor_col}")
        
        if return_col not in data.columns:
            raise ValueError(f"未找到收益率列: {return_col}")
    
    def _calculate_ic_score(self, data: pd.DataFrame, factor_col: str, return_col: str) -> float:
        """计算IC评分组件。"""
        # 计算IC
        ic_values = []
        for date in data['datetime'].unique():
            date_data = data[data['datetime'] == date]
            if len(date_data) > 1:
                ic = date_data[factor_col].corr(date_data[return_col])
                if not np.isnan(ic):
                    ic_values.append(ic)
        
        if not ic_values:
            return 0
        
        # 基于IC均值和胜率计算评分
        ic_mean = np.mean(ic_values)
        ic_win_rate = np.mean([ic > 0 for ic in ic_values])
        
        # 评分逻辑
        ic_score = abs(ic_mean) * 20 + (ic_win_rate - 0.5) * 40
        
        return min(self.max_ic_score, max(0, ic_score))
    
    def _calculate_return_score(self, data: pd.DataFrame, factor_col: str, return_col: str) -> float:
        """计算收益率评分组件。"""
        # 简化的收益率评分：基于因子与收益率的整体相关性
        correlation = data[factor_col].corr(data[return_col])
        
        if np.isnan(correlation):
            return 0
        
        # 评分逻辑
        return_score = abs(correlation) * self.max_return_score
        
        return min(self.max_return_score, max(0, return_score))
    
    def _calculate_stability_score(self, data: pd.DataFrame, factor_col: str, return_col: str) -> float:
        """计算稳定性评分组件。"""
        # 计算因子值的稳定性
        factor_std = data[factor_col].std()
        factor_mean = abs(data[factor_col].mean())
        
        if factor_mean == 0:
            return 0
        
        # 稳定性评分：变异系数的倒数
        cv = factor_std / factor_mean
        stability_score = self.max_stability_score / (1 + cv)
        
        return min(self.max_stability_score, max(0, stability_score))


# ========== 测试运行示例 ==========

if __name__ == "__main__":
    # 这个示例展示了如何运行TDD测试
    pytest.main([__file__, "-v", "--tb=short"])
