import unittest
import pandas as pd
from pathlib import Path
import sys
import numpy as np

# 确保可以从测试文件正确导入 analysis_performer
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from analysis_performer import perform_analysis

class TestAnalysisPerformer(unittest.TestCase):
    """
    测试 analysis_performer.py 的功能。
    """

    def setUp(self):
        """
        为所有测试准备一个通用的DataFrame。
        这个DataFrame模拟了从data_loader加载的数据。
        """
        self.sample_data = pd.DataFrame({
            'datetime': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']),
            'symbol': ['000001.SZ'] * 5,
            'close': [10, 11, 12, 11, 13]
        })

    def test_calculate_momentum_factor(self):
        """
        测试: 能否正确计算动量(MOM)因子。
        这个测试现在会失败，因为函数返回的是字符串而不是DataFrame。
        我们将修改函数以让此测试通过。
        """
        print("\n--- 运行目标测试: test_calculate_momentum_factor ---")
        
        # 动量因子 (1期) = close / close.shift(1) - 1
        # 我们将使用一个简单的实现作为目标
        
        # 调用函数
        result_df = perform_analysis(self.sample_data.copy(), ['MOM'])

        # 断言1: 返回值应该是一个DataFrame
        self.assertIsInstance(result_df, pd.DataFrame, "返回值应该是DataFrame")

        # 断言2: 结果中应该包含 'MOM' 列
        self.assertIn('MOM', result_df.columns, "结果中应包含 'MOM' 列")

        # 断言3: 动量因子的值应该正确 (这里我们只检查第一个非NaN值)
        # close[1]/close[0] - 1 = 11/10 - 1 = 0.1
        expected_mom_value = 0.1
        self.assertAlmostEqual(result_df['MOM'].iloc[1], expected_mom_value, places=4)
        
        print("--- 目标测试通过 ---")

    def test_calculate_volatility_factor(self):
        """
        测试: 能否正确计算波动率(VOL)因子。
        这个测试在 'VOL' 逻辑实现前应该会失败。
        """
        print("\n--- 运行目标测试: test_calculate_volatility_factor ---")
        
        # 波动率因子定义为收盘价收益率的2期滚动标准差
        # 调用函数
        result_df = perform_analysis(self.sample_data.copy(), ['VOL'])

        # 断言1: 返回值应该是一个DataFrame
        self.assertIsInstance(result_df, pd.DataFrame, "返回值应该是DataFrame")

        # 断言2: 结果中应该包含 'VOL' 列
        self.assertIn('VOL', result_df.columns, "结果中应包含 'VOL' 列")

        # 断言3: 波动率因子的值应该正确
        # returns = [NaN, 0.1, 0.090909, -0.083333, 0.181818]
        # vol[2] = std(returns[1], returns[2]) = std(0.1, 0.090909) = 0.006429
        # pandas.Series.std uses ddof=1 by default.
        expected_vol_value = 0.0064
        self.assertAlmostEqual(result_df['VOL'].iloc[2], expected_vol_value, places=4)
        
        print("--- 目标测试通过 ---")


if __name__ == '__main__':
    unittest.main()
