import unittest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 确保可以从测试文件正确导入
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# 假设新功能将在此模块中实现
from composite_factor_engine import calculate_composite_factors

class TestCompositeFactorEngine(unittest.TestCase):
    """
    测试 composite_factor_engine.py 的功能。
    """

    def setUp(self):
        """
        为测试准备一个包含基础因子的DataFrame。
        """
        self.sample_data = pd.DataFrame({
            'datetime': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
            'symbol': ['A', 'A', 'A'],
            'MOM': [0.1, -0.05, 0.2],
            'VOL': [0.02, 0.03, 0.01]
        })

    def test_calculate_simple_composite_factor(self):
        """
        测试: 能否根据表达式正确计算一个简单的复合因子。
        """
        print("\n--- 运行目标测试: test_calculate_simple_composite_factor ---")
        
        # 定义复合因子表达式
        composite_definitions = {
            'COMP_A': 'MOM + VOL'
        }
        
        # 调用函数
        result_df = calculate_composite_factors(self.sample_data.copy(), composite_definitions)

        # 断言1: 返回值应该是一个DataFrame
        self.assertIsInstance(result_df, pd.DataFrame, "返回值应该是DataFrame")

        # 断言2: 结果中应该包含 'COMP_A' 列
        self.assertIn('COMP_A', result_df.columns, "结果中应包含 'COMP_A' 列")

        # 断言3: 复合因子的值应该正确
        # COMP_A[0] = MOM[0] + VOL[0] = 0.1 + 0.02 = 0.12
        expected_value = 0.12
        self.assertAlmostEqual(result_df['COMP_A'].iloc[0], expected_value, places=4)
        
        print("--- 目标测试通过 ---")

    def test_handles_invalid_expression(self):
        """
        测试: 当提供无效表达式时，函数应能优雅地处理。
        """
        print("\n--- 运行目标测试: test_handles_invalid_expression ---")
        
        # 定义一个包含无效列名的表达式
        composite_definitions = {
            'COMP_B': 'MOM * INVALID_FACTOR'
        }
        
        # 调用函数，预期它不会崩溃，并返回原始DataFrame
        result_df = calculate_composite_factors(self.sample_data.copy(), composite_definitions)
        
        # 断言: 'COMP_B' 列不应该被创建
        self.assertNotIn('COMP_B', result_df.columns, "不应创建包含无效表达式的列")
        
        print("--- 目标测试通过 ---")


if __name__ == '__main__':
    unittest.main()
