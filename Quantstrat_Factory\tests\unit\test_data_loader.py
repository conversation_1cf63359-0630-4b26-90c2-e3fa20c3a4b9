import unittest
import pandas as pd
from pathlib import Path
import sys

# 确保可以从测试文件正确导入 load_data
# test_data_loader.py -> web_app
current_dir = Path(__file__).parent
# 由于 data_loader 依赖 feature_store_client，需要将项目根目录也加入 sys.path
project_root = current_dir.parent.parent.parent
sys.path.insert(0, str(project_root))
sys.path.append(str(current_dir))


from data_loader import load_data

class TestDataLoader(unittest.TestCase):
    """
    测试 data_loader.py 的功能。
    """

    def test_load_data_returns_non_empty_dataframe(self):
        """
        测试: 在一个已知有数据的日期范围内，load_data 函数应返回一个非空的DataFrame。
        这个测试旨在复现“数据为空”的问题。
        """
        print("\n--- 运行测试: test_load_data_returns_non_empty_dataframe ---")
        
        # 根据 inspect_feature_store.py 的诊断结果，使用一个已知有数据的日期范围
        start_date = '2020-10-10'
        end_date = '2020-10-20'
        stock_pool = 'ALL' # 使用 'ALL' 以避免股票池过滤引入的复杂性

        # 调用被测试的函数 - 传入空的因子列表，预期只加载基础数据
        df = load_data(stock_pool, start_date, end_date, [], [])

        # 打印一些诊断信息
        print(f"load_data 返回的DataFrame形状: {df.shape if isinstance(df, pd.DataFrame) else '非DataFrame'}")
        
        # 核心断言：检查返回的是否为DataFrame，并且不为空
        self.assertIsInstance(df, pd.DataFrame, "返回的不是一个DataFrame对象")
        self.assertFalse(df.empty, f"数据加载失败，返回了一个空的DataFrame。股票池: {stock_pool}, 日期: {start_date} to {end_date}")
        
        print("--- 测试通过 ---")

    def test_load_data_with_technical_factors(self):
        """
        测试: 能否正确加载指定的技术指标因子。
        这个测试在 data_loader 支持动态因子加载前应该会失败。
        """
        print("\n--- 运行目标测试: test_load_data_with_technical_factors ---")
        start_date = '2020-10-10'
        end_date = '2020-10-20'
        stock_pool = 'ALL'
        # 假设 'MA_5' 是一个存在于 'technical' 目录下的因子
        selected_factors = ['MA_5']

        # 调用函数
        df = load_data(stock_pool, start_date, end_date, selected_factors, [])

        # 断言1: 返回值应该是一个DataFrame
        self.assertIsInstance(df, pd.DataFrame, "返回值应该是DataFrame")

        # 断言2: 结果中应该包含 'MA_5' 列
        self.assertIn('MA_5', df.columns, "结果中应包含 'MA_5' 列")
        
        print("--- 目标测试通过 ---")

    def test_calculate_forward_returns(self):
        """
        测试: 能否正确计算未来收益率。
        """
        print("\n--- 运行目标测试: test_calculate_forward_returns ---")
        start_date = '2020-10-10'
        end_date = '2020-10-20'
        stock_pool = 'ALL'
        
        # 请求计算未来1期和5期的收益率
        df = load_data(stock_pool, start_date, end_date, [], forward_return_periods=[1, 5])

        # 断言1: 结果中应包含相应的列
        self.assertIn('fwd_return_1d', df.columns, "结果中应包含 'fwd_return_1d' 列")
        self.assertIn('fwd_return_5d', df.columns, "结果中应包含 'fwd_return_5d' 列")

        # 断言2: 验证一个具体的值
        # 为了验证，我们需要一个可预测的数据子集
        # 假设我们知道 000001.SZ 在某两天的数据
        # close_t0 = 15.0, close_t1 = 15.75
        # fwd_return_1d at t0 should be (15.75 / 15.0) - 1 = 0.05
        # 这个断言依赖于实际数据，如果数据变化可能会失败，但对于开发阶段是有效的
        # 暂时注释掉，因为我们无法保证测试数据的稳定性
        # sub_df = df[df['symbol'] == 'some_known_symbol']
        # self.assertAlmostEqual(sub_df['fwd_return_1d'].iloc[0], 0.05, places=4)
        
        print("--- 目标测试通过 ---")


if __name__ == '__main__':
    unittest.main()
