"""
数据源管理模块的单元测试。

遵循TDD原则，测试数据源管理的各项功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, date
import tempfile
import shutil
from pathlib import Path

from src.data.sources import (
    DataSourceManager,
    DataSourceBase,
    DataSourceConfig,
    LocalDataSource,
    WindDataSource,
    TonghuashunDataSource
)


class TestDataSourceConfig:
    """测试数据源配置类。"""
    
    def test_config_creation(self):
        """测试配置创建。"""
        config = DataSourceConfig(
            name="test_source",
            enabled=True,
            priority=1
        )
        
        assert config.name == "test_source"
        assert config.enabled is True
        assert config.priority == 1
        assert config.timeout == 30
        assert config.retry_count == 3
        assert config.cache_enabled is True
        assert config.config == {}
    
    def test_config_with_custom_params(self):
        """测试自定义参数配置。"""
        custom_config = {"api_key": "test_key", "endpoint": "test_url"}
        config = DataSourceConfig(
            name="custom_source",
            timeout=60,
            config=custom_config
        )
        
        assert config.timeout == 60
        assert config.config == custom_config


class TestDataSourceBase:
    """测试数据源基类。"""
    
    def test_abstract_methods(self):
        """测试抽象方法。"""
        config = DataSourceConfig(name="test")
        
        # 不能直接实例化抽象类
        with pytest.raises(TypeError):
            DataSourceBase(config)
    
    def test_validate_symbols(self):
        """测试股票代码验证。"""
        # 创建一个具体的实现来测试基类方法
        class TestDataSource(DataSourceBase):
            def connect(self): return True
            def disconnect(self): pass
            def is_connected(self): return True
            def get_stock_list(self, market="all"): return []
            def get_daily_data(self, symbols, start_date, end_date, fields=None): return pd.DataFrame()
            def get_minute_data(self, symbols, start_date, end_date, frequency="1min", fields=None): return pd.DataFrame()
            def get_fundamental_data(self, symbols, start_date, end_date, fields=None): return pd.DataFrame()
        
        config = DataSourceConfig(name="test")
        source = TestDataSource(config)
        
        # 测试单个股票代码
        result = source.validate_symbols("600000")
        assert result == ["600000"]
        
        # 测试多个股票代码
        result = source.validate_symbols(["600000", "000001", "300001"])
        assert result == ["600000", "000001", "300001"]
        
        # 测试无效代码
        result = source.validate_symbols(["12345", ""])
        assert result == []
    
    def test_normalize_date(self):
        """测试日期标准化。"""
        class TestDataSource(DataSourceBase):
            def connect(self): return True
            def disconnect(self): pass
            def is_connected(self): return True
            def get_stock_list(self, market="all"): return []
            def get_daily_data(self, symbols, start_date, end_date, fields=None): return pd.DataFrame()
            def get_minute_data(self, symbols, start_date, end_date, frequency="1min", fields=None): return pd.DataFrame()
            def get_fundamental_data(self, symbols, start_date, end_date, fields=None): return pd.DataFrame()
        
        config = DataSourceConfig(name="test")
        source = TestDataSource(config)
        
        # 测试字符串日期
        assert source.normalize_date("20230101") == "2023-01-01"
        assert source.normalize_date("2023-01-01") == "2023-01-01"
        
        # 测试datetime对象
        dt = datetime(2023, 1, 1)
        assert source.normalize_date(dt) == "2023-01-01"
        
        # 测试date对象
        d = date(2023, 1, 1)
        assert source.normalize_date(d) == "2023-01-01"
        
        # 测试无效日期
        with pytest.raises(ValueError):
            source.normalize_date("invalid_date")


class TestLocalDataSource:
    """测试本地数据源。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = DataSourceConfig(
            name="local_test",
            config={
                "data_paths": {
                    "raw_minute": str(Path(self.temp_dir) / "raw"),
                    "cleaned_minute": str(Path(self.temp_dir) / "cleaned"),
                    "daily": str(Path(self.temp_dir) / "daily")
                }
            }
        )
        
        # 创建测试目录
        (Path(self.temp_dir) / "raw").mkdir(parents=True)
        (Path(self.temp_dir) / "cleaned").mkdir(parents=True)
        (Path(self.temp_dir) / "daily").mkdir(parents=True)
    
    def teardown_method(self):
        """清理测试环境。"""
        shutil.rmtree(self.temp_dir)
    
    def test_connection(self):
        """测试连接功能。"""
        source = LocalDataSource(self.config)
        
        # 测试成功连接
        assert source.connect() is True
        assert source.is_connected() is True
        
        # 测试断开连接
        source.disconnect()
        assert source.is_connected() is False
    
    def test_get_stock_list_from_cleaned_data(self):
        """测试从清洗数据获取股票列表。"""
        source = LocalDataSource(self.config)
        source.connect()
        
        # 创建测试股票目录
        cleaned_path = Path(self.temp_dir) / "cleaned"
        (cleaned_path / "sh600000").mkdir()
        (cleaned_path / "sz000001").mkdir()
        (cleaned_path / "sz300001").mkdir()
        
        stocks = source.get_stock_list()
        assert "sh600000" in stocks
        assert "sz000001" in stocks
        assert "sz300001" in stocks
        
        # 测试市场过滤
        sh_stocks = source.get_stock_list(market="sh")
        assert "sh600000" in sh_stocks
        assert "sz000001" not in sh_stocks
    
    def test_get_minute_data(self):
        """测试获取分钟数据。"""
        source = LocalDataSource(self.config)
        source.connect()
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'datetime': pd.date_range('2023-01-01 09:30:00', periods=10, freq='1min'),
            'open': np.random.rand(10) * 100,
            'high': np.random.rand(10) * 100,
            'low': np.random.rand(10) * 100,
            'close': np.random.rand(10) * 100,
            'volume': np.random.randint(1000, 10000, 10)
        })
        
        # 保存测试数据
        stock_dir = Path(self.temp_dir) / "cleaned" / "sh600000" / "year=2023"
        stock_dir.mkdir(parents=True)
        test_data.to_parquet(stock_dir / "cleaned_data.parquet", index=False)
        
        # 测试数据获取
        result = source.get_minute_data(
            symbols=["sh600000"],
            start_date="2023-01-01",
            end_date="2023-01-01"
        )
        
        assert not result.empty
        assert "symbol" in result.columns
        assert result["symbol"].iloc[0] == "sh600000"


class TestDataSourceManager:
    """测试数据源管理器。"""
    
    def test_initialization(self):
        """测试初始化。"""
        config = {
            "local": {"enabled": True},
            "wind": {"enabled": False},
            "tonghuashun": {"enabled": False}
        }
        
        manager = DataSourceManager(config)
        
        assert "local" in manager.data_sources
        assert manager.cache_enabled is True
        assert isinstance(manager.cache, dict)
    
    def test_connect_all(self):
        """测试连接所有数据源。"""
        config = {"local": {"enabled": True}}
        manager = DataSourceManager(config)
        
        with patch.object(manager.data_sources["local"], "connect", return_value=True):
            results = manager.connect_all()
            assert results["local"] is True
            assert "local" in manager.connected_sources
    
    def test_get_available_sources(self):
        """测试获取可用数据源。"""
        config = {"local": {"enabled": True}}
        manager = DataSourceManager(config)
        
        with patch.object(manager.data_sources["local"], "connect", return_value=True):
            with patch.object(manager.data_sources["local"], "is_connected", return_value=True):
                manager.connect_all()
                available = manager.get_available_sources()
                assert "local" in available
    
    def test_cache_functionality(self):
        """测试缓存功能。"""
        config = {"local": {"enabled": True}}
        manager = DataSourceManager(config)
        
        # 模拟数据源
        mock_source = Mock()
        mock_source.get_stock_list.return_value = ["600000", "000001"]
        mock_source.is_connected.return_value = True
        manager.data_sources["local"] = mock_source
        manager.connected_sources = ["local"]
        
        # 第一次调用
        result1 = manager.get_stock_list()
        assert result1 == ["600000", "000001"]
        
        # 第二次调用应该从缓存获取
        result2 = manager.get_stock_list()
        assert result2 == ["600000", "000001"]
        
        # 验证数据源只被调用一次
        assert mock_source.get_stock_list.call_count == 1
    
    def test_fallback_mechanism(self):
        """测试回退机制。"""
        config = {
            "source1": {"enabled": True, "priority": 1},
            "source2": {"enabled": True, "priority": 2}
        }
        
        manager = DataSourceManager(config)
        
        # 模拟数据源
        mock_source1 = Mock()
        mock_source1.get_daily_data.return_value = pd.DataFrame()  # 返回空数据
        mock_source1.is_connected.return_value = True
        mock_source1.config.priority = 1
        
        mock_source2 = Mock()
        test_data = pd.DataFrame({"close": [100, 101, 102]})
        mock_source2.get_daily_data.return_value = test_data
        mock_source2.is_connected.return_value = True
        mock_source2.config.priority = 2
        
        manager.data_sources = {"source1": mock_source1, "source2": mock_source2}
        manager.connected_sources = ["source1", "source2"]
        
        # 测试回退机制
        result = manager.get_daily_data(
            symbols=["600000"],
            start_date="2023-01-01",
            end_date="2023-01-02",
            fallback=True
        )
        
        assert not result.empty
        assert len(result) == 3


class TestWindDataSource:
    """测试Wind数据源。"""
    
    def test_initialization(self):
        """测试初始化。"""
        config = DataSourceConfig(name="wind")
        source = WindDataSource(config)
        
        assert source.name == "wind"
        assert source.w is None
        assert source.is_connected() is False
    
    @patch('src.data.sources.wind_source.logger')
    def test_connection_success(self, mock_logger):
        """测试成功连接。"""
        config = DataSourceConfig(name="wind")
        source = WindDataSource(config)

        # 模拟WindPy模块
        mock_w = Mock()
        mock_result = Mock()
        mock_result.ErrorCode = 0
        mock_w.start.return_value = mock_result

        # 模拟WindPy导入成功
        with patch('builtins.__import__') as mock_import:
            # 创建模拟的WindPy模块
            mock_windpy = Mock()
            mock_windpy.w = mock_w

            def side_effect(name, *args, **kwargs):
                if name == 'WindPy':
                    return mock_windpy
                return __import__(name, *args, **kwargs)

            mock_import.side_effect = side_effect

            result = source.connect()
            assert result is True
            assert source.is_connected() is True
    
    def test_validate_symbols(self):
        """测试Wind股票代码验证。"""
        config = DataSourceConfig(name="wind")
        source = WindDataSource(config)
        
        # 测试代码转换
        result = source.validate_symbols(["600000", "000001"])
        assert "600000.SH" in result
        assert "000001.SZ" in result
        
        # 测试已有后缀的代码
        result = source.validate_symbols(["600000.SH"])
        assert result == ["600000.SH"]


class TestTonghuashunDataSource:
    """测试同花顺数据源。"""
    
    def test_initialization(self):
        """测试初始化。"""
        config = DataSourceConfig(name="tonghuashun")
        source = TonghuashunDataSource(config)
        
        assert source.name == "tonghuashun"
        assert source.session is None
        assert source.is_connected() is False
    
    def test_convert_to_ths_code(self):
        """测试同花顺代码转换。"""
        config = DataSourceConfig(name="tonghuashun")
        source = TonghuashunDataSource(config)
        
        # 测试深圳股票
        result = source._convert_to_ths_code("000001")
        assert result == "hs_000001"
        
        # 测试上海股票
        result = source._convert_to_ths_code("600000")
        assert result == "sh_600000"
        
        # 测试带后缀的代码
        result = source._convert_to_ths_code("000001.SZ")
        assert result == "hs_000001"


@pytest.fixture
def sample_price_data():
    """生成样本价格数据。"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    data = pd.DataFrame({
        'date': dates,
        'open': 100 + np.random.randn(100).cumsum() * 0.5,
        'high': 100 + np.random.randn(100).cumsum() * 0.5 + 1,
        'low': 100 + np.random.randn(100).cumsum() * 0.5 - 1,
        'close': 100 + np.random.randn(100).cumsum() * 0.5,
        'volume': np.random.randint(1000000, 10000000, 100)
    })
    
    # 确保价格关系正确
    data['high'] = data[['open', 'high', 'close']].max(axis=1) + np.random.rand(100) * 0.5
    data['low'] = data[['open', 'low', 'close']].min(axis=1) - np.random.rand(100) * 0.5
    
    return data


class TestIntegration:
    """集成测试。"""
    
    def test_end_to_end_data_retrieval(self, sample_price_data):
        """测试端到端数据获取流程。"""
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 设置配置
            config = {
                "local": {
                    "enabled": True,
                    "data_paths": {
                        "cleaned_minute": str(Path(temp_dir) / "cleaned")
                    }
                }
            }
            
            # 创建测试数据
            stock_dir = Path(temp_dir) / "cleaned" / "sh600000" / "year=2023"
            stock_dir.mkdir(parents=True)
            
            minute_data = sample_price_data.copy()
            minute_data['datetime'] = pd.date_range('2023-01-01 09:30:00', periods=len(minute_data), freq='1min')
            minute_data['symbol'] = 'sh600000'
            minute_data.to_parquet(stock_dir / "cleaned_data.parquet", index=False)
            
            # 测试数据管理器
            with DataSourceManager(config) as manager:
                # 获取股票列表
                stocks = manager.get_stock_list()
                assert "sh600000" in stocks
                
                # 获取分钟数据
                data = manager.get_minute_data(
                    symbols=["sh600000"],
                    start_date="2023-01-01",
                    end_date="2023-01-01"
                )
                
                assert not data.empty
                assert "symbol" in data.columns
                assert all(data["symbol"] == "sh600000")
        
        finally:
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
