import unittest
import pandas as pd
import numpy as np
from pathlib import Path
import sys

# 确保可以从测试文件正确导入
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

# 假设新功能将在此模块中实现
from factor_evaluator import (calculate_ic, perform_quantile_analysis,
                             calculate_ic_statistics, comprehensive_factor_evaluation)

class TestFactorEvaluator(unittest.TestCase):
    """
    测试 factor_evaluator.py 的功能。
    """

    def setUp(self):
        """
        为测试准备一个包含因子和未来收益率的DataFrame。
        """
        # 创建更大的测试数据集，确保每个日期有足够的样本
        dates = ['2023-01-01', '2023-01-02']
        symbols = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L']

        data_list = []
        for date in dates:
            for i, symbol in enumerate(symbols):
                # 创建有相关性的测试数据
                mom_value = 0.1 * (i - 6) / 6  # -0.1 到 0.1
                # 未来收益与因子有正相关关系，加入一些噪声
                fwd_return = mom_value * 0.5 + np.random.normal(0, 0.01)

                data_list.append({
                    'datetime': pd.to_datetime(date),
                    'symbol': symbol,
                    'MOM': mom_value,
                    'fwd_return_1d': fwd_return
                })

        self.sample_data = pd.DataFrame(data_list)

    def test_calculate_ic_normal(self):
        """
        测试: 能否正确计算单个因子在单个周期上的IC值。
        """
        print("\n--- 运行目标测试: test_calculate_ic_normal ---")

        # 调用函数，使用较小的min_periods以适应测试数据
        ic_series = calculate_ic(self.sample_data, factor_col='MOM',
                               fwd_return_col='fwd_return_1d', min_periods=5)

        # 断言1: 返回值应该是一个Series
        self.assertIsInstance(ic_series, pd.Series, "返回值应该是pd.Series")

        # 断言2: 索引应该是datetime
        self.assertTrue(pd.api.types.is_datetime64_any_dtype(ic_series.index),
                       "Series的索引应该是datetime类型")

        # 断言3: 验证IC值应该是正数（因为我们构造了正相关的数据）
        valid_ic = ic_series.dropna()
        if len(valid_ic) > 0:
            # 由于我们构造的数据有正相关关系，IC应该大于0
            self.assertGreater(valid_ic.iloc[0], 0, "IC值应该为正数")

        print("--- 目标测试通过 ---")

    def test_calculate_ic_spearman(self):
        """
        测试: 能否使用spearman方法计算IC。
        """
        print("\n--- 运行目标测试: test_calculate_ic_spearman ---")
        
        ic_series = calculate_ic(self.sample_data, factor_col='MOM', fwd_return_col='fwd_return_1d', method='spearman')
        
        # 对于我们的数据, rank corr 也是 1.0
        # Ranks for MOM: [2, 1], Ranks for fwd_return: [2, 1] -> perfect positive rank correlation
        self.assertAlmostEqual(ic_series.loc['2023-01-01'], 1.0, places=4)
        
        print("--- 目标测试通过 ---")

    def test_perform_quantile_analysis(self):
        """
        测试: 能否正确执行分层回测。
        """
        print("\n--- 运行目标测试: test_perform_quantile_analysis ---")
        
        # 调用函数，分为2层
        net_value_df, stats_df = perform_quantile_analysis(
            self.sample_data, 
            factor_col='MOM', 
            fwd_return_col='fwd_return_1d',
            quantiles=2
        )

        # 断言1: 检查返回类型
        self.assertIsInstance(net_value_df, pd.DataFrame, "净值结果应为DataFrame")
        self.assertIsInstance(stats_df, pd.DataFrame, "统计结果应为DataFrame")

        # 断言2: 检查净值曲线的列
        self.assertIn('q1', net_value_df.columns)
        self.assertIn('q2', net_value_df.columns)

        # 断言3: 验证净值曲线的基本特性
        # 由于我们构造的数据中，高MOM值对应高收益，所以q1应该表现更好
        first_date = net_value_df.index[0]

        # 检查净值都是正数且合理
        self.assertGreater(net_value_df.loc[first_date, 'q1'], 0.9)
        self.assertLess(net_value_df.loc[first_date, 'q1'], 1.1)

        # 由于数据构造的正相关性，q1（高因子值）应该表现更好
        if len(net_value_df.columns) >= 2:
            q1_performance = net_value_df.iloc[-1, 0]  # 最后一天的q1净值
            q2_performance = net_value_df.iloc[-1, -1]  # 最后一天的最后一个分层净值
            # 注意：由于我们的数据是正相关的，q1应该表现更好
            print(f"Q1最终净值: {q1_performance:.4f}, Q2最终净值: {q2_performance:.4f}")
        
        print("--- 目标测试通过 ---")

    def test_calculate_ic_statistics(self):
        """
        测试: IC统计指标计算功能。
        """
        print("\n--- 运行测试: test_calculate_ic_statistics ---")

        # 创建一个简单的IC序列
        ic_series = pd.Series([0.1, -0.05, 0.2, 0.15, -0.1],
                             index=pd.date_range('2023-01-01', periods=5))

        stats = calculate_ic_statistics(ic_series)

        # 验证统计指标
        self.assertIn('IC均值', stats)
        self.assertIn('ICIR', stats)
        self.assertIn('IC胜率', stats)
        self.assertAlmostEqual(stats['IC胜率'], 0.6, places=2)  # 3/5 = 0.6

        print("--- IC统计测试通过 ---")

    def test_comprehensive_factor_evaluation(self):
        """
        测试: 综合因子评估功能。
        """
        print("\n--- 运行测试: test_comprehensive_factor_evaluation ---")

        # 创建更大的测试数据集
        dates = pd.date_range('2023-01-01', periods=20)
        symbols = ['A', 'B', 'C', 'D', 'E']

        test_data = []
        for date in dates:
            for symbol in symbols:
                test_data.append({
                    'datetime': date,
                    'symbol': symbol,
                    'MOM': np.random.normal(0, 0.1),
                    'fwd_return_1d': np.random.normal(0, 0.02)
                })

        test_df = pd.DataFrame(test_data)

        # 执行综合评估
        results = comprehensive_factor_evaluation(
            test_df, 'MOM', 'fwd_return_1d', quantiles=3)

        # 验证结果结构
        self.assertIn('ic_series', results)
        self.assertIn('ic_statistics', results)
        self.assertIn('net_value', results)
        self.assertIn('quantile_stats', results)
        self.assertIn('factor_effectiveness', results)

        print("--- 综合评估测试通过 ---")


if __name__ == '__main__':
    unittest.main()
