"""
因子库模块的单元测试。

遵循TDD原则，测试因子计算和分析功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from src.features.factors.factor_library import FactorLibrary
from src.features.factors.fundamental_factors import FundamentalFactors
from src.features.factors.technical_factors import TechnicalFactors
from src.features.factors.sentiment_factors import SentimentFactors


class TestFactorLibrary:
    """测试因子库主类。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.factor_lib = FactorLibrary()
    
    def test_initialization(self):
        """测试因子库初始化。"""
        assert self.factor_lib is not None
        assert hasattr(self.factor_lib, 'fundamental')
        assert hasattr(self.factor_lib, 'technical')
        assert hasattr(self.factor_lib, 'sentiment')
    
    def test_get_available_factors(self):
        """测试获取可用因子列表。"""
        factors = self.factor_lib.get_available_factors()
        
        assert isinstance(factors, dict)
        assert 'fundamental' in factors
        assert 'technical' in factors
        assert 'sentiment' in factors
        
        # 每个类别应该包含因子列表
        for category, factor_list in factors.items():
            assert isinstance(factor_list, list)
    
    def test_calculate_factor(self, sample_ohlcv_data):
        """测试单个因子计算。"""
        # 测试技术因子计算
        result = self.factor_lib.calculate_factor(
            data=sample_ohlcv_data,
            factor_name='rsi_14',
            category='technical'
        )
        
        assert isinstance(result, pd.Series)
        assert len(result) == len(sample_ohlcv_data)
        assert result.name == 'rsi_14'
    
    def test_calculate_multiple_factors(self, sample_ohlcv_data):
        """测试批量因子计算。"""
        factor_names = ['rsi_14', 'sma_20', 'volatility_20']
        
        result = self.factor_lib.calculate_multiple_factors(
            data=sample_ohlcv_data,
            factor_names=factor_names,
            category='technical'
        )
        
        assert isinstance(result, pd.DataFrame)
        assert len(result) == len(sample_ohlcv_data)
        
        # 检查所有因子列都存在
        for factor_name in factor_names:
            assert factor_name in result.columns


class TestFundamentalFactors:
    """测试基本面因子。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.fundamental = FundamentalFactors()
    
    def test_pe_ratio(self, sample_fundamental_data):
        """测试PE比率计算。"""
        result = self.fundamental.calculate_pe_ratio(sample_fundamental_data)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'pe_ratio'
        
        # PE比率应该为正数（除非有负收益）
        valid_pe = result.dropna()
        if len(valid_pe) > 0:
            assert (valid_pe > 0).any()  # 至少有一些正值
    
    def test_pb_ratio(self, sample_fundamental_data):
        """测试PB比率计算。"""
        result = self.fundamental.calculate_pb_ratio(sample_fundamental_data)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'pb_ratio'
        
        # PB比率应该为正数
        valid_pb = result.dropna()
        if len(valid_pb) > 0:
            assert (valid_pb > 0).all()
    
    def test_roe(self, sample_fundamental_data):
        """测试ROE计算。"""
        result = self.fundamental.calculate_roe(sample_fundamental_data)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'roe'
        
        # ROE可以为负数，但应该在合理范围内
        valid_roe = result.dropna()
        if len(valid_roe) > 0:
            assert (valid_roe >= -1).all()  # ROE不应该小于-100%
            assert (valid_roe <= 5).all()   # ROE不应该大于500%
    
    def test_debt_to_equity(self, sample_fundamental_data):
        """测试资产负债率计算。"""
        result = self.fundamental.calculate_debt_to_equity(sample_fundamental_data)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'debt_to_equity'
        
        # 资产负债率应该为非负数
        valid_de = result.dropna()
        if len(valid_de) > 0:
            assert (valid_de >= 0).all()


class TestTechnicalFactors:
    """测试技术面因子。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.technical = TechnicalFactors()
    
    def test_momentum_factor(self, sample_ohlcv_data):
        """测试动量因子计算。"""
        result = self.technical.calculate_momentum(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'momentum_20'
        assert len(result) == len(sample_ohlcv_data)
        
        # 前19个值应该是NaN
        assert pd.isna(result.iloc[:19]).all()
    
    def test_reversal_factor(self, sample_ohlcv_data):
        """测试反转因子计算。"""
        result = self.technical.calculate_reversal(sample_ohlcv_data, period=5)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'reversal_5'
        assert len(result) == len(sample_ohlcv_data)
    
    def test_volatility_factor(self, sample_ohlcv_data):
        """测试波动率因子计算。"""
        result = self.technical.calculate_volatility(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'volatility_20'
        assert len(result) == len(sample_ohlcv_data)
        
        # 波动率应该为非负数
        valid_vol = result.dropna()
        if len(valid_vol) > 0:
            assert (valid_vol >= 0).all()
    
    def test_volume_factor(self, sample_ohlcv_data):
        """测试成交量因子计算。"""
        result = self.technical.calculate_volume_factor(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'volume_factor_20'
        assert len(result) == len(sample_ohlcv_data)
    
    def test_price_factor(self, sample_ohlcv_data):
        """测试价格因子计算。"""
        result = self.technical.calculate_price_factor(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'price_factor_20'
        assert len(result) == len(sample_ohlcv_data)


class TestSentimentFactors:
    """测试情绪面因子。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.sentiment = SentimentFactors()
    
    def test_money_flow_factor(self, sample_ohlcv_data):
        """测试资金流向因子计算。"""
        result = self.sentiment.calculate_money_flow(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'money_flow_20'
        assert len(result) == len(sample_ohlcv_data)
    
    def test_turnover_factor(self, sample_ohlcv_data):
        """测试换手率因子计算。"""
        # 需要添加流通股本数据
        data_with_shares = sample_ohlcv_data.copy()
        data_with_shares['shares_outstanding'] = 1000000000  # 10亿股
        
        result = self.sentiment.calculate_turnover_rate(data_with_shares, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'turnover_rate_20'
        assert len(result) == len(data_with_shares)
        
        # 换手率应该为非负数
        valid_turnover = result.dropna()
        if len(valid_turnover) > 0:
            assert (valid_turnover >= 0).all()
    
    def test_vwap_factor(self, sample_ohlcv_data):
        """测试VWAP因子计算。"""
        result = self.sentiment.calculate_vwap_factor(sample_ohlcv_data, period=20)
        
        assert isinstance(result, pd.Series)
        assert result.name == 'vwap_factor_20'
        assert len(result) == len(sample_ohlcv_data)


@pytest.fixture
def sample_ohlcv_data():
    """生成样本OHLCV数据。"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=100, freq='D')

    # 生成真实的价格序列
    base_price = 100.0
    returns = np.random.normal(0, 0.02, 100)
    prices = [base_price]

    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))

    data = pd.DataFrame({
        'datetime': dates,
        'close': prices
    })

    # 生成OHLV数据
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])

    # 确保high >= max(open, close), low <= min(open, close)
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, 100))
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, 100))
    data['volume'] = np.random.randint(1000000, 10000000, 100)

    return data


@pytest.fixture
def sample_fundamental_data():
    """生成样本基本面数据。"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=50, freq='D')

    data = pd.DataFrame({
        'date': dates,
        'market_cap': np.random.uniform(1e9, 1e12, 50),  # 市值
        'total_equity': np.random.uniform(1e8, 1e11, 50),  # 总资产
        'net_income': np.random.uniform(-1e8, 1e9, 50),   # 净利润
        'book_value': np.random.uniform(1e8, 1e11, 50),   # 账面价值
        'total_debt': np.random.uniform(0, 1e10, 50),     # 总负债
        'revenue': np.random.uniform(1e8, 1e10, 50),      # 营收
        'operating_income': np.random.uniform(-1e7, 1e9, 50)  # 营业利润
    })

    return data


class TestFactorAnalysis:
    """测试因子分析功能。"""
    
    def test_factor_correlation(self, sample_factor_data):
        """测试因子相关性分析。"""
        factor_lib = FactorLibrary()
        
        correlation_matrix = factor_lib.analyze_factor_correlation(sample_factor_data)
        
        assert isinstance(correlation_matrix, pd.DataFrame)
        assert correlation_matrix.shape[0] == correlation_matrix.shape[1]
        
        # 对角线应该为1
        np.testing.assert_array_almost_equal(
            np.diag(correlation_matrix), 
            np.ones(correlation_matrix.shape[0])
        )
    
    def test_factor_ic_analysis(self, sample_factor_data):
        """测试因子IC分析。"""
        factor_lib = FactorLibrary()
        
        ic_results = factor_lib.analyze_factor_ic(
            factor_data=sample_factor_data,
            return_data=sample_factor_data[['return_1d', 'return_5d', 'return_20d']]
        )
        
        assert isinstance(ic_results, pd.DataFrame)
        assert 'ic_mean' in ic_results.columns
        assert 'ic_std' in ic_results.columns
        assert 'ic_ir' in ic_results.columns
        
        # IC值应该在-1到1之间
        assert (ic_results['ic_mean'].abs() <= 1).all()
    
    def test_factor_decay_analysis(self, sample_factor_data):
        """测试因子衰减分析。"""
        factor_lib = FactorLibrary()
        
        decay_results = factor_lib.analyze_factor_decay(
            factor_data=sample_factor_data,
            periods=[1, 5, 10, 20]
        )
        
        assert isinstance(decay_results, pd.DataFrame)
        assert len(decay_results.columns) == 4  # 4个周期
        
        # 检查列名
        expected_columns = ['decay_1', 'decay_5', 'decay_10', 'decay_20']
        for col in expected_columns:
            assert col in decay_results.columns


class TestFactorOrthogonalization:
    """测试因子正交化。"""
    
    def test_gram_schmidt_orthogonalization(self, sample_factor_data):
        """测试Gram-Schmidt正交化。"""
        factor_lib = FactorLibrary()
        
        factor_cols = ['factor_1', 'factor_2', 'factor_3']
        original_factors = sample_factor_data[factor_cols]
        
        orthogonal_factors = factor_lib.orthogonalize_factors(
            original_factors, 
            method='gram_schmidt'
        )
        
        assert isinstance(orthogonal_factors, pd.DataFrame)
        assert orthogonal_factors.shape == original_factors.shape
        
        # 检查正交性（相关性应该接近0）
        correlation_matrix = orthogonal_factors.corr()
        
        # 非对角线元素应该接近0
        for i in range(len(factor_cols)):
            for j in range(i+1, len(factor_cols)):
                assert abs(correlation_matrix.iloc[i, j]) < 0.1
    
    def test_pca_orthogonalization(self, sample_factor_data):
        """测试PCA正交化。"""
        factor_lib = FactorLibrary()
        
        factor_cols = ['factor_1', 'factor_2', 'factor_3']
        original_factors = sample_factor_data[factor_cols]
        
        orthogonal_factors = factor_lib.orthogonalize_factors(
            original_factors, 
            method='pca',
            n_components=2
        )
        
        assert isinstance(orthogonal_factors, pd.DataFrame)
        assert orthogonal_factors.shape[0] == original_factors.shape[0]
        assert orthogonal_factors.shape[1] == 2  # 降维到2个主成分


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
