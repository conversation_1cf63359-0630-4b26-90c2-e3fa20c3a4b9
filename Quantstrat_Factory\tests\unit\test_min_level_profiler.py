import unittest
import pandas as pd
from pathlib import Path
import shutil
import configparser
import sys
import os

# 将项目根目录和profilers目录添加到sys.path
ROOT_DIR = Path(__file__).resolve().parents[2] # Quantstrat_Factory
PROFILERS_DIR = ROOT_DIR / "02_feature_profiler" / "profilers"
sys.path.append(str(ROOT_DIR))
sys.path.append(str(PROFILERS_DIR))

# 尝试导入 MinLevelProfiler 和 FeatureStoreClient
try:
    from min_level_profiler import MinLevelProfiler
    from feature_store_client import FeatureStoreClient
except ImportError as e:
    print(f"测试文件导入错误: {e}")
    MinLevelProfiler = None
    FeatureStoreClient = None

# 模拟的因子计算函数
def mock_calculate_alpha_factor_1(df_symbol_minutes: pd.DataFrame, params: dict = None) -> pd.DataFrame:
    factor_name = "alpha_test_factor_1"
    results = []
    for date, group_df in df_symbol_minutes.groupby(df_symbol_minutes['datetime'].dt.date):
        if not group_df.empty:
            results.append({
                'datetime': group_df['datetime'].iloc[-1], # 日期最后一分钟
                'symbol': group_df['symbol'].iloc[0],
                factor_name: group_df['close'].mean() # 示例：当日收盘价均值
            })
    return pd.DataFrame(results)

def mock_calculate_alpha_factor_2(df_symbol_minutes: pd.DataFrame, params: dict = None) -> pd.DataFrame:
    factor_name = "alpha_test_factor_2"
    results = []
    for date, group_df in df_symbol_minutes.groupby(df_symbol_minutes['datetime'].dt.date):
        if not group_df.empty:
            results.append({
                'datetime': group_df['datetime'].iloc[-1],
                'symbol': group_df['symbol'].iloc[0],
                factor_name: group_df['volume'].sum() # 示例：当日总成交量
            })
    return pd.DataFrame(results)

MOCK_ALPHA_FACTOR_CALCULATORS = [
    {"function": mock_calculate_alpha_factor_1, "name": "alpha_test_factor_1", "category": "alpha"},
    {"function": mock_calculate_alpha_factor_2, "name": "alpha_test_factor_2", "category": "alpha"},
]

class TestMinLevelProfiler(unittest.TestCase):

    def setUp(self):
        self.test_root_dir = ROOT_DIR / "temp_test_min_profiler"
        self.test_root_dir.mkdir(parents=True, exist_ok=True)

        self.config = configparser.ConfigParser()
        self.config['Paths'] = {
            'data_root': str(self.test_root_dir / 'data'),
            'cleaned_data_path': str(self.test_root_dir / 'data' / 'cleaned'), # MinLevelProfiler会加 /min
            'feature_store_path': str(self.test_root_dir / 'feature_store'), # MinLevelProfiler会加 /minute_features
            'min_data_path': str(self.test_root_dir / 'data' / '1min_zip_source') # 假设原始zip数据源
        }
        self.config_path = self.test_root_dir / 'test_config.ini'
        with open(self.config_path, 'w') as configfile:
            self.config.write(configfile)

        # 创建模拟的清洗后分钟数据 (MinLevelProfiler的输入)
        self.cleaned_min_data_dir = Path(self.config['Paths']['cleaned_data_path']) / 'min'
        self.cleaned_min_data_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建模拟的因子定义文件
        self.minute_factors_dir = self.test_root_dir / "02_feature_profiler" / "profilers" / "minute_factors"
        self.minute_factors_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建一个模拟的 alpha_factors.py
        mock_alpha_factors_content = f"""
import pandas as pd
# 实际因子函数需要从测试文件中导入或在此定义
# 为了简单，测试时 MinLevelProfiler 会通过 monkeypatching 来使用这里的 MOCK_ALPHA_FACTOR_CALCULATORS
FACTOR_CALCULATORS = [] 
"""
        with open(self.minute_factors_dir / "mock_alpha_factors.py", "w", encoding="utf-8") as f:
            f.write(mock_alpha_factors_content)
        
        # 确保 __init__.py 存在，以便 profilers.minute_factors 可以被视为包
        Path(self.minute_factors_dir.parent / "__init__.py").touch(exist_ok=True)
        Path(self.minute_factors_dir / "__init__.py").touch(exist_ok=True)


        if MinLevelProfiler is None or FeatureStoreClient is None:
            self.fail("MinLevelProfiler or FeatureStoreClient failed to import.")
            
        # 模拟 FeatureStoreClient，因为我们主要测试 MinLevelProfiler 的文件输出
        # MinLevelProfiler 内部会实例化它自己的 FeatureStoreClient
        # 但它的输出路径会基于我们提供的config

    def tearDown(self):
        # 清理测试目录
        if self.test_root_dir.exists():
            shutil.rmtree(self.test_root_dir)

    def create_mock_cleaned_minute_data(self, symbol, date_str, num_minutes=5):
        """创建单个股票单日的模拟清洗后分钟数据文件"""
        df_data = []
        start_time = pd.to_datetime(f"{date_str} 09:30:00")
        for i in range(num_minutes):
            dt = start_time + pd.Timedelta(minutes=i)
            df_data.append({
                'datetime': dt,
                'symbol': symbol,
                'open': 10.0 + i*0.1,
                'high': 10.1 + i*0.1,
                'low': 9.9 + i*0.1,
                'close': 10.05 + i*0.1,
                'volume': 1000 + i*100
            })
        df = pd.DataFrame(df_data)
        
        # MinLevelProfiler._load_cleaned_min_data 期望的文件名格式
        # 它会查找 min_*_cleaned.parquet，然后合并，再按profiler的start/end date过滤
        # 为了测试简单，我们直接创建一个符合其内部日期范围的文件
        file_name = f"min_{date_str}_to_{date_str}_cleaned.parquet"
        file_path = self.cleaned_min_data_dir / file_name
        df.to_parquet(file_path)
        return file_path

    @unittest.skipIf(MinLevelProfiler is None, "MinLevelProfiler not imported")
    def test_compute_and_save_single_day_single_symbol(self):
        """
        测试 MinLevelProfiler 为单个股票单个日期计算并保存所有请求的分钟因子到一个宽表。
        """
        symbol = "000001.SZ"
        date_str = "2023-01-01"
        self.create_mock_cleaned_minute_data(symbol, date_str)

        # Monkeypatch _load_all_factor_calculators to return our mock calculators
        # and point factor_file scanning to our test dir
        original_load_calculators = MinLevelProfiler._load_all_factor_calculators
        original_profilers_init_path = Path(sys.modules['min_level_profiler'].__file__).parent

        def mock_load_all_calculators(slf):
            # 模拟从特定测试文件加载
            # 这里直接返回预定义的MOCK_ALPHA_FACTOR_CALCULATORS
            # 确保只包含我们想测试的类别
            return [calc for calc in MOCK_ALPHA_FACTOR_CALCULATORS if calc['category'] == 'alpha']

        MinLevelProfiler._load_all_factor_calculators = mock_load_all_calculators
        # 修改 MinLevelProfiler 内部查找 minute_factors 的基路径
        # 这是为了让它在测试时扫描我们模拟的 minute_factors 目录
        # (或者更简单，直接让 mock_load_all_calculators 返回我们想要的)
        # setattr(sys.modules['min_level_profiler'], '__file__', str(self.minute_factors_dir.parent / '__init__.py'))


        profiler = MinLevelProfiler(config=self.config, start_date=date_str, end_date=date_str)
        
        # 期望的输出路径 (由 FeatureStoreClient 内部逻辑决定)
        # client = FeatureStoreClient(config_path=self.config_path) # 用测试config
        # symbol_path_safe = client.get_symbol_path(symbol)
        # expected_output_dir = client.minute_features_by_symbol_date_path / symbol_path_safe
        # expected_file_path = expected_output_dir / f"{date_str}_minute_features.parquet"
        
        # 由于 MinLevelProfiler 当前的保存逻辑是每个因子一个文件，我们需要先修改它
        # 暂时，我们先验证它是否能运行，后续再修改保存逻辑并完善此测试
        
        profiler.compute_and_save_features(requested_feature_sets_or_names=['alpha']) # 请求计算alpha因子

        
        # 预期 FeatureStoreClient 会被 MinLevelProfiler 内部使用测试的 config_path 初始化
        # MinLevelProfiler 内部的 feature_store_client.minute_features_by_symbol_date_path
        # 将会是 self.test_root_dir / 'feature_store' / 'minute_features'
        
        client_for_path = FeatureStoreClient(config_path=self.config_path)
        symbol_path_safe = client_for_path.get_symbol_path(symbol)
        expected_output_dir = client_for_path.minute_features_by_symbol_date_path / symbol_path_safe
        expected_file_path = expected_output_dir / f"{date_str}_minute_features.parquet"

        profiler.compute_and_save_features(requested_feature_sets_or_names=['alpha'])

        # 断言输出文件已创建
        self.assertTrue(expected_file_path.exists(), f"期望的输出文件未找到: {expected_file_path}")

        # 断言文件内容
        df_output = pd.read_parquet(expected_file_path)
        self.assertIn("alpha_test_factor_1", df_output.columns, "输出文件应包含 alpha_test_factor_1")
        self.assertIn("alpha_test_factor_2", df_output.columns, "输出文件应包含 alpha_test_factor_2")
        
        # 因为我们的模拟因子是每日一个值，所以聚合后的宽表应该只有一行代表这一天
        # 并且这一行应该包含所有分钟数据计算出的那个日级别值
        # 如果 MinLevelProfiler 的目标是保存原始分钟序列 + 因子列，则 len(df_output) 会是 num_minutes
        # 当前的因子计算函数返回的是每日一个值，所以预期是1行。
        self.assertEqual(len(df_output), 1, "对于每日聚合的因子，输出文件应只有一行")
        self.assertEqual(df_output['symbol'].iloc[0], symbol, "输出文件中的symbol不正确")
        self.assertEqual(pd.to_datetime(df_output['datetime'].iloc[0]).strftime('%Y-%m-%d'), date_str, "输出文件中的datetime不正确")

        # 验证因子值 (基于模拟数据的简单计算)
        # alpha_test_factor_1 是 close 均值: 10.05, 10.15, 10.25, 10.35, 10.45 -> mean = 10.25
        # alpha_test_factor_2 是 volume 总和: 1000, 1100, 1200, 1300, 1400 -> sum = 6000
        self.assertAlmostEqual(df_output["alpha_test_factor_1"].iloc[0], 10.25, places=2)
        self.assertEqual(df_output["alpha_test_factor_2"].iloc[0], 6000)
        
        # 恢复 monkeypatch
        MinLevelProfiler._load_all_factor_calculators = original_load_calculators
        # setattr(sys.modules['min_level_profiler'], '__file__', str(original_profilers_init_path / '__init__.py'))

        print(f"测试 test_compute_and_save_single_day_single_symbol 完成。已验证输出文件: {expected_file_path}")


if __name__ == '__main__':
    unittest.main()
