"""
性能优化模块的单元测试。

遵循TDD原则，测试数据处理、缓存、并行计算等性能优化功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import time
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor
import tempfile
from pathlib import Path

from src.performance.data_optimizer import DataOptimizer
from src.performance.cache_manager import CacheManager
from src.performance.parallel_processor import ParallelProcessor
from src.performance.memory_manager import MemoryManager


class TestDataOptimizer:
    """测试数据优化器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.optimizer = DataOptimizer()
    
    def test_initialization(self):
        """测试数据优化器初始化。"""
        assert self.optimizer is not None
        assert hasattr(self.optimizer, 'optimize_dtypes')
        assert hasattr(self.optimizer, 'compress_data')
    
    def test_optimize_dtypes(self, sample_mixed_data):
        """测试数据类型优化。"""
        original_memory = sample_mixed_data.memory_usage(deep=True).sum()
        
        optimized_data = self.optimizer.optimize_dtypes(sample_mixed_data)
        optimized_memory = optimized_data.memory_usage(deep=True).sum()
        
        # 优化后内存使用应该减少
        assert optimized_memory <= original_memory
        
        # 数据内容应该保持一致
        pd.testing.assert_frame_equal(
            sample_mixed_data.astype(str), 
            optimized_data.astype(str)
        )
    
    def test_compress_data(self, sample_large_data):
        """测试数据压缩。"""
        compressed_data = self.optimizer.compress_data(sample_large_data)
        
        assert compressed_data is not None
        assert len(compressed_data) < len(sample_large_data.to_pickle())
    
    def test_optimize_categorical_data(self, sample_categorical_data):
        """测试分类数据优化。"""
        original_memory = sample_categorical_data.memory_usage(deep=True).sum()
        
        optimized_data = self.optimizer.optimize_categorical_data(sample_categorical_data)
        optimized_memory = optimized_data.memory_usage(deep=True).sum()
        
        # 分类数据优化后内存应该减少
        assert optimized_memory < original_memory
    
    def test_chunked_processing(self, sample_large_data):
        """测试分块处理。"""
        chunk_size = 1000
        
        def process_chunk(chunk):
            return chunk.sum()
        
        result = self.optimizer.process_in_chunks(
            sample_large_data, 
            process_chunk, 
            chunk_size=chunk_size
        )
        
        assert result is not None
        assert len(result) == len(sample_large_data) // chunk_size + (1 if len(sample_large_data) % chunk_size else 0)
    
    def test_vectorized_operations(self, sample_numeric_data):
        """测试向量化操作。"""
        # 测试向量化计算比循环快
        start_time = time.time()
        vectorized_result = self.optimizer.vectorized_calculation(sample_numeric_data)
        vectorized_time = time.time() - start_time
        
        start_time = time.time()
        loop_result = self.optimizer.loop_calculation(sample_numeric_data)
        loop_time = time.time() - start_time
        
        # 向量化应该更快
        assert vectorized_time < loop_time
        
        # 结果应该一致
        np.testing.assert_array_almost_equal(vectorized_result, loop_result)


class TestCacheManager:
    """测试缓存管理器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.cache_manager = CacheManager()
    
    def test_initialization(self):
        """测试缓存管理器初始化。"""
        assert self.cache_manager is not None
        assert hasattr(self.cache_manager, 'get')
        assert hasattr(self.cache_manager, 'set')
        assert hasattr(self.cache_manager, 'clear')
    
    def test_basic_cache_operations(self):
        """测试基本缓存操作。"""
        key = "test_key"
        value = {"data": "test_value"}
        
        # 设置缓存
        result = self.cache_manager.set(key, value)
        assert result is True
        
        # 获取缓存
        cached_value = self.cache_manager.get(key)
        assert cached_value == value
        
        # 删除缓存
        result = self.cache_manager.delete(key)
        assert result is True
        
        # 确认已删除
        cached_value = self.cache_manager.get(key)
        assert cached_value is None
    
    def test_cache_expiration(self):
        """测试缓存过期。"""
        key = "expire_test"
        value = "test_value"
        ttl = 1  # 1秒过期
        
        # 设置带过期时间的缓存
        result = self.cache_manager.set(key, value, ttl=ttl)
        assert result is True
        
        # 立即获取应该成功
        cached_value = self.cache_manager.get(key)
        assert cached_value == value
        
        # 等待过期
        time.sleep(1.1)
        
        # 过期后应该返回None
        cached_value = self.cache_manager.get(key)
        assert cached_value is None
    
    def test_cache_size_limit(self):
        """测试缓存大小限制。"""
        # 设置小的缓存大小限制
        limited_cache = CacheManager(max_size=3)
        
        # 添加超过限制的项目
        for i in range(5):
            limited_cache.set(f"key_{i}", f"value_{i}")
        
        # 检查缓存大小
        cache_size = limited_cache.size()
        assert cache_size <= 3
    
    def test_cache_hit_rate(self):
        """测试缓存命中率。"""
        # 添加一些缓存项
        for i in range(10):
            self.cache_manager.set(f"key_{i}", f"value_{i}")
        
        # 进行一些查询
        hits = 0
        total_queries = 20
        
        for i in range(total_queries):
            key = f"key_{i % 15}"  # 有些key存在，有些不存在
            if self.cache_manager.get(key) is not None:
                hits += 1
        
        hit_rate = hits / total_queries
        stats = self.cache_manager.get_stats()
        
        assert 'hit_rate' in stats
        assert stats['hit_rate'] == hit_rate
    
    def test_cache_persistence(self, temp_dir):
        """测试缓存持久化。"""
        persistent_cache = CacheManager(persistent=True, cache_dir=str(temp_dir))
        
        # 添加缓存项
        key = "persistent_key"
        value = {"data": "persistent_value"}
        
        persistent_cache.set(key, value)
        
        # 创建新的缓存实例
        new_cache = CacheManager(persistent=True, cache_dir=str(temp_dir))
        
        # 应该能够获取之前的缓存
        cached_value = new_cache.get(key)
        assert cached_value == value


class TestParallelProcessor:
    """测试并行处理器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.processor = ParallelProcessor()
    
    def test_initialization(self):
        """测试并行处理器初始化。"""
        assert self.processor is not None
        assert hasattr(self.processor, 'process_parallel')
        assert hasattr(self.processor, 'process_concurrent')
    
    def test_thread_pool_processing(self, sample_task_list):
        """测试线程池处理。"""
        def simple_task(x):
            return x * 2
        
        # 串行处理
        start_time = time.time()
        serial_results = [simple_task(x) for x in sample_task_list]
        serial_time = time.time() - start_time
        
        # 并行处理
        start_time = time.time()
        parallel_results = self.processor.process_with_threads(
            simple_task, 
            sample_task_list, 
            max_workers=4
        )
        parallel_time = time.time() - start_time
        
        # 结果应该一致
        assert parallel_results == serial_results
        
        # 对于I/O密集型任务，并行应该更快（这里是计算密集型，可能不明显）
        # assert parallel_time <= serial_time * 1.1  # 允许一些开销
    
    def test_process_pool_processing(self, sample_task_list):
        """测试进程池处理。"""
        def cpu_intensive_task(x):
            # 模拟CPU密集型任务
            result = 0
            for i in range(x * 1000):
                result += i
            return result
        
        # 并行处理
        parallel_results = self.processor.process_with_processes(
            cpu_intensive_task, 
            sample_task_list[:5],  # 减少任务数量以加快测试
            max_workers=2
        )
        
        # 串行处理验证结果
        serial_results = [cpu_intensive_task(x) for x in sample_task_list[:5]]
        
        assert parallel_results == serial_results
    
    def test_batch_processing(self, sample_large_data):
        """测试批处理。"""
        def batch_processor(batch):
            return batch.sum()
        
        batch_size = 1000
        results = self.processor.process_in_batches(
            sample_large_data,
            batch_processor,
            batch_size=batch_size
        )
        
        assert len(results) > 0
        assert all(isinstance(r, (int, float, np.number)) for r in results)
    
    def test_async_processing(self):
        """测试异步处理。"""
        async def async_task(x):
            return x ** 2
        
        tasks = [1, 2, 3, 4, 5]
        results = self.processor.process_async(async_task, tasks)
        
        expected = [x ** 2 for x in tasks]
        assert results == expected


class TestMemoryManager:
    """测试内存管理器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.memory_manager = MemoryManager()
    
    def test_initialization(self):
        """测试内存管理器初始化。"""
        assert self.memory_manager is not None
        assert hasattr(self.memory_manager, 'get_memory_usage')
        assert hasattr(self.memory_manager, 'optimize_memory')
    
    def test_memory_monitoring(self):
        """测试内存监控。"""
        # 获取当前内存使用
        memory_info = self.memory_manager.get_memory_usage()
        
        assert isinstance(memory_info, dict)
        assert 'total' in memory_info
        assert 'available' in memory_info
        assert 'percent' in memory_info
        assert 'used' in memory_info
    
    def test_memory_optimization(self, sample_large_data):
        """测试内存优化。"""
        # 记录优化前的内存使用
        initial_memory = self.memory_manager.get_memory_usage()
        
        # 执行内存优化
        optimized_data = self.memory_manager.optimize_memory(sample_large_data)
        
        # 检查优化结果
        assert optimized_data is not None
        assert len(optimized_data) == len(sample_large_data)
    
    def test_garbage_collection(self):
        """测试垃圾回收。"""
        # 创建一些大对象
        large_objects = []
        for i in range(10):
            large_objects.append(np.random.randn(1000, 1000))
        
        initial_memory = self.memory_manager.get_memory_usage()
        
        # 删除对象
        del large_objects
        
        # 强制垃圾回收
        freed_memory = self.memory_manager.force_garbage_collection()
        
        final_memory = self.memory_manager.get_memory_usage()
        
        assert freed_memory >= 0
        assert final_memory['used'] <= initial_memory['used']
    
    def test_memory_profiling(self):
        """测试内存分析。"""
        def memory_intensive_function():
            data = np.random.randn(10000, 100)
            return data.sum()
        
        profile_result = self.memory_manager.profile_memory(memory_intensive_function)
        
        assert isinstance(profile_result, dict)
        assert 'peak_memory' in profile_result
        assert 'memory_increment' in profile_result


# Fixtures for test data
@pytest.fixture
def sample_mixed_data():
    """生成混合类型测试数据。"""
    np.random.seed(42)
    data = {
        'int_col': np.random.randint(0, 100, 10000),
        'float_col': np.random.randn(10000),
        'str_col': [f'item_{i}' for i in range(10000)],
        'bool_col': np.random.choice([True, False], 10000),
        'category_col': np.random.choice(['A', 'B', 'C', 'D'], 10000)
    }
    return pd.DataFrame(data)


@pytest.fixture
def sample_large_data():
    """生成大型测试数据。"""
    np.random.seed(42)
    return pd.DataFrame(np.random.randn(50000, 10))


@pytest.fixture
def sample_categorical_data():
    """生成分类测试数据。"""
    np.random.seed(42)
    categories = ['Category_' + str(i) for i in range(100)]
    data = {
        'category': np.random.choice(categories, 10000),
        'value': np.random.randn(10000)
    }
    return pd.DataFrame(data)


@pytest.fixture
def sample_numeric_data():
    """生成数值测试数据。"""
    np.random.seed(42)
    return np.random.randn(10000)


@pytest.fixture
def sample_task_list():
    """生成任务列表。"""
    return list(range(1, 21))


@pytest.fixture
def temp_dir():
    """创建临时目录。"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    import shutil
    shutil.rmtree(temp_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
