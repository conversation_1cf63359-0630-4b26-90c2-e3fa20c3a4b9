import unittest
import os
import pandas as pd
import numpy as np # Added numpy import
from unittest.mock import patch, MagicMock
from sieve_engine import run_sieve, _process_stock_data, _apply_sieve_to_stock, aroon, _get_operator_fn

class TestSieveEngine(unittest.TestCase):

    def setUp(self):
        # Create a dummy config.ini for testing
        self.config_path = 'test_config.ini'
        with open(self.config_path, 'w') as f:
            f.write('[Paths]\n')
            f.write('stock_data_day_path = /mock/data/day\n')

        # Mock data for _process_stock_data
        self.mock_df_data = {
            'datetime': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05']),
            'open': [10, 11, 12, 11, 10],
            'high': [12, 13, 14, 12, 11],
            'low': [9, 10, 11, 10, 9],
            'close': [11, 12, 13, 10, 9],
            'volume': [100, 120, 110, 90, 80]
        }
        self.mock_df = pd.DataFrame(self.mock_df_data).set_index('datetime')

    def tearDown(self):
        # Clean up dummy config file
        if os.path.exists(self.config_path):
            os.remove(self.config_path)

    @patch('sieve_engine.pd.read_parquet')
    @patch('sieve_engine.os.listdir')
    @patch('sieve_engine.os.path.exists') # Patch os.path.exists
    def test_run_sieve_basic(self, mock_exists, mock_listdir, mock_read_parquet):
        mock_exists.return_value = True # Mock data directory to exist
        # Mock os.listdir to return dummy parquet files
        mock_listdir.return_value = ['000001.parquet', '000002.parquet']

        # Mock pd.read_parquet to return a dummy DataFrame
        mock_read_parquet.return_value = self.mock_df.copy()

        segments = [2, 2]
        conditions = [
            {
                "logic": "AND",
                "rules": [
                    {"metric": "pct_chg", "operator": ">", "value": 0},
                ]
            },
            {
                "logic": "AND",
                "rules": [
                    {"metric": "volume_ratio", "operator": ">", "value": 0.5},
                ]
            }
        ]

        # Call run_sieve
        matched_stocks = run_sieve(
            start_date="2023-01-01",
            end_date="2023-01-05",
            segments=segments,
            conditions=conditions,
            config_path=self.config_path
        )
        
        # Assertions
        self.assertIsInstance(matched_stocks, list)
        # Since the mock data is simple, we expect some matches if conditions are met
        # For a more robust test, you'd craft mock_df and conditions to guarantee specific matches
        self.assertGreaterEqual(len(matched_stocks), 0) # Should return a list, possibly empty or with matches

    def test_aroon(self):
        df = self.mock_df.copy()
        df_aroon = aroon(df, period=2) # Use a smaller period for simple test data
        self.assertIn('aroon_up', df_aroon.columns)
        self.assertIn('aroon_down', df_aroon.columns)
        self.assertFalse(df_aroon[['aroon_up', 'aroon_down']].isnull().all().all())

    def test_get_operator_fn(self):
        self.assertEqual(_get_operator_fn('>'), np.greater)
        self.assertEqual(_get_operator_fn('<='), np.less_equal)
        self.assertIsNone(_get_operator_fn('invalid'))

    @patch('sieve_engine.pd.read_parquet')
    def test_process_stock_data(self, mock_read_parquet):
        mock_read_parquet.return_value = self.mock_df_data.copy() # Return dict for direct DataFrame creation
        
        # Convert mock_read_parquet.return_value to DataFrame inside the mock
        def side_effect(file_path):
            df = pd.DataFrame(self.mock_df_data)
            df['datetime'] = pd.to_datetime(df['datetime'])
            return df

        mock_read_parquet.side_effect = side_effect

        processed_df = _process_stock_data(
            'mock_path/000001.parquet',
            '2023-01-01',
            '2023-01-05',
            m_value=2,
            aroon_period=2
        )
        self.assertIsNotNone(processed_df)
        self.assertIn('volume_ratio', processed_df.columns)
        self.assertIn('pct_chg', processed_df.columns)
        self.assertIn('aroon_up', processed_df.columns)
        self.assertFalse(processed_df.empty)

    def test_apply_sieve_to_stock(self):
        # Create a more suitable DataFrame for testing sieve logic
        data = {
            'datetime': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05', '2023-01-06', '2023-01-07']),
            'open': [10, 11, 12, 11, 10, 10, 10],
            'high': [12, 13, 14, 12, 11, 11, 11],
            'low': [9, 10, 11, 10, 9, 9, 9],
            'close': [11, 12, 13, 10, 9, 10, 11],
            'volume': [100, 120, 110, 90, 80, 70, 60]
        }
        df = pd.DataFrame(data).set_index('datetime')
        df['pct_chg'] = df['close'].pct_change() * 100
        df['volume_ratio'] = df['volume'] / df['volume'].rolling(window=2, min_periods=1).mean()

        segments = [2, 2] # Two segments, each 2 days long
        conditions = [
            { # Segment 1: pct_chg > 0
                "logic": "AND",
                "rules": [{"metric": "pct_chg", "operator": ">", "value": 0}]
            },
            { # Segment 2: volume_ratio < 1
                "logic": "AND",
                "rules": [{"metric": "volume_ratio", "operator": "<", "value": 1}]
            }
        ]
        
        # Expected:
        # 2023-01-01: pct_chg > 0 (True)
        # 2023-01-02: pct_chg > 0 (True)
        # 2023-01-03: pct_chg > 0 (True)
        # 2023-01-04: pct_chg < 0 (False)
        # 2023-01-05: pct_chg < 0 (False)
        # 2023-01-06: pct_chg > 0 (True)
        # 2023-01-07: pct_chg > 0 (True)

        # volume_ratio (rolling 2-day mean):
        # 2023-01-01: 100 / 100 = 1
        # 2023-01-02: 120 / ((100+120)/2) = 1.09
        # 2023-01-03: 110 / ((120+110)/2) = 0.95
        # 2023-01-04: 90 / ((110+90)/2) = 0.9
        # 2023-01-05: 80 / ((90+80)/2) = 0.94
        # 2023-01-06: 70 / ((80+70)/2) = 0.93
        # 2023-01-07: 60 / ((70+60)/2) = 0.92

        # Segment 1 (pct_chg > 0): [T, T, T, F, F, T, T]
        # Segment 2 (volume_ratio < 1): [F, F, T, T, T, T, T]

        # A match requires 2 consecutive days of Segment 1, followed by 2 consecutive days of Segment 2.
        # Example:
        # Day X-3: S1 True
        # Day X-2: S1 True
        # Day X-1: S2 True
        # Day X: S2 True
        
        # Let's manually trace for a match ending on 2023-01-07:
        # Seg 2 (vol_ratio < 1): 2023-01-07 (T), 2023-01-06 (T) -> Match
        # Seg 1 (pct_chg > 0): 2023-01-05 (F), 2023-01-04 (F) -> No Match
        # So, no match ending on 2023-01-07.

        # Let's try ending on 2023-01-05:
        # Seg 2 (vol_ratio < 1): 2023-01-05 (T), 2023-01-04 (T) -> Match
        # Seg 1 (pct_chg > 0): 2023-01-03 (T), 2023-01-02 (T) -> Match
        # This should be a match: (2023-01-02, 2023-01-05)

        matches = _apply_sieve_to_stock(df, segments, conditions)
        self.assertIsInstance(matches, list)
        self.assertIn(('2023-01-02', '2023-01-05'), matches)


if __name__ == '__main__':
    unittest.main()
