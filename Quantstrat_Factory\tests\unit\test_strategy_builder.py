"""
策略构建器模块的单元测试。

遵循TDD原则，测试策略构建、管理和模板功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from src.strategy.builder.strategy_builder import StrategyBuilder
from src.strategy.builder.strategy_template import StrategyTemplate
from src.strategy.builder.strategy_manager import StrategyManager


class TestStrategyBuilder:
    """测试策略构建器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.builder = StrategyBuilder()
    
    def test_initialization(self):
        """测试策略构建器初始化。"""
        assert self.builder is not None
        assert hasattr(self.builder, 'strategy_config')
        assert hasattr(self.builder, 'factors')
        assert hasattr(self.builder, 'signals')
        assert hasattr(self.builder, 'risk_controls')
    
    def test_add_factor(self):
        """测试添加因子。"""
        factor_config = {
            'name': 'momentum_20',
            'type': 'technical',
            'weight': 0.5,
            'parameters': {'period': 20}
        }
        
        result = self.builder.add_factor(factor_config)
        
        assert result is True
        assert len(self.builder.factors) == 1
        assert self.builder.factors[0]['name'] == 'momentum_20'
    
    def test_add_signal_rule(self):
        """测试添加信号规则。"""
        signal_config = {
            'name': 'long_signal',
            'condition': 'momentum_20 > 0.05',
            'action': 'buy',
            'weight': 1.0
        }
        
        result = self.builder.add_signal_rule(signal_config)
        
        assert result is True
        assert len(self.builder.signals) == 1
        assert self.builder.signals[0]['name'] == 'long_signal'
    
    def test_add_risk_control(self):
        """测试添加风险控制。"""
        risk_config = {
            'name': 'position_limit',
            'type': 'position',
            'max_position': 0.1,
            'stop_loss': 0.05
        }
        
        result = self.builder.add_risk_control(risk_config)
        
        assert result is True
        assert len(self.builder.risk_controls) == 1
        assert self.builder.risk_controls[0]['name'] == 'position_limit'
    
    def test_build_strategy(self):
        """测试构建策略。"""
        # 添加因子
        self.builder.add_factor({
            'name': 'momentum_20',
            'type': 'technical',
            'weight': 0.6
        })
        
        self.builder.add_factor({
            'name': 'volatility_20',
            'type': 'technical',
            'weight': 0.4
        })
        
        # 添加信号规则
        self.builder.add_signal_rule({
            'name': 'long_signal',
            'condition': 'composite_score > 0.5',
            'action': 'buy'
        })
        
        # 构建策略
        strategy = self.builder.build_strategy('test_strategy')
        
        assert strategy is not None
        assert strategy['name'] == 'test_strategy'
        assert len(strategy['factors']) == 2
        assert len(strategy['signals']) == 1
    
    def test_validate_strategy(self):
        """测试策略验证。"""
        # 构建一个完整的策略
        self.builder.add_factor({
            'name': 'momentum_20',
            'type': 'technical',
            'weight': 1.0  # 权重总和为1.0
        })

        self.builder.add_signal_rule({
            'name': 'long_signal',
            'condition': 'momentum_20 > 0',
            'action': 'buy'
        })

        strategy = self.builder.build_strategy('valid_strategy')

        # 验证策略
        is_valid, errors = self.builder.validate_strategy(strategy)

        assert is_valid is True
        assert len(errors) == 0
    
    def test_validate_invalid_strategy(self):
        """测试无效策略验证。"""
        # 构建一个无效的策略（没有因子）
        strategy = self.builder.build_strategy('invalid_strategy')
        
        # 验证策略
        is_valid, errors = self.builder.validate_strategy(strategy)
        
        assert is_valid is False
        assert len(errors) > 0
        assert any('因子' in error for error in errors)


class TestStrategyTemplate:
    """测试策略模板。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.template = StrategyTemplate()
    
    def test_get_available_templates(self):
        """测试获取可用模板。"""
        templates = self.template.get_available_templates()
        
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # 检查模板结构
        for template in templates:
            assert 'name' in template
            assert 'description' in template
            assert 'category' in template
    
    def test_get_momentum_template(self):
        """测试获取动量策略模板。"""
        template = self.template.get_template('momentum_strategy')
        
        assert template is not None
        assert template['name'] == 'momentum_strategy'
        assert 'factors' in template
        assert 'signals' in template
        assert len(template['factors']) > 0
    
    def test_get_mean_reversion_template(self):
        """测试获取均值回归策略模板。"""
        template = self.template.get_template('mean_reversion_strategy')
        
        assert template is not None
        assert template['name'] == 'mean_reversion_strategy'
        assert 'factors' in template
        assert 'signals' in template
    
    def test_get_multi_factor_template(self):
        """测试获取多因子策略模板。"""
        template = self.template.get_template('multi_factor_strategy')
        
        assert template is not None
        assert template['name'] == 'multi_factor_strategy'
        assert len(template['factors']) >= 2
    
    def test_create_custom_template(self):
        """测试创建自定义模板。"""
        custom_config = {
            'name': 'custom_strategy',
            'description': '自定义策略模板',
            'category': 'custom',
            'factors': [
                {'name': 'rsi_14', 'type': 'technical', 'weight': 1.0}
            ],
            'signals': [
                {'name': 'oversold', 'condition': 'rsi_14 < 30', 'action': 'buy'}
            ]
        }
        
        result = self.template.create_template(custom_config)
        
        assert result is True
        
        # 验证模板已创建
        template = self.template.get_template('custom_strategy')
        assert template is not None
        assert template['name'] == 'custom_strategy'
    
    def test_apply_template_to_builder(self):
        """测试将模板应用到构建器。"""
        builder = StrategyBuilder()
        template = self.template.get_template('momentum_strategy')
        
        result = self.template.apply_template(builder, template)
        
        assert result is True
        assert len(builder.factors) > 0
        assert len(builder.signals) > 0


class TestStrategyManager:
    """测试策略管理器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.manager = StrategyManager()
    
    def test_initialization(self):
        """测试策略管理器初始化。"""
        assert self.manager is not None
        assert hasattr(self.manager, 'strategies')
        assert isinstance(self.manager.strategies, dict)
    
    def test_save_strategy(self):
        """测试保存策略。"""
        strategy = {
            'name': 'test_strategy',
            'description': '测试策略',
            'factors': [
                {'name': 'momentum_20', 'type': 'technical', 'weight': 1.0}
            ],
            'signals': [
                {'name': 'long_signal', 'condition': 'momentum_20 > 0', 'action': 'buy'}
            ],
            'created_at': datetime.now(),
            'version': '1.0'
        }
        
        result = self.manager.save_strategy(strategy)
        
        assert result is True
        assert 'test_strategy' in self.manager.strategies
    
    def test_load_strategy(self):
        """测试加载策略。"""
        # 先保存一个策略
        strategy = {
            'name': 'load_test_strategy',
            'description': '加载测试策略',
            'factors': [],
            'signals': []
        }
        
        self.manager.save_strategy(strategy)
        
        # 加载策略
        loaded_strategy = self.manager.load_strategy('load_test_strategy')
        
        assert loaded_strategy is not None
        assert loaded_strategy['name'] == 'load_test_strategy'
    
    def test_list_strategies(self):
        """测试列出策略。"""
        # 保存几个策略
        for i in range(3):
            strategy = {
                'name': f'strategy_{i}',
                'description': f'策略 {i}',
                'factors': [],
                'signals': []
            }
            self.manager.save_strategy(strategy)
        
        strategies = self.manager.list_strategies()
        
        assert isinstance(strategies, list)
        assert len(strategies) >= 3
        
        # 检查策略信息
        strategy_names = [s['name'] for s in strategies]
        assert 'strategy_0' in strategy_names
        assert 'strategy_1' in strategy_names
        assert 'strategy_2' in strategy_names
    
    def test_delete_strategy(self):
        """测试删除策略。"""
        # 先保存一个策略
        strategy = {
            'name': 'delete_test_strategy',
            'description': '删除测试策略',
            'factors': [],
            'signals': []
        }
        
        self.manager.save_strategy(strategy)
        assert 'delete_test_strategy' in self.manager.strategies
        
        # 删除策略
        result = self.manager.delete_strategy('delete_test_strategy')
        
        assert result is True
        assert 'delete_test_strategy' not in self.manager.strategies
    
    def test_update_strategy(self):
        """测试更新策略。"""
        # 先保存一个策略
        strategy = {
            'name': 'update_test_strategy',
            'description': '更新测试策略',
            'factors': [],
            'signals': [],
            'version': '1.0'
        }
        
        self.manager.save_strategy(strategy)
        
        # 更新策略
        updated_strategy = strategy.copy()
        updated_strategy['description'] = '已更新的测试策略'
        updated_strategy['version'] = '1.1'
        
        result = self.manager.update_strategy(updated_strategy)
        
        assert result is True
        
        # 验证更新
        loaded_strategy = self.manager.load_strategy('update_test_strategy')
        assert loaded_strategy['description'] == '已更新的测试策略'
        assert loaded_strategy['version'] == '1.1'
    
    def test_clone_strategy(self):
        """测试克隆策略。"""
        # 先保存一个策略
        original_strategy = {
            'name': 'original_strategy',
            'description': '原始策略',
            'factors': [
                {'name': 'momentum_20', 'type': 'technical', 'weight': 1.0}
            ],
            'signals': [
                {'name': 'long_signal', 'condition': 'momentum_20 > 0', 'action': 'buy'}
            ]
        }
        
        self.manager.save_strategy(original_strategy)
        
        # 克隆策略
        cloned_strategy = self.manager.clone_strategy('original_strategy', 'cloned_strategy')
        
        assert cloned_strategy is not None
        assert cloned_strategy['name'] == 'cloned_strategy'
        assert cloned_strategy['description'] == '原始策略 (副本)'
        assert len(cloned_strategy['factors']) == len(original_strategy['factors'])
        assert len(cloned_strategy['signals']) == len(original_strategy['signals'])
        
        # 验证克隆的策略已保存
        assert 'cloned_strategy' in self.manager.strategies


class TestStrategyValidation:
    """测试策略验证功能。"""
    
    def test_validate_factor_weights(self):
        """测试因子权重验证。"""
        builder = StrategyBuilder()
        
        # 添加权重总和不为1的因子
        builder.add_factor({'name': 'factor1', 'weight': 0.3})
        builder.add_factor({'name': 'factor2', 'weight': 0.4})
        
        strategy = builder.build_strategy('test_strategy')
        is_valid, errors = builder.validate_strategy(strategy)
        
        # 应该有权重警告
        assert any('权重' in error for error in errors)
    
    def test_validate_signal_conditions(self):
        """测试信号条件验证。"""
        builder = StrategyBuilder()
        
        # 添加无效的信号条件
        builder.add_signal_rule({
            'name': 'invalid_signal',
            'condition': 'invalid_factor > 0',  # 引用不存在的因子
            'action': 'buy'
        })
        
        strategy = builder.build_strategy('test_strategy')
        is_valid, errors = builder.validate_strategy(strategy)
        
        # 应该有信号条件错误
        assert any('信号' in error or '条件' in error for error in errors)
    
    def test_validate_risk_controls(self):
        """测试风险控制验证。"""
        builder = StrategyBuilder()
        
        # 添加无效的风险控制
        builder.add_risk_control({
            'name': 'invalid_risk',
            'type': 'position',
            'max_position': 1.5  # 超过100%
        })
        
        strategy = builder.build_strategy('test_strategy')
        is_valid, errors = builder.validate_strategy(strategy)
        
        # 应该有风险控制错误
        assert any('风险' in error or '仓位' in error for error in errors)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
