"""
技术指标模块的单元测试。

遵循TDD原则，测试各种技术指标的计算功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch

from src.features.indicators.technical_indicators import TechnicalIndicators


class TestTechnicalIndicators:
    """测试技术指标计算器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.calculator = TechnicalIndicators()
        
        # 创建测试数据
        np.random.seed(42)
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        
        self.test_data = pd.DataFrame({
            'date': dates,
            'open': 100 + np.random.randn(100).cumsum() * 0.5,
            'high': 100 + np.random.randn(100).cumsum() * 0.5 + 1,
            'low': 100 + np.random.randn(100).cumsum() * 0.5 - 1,
            'close': 100 + np.random.randn(100).cumsum() * 0.5,
            'volume': np.random.randint(1000000, 10000000, 100)
        })
        
        # 确保价格关系正确
        self.test_data['high'] = self.test_data[['open', 'high', 'close']].max(axis=1) + 0.5
        self.test_data['low'] = self.test_data[['open', 'low', 'close']].min(axis=1) - 0.5
    
    def test_calculate_sma(self):
        """测试简单移动平均线计算。"""
        close_prices = self.test_data['close']
        
        # 测试20日SMA
        sma_20 = self.calculator.calculate_sma(close_prices, period=20)
        
        # 验证结果
        assert isinstance(sma_20, pd.Series)
        assert len(sma_20) == len(close_prices)
        
        # 前19个值应该是NaN
        assert pd.isna(sma_20.iloc[:19]).all()
        
        # 第20个值应该是前20个收盘价的平均值
        expected_sma_20 = close_prices.iloc[:20].mean()
        assert abs(sma_20.iloc[19] - expected_sma_20) < 1e-10
        
        # 测试边界情况
        sma_1 = self.calculator.calculate_sma(close_prices, period=1)
        pd.testing.assert_series_equal(sma_1, close_prices, check_names=False)
    
    def test_calculate_ema(self):
        """测试指数移动平均线计算。"""
        close_prices = self.test_data['close']
        
        # 测试12日EMA
        ema_12 = self.calculator.calculate_ema(close_prices, period=12)
        
        # 验证结果
        assert isinstance(ema_12, pd.Series)
        assert len(ema_12) == len(close_prices)
        
        # EMA的第一个值应该等于第一个收盘价
        assert abs(ema_12.iloc[0] - close_prices.iloc[0]) < 1e-10
        
        # EMA应该是递增计算的
        assert not pd.isna(ema_12.iloc[1])
    
    def test_calculate_rsi(self):
        """测试RSI指标计算。"""
        close_prices = self.test_data['close']
        
        # 测试14日RSI
        rsi_14 = self.calculator.calculate_rsi(close_prices, period=14)
        
        # 验证结果
        assert isinstance(rsi_14, pd.Series)
        assert len(rsi_14) == len(close_prices)
        
        # RSI值应该在0-100之间
        valid_rsi = rsi_14.dropna()
        assert (valid_rsi >= 0).all()
        assert (valid_rsi <= 100).all()
        
        # 前13个值应该是NaN（因为需要计算差值和滚动平均）
        assert pd.isna(rsi_14.iloc[:13]).all()
    
    def test_calculate_macd(self):
        """测试MACD指标计算。"""
        close_prices = self.test_data['close']
        
        # 测试MACD计算
        macd_result = self.calculator.calculate_macd(close_prices)
        
        # 验证返回结果结构
        assert isinstance(macd_result, dict)
        assert 'macd' in macd_result
        assert 'signal' in macd_result
        assert 'histogram' in macd_result
        
        # 验证各个序列
        macd = macd_result['macd']
        signal = macd_result['signal']
        histogram = macd_result['histogram']
        
        assert isinstance(macd, pd.Series)
        assert isinstance(signal, pd.Series)
        assert isinstance(histogram, pd.Series)
        
        # 验证柱状图 = MACD - 信号线
        pd.testing.assert_series_equal(histogram, macd - signal, check_names=False)
    
    def test_calculate_kdj(self):
        """测试KDJ指标计算。"""
        high = self.test_data['high']
        low = self.test_data['low']
        close = self.test_data['close']
        
        # 测试KDJ计算
        kdj_result = self.calculator.calculate_kdj(high, low, close)
        
        # 验证返回结果结构
        assert isinstance(kdj_result, dict)
        assert 'k' in kdj_result
        assert 'd' in kdj_result
        assert 'j' in kdj_result
        
        k = kdj_result['k']
        d = kdj_result['d']
        j = kdj_result['j']
        
        # 验证K、D值在0-100之间
        valid_k = k.dropna()
        valid_d = d.dropna()
        
        if len(valid_k) > 0:
            assert (valid_k >= 0).all()
            assert (valid_k <= 100).all()
        
        if len(valid_d) > 0:
            assert (valid_d >= 0).all()
            assert (valid_d <= 100).all()
        
        # 验证J值计算公式：J = 3K - 2D
        valid_indices = ~(k.isna() | d.isna())
        if valid_indices.any():
            expected_j = 3 * k[valid_indices] - 2 * d[valid_indices]
            pd.testing.assert_series_equal(
                j[valid_indices], 
                expected_j, 
                check_names=False,
                rtol=1e-10
            )
    
    def test_calculate_bollinger_bands(self):
        """测试布林带计算。"""
        close_prices = self.test_data['close']
        
        # 测试布林带计算
        bb_result = self.calculator.calculate_bollinger_bands(close_prices, period=20, std_dev=2.0)
        
        # 验证返回结果结构
        assert isinstance(bb_result, dict)
        assert 'upper' in bb_result
        assert 'middle' in bb_result
        assert 'lower' in bb_result
        
        upper = bb_result['upper']
        middle = bb_result['middle']
        lower = bb_result['lower']
        
        # 验证中轨是SMA
        expected_middle = self.calculator.calculate_sma(close_prices, period=20)
        pd.testing.assert_series_equal(middle, expected_middle, check_names=False)
        
        # 验证上轨 > 中轨 > 下轨
        valid_indices = ~(upper.isna() | middle.isna() | lower.isna())
        if valid_indices.any():
            assert (upper[valid_indices] >= middle[valid_indices]).all()
            assert (middle[valid_indices] >= lower[valid_indices]).all()
    
    def test_calculate_atr(self):
        """测试ATR指标计算。"""
        high = self.test_data['high']
        low = self.test_data['low']
        close = self.test_data['close']
        
        # 测试ATR计算
        atr = self.calculator.calculate_atr(high, low, close, period=14)
        
        # 验证结果
        assert isinstance(atr, pd.Series)
        assert len(atr) == len(close)
        
        # ATR值应该大于等于0
        valid_atr = atr.dropna()
        assert (valid_atr >= 0).all()
        
        # 前13个值应该是NaN
        assert pd.isna(atr.iloc[:13]).all()
    
    def test_calculate_williams_r(self):
        """测试威廉指标计算。"""
        high = self.test_data['high']
        low = self.test_data['low']
        close = self.test_data['close']
        
        # 测试Williams %R计算
        wr = self.calculator.calculate_williams_r(high, low, close, period=14)
        
        # 验证结果
        assert isinstance(wr, pd.Series)
        assert len(wr) == len(close)
        
        # Williams %R值应该在-100到0之间
        valid_wr = wr.dropna()
        if len(valid_wr) > 0:
            assert (valid_wr >= -100).all()
            assert (valid_wr <= 0).all()
    
    def test_calculate_cci(self):
        """测试CCI指标计算。"""
        high = self.test_data['high']
        low = self.test_data['low']
        close = self.test_data['close']
        
        # 测试CCI计算
        cci = self.calculator.calculate_cci(high, low, close, period=20)
        
        # 验证结果
        assert isinstance(cci, pd.Series)
        assert len(cci) == len(close)
        
        # CCI值通常在-200到+200之间，但理论上可以超出这个范围
        valid_cci = cci.dropna()
        assert len(valid_cci) > 0
    
    def test_calculate_obv(self):
        """测试OBV指标计算。"""
        close = self.test_data['close']
        volume = self.test_data['volume']
        
        # 测试OBV计算
        obv = self.calculator.calculate_obv(close, volume)
        
        # 验证结果
        assert isinstance(obv, pd.Series)
        assert len(obv) == len(close)
        
        # 第一个OBV值应该等于第一个成交量
        assert abs(obv.iloc[0] - volume.iloc[0]) < 1e-10
        
        # OBV应该是累积的
        assert not pd.isna(obv).any()
    
    def test_calculate_multiple_indicators(self):
        """测试批量计算多个指标。"""
        indicators = ['sma_20', 'ema_12', 'rsi_14', 'macd', 'kdj', 'bollinger']
        
        # 测试批量计算
        result = self.calculator.calculate_multiple_indicators(self.test_data, indicators)
        
        # 验证结果包含原始数据
        for col in self.test_data.columns:
            assert col in result.columns
        
        # 验证新增的指标列
        assert 'sma_20' in result.columns
        assert 'ema_12' in result.columns
        assert 'rsi_14' in result.columns
        assert 'macd' in result.columns
        assert 'macd_signal' in result.columns
        assert 'macd_histogram' in result.columns
        assert 'kdj_k' in result.columns
        assert 'kdj_d' in result.columns
        assert 'kdj_j' in result.columns
        assert 'bb_upper' in result.columns
        assert 'bb_middle' in result.columns
        assert 'bb_lower' in result.columns
    
    def test_edge_cases(self):
        """测试边界情况。"""
        # 测试空数据
        empty_series = pd.Series([], dtype=float)
        sma_empty = self.calculator.calculate_sma(empty_series, period=20)
        assert len(sma_empty) == 0
        
        # 测试单个数据点
        single_point = pd.Series([100.0])
        sma_single = self.calculator.calculate_sma(single_point, period=1)
        assert sma_single.iloc[0] == 100.0
        
        # 测试周期大于数据长度
        short_data = pd.Series([100, 101, 102])
        sma_long_period = self.calculator.calculate_sma(short_data, period=10)
        assert pd.isna(sma_long_period).all()
    
    def test_data_validation(self):
        """测试数据验证。"""
        # 测试缺少必要列的情况
        incomplete_data = pd.DataFrame({
            'open': [100, 101, 102],
            'close': [100, 101, 102]
            # 缺少 high, low, volume
        })
        
        result = self.calculator.calculate_multiple_indicators(incomplete_data, ['kdj'])
        
        # 应该返回原始数据，但不包含KDJ指标
        assert 'kdj_k' not in result.columns
        assert len(result) == len(incomplete_data)


@pytest.fixture
def sample_ohlcv_data():
    """生成样本OHLCV数据。"""
    np.random.seed(42)
    dates = pd.date_range('2023-01-01', periods=50, freq='D')
    
    # 生成更真实的价格数据
    base_price = 100
    returns = np.random.normal(0, 0.02, 50)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    data = pd.DataFrame({
        'date': dates,
        'close': prices
    })
    
    # 生成OHLV数据
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, 50))
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, 50))
    data['volume'] = np.random.randint(1000000, 10000000, 50)
    
    return data


class TestIndicatorAccuracy:
    """测试指标计算精度。"""
    
    def test_sma_accuracy(self, sample_ohlcv_data):
        """测试SMA计算精度。"""
        calculator = TechnicalIndicators()
        close_prices = sample_ohlcv_data['close']
        
        # 计算5日SMA
        sma_5 = calculator.calculate_sma(close_prices, period=5)
        
        # 手动验证第5个值
        expected_value = close_prices.iloc[:5].mean()
        assert abs(sma_5.iloc[4] - expected_value) < 1e-10
        
        # 手动验证第10个值
        expected_value = close_prices.iloc[5:10].mean()
        assert abs(sma_5.iloc[9] - expected_value) < 1e-10
    
    def test_rsi_boundary_values(self, sample_ohlcv_data):
        """测试RSI边界值。"""
        calculator = TechnicalIndicators()
        
        # 创建极端上涨的数据（更强的趋势）
        rising_prices = pd.Series([100 + i for i in range(30)])  # 连续30天上涨
        rsi_rising = calculator.calculate_rsi(rising_prices, period=14)

        # RSI应该较高（但不一定>80，因为RSI计算的是相对强度）
        final_rsi = rsi_rising.dropna().iloc[-1]
        assert final_rsi > 50  # 上涨趋势应该有较高RSI

        # 创建极端下跌的数据
        falling_prices = pd.Series([100 - i for i in range(30)])  # 连续30天下跌
        rsi_falling = calculator.calculate_rsi(falling_prices, period=14)

        # RSI应该较低
        final_rsi = rsi_falling.dropna().iloc[-1]
        assert final_rsi < 50  # 下跌趋势应该有较低RSI


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
