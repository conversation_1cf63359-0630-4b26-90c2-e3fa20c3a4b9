"""
可视化模块的单元测试。

遵循TDD原则，测试图表生成、报告系统和数据探索功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import tempfile
from pathlib import Path

from src.visualization.chart_generator import ChartGenerator
from src.visualization.report_generator import ReportGenerator
from src.visualization.dashboard import Dashboard


class TestChartGenerator:
    """测试图表生成器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.chart_gen = ChartGenerator()
    
    def test_initialization(self):
        """测试图表生成器初始化。"""
        assert self.chart_gen is not None
        assert hasattr(self.chart_gen, 'supported_chart_types')
        assert isinstance(self.chart_gen.supported_chart_types, list)
    
    def test_create_line_chart(self, sample_time_series_data):
        """测试创建线图。"""
        chart = self.chart_gen.create_line_chart(
            data=sample_time_series_data,
            x_col='date',
            y_col='value',
            title='测试线图'
        )
        
        assert chart is not None
        assert hasattr(chart, 'to_html') or hasattr(chart, 'to_json')
    
    def test_create_candlestick_chart(self, sample_ohlcv_data):
        """测试创建K线图。"""
        chart = self.chart_gen.create_candlestick_chart(
            data=sample_ohlcv_data,
            title='测试K线图'
        )
        
        assert chart is not None
    
    def test_create_bar_chart(self, sample_categorical_data):
        """测试创建柱状图。"""
        chart = self.chart_gen.create_bar_chart(
            data=sample_categorical_data,
            x_col='category',
            y_col='value',
            title='测试柱状图'
        )
        
        assert chart is not None
    
    def test_create_scatter_plot(self, sample_scatter_data):
        """测试创建散点图。"""
        chart = self.chart_gen.create_scatter_plot(
            data=sample_scatter_data,
            x_col='x',
            y_col='y',
            title='测试散点图'
        )
        
        assert chart is not None
    
    def test_create_heatmap(self, sample_correlation_matrix):
        """测试创建热力图。"""
        chart = self.chart_gen.create_heatmap(
            data=sample_correlation_matrix,
            title='测试热力图'
        )
        
        assert chart is not None
    
    def test_create_histogram(self, sample_distribution_data):
        """测试创建直方图。"""
        chart = self.chart_gen.create_histogram(
            data=sample_distribution_data,
            column='value',
            title='测试直方图'
        )
        
        assert chart is not None
    
    def test_create_box_plot(self, sample_distribution_data):
        """测试创建箱线图。"""
        chart = self.chart_gen.create_box_plot(
            data=sample_distribution_data,
            y_col='value',
            title='测试箱线图'
        )
        
        assert chart is not None
    
    def test_create_performance_chart(self, sample_performance_data):
        """测试创建业绩图表。"""
        chart = self.chart_gen.create_performance_chart(
            data=sample_performance_data,
            title='测试业绩图表'
        )
        
        assert chart is not None
    
    def test_create_drawdown_chart(self, sample_performance_data):
        """测试创建回撤图表。"""
        chart = self.chart_gen.create_drawdown_chart(
            data=sample_performance_data,
            title='测试回撤图表'
        )
        
        assert chart is not None
    
    def test_save_chart(self, sample_time_series_data, temp_dir):
        """测试保存图表。"""
        chart = self.chart_gen.create_line_chart(
            data=sample_time_series_data,
            x_col='date',
            y_col='value',
            title='测试保存'
        )
        
        output_path = temp_dir / "test_chart.html"
        result = self.chart_gen.save_chart(chart, str(output_path))
        
        assert result is True
        assert output_path.exists()
    
    def test_batch_create_charts(self, sample_time_series_data):
        """测试批量创建图表。"""
        chart_configs = [
            {
                'type': 'line',
                'data': sample_time_series_data,
                'x_col': 'date',
                'y_col': 'value',
                'title': '图表1'
            },
            {
                'type': 'bar',
                'data': sample_time_series_data.head(10),
                'x_col': 'date',
                'y_col': 'value',
                'title': '图表2'
            }
        ]
        
        charts = self.chart_gen.batch_create_charts(chart_configs)
        
        assert isinstance(charts, list)
        assert len(charts) == 2


class TestReportGenerator:
    """测试报告生成器。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.report_gen = ReportGenerator()
    
    def test_initialization(self):
        """测试报告生成器初始化。"""
        assert self.report_gen is not None
        assert hasattr(self.report_gen, 'supported_formats')
        assert isinstance(self.report_gen.supported_formats, list)
    
    def test_create_factor_analysis_report(self, sample_factor_data):
        """测试创建因子分析报告。"""
        report = self.report_gen.create_factor_analysis_report(
            factor_data=sample_factor_data,
            title='因子分析报告'
        )
        
        assert report is not None
        assert 'title' in report
        assert 'sections' in report
        assert len(report['sections']) > 0
    
    def test_create_backtest_report(self, sample_backtest_results):
        """测试创建回测报告。"""
        report = self.report_gen.create_backtest_report(
            backtest_results=sample_backtest_results,
            title='回测报告'
        )
        
        assert report is not None
        assert 'performance_metrics' in report
        assert 'charts' in report
    
    def test_create_strategy_report(self, sample_strategy_data):
        """测试创建策略报告。"""
        report = self.report_gen.create_strategy_report(
            strategy_data=sample_strategy_data,
            title='策略报告'
        )
        
        assert report is not None
        assert 'strategy_info' in report
        assert 'performance' in report
    
    def test_create_risk_report(self, sample_risk_data):
        """测试创建风险报告。"""
        report = self.report_gen.create_risk_report(
            risk_data=sample_risk_data,
            title='风险报告'
        )
        
        assert report is not None
        assert 'risk_metrics' in report
    
    def test_export_report_html(self, sample_factor_data, temp_dir):
        """测试导出HTML报告。"""
        report = self.report_gen.create_factor_analysis_report(
            factor_data=sample_factor_data,
            title='测试报告'
        )
        
        output_path = temp_dir / "test_report.html"
        result = self.report_gen.export_report(report, str(output_path), format='html')
        
        assert result is True
        assert output_path.exists()
    
    def test_export_report_pdf(self, sample_factor_data, temp_dir):
        """测试导出PDF报告。"""
        report = self.report_gen.create_factor_analysis_report(
            factor_data=sample_factor_data,
            title='测试报告'
        )
        
        output_path = temp_dir / "test_report.pdf"
        result = self.report_gen.export_report(report, str(output_path), format='pdf')
        
        # PDF导出可能需要额外依赖，如果失败则跳过
        if result:
            assert output_path.exists()
    
    def test_add_custom_section(self, sample_factor_data):
        """测试添加自定义报告章节。"""
        report = self.report_gen.create_factor_analysis_report(
            factor_data=sample_factor_data,
            title='测试报告'
        )
        
        custom_section = {
            'title': '自定义章节',
            'content': '这是自定义内容',
            'charts': []
        }
        
        updated_report = self.report_gen.add_section(report, custom_section)
        
        assert len(updated_report['sections']) > len(report['sections'])
        assert any(section['title'] == '自定义章节' for section in updated_report['sections'])


class TestDashboard:
    """测试仪表板。"""
    
    def setup_method(self):
        """设置测试环境。"""
        self.dashboard = Dashboard()
    
    def test_initialization(self):
        """测试仪表板初始化。"""
        assert self.dashboard is not None
        assert hasattr(self.dashboard, 'widgets')
        assert isinstance(self.dashboard.widgets, list)
    
    def test_add_widget(self):
        """测试添加组件。"""
        widget_config = {
            'type': 'chart',
            'title': '测试图表',
            'data_source': 'test_data',
            'chart_type': 'line'
        }
        
        result = self.dashboard.add_widget(widget_config)
        
        assert result is True
        assert len(self.dashboard.widgets) == 1
        assert self.dashboard.widgets[0]['title'] == '测试图表'
    
    def test_remove_widget(self):
        """测试移除组件。"""
        # 先添加一个组件
        widget_config = {
            'type': 'chart',
            'title': '测试图表',
            'data_source': 'test_data'
        }
        
        self.dashboard.add_widget(widget_config)
        widget_id = self.dashboard.widgets[0]['id']
        
        # 移除组件
        result = self.dashboard.remove_widget(widget_id)
        
        assert result is True
        assert len(self.dashboard.widgets) == 0
    
    def test_update_widget(self):
        """测试更新组件。"""
        # 先添加一个组件
        widget_config = {
            'type': 'chart',
            'title': '原始标题',
            'data_source': 'test_data'
        }
        
        self.dashboard.add_widget(widget_config)
        widget_id = self.dashboard.widgets[0]['id']
        
        # 更新组件
        updated_config = {
            'title': '更新后的标题',
            'chart_type': 'bar'
        }
        
        result = self.dashboard.update_widget(widget_id, updated_config)
        
        assert result is True
        assert self.dashboard.widgets[0]['title'] == '更新后的标题'
        assert self.dashboard.widgets[0]['chart_type'] == 'bar'
    
    def test_get_layout(self):
        """测试获取布局。"""
        # 添加几个组件
        for i in range(3):
            widget_config = {
                'type': 'chart',
                'title': f'图表{i+1}',
                'data_source': f'data_{i+1}'
            }
            self.dashboard.add_widget(widget_config)
        
        layout = self.dashboard.get_layout()
        
        assert isinstance(layout, dict)
        assert 'widgets' in layout
        assert len(layout['widgets']) == 3
    
    def test_save_dashboard(self, temp_dir):
        """测试保存仪表板。"""
        # 添加组件
        widget_config = {
            'type': 'chart',
            'title': '测试图表',
            'data_source': 'test_data'
        }
        self.dashboard.add_widget(widget_config)
        
        # 保存仪表板
        save_path = temp_dir / "test_dashboard.json"
        result = self.dashboard.save_dashboard(str(save_path))
        
        assert result is True
        assert save_path.exists()
    
    def test_load_dashboard(self, temp_dir):
        """测试加载仪表板。"""
        # 先保存一个仪表板
        widget_config = {
            'type': 'chart',
            'title': '测试图表',
            'data_source': 'test_data'
        }
        self.dashboard.add_widget(widget_config)
        
        save_path = temp_dir / "test_dashboard.json"
        self.dashboard.save_dashboard(str(save_path))
        
        # 创建新的仪表板实例并加载
        new_dashboard = Dashboard()
        result = new_dashboard.load_dashboard(str(save_path))
        
        assert result is True
        assert len(new_dashboard.widgets) == 1
        assert new_dashboard.widgets[0]['title'] == '测试图表'


# Fixtures for test data
@pytest.fixture
def sample_time_series_data():
    """生成时间序列测试数据。"""
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    values = np.cumsum(np.random.randn(100)) + 100
    
    return pd.DataFrame({
        'date': dates,
        'value': values
    })


@pytest.fixture
def sample_categorical_data():
    """生成分类测试数据。"""
    categories = ['A', 'B', 'C', 'D', 'E']
    values = np.random.randint(10, 100, 5)
    
    return pd.DataFrame({
        'category': categories,
        'value': values
    })


@pytest.fixture
def sample_scatter_data():
    """生成散点图测试数据。"""
    n = 100
    x = np.random.randn(n)
    y = 2 * x + np.random.randn(n) * 0.5
    
    return pd.DataFrame({
        'x': x,
        'y': y
    })


@pytest.fixture
def sample_correlation_matrix():
    """生成相关性矩阵测试数据。"""
    np.random.seed(42)
    data = np.random.randn(100, 5)
    df = pd.DataFrame(data, columns=['A', 'B', 'C', 'D', 'E'])
    return df.corr()


@pytest.fixture
def sample_distribution_data():
    """生成分布测试数据。"""
    values = np.random.normal(0, 1, 1000)
    
    return pd.DataFrame({
        'value': values
    })


@pytest.fixture
def sample_performance_data():
    """生成业绩测试数据。"""
    dates = pd.date_range('2023-01-01', periods=252, freq='D')
    returns = np.random.normal(0.001, 0.02, 252)
    cumulative_returns = (1 + pd.Series(returns)).cumprod()
    
    return pd.DataFrame({
        'date': dates,
        'cumulative_return': cumulative_returns,
        'daily_return': returns
    })


@pytest.fixture
def sample_backtest_results():
    """生成回测结果测试数据。"""
    return {
        'total_return': 0.25,
        'annual_return': 0.12,
        'volatility': 0.15,
        'sharpe_ratio': 0.8,
        'max_drawdown': -0.08,
        'win_rate': 0.55,
        'profit_loss_ratio': 1.2,
        'trades': 150
    }


@pytest.fixture
def sample_strategy_data():
    """生成策略测试数据。"""
    return {
        'name': 'test_strategy',
        'description': '测试策略',
        'factors': ['momentum', 'volatility'],
        'performance': {
            'return': 0.15,
            'volatility': 0.12,
            'sharpe': 1.25
        }
    }


@pytest.fixture
def sample_risk_data():
    """生成风险测试数据。"""
    return {
        'var_95': -0.025,
        'var_99': -0.045,
        'expected_shortfall': -0.055,
        'beta': 1.1,
        'tracking_error': 0.08
    }


@pytest.fixture
def temp_dir():
    """创建临时目录。"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    import shutil
    shutil.rmtree(temp_path)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
